<?php

use App\Jobs\PushNotification;
use App\Models\Notification;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Str;

uses(RefreshDatabase::class);

/*test('new users can register', function () {
    // phone 11 ky tu
    $response = $this->post('/api/v1/user/authenticate', [
        'phone' => '09881711771',
    ]);

    expect($response->json())
        ->toHaveKey('data.apiToken')
        ->error_code->toBe(0);
});*/

/*
test('can update user information', function () {
    $this->actingAs($user = User::factory()->create());

    /**
     * name - ít nhất 3 ký tự, nhiều nhất 190 ký tự
     * position - ít nhất 5 ký tự, nhiều nhất 190 ký tự
     */
    /*$testName = 'Hello world';
    $position = 'staff';
    $birthday = '2000-01-01';

    $this->post('/api/v1/user/update-information', [
        'name' => $testName,
        'position' => $position,
        'birthday' => $birthday,
    ]);

   expect($user->fresh())
       ->name->toBe($testName)
       ->position->toBe($position)
       ->birthday->toDateString()->toBe($birthday);
});
*/

test('can get user profile', function () {
    $this->actingAs($user = User::factory()->create());

    $response = $this->get('/api/v1/user/profile', [
        'user_id' => $user->getKey(),
    ]);

    expect($response->json())
        ->toHaveKey('data.user')
        ->error_code->toBe(0);
});

/*
test('user can toggle follow', function () {
    $firstUser = User::factory()->create();
    $this->actingAs(User::factory()->create());

    Queue::fake();

    $response = $this->post('/api/v1/user/toggle-follow', [
        'user_id' => $firstUser->getKey(),
    ]);

    Queue::assertPushed(PushNotification::class);

    expect($response->json())
        ->error_code->toBe(0)
        ->errors->toBeNull();
});

test('can get user followers', function () {
    $this->actingAs($user = User::factory()->create());

    $response = $this->get('/api/v1/user/followers', [
        'user_id' => $user->getKey(),
    ]);

    expect($response->json())
        ->toHaveKey('data.users')
        ->error_code->toBe(0);
});

test('can get user followings', function () {
    $this->actingAs($user = User::factory()->create());

    $response = $this->get('/api/v1/user/followings', [
        'user_id' => $user->getKey(),
    ]);

    expect($response->json())
        ->toHaveKey('data.users')
        ->error_code->toBe(0);
});
*/

test('can get user posts', function () {
    $this->actingAs($user = User::factory()->create());

    $response = $this->get('/api/v1/user/posts', [
        'user_id' => $user->getKey(),
    ]);

    expect($response->json())
        ->toHaveKey('data.posts')
        ->error_code->toBe(0);
});

test('can add user device-token', function () {
    /** @var User $user */
    $this->actingAs($user = User::factory()->create())
        ->post('/api/v1/user/add-device-token', [
            'device_token' => ($token = Str::random(100)),
        ]);

    expect($user->devices()->first())
        ->token->toBe($token)
        ->status->toBe(1);
});

test('user can get notifications', function () {
    /** @var User $user */
    $this->actingAs(User::factory()->create());

    $response = $this->get('/api/v1/user/notifications?type=all');

    expect($response->json())
        ->toHaveKey('data.notifications')
        ->toHaveKey('data.pins')
        ->error_code->toBe(0);
});

test('user can mark-notification-read', function () {
    /** @var User $user */
    $this->actingAs($user = User::factory()->create());

    Notification::factory()->create([
        'user_id' => $user->getKey(),
        'object_type' => 'post',
        'object_id' => 1,
        'type' => 0,
    ]);

    /** @var Notification $notification */
    $notification = $user->notifications()->first();
    expect($notification->status)->toBe(0);

    $this->post('/api/v1/user/mark-notification-read', [
        'id' => $notification->getKey(),
    ]);

    expect($notification->refresh()->status)->toBe(1);
});
