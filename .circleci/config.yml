version: 2.1

executors:
  self-hosted-vps:
    machine:
      resource_class: exidea_circleci_runner/almalinux
    working_directory: /tmp/answerr_workspace_dev

jobs:
  build-and-test:
    executor: self-hosted-vps
    steps:
      - run:
          name: Checkout
          command: git clone -b dev-new **************:ductv-exidea/honne-server.git ./

      - run:
          name: Build docker image for testing
          command: docker build -t exidea/answerr_dev:latest -f ./deployment/circleci/Dockerfile .

      - run:
          name: Run tests in container
          command: |
            docker run --rm \
              --memory=8g \
              --cpus=4 \
              -v $(pwd):/app \
              -w /app exidea/answerr_dev:latest \
              bash -c "./deployment/circleci/build-and-test.sh"

      - run:
          name: Clean workspace
          command: sudo rm -rf /tmp/answerr_workspace_dev/*

  deploy:
    executor: self-hosted-vps
    steps:
      - run:
          name: Deploy
          command: |
            ssh -i ~/.ssh/id_ed25519 $DEV_SSH_USER@$DEV_SSH_IP "/var/www/dev/exidea/honne-new/deploy_run.sh"

workflows:
  build-and-deploy:
    jobs:
      - build-and-test:
          filters:
            branches:
              only: dev-new

      - deploy:
          requires:
            - build-and-test
          filters:
            branches:
              only: dev-new
