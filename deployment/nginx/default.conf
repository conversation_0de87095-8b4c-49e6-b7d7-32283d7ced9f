server {
	listen 8000 default_server;
	#server_name staging.answerr.app console-staging.answerr.app;

	charset utf-8;
	client_max_body_size 128M;

	# Cache Static Files
	location ~* \.(html|css|js|png|jpg|jpeg|gif|ico|svg|eot|woff|ttf)$ {
		add_header Access-Control-Allow-Origin *;

		expires max;
		access_log off;
		add_header Cache-Control "public";
	}

	# Do not allow access to hidden files.
	location ~ /\. {
        deny all;
    }

	location = /favicon.ico {
        access_log off;
        log_not_found off;
    }

	location = /robots.txt  {
        access_log off;
        log_not_found off;
    }

	###	Security Headers
	# [WARNING] Strict-Transport-Security will stop HTTP access for specified time.
	add_header Strict-Transport-Security "max-age=63072000";

	# [WARNING] X-Frame-Options DENY will break iframed sites.
	add_header X-Frame-Options SAMEORIGIN;

	add_header X-XSS-Protection "1; mode=block";
	add_header X-Content-Type-Options "nosniff";
	add_header X-Powered-By "Honne-Application";

	error_page 404 /index.php;

	root /var/www/html/public;
	index index.php index.html index.htm;

	access_log off;
	error_log /dev/stdout;

	location / {
        auth_basic "Private Property";
        auth_basic_user_file /etc/nginx/.htpasswd;

        autoindex on;
        try_files $uri $uri/ @octane;
    }

    location /up {
        auth_basic off;
        default_type text/plain;
        return 200 "OK\n";
    }

    location /api/ {
        auth_basic off;
        try_files $uri $uri/ @octane;
    }

    location /news/ {
        auth_basic off;
        try_files $uri $uri/ @octane;
    }

    location /build/manifest.json {
        auth_basic off;
    }

	location = /index.php {
		try_files /nonexistent_file @octane;
	}

	location @octane {
		set $suffix "";

		if ($uri = /index.php) {
		    set $suffix ?$query_string;
		}

		proxy_http_version 1.1;
		proxy_set_header Host $http_host;
		proxy_set_header Scheme $scheme;
		proxy_set_header SERVER_PORT $server_port;
		proxy_set_header REMOTE_ADDR $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection $connection_upgrade;

		proxy_pass http://127.0.0.1:8001$suffix;
	}
}
