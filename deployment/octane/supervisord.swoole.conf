[program:octane]
process_name=%(program_name)s_%(process_num)s
command=php %(ENV_ROOT)s/artisan octane:swoole --host=0.0.0.0 --port=8001 --workers=auto --task-workers=1
user=%(ENV_USER)s
autostart=true
autorestart=true
environment=LARAVEL_OCTANE="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

#[program:queue]
#process_name=%(program_name)s_%(process_num)s
#command=php %(ENV_ROOT)s/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
#user=%(ENV_USER)s
#autostart=true
#autorestart=true
#stdout_logfile=/dev/stdout
#stdout_logfile_maxbytes=0
#stderr_logfile=/dev/stderr
#stderr_logfile_maxbytes=0
#stopwaitsecs=3600

[program:horizon]
process_name=%(program_name)s_%(process_num)s
command=php %(ENV_ROOT)s/artisan horizon
user=%(ENV_USER)s
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stopwaitsecs=3600

[program:scheduler]
process_name=%(program_name)s_%(process_num)s
command=supercronic -overlapping /etc/supercronic/laravel
user=%(ENV_USER)s
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:clear-scheduler-cache]
process_name=%(program_name)s_%(process_num)s
command=php %(ENV_ROOT)s/artisan schedule:clear-cache
user=%(ENV_USER)s
autostart=true
autorestart=false
startsecs=0
startretries=1
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
