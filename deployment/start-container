#!/usr/bin/env sh

# Run Laravel migrations
php artisan migrate --force

# Create symbolic link for storage
php artisan storage:link

# Clear cache
#php artisan cache:clear

# Clear and optimize the application cache
php artisan optimize:clear
#php artisan optimize

# clear route cache
php artisan route:clear
php artisan cache:clear

php artisan app:sync-community
#php artisan scout:delete-index "App\Models\UserProfile"
php artisan scout:flush "App\Models\UserProfile"
php artisan scout:import "App\Models\UserProfile"

# clear config
php artisan config:clear
php artisan env

# run laravel octane application
exec /usr/bin/supervisord -c /etc/supervisor/supervisord.conf
