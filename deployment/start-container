#!/usr/bin/env sh
set -e

# Clear caches
echo "Clear caches..."
php artisan optimize:clear

echo "Enable maintenance mode..."
php artisan down --secret="xA8N42o46f1WTho3" || true

# Run migrations
echo "Running migrations..."
php artisan migrate --force

# Create storage symlink (ignore if exists)
php artisan storage:link || true

#php artisan app:sync-community
#php artisan scout:delete-index "App\Models\UserProfile"
#php artisan scout:flush "App\Models\UserProfile"
#php artisan scout:import "App\Models\UserProfile"

echo "Disable maintenance mode..."
php artisan up

# Start Supervisor in foreground
exec /usr/bin/supervisord -n -c /etc/supervisor/supervisord.conf
