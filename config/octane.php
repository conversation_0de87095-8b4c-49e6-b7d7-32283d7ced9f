<?php

use App\Helpers\ApiResponse;
use App\Helpers\ChatWork;
use App\Helpers\Encryptor;
use App\Helpers\LazyResponseData;
use App\Helpers\FirebaseCloudMessage;
use App\Helpers\QueryLog;
use App\Helpers\QuestHelper;
use App\Helpers\Storage;
use App\Helpers\UserBlockHelper;
use App\Listeners\ClearData;
use App\Listeners\UpdateUserLatestLoggedInTime;
use App\Repositories\AnswerRepository;
use App\Repositories\AttachedSurveyRepository;
use App\Repositories\CommentRepository;
use App\Repositories\CommunityRepository;
use App\Repositories\CommunityUserRepository;
use App\Repositories\FeedViewedStatRepository;
use App\Repositories\NewsRepository;
use App\Repositories\PostAnswerRepository;
use App\Repositories\PostRepository;
use App\Repositories\PremiumFeatureRepository;
use App\Repositories\QuestionRepository;
use App\Repositories\QuestRepository;
use App\Repositories\SettingRepository;
use App\Repositories\SurveyRepository;
use App\Repositories\UserRepository;
use App\Repositories\PostViewHistoryRepository;
use App\Repositories\ViewedPostRepository;
use Laravel\Octane\Contracts\OperationTerminated;
use Laravel\Octane\Events\RequestHandled;
use Laravel\Octane\Events\RequestReceived;
use Laravel\Octane\Events\RequestTerminated;
use Laravel\Octane\Events\TaskReceived;
use Laravel\Octane\Events\TaskTerminated;
use Laravel\Octane\Events\TickReceived;
use Laravel\Octane\Events\TickTerminated;
use Laravel\Octane\Events\WorkerErrorOccurred;
use Laravel\Octane\Events\WorkerStarting;
use Laravel\Octane\Events\WorkerStopping;
use Laravel\Octane\Listeners\CloseMonologHandlers;
use Laravel\Octane\Listeners\CollectGarbage;
use Laravel\Octane\Listeners\DisconnectFromDatabases;
use Laravel\Octane\Listeners\EnsureUploadedFilesAreValid;
use Laravel\Octane\Listeners\EnsureUploadedFilesCanBeMoved;
use Laravel\Octane\Listeners\FlushOnce;
use Laravel\Octane\Listeners\FlushTemporaryContainerInstances;
use Laravel\Octane\Listeners\FlushUploadedFiles;
use Laravel\Octane\Listeners\ReportException;
use Laravel\Octane\Listeners\StopWorkerIfNecessary;
use Laravel\Octane\Octane;

return [

    /*
    |--------------------------------------------------------------------------
    | Octane Server
    |--------------------------------------------------------------------------
    |
    | This value determines the default "server" that will be used by Octane
    | when starting, restarting, or stopping your server via the CLI. You
    | are free to change this to the supported server of your choosing.
    |
    | Supported: "roadrunner", "swoole", "frankenphp"
    |
    */

    'server' => env('OCTANE_SERVER', 'roadrunner'),

    /*
    |--------------------------------------------------------------------------
    | Force HTTPS
    |--------------------------------------------------------------------------
    |
    | When this configuration value is set to "true", Octane will inform the
    | framework that all absolute links must be generated using the HTTPS
    | protocol. Otherwise your links may be generated using plain HTTP.
    |
    */

    'https' => env('OCTANE_HTTPS', false),

    /*
    |--------------------------------------------------------------------------
    | Octane Listeners
    |--------------------------------------------------------------------------
    |
    | All of the event listeners for Octane's events are defined below. These
    | listeners are responsible for resetting your application's state for
    | the next request. You may even add your own listeners to the list.
    |
    */

    'listeners' => [
        WorkerStarting::class => [
            EnsureUploadedFilesAreValid::class,
            EnsureUploadedFilesCanBeMoved::class,
        ],

        RequestReceived::class => [
            ...Octane::prepareApplicationForNextOperation(),
            ...Octane::prepareApplicationForNextRequest(),
        ],

        RequestHandled::class => [
            //
        ],

        RequestTerminated::class => [
            FlushUploadedFiles::class,
            ClearData::class,
            UpdateUserLatestLoggedInTime::class,
        ],

        TaskReceived::class => [
            ...Octane::prepareApplicationForNextOperation(),
        ],

        TaskTerminated::class => [
            //
        ],

        TickReceived::class => [
            ...Octane::prepareApplicationForNextOperation(),
        ],

        TickTerminated::class => [
            //
        ],

        OperationTerminated::class => [
            FlushOnce::class,
            FlushTemporaryContainerInstances::class,
            // DisconnectFromDatabases::class,
            // CollectGarbage::class,
        ],

        WorkerErrorOccurred::class => [
            ReportException::class,
            StopWorkerIfNecessary::class,
        ],

        WorkerStopping::class => [
            CloseMonologHandlers::class,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Warm / Flush Bindings
    |--------------------------------------------------------------------------
    |
    | The bindings listed below will either be pre-warmed when a worker boots
    | or they will be flushed before every new request. Flushing a binding
    | will force the container to resolve that binding again when asked.
    |
    */

    'warm' => [
        ...Octane::defaultServicesToWarm(),

        /**
         * helpers
         */
        ApiResponse::class,
        ChatWork::class,
        Encryptor::class,
        LazyResponseData::class,
        FirebaseCloudMessage::class,
        QueryLog::class,
        QuestHelper::class,
        Storage::class,
        UserBlockHelper::class,

        /**
         * repositories
         */
        AnswerRepository::class,
        AttachedSurveyRepository::class,
        CommentRepository::class,
        CommunityRepository::class,
        CommunityUserRepository::class,
        FeedViewedStatRepository::class,
        NewsRepository::class,
        PostRepository::class,
        PostAnswerRepository::class,
        PremiumFeatureRepository::class,
        QuestionRepository::class,
        QuestRepository::class,
        SettingRepository::class,
        SurveyRepository::class,
        UserRepository::class,
        PostViewHistoryRepository::class,
        ViewedPostRepository::class,
    ],

    'flush' => [

    ],

    /*
    |--------------------------------------------------------------------------
    | Octane Swoole Tables
    |--------------------------------------------------------------------------
    |
    | While using Swoole, you may define additional tables as required by the
    | application. These tables can be used to store data that needs to be
    | quickly accessed by other workers on the particular Swoole server.
    |
    */

    'tables' => [
        'example:1000' => [
            'name' => 'string:1000',
            'votes' => 'int',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Octane Swoole Cache Table
    |--------------------------------------------------------------------------
    |
    | While using Swoole, you may leverage the Octane cache, which is powered
    | by a Swoole table. You may set the maximum number of rows as well as
    | the number of bytes per row using the configuration options below.
    |
    */

    'cache' => [
        'rows' => 1000,
        'bytes' => 10000,
    ],

    /*
    |--------------------------------------------------------------------------
    | File Watching
    |--------------------------------------------------------------------------
    |
    | The following list of files and directories will be watched when using
    | the --watch option offered by Octane. If any of the directories and
    | files are changed, Octane will automatically reload your workers.
    |
    */

    'watch' => [
        'app',
        'bootstrap',
        'config/**/*.php',
        'database/**/*.php',
        'helpers',
        'lang',
        'public/**/*.php',
        'resources/**/*.php',
        'routes',
        'composer.lock',
        '.env',
    ],

    /*
    |--------------------------------------------------------------------------
    | Garbage Collection Threshold
    |--------------------------------------------------------------------------
    |
    | When executing long-lived PHP scripts such as Octane, memory can build
    | up before being cleared by PHP. You can force Octane to run garbage
    | collection if your application consumes this amount of megabytes.
    |
    */

    'garbage' => 100,

    /*
    |--------------------------------------------------------------------------
    | Maximum Execution Time
    |--------------------------------------------------------------------------
    |
    | The following setting configures the maximum execution time for requests
    | being handled by Octane. You may set this value to 0 to indicate that
    | there isn't a specific time limit on Octane request execution time.
    |
    */

    'max_execution_time' => 30,

    'swoole' => [
        'options' => [
            'log_file' => storage_path('logs/swoole_http.log'),
            'upload_tmp_dir' => storage_path('tmp'),
            'send_yield' => true,
            'http_compression' => true,
            'http_compression_level' => 6, // 1 - 9
            'compression_min_length' => 20,
            'package_max_length' => 10 * 1024 * 1024, // 10MB
            'socket_buffer_size' => 10 * 1024 * 1024, // 10MB
            'upload_max_filesize' => 100 * 1024 * 1024, // 100MB
            'open_http2_protocol' => true,
            //'document_root' => public_path(),
            //'enable_static_handler' => true,
        ],
    ],
];
