{"The :attribute must contain at least one letter.": "The :attribute must contain at least one letter.", "The :attribute must contain at least one number.": "The :attribute must contain at least one number.", "The :attribute must contain at least one symbol.": "The :attribute must contain at least one symbol.", "The :attribute must contain at least one uppercase and one lowercase letter.": "The :attribute must contain at least one uppercase and one lowercase letter.", "The given :attribute has appeared in a data leak. Please choose a different :attribute.": "The given :attribute has appeared in a data leak. Please choose a different :attribute.", "You have successfully updated your personal information.": "個人情報の更新に成功しました。", "You have successfully changed your password.": "パスワードの変更に成功しました。", "You have successfully saved data.": "データの保存に成功しました。", "You have successfully deleted data.": "データの削除に成功しました。", "The provided password does not match your current password.": "現在のパスワードが正しくありません。", "throttle": "許可された回数（:total回）を超えてリクエストしました。\n:seconds秒後にもう一度お試しください。", "updateSurvey": "アンケート編集", "createNewSurvey": "アンケートを追加", "checkbox": "チェックボックス", "select": "プールダウン", "text": "記述式(星なし)", "text_with_star": "記述式(星有り)", "public.none": "非公開", "public.platform": "公開PF", "public.app": "公開APP", "public.platform_app": "公開PF,APP", "createNewAttachedSurvey": "アンケート差し込み追加", "updateAttachedSurvey": "アンケート差し込み編集", "Validation Exception": "エラーが発生しました", "feedType.general": "総合", "feedType.friend": "友だち", "feedType.follow": "フォロー", "feedType.latest": "新着のポスト", "feedType.recommended_answer": "評価の高い回答", "feedType.laugh": "オーギリ", "feedType.rate_none": "", "feedType.owner": "あなたのアクション", "feedType.topics": "TOPICS", "feedType.discover": "Discover", "feedType.raise_hand": "体験者レビュー", "postType.default": "Free", "postType.smile": "ゆるい質問・相談", "postType.not_smile": "真剣な質問・相談", "postType.laugh": "オーギリ", "postType.raise_hand": "体験者レビュー", "postType.multiple": "どっち", "postType.gender": "性別", "postType.answerr_q": "Answerr_Q", "postType.answerr_topic": "Answerr_Topic", "user.status.0": "削除", "user.status.1": "アクティブ", "user.status.2": "凍結", "user.status.3": "検証済み", "post.status.0": "削除", "post.status.1": "アクティブ", "postAnswer.status.0": "削除", "postAnswer.status.1": "アクティブ", "postAnswer.level.1": "アンサー", "postAnswer.level.2": "リアンサー", "postAnswer.level.3": "リリアンサー", "and": "と:usernameさん", "and.other": "と他:amount人", "rewarded": "ベストアンサー: :amountコイン\n", "notification.0": "「:post_content」という投稿に:usernameさん:andがAnswerrしました。", "notification.1": "「:answer_content」のAnswerrに:usernameさんから:amountコインがプレゼントされました。", "notification.2": "「:answer_content」のAnswerrに:usernameさんが:amountいいねしました。", "notification.3": "「:answer_content」のAnswerrに:usernameさん:andがリアンサーしました。", "notification.4": ":jobのお仕事の結果が承認されました:amountコインがプレゼントされます。", "notification.5": ":jobのお仕事の結果が承認されました:amountポイントがプレゼントされます。", "notification.6": ":jobのお仕事の結果は承認されませんでした。", "notification.7": "運営から:jobのお仕事が届きました。", "notification.8": ":usernameさんからフォローされました。", "notification.9": "「:post_content」の質問が:usernameさん:andから応援されました。", "notification.10": "「:post_content」の質問のBEST ANSWERRを決めましょう。", "notification.11": ":rewardedText「:answer_content」のAnswerに:usernameさんからBest Answerrが付与されました。", "notification.12": ":usernameさんからあなたに質問が届きました。", "notification.13": ":usernameさんが困っているみたいだよ。", "notification.14": "ようこそanswerrへ！招待リンクを使って登録してくれたので特別に:amount:unitをプレゼントするよ。", "notification.15": "おめでとう！あなたの紹介でユーザーが増えたのでコインをプレゼントするよ。", "notification.16": ":usernameから友だち申請が届きました。", "notification.17": ":usernameと友だちになりました。", "notification.18": ":usernameがコミュニティ「:community」に招待しました。", "notification.19": "あなたが紹介したユーザーがanswerrで大活躍！お礼に招待枠を1つプレゼントするよ。", "notification.20": "「:answer_content」のAnswerrに:usernameさん:andがいいねしました。", "notification.21": "プロフィール入力ありがとう！\n1コインプレゼントするよ。", "notification.22": "おめでとう！投稿に初めてのアンサーが付きました。\n1コインプレゼントするよ。", "notification.23": "職業情報の更新ありがとう！\n:amountコインプレゼントするよ。", "notification.24": "性別の更新ありがとう！\n:amountコインプレゼントするよ。", "Invalid post answer ID.": "無効な投稿の回答IDです。", "Invalid post ID.": "無効な投稿IDです。", "You have not donated this post answer yet.": "こちらの投稿の回答をまだ寄付していません。", "You can not donate your post answer.": "自分の投稿の回答を寄付することはできません。", "You can not like your answer.": "自分の回答に「いいね」することはできません。", "You can not like your post.": "自分の投稿に「いいね」することはできません。", "You have liked this post.": "こちらの投稿に「いいね」しました。", "Invalid user ID.": "無効なユーザーIDです。", "You have not liked this answer yet.": "こちらの回答にまだ「いいね」していません。", "You have not liked this post yet.": "こちらの投稿にまだ「いいね」していません。", "You do not have permission to perform this action.": "この操作を行う権限がありません。", "You have already voted best-answer for this post.": "こちらの投稿に対してすでにBest Answerrとして投票していgit statuます。", "Invalid question ID.": "無効な質問IDです。", "Invalid notification ID.": "無効な通知IDです。", "Your account has been deleted or disabled.": "あなたのアカウントは削除または無効化されています。", "Invalid attached ID.": "無効な差し込みIDです。", "Invalid survey ID.": "無効なアンケートIDです。", "Invalid attached survey ID.": "無効な差し込むアンケートIDです。", "You do not have enough points to perform this action.": "押した回数が所有ポイントの上限を超えています。", "You do not have enough coins to perform this action.": "押した回数が所有コインの上限を超えています。", "You cannot unlike this answer because it has expired.": "期限が切れているため、「いいねを取り消す」ことはできません。", "You cannot cancel this donation because it has expired.": "期限が切れているため、「コインを取り消す」ことはできません。", "profileType.student": "学生", "profileType.employee": "社会人", "profileType.other": "その他", "profileType.": "N/A", "- Field of work: ": "- Field of work: ", "- Field of expertise: ": "- Field of expertise: ", "- Service in charge: ": "- Service in charge: ", "Instruct: Given a web search query, retrieve relevant passages that answer the query.": "Instruct: Given a web search query, retrieve relevant passages that answer the query.", "Instruct: Given a web search query, retrieve relevant passages that are similar to the query.": "Instruct: Given a web search query, retrieve relevant passages that are similar to the query.", "Unable to send OTP code, please try again later.": "OTPコードを送信できませんでした。しばらくしてからもう一度お試しください。", "otp_choice": "{1} 後:second秒で再度OTPを送信できます。|[2,*] 後:second秒で再度OTPを送信できます。", "An error occurred while uploading the image.": "画像のアップロード中にエラーが発生しました。", "An error occurred while uploading the avatar.": "プロフィール画像のアップロード中にエラーが発生しました。", "validationException.": "Validation Exception", "validationException.updateInformation": "すべての情報をご入力いただき、プロフィールを完成させてください。", "news.status.0": "削除", "news.status.1": "アクティブ", "Invalid news ID.": "無効なニュースIDです。", "Age must be greater than 17 years old.": "18才未満が利用できません", "Your phone number has been already existed.": "この電話番号は既に登録されています。", "Your account does not exist.": "あなたのアカウントは存在しません。", "Your referrer code is invalid.": "招待リンクは無効です。", "quest.status.0": "削除", "quest.status.1": "アクティブ", "quest.type.answered": "質問を投稿してみよう", "quest.type.followed": "フォロワーを増やしてみよう", "quest.type.friend": "友だちになる", "quest.type.invited": "招待リンクを使ってみよう", "quest.type.voted": "ベストアンサーを獲得しよう", "quest.type.hobby": "プロフィールを充実させよう", "quest.type.career": "学生かな？それとも社会人？", "quest.type.gender": "性別", "quest.unit.point": "ポイント", "quest.unit.coin": "コイン", "existedQuestForType": "こちらのクエストタイプでクエストはすでに存在しています。", "quest.answered.amount": "+:amount:unit以上", "quest.followed.amount": "+:amount:unit", "quest.friend.amount": "+:amount:unit", "quest.invited.amount": "+:amount:unit", "quest.voted.amount": "+:amount:unit以上", "quest.hobby.amount": "+:amount :unit", "quest.career.amount": "+:amount :unit", "quest.gender.amount": "+:amount :unit", "You have already blocked this user.": "あなたはすでにこのユーザーをブロックしています。", "blockedMessage": "情報を見ることができません。", "You cannot add this person because there is no friend request yet.": "まだ友だち申請が送られていないため、このユーザーを追加することはできません。", "You cannot unfriend this user because you are not friends.": "友だちではないため、このユーザーを削除することはできません。", "You cannot send the friend invitation.": "このユーザーに友だち申請を送信することはできません。", "You cannot ignore the friend invitation.": "このユーザーからの友だち申請を削除することはできません。", "You cannot cancel the friend invitation.": "送信した友だち申請をキャンセルすることはできません。", "You do not have permission to perform this action in this community.": "この投稿はコミュニティに属しています。\n回答や投稿をするには、コミュニティに参加する必要があります。", "You already have this premium feature activated.": "このプレミアム機能はすでに有効化されています。", "You can not like more than 5 points.": "You can not like more than 5 points.", "gender.0": "", "gender.1": "男性", "gender.2": "女性", "gender.3": "未回答", "premiumFeature.gender": "性別に応じて記事のロックを解除する", "premiumFeature.pin_answer": "回答をピン留めする", "Service Unavailable": "メンテナンスのため、サービスは一時的に中断されています。ご理解のほどよろしくお願いいたします。再度お試しください。", "You do not have permission to pin this answer.": "この回答をピン留めする権限がありません。", "": ""}