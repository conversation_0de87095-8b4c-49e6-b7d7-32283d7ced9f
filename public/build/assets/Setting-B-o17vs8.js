import{T as $}from"./@inertiajs-Dt0-hqjZ.js";import{_ as v}from"./AppLayout-DKZEmXIb.js";import{_ as V}from"./PrimaryButton-DE9sqoJj.js";import{_ as k}from"./LoadingIcon-CesYxFkK.js";import{_ as u}from"./TextInput-C52bsWxF.js";import{_ as f}from"./InputError-gQdwtcoE.js";import{i as S,k as x,o as i,S as p,a as t,c as m,P as n,F as h,M as g,l as r,a4 as l,K as j}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";const y={class:"flex-1 flex items-center"},U=["textContent"],B=["textContent"],F={class:"max-w-7xl mx-auto py-6 px-6"},N={class:"bg-white rounded-md shadow flex flex-col overflow-auto"},O={class:"w-full"},L={class:"text-left flex"},D=["textContent"],E={class:"text-left flex border-t even:bg-blue-50 odd:bg-white"},K=["textContent"],M=["textContent"],P=["textContent"],z=["textContent"],A={class:"border-l flex-1 flex flex-col"},G={class:"border-l flex-1 flex flex-col"},H={class:"bg-white rounded-md shadow flex flex-col overflow-auto mt-6"},I={class:"w-full"},J={class:"text-left flex"},Q=["textContent"],R={class:"text-left flex border-t even:bg-blue-50 odd:bg-white"},W=["textContent"],X=["textContent"],Y=["textContent"],Z={class:"border-l flex-1 flex flex-col"},Ot={__name:"Setting",props:{settings:Object,timeSettings:Object},setup(b){const d=b,C=S("$toast"),e=$({...d.settings,time:d.timeSettings}),w=()=>{if(e.processing)return!1;e.errors={},e.transform(s=>s).post(route("setting.store"),{preserveScroll:!0,preserveState:!0,onSuccess:s=>{s.props.jetstream.flash.message&&C.success(s.props.jetstream.flash.message);for(const[_,c]of Object.entries(s.props.settings))e[_]=c}})};return(s,_)=>(i(),x(v,{title:s.$t("systemSetting")},{header:p(()=>[t("div",y,[t("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:n(s.$t("systemSetting"))},null,8,U),r(V,{class:"normal-case",disabled:l(e).processing,onClick:w},{default:p(()=>[l(e).processing?(i(),x(k,{key:0,class:"mr-2"})):j("",!0),t("span",{class:"text-sm",textContent:n(s.$t("save"))},null,8,B)]),_:1},8,["disabled"])])]),default:p(()=>[t("div",F,[t("div",N,[t("table",O,[t("tbody",null,[t("tr",L,[t("th",{colspan:"3",textContent:n(s.$t("qaListConfig"))},null,8,D)]),t("tr",E,[t("th",{class:"w-64",textContent:n(s.$t("qaType"))},null,8,K),t("th",{class:"border-l flex-1",textContent:n(s.$t("order"))},null,8,M),t("th",{class:"border-l flex-1",textContent:n(s.$t("rate"))},null,8,P)]),(i(!0),m(h,null,g(d.settings,(c,o)=>(i(),m("tr",{key:o,class:"text-left flex border-t even:bg-blue-50 odd:bg-white"},[t("td",{class:"w-64",textContent:n(s.$t("qaType."+o))},null,8,z),t("td",A,[r(u,{class:"block w-full",type:"text",modelValue:l(e)[o].index,"onUpdate:modelValue":a=>l(e)[o].index=a,disabled:l(e).processing},null,8,["modelValue","onUpdate:modelValue","disabled"]),r(f,{class:"w-full",message:l(e).errors[o+".index"]},null,8,["message"])]),t("td",G,[r(u,{class:"block w-full",type:"text",modelValue:l(e)[o].limit,"onUpdate:modelValue":a=>l(e)[o].limit=a,disabled:l(e).processing},null,8,["modelValue","onUpdate:modelValue","disabled"]),r(f,{class:"w-full",message:l(e).errors[o+".limit"]},null,8,["message"])])]))),128))])])]),t("div",H,[t("table",I,[t("tbody",null,[t("tr",J,[t("th",{colspan:"2",textContent:n(s.$t("homeTimeConfig"))},null,8,Q)]),t("tr",R,[t("th",{class:"w-64",textContent:n(s.$t("group"))},null,8,W),t("th",{class:"border-l flex-1",textContent:n(s.$t("time"))},null,8,X)]),(i(!0),m(h,null,g(d.timeSettings,(c,o)=>(i(),m("tr",{key:o,class:"text-left flex border-t even:bg-blue-50 odd:bg-white"},[t("td",{class:"w-64",textContent:n(s.$t("feedType."+o))},null,8,Y),t("td",Z,[r(u,{class:"block w-full",type:"text",modelValue:l(e).time[o],"onUpdate:modelValue":a=>l(e).time[o]=a,disabled:l(e).processing},null,8,["modelValue","onUpdate:modelValue","disabled"]),r(f,{class:"w-full",message:l(e).errors["time."+o]},null,8,["message"])])]))),128))])])])])]),_:1},8,["title"]))}};export{Ot as default};
