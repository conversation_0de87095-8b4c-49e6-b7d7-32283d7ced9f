import{T as C,i as b}from"./@inertiajs-BhKdJayA.js";import{s as S,a as u}from"./primevue-u0EmObz-.js";import{_ as k}from"./AppLayout-m_I9gnvX.js";import{_ as B}from"./InputLabel-BTXevqr4.js";import{_ as F}from"./SearchInput-CdoSYJL3.js";import{_ as P}from"./Pagination-DDsmbrzN.js";import{c as V}from"./index-DHV2tfOS.js";import{s as d}from"./ziggy-js-RmARJSO4.js";import{_}from"./GridContainer-n7ZDMxOZ.js";import{v as N,b as D,k as h,o as p,S as l,a as m,l as s,K as v,a4 as t,O as L,P as g}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./@primeuix-CNwdBq9K.js";import"./@primevue-Bw51iWDD.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BWHXZF7Q.js";import"./PrimaryButton-DE9sqoJj.js";import"./FixedSelectionBox-CwNS68U7.js";import"./SelectionBox-58uvzdoT.js";import"./LoadingIcon-CesYxFkK.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-ccBf1-WH.js";import"./lodash-DBgjQQU6.js";import"./TextInput-C52bsWxF.js";import"./@headlessui-gOb5_P77.js";import"./moment-C5S46NFB.js";const T={class:"flex-1 flex items-center"},j=["textContent"],I={class:"p-6 sm:mx-2"},O={class:"flex items-stretch"},E={class:"mt-0 w-80 mr-8"},H=["innerHTML"],M=["src"],$e={__name:"Index",props:{filters:Object,features:Object},setup(r){const c=r,o=C({search:c.filters.search??"",limit:c.filters.limit??10}),a=N({showSearchClearButton:(c.filters.search??"").trim().length>0,searching:!1,activePage:null,busy:D(()=>a.activePage!==null||a.searching||o.processing)}),f=()=>{if(o.processing)return!1;o.transform(e=>V(e)).get(d("premiumFeature.list"),{preserveScroll:!0,onSuccess:()=>{}})},$=()=>{a.showSearchClearButton=!0,f()},y=()=>{o.search="",a.showSearchClearButton=!1,f()},w=e=>{a.activePage=e,a.searching=!0},x=e=>{o.limit=e,o.search="",f()};return(e,n)=>(p(),h(k,{title:e.$t("premiumFeature.list")},{header:l(()=>[m("div",T,[m("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:g(e.$t("premiumFeature.list"))},null,8,j),s(t(b),{class:"primary-button text-sm",href:t(d)("premiumFeature.form",{feature:""}),textContent:g(e.$t("addNew"))},null,8,["href","textContent"])])]),default:l(()=>[m("div",I,[m("div",O,[m("div",E,[s(B,{for:"search",value:e.$t("search")},null,8,["value"]),s(F,{id:"search",class:"block w-full",modelValue:t(o).search,"onUpdate:modelValue":n[0]||(n[0]=i=>t(o).search=i),placeholder:e.$t("premiumFeature.search"),disabled:a.busy,"show-clear-button":a.showSearchClearButton,onInput:n[1]||(n[1]=i=>a.showSearchClearButton=!1),onClearSearch:y,onEnter:$},null,8,["modelValue","placeholder","disabled","show-clear-button"])])]),s(_,{loading:a.busy},{default:l(()=>[s(t(S),{value:r.features.data},{empty:l(()=>[L(g(e.$t(r.filters.search&&r.filters.search!==""||r.filters.status&&r.filters.status!==""?"emptyResult":"emptyData")),1)]),default:l(()=>[s(t(u),{class:"number-column small",field:"premium_id",header:e.$t("ID")},null,8,["header"]),s(t(u),{class:"w-96",field:"name",header:e.$t("premiumFeature.name")},null,8,["header"]),s(t(u),{class:"flex-1",header:e.$t("premiumFeature.description")},{body:l(({data:i})=>[m("div",{innerHTML:i.description.replace(/(\r\n|\n|\r)/g,"<br />")},null,8,H)]),_:1},8,["header"]),s(t(u),{class:"status-column",field:"price",header:e.$t("coin")},null,8,["header"]),s(t(u),{class:"status-column",header:e.$t("image")},{body:l(({data:i})=>[m("img",{class:"max-w-full max-h-full",src:i.image},null,8,M)]),_:1},8,["header"]),s(t(u),{class:"action-column small"},{body:l(({data:i})=>[i.status!==0?(p(),h(t(b),{key:0,href:t(d)("premiumFeature.form",{feature:i.premium_id})},{default:l(()=>n[2]||(n[2]=[m("i",{class:"pi pi-pen-to-square text-blue-400 hover:text-blue-600"},null,-1)])),_:2},1032,["href"])):v("",!0)]),_:1})]),_:1},8,["value"])]),_:1},8,["loading"]),r.features.data.length>0?(p(),h(P,{key:0,class:"mt-5 flex items-center justify-center mx-auto",links:r.features.links,active:a.activePage,disabled:a.busy,limit:r.features.per_page,total:r.features.total,from:r.features.from,to:r.features.to,onProgress:w,onChangeLimit:x},null,8,["links","active","disabled","limit","total","from","to"])):v("",!0)])]),_:1},8,["title"]))}};export{$e as default};
