import{g as m}from"./index-DHV2tfOS.js";import{Q as A,N as g,i as b}from"./@inertiajs-Dt0-hqjZ.js";import{t as N,p as V}from"./lodash-Bx_YDCCc.js";import{d as T}from"./@element-plus-CyTLADhX.js";import{_ as E}from"./AppLayout-DKZEmXIb.js";import{_ as F}from"./InputLabel-BTXevqr4.js";import{_ as z}from"./TextInput-C52bsWxF.js";import{_ as O}from"./Pagination-Dmt48FUb.js";import{_ as Q}from"./LoadingIcon-CesYxFkK.js";import{_ as M,a as R}from"./SecondaryButton-BoI1NwE9.js";import{_ as W}from"./PrimaryButton-DE9sqoJj.js";import{i as K,v as L,c as a,o as r,l as i,k as $,K as h,S as p,a as t,a4 as c,R as f,P as e,F as _,M as C}from"./@vue-BnW70ngI.js";import"./moment-C5S46NFB.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./FixedSelectionBox-CkXOgkaT.js";import"./SelectionBox-CzAgH5wz.js";import"./@heroicons-BLousAGu.js";import"./@headlessui-gOb5_P77.js";const P=["textContent"],U={class:"py-6 px-6 sm:mx-2"},G={class:"flex items-center items-stretch"},H={class:"mt-0 w-80"},J={class:"w-full"},X={class:"text-left flex"},Y=["textContent"],Z=["textContent"],tt=["textContent"],et=["textContent"],nt=["textContent"],st=["textContent"],ot=["textContent"],lt=["textContent"],rt=["textContent"],at=["textContent"],it={class:"number-column"},ct=["textContent"],dt=["textContent"],ut=["textContent"],mt=["textContent"],ht=["textContent"],pt={class:"p-0 border-l",colspan:"2"},ft={key:0,class:"w-full h-full"},_t=["textContent"],xt=["textContent"],Ct={class:"p-0 border-l",colspan:"2"},vt={key:0,class:"w-full h-full"},yt=["textContent"],gt=["textContent"],bt={class:"border-l w-[56px]"},$t={key:1,class:"flex border-t"},kt=["textContent"],wt=["textContent"],St=["textContent"],It={class:"flex items-center justify-end px-3 py-3"},qt=["textContent"],ye={__name:"Index",props:{filters:Object,attachedItems:Object},setup(d){const k=K("$toast"),v=A(),o=L({searched:d.filters.search??"",searching:!1,confirmDeletion:!1,open:!1,deleteId:null,deleting:!1}),w=N(()=>g.get(route("attachedSurvey.list"),V({search:o.searched}),{preserveState:!0,onBefore:()=>{o.searching=!0},onFinish:()=>{o.searching=!1}}),1e3),x=()=>{o.open=!1,setTimeout(()=>o.confirmDeletion=!1,150)},S=n=>{o.deleteId=n,o.confirmDeletion=!0,setTimeout(()=>o.open=!0,150)},I=()=>{o.deleting||g.post(route("attachedSurvey.delete"),{id:o.deleteId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{o.deleting=!0},onSuccess:()=>{x(),o.deleteId=null,v.props.jetstream.flash.message&&k.success(v.props.jetstream.flash.message)},onFinish:()=>{o.deleting=!1}})},q=n=>{const l={};return n.forEach(s=>{l[s.question_id]||(l[s.question_id]={question_id:s.question_id,content:s.question_content,choices:[]}),l[s.question_id].choices.push({choice_id:s.choice_id,content:s.content})}),Object.keys(l).map(s=>l[s])};return(n,l)=>(r(),a(_,null,[i(E,{title:n.$t("listSurvey")},{header:p(()=>[t("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:e(n.$t("listAttachedSurvey"))},null,8,P),i(c(b),{class:"ml-auto flex items-center justify-center px-4 py-2 bg-sky-500 border border-transparent rounded-md font-semibold text-white uppercase transition ease-in-out duration-150 hover:bg-sky-600 focus:outline-none h-[38px]",textContent:e(n.$t("addNew")),href:n.route("attachedSurvey.create")},null,8,["textContent","href"])]),default:p(()=>[t("div",U,[t("div",G,[t("div",H,[i(F,{for:"search",value:n.$t("attachedSurveySearch")},null,8,["value"]),i(z,{class:"mt-1 block w-full",id:"search",modelValue:o.searched,"onUpdate:modelValue":l[0]||(l[0]=s=>o.searched=s),placeholder:"...",disabled:o.searching,onEnter:c(w)},null,8,["modelValue","disabled","onEnter"])])]),t("div",{class:f(["bg-white rounded-md shadow overflow-auto mt-5 flex flex-col",{"grid-loading":o.searching}])},[o.searching?(r(),$(Q,{key:0,class:"grid-loading-icon",size:24,color:"rgb(55 65 81)"})):h("",!0),t("table",J,[t("tr",X,[t("th",{class:"number-column",textContent:e(n.$t("number"))},null,8,Y),t("th",{class:"title-flex-column",textContent:e(n.$t("attachedSurveyTitle"))},null,8,Z),t("th",{class:"survey-id-column",textContent:e(n.$t("surveyID"))},null,8,tt),t("th",{class:"survey-title-column",textContent:e(n.$t("surveyTitle"))},null,8,et),t("th",{class:"attached-id-column",textContent:e(n.$t("surveyIDWasAttached"))},null,8,nt),t("th",{class:"survey-title-column",textContent:e(n.$t("surveyTitleWasAttached"))},null,8,st),t("th",{class:"question-id-column",textContent:e(n.$t("questionID"))},null,8,ot),t("th",{class:"question-content-column",textContent:e(n.$t("questionContent"))},null,8,lt),t("th",{class:"answer-id-column",textContent:e(n.$t("answerID"))},null,8,rt),t("th",{class:"answer-content-column",textContent:e(n.$t("answerContent"))},null,8,at),l[1]||(l[1]=t("th",{class:"w-[56px]"},null,-1))]),d.attachedItems.data.length>0?(r(!0),a(_,{key:0},C(d.attachedItems.data,(s,D)=>(r(),a("tr",{class:f(["flex border-t",[D%2===0?"bg-blue-50":""]])},[t("td",it,[i(c(b),{class:"hover:text-sky-600 hover:underline",textContent:e(s.attached_id),href:n.route("attachedSurvey.update",{attachedSurvey:s.attached_id})},null,8,["textContent","href"])]),t("td",{class:"title-flex-column",textContent:e(s.title)},null,8,ct),t("td",{class:"survey-id-column",textContent:e(c(m)(s.survey.survey_id,"S"))},null,8,dt),t("td",{class:"survey-title-column",textContent:e(s.survey.title)},null,8,ut),t("td",{class:"attached-id-column",textContent:e(c(m)(s.toSurvey.survey_id,"S"))},null,8,mt),t("td",{class:"survey-title-column",textContent:e(s.toSurvey.title)},null,8,ht),t("td",pt,[s.choices.length>0?(r(),a("table",ft,[(r(!0),a(_,null,C(q(s.choices),(u,j)=>(r(),a("tr",{class:f(["flex h-full",[j>0?"border-t":""]])},[t("td",{class:"question-id-column",textContent:e(c(m)(u.question_id,"Q"))},null,8,_t),t("td",{class:"question-content-column",textContent:e(u.content)},null,8,xt),t("td",Ct,[u.choices.length>0?(r(),a("table",vt,[(r(!0),a(_,null,C(u.choices,(y,B)=>(r(),a("tr",{class:f(["flex h-full",[B>0?"border-t":""]])},[t("td",{class:"answer-id-column",textContent:e(c(m)(y.choice_id,"A"))},null,8,yt),t("td",{class:"answer-content-column",textContent:e(y.content)},null,8,gt)],2))),256))])):h("",!0)])],2))),256))])):h("",!0)]),t("td",bt,[i(c(T),{class:"w-6 text-gray-500 transition ease-in-out duration-150 hover:cursor-pointer hover:text-red-500",onClick:u=>S(s.attached_id)},null,8,["onClick"])])],2))),256)):(r(),a("tr",$t,[t("td",{colspan:"11",textContent:e(n.$t(d.filters.search&&d.filters.search!==""?"emptyResult":"emptyData"))},null,8,kt)]))])],2),i(O,{class:"mt-5 flex items-center justify-center mx-auto",links:d.attachedItems.links},null,8,["links"])])]),_:1},8,["title"]),o.confirmDeletion?(r(),$(R,{key:0,show:o.open,closeable:!0,size:"lg","padding-vertical":"py-20",onClose:x},{default:p(()=>[t("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:e(n.$t("confirm"))},null,8,wt),t("div",{class:"border-t px-3 py-4 font-light",textContent:e(n.$t("attachedSurveyDeleteConfirmation"))},null,8,St),l[2]||(l[2]=t("div",{class:"border-t"},null,-1)),t("div",It,[i(M,{class:"mr-3 text-sm h-[38px]",textContent:e(n.$t("cancel")),onClick:x},null,8,["textContent"]),i(W,{class:"text-sm"},{default:p(()=>[t("span",{class:"text-sm",textContent:e(n.$t("delete")),onClick:I},null,8,qt)]),_:1})])]),_:1},8,["show"])):h("",!0)],64))}};export{ye as default};
