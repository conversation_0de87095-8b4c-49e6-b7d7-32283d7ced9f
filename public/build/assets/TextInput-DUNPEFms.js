import{r as a,e as r,c as d,o as i,$ as p}from"./@vue-BnW70ngI.js";const f=["value","disabled","placeholder"],y={__name:"TextInput",props:{modelValue:{default:"",validator(o){return o===null||typeof o=="string"||typeof o=="number"||typeof o=="object"}},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:""}},emits:["update:modelValue","enter","blur","focus"],setup(o,{expose:u}){const n=a(null);r(()=>{n.value.hasAttribute("autofocus")&&setTimeout(()=>n.value.focus(),50)}),u({focus:()=>n.value.focus()});const s=t=>{["ArrowUp","ArrowDown","Enter"," ","Home","End","Escape"].includes(t.key)&&t.stopPropagation()};return(t,e)=>(i(),d("input",{class:"input-text",ref_key:"input",ref:n,value:o.modelValue,disabled:o.disabled,placeholder:o.placeholder,onInput:e[0]||(e[0]=l=>t.$emit("update:modelValue",l.target.value)),onKeyup:e[1]||(e[1]=p(l=>t.$emit("enter"),["enter"])),onKeydown:s,onBlur:e[2]||(e[2]=l=>t.$emit("blur",l)),onFocus:e[3]||(e[3]=l=>t.$emit("focus",l)),autocomplete:"off"},null,40,f))}};export{y as _};
