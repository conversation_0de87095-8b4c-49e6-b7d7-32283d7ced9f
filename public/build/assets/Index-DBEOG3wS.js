import{u as E}from"./vue-i18n-kWKo0idO.js";import{f as O,c as T}from"./index-BxmPUm2h.js";import{Q as F,T as L,i as f,N as K}from"./@inertiajs-Dt0-hqjZ.js";import{s as Q,a as m}from"./primevue-CrCPcMFN.js";import{_ as R}from"./AppLayout-_qQ0AdHn.js";import{_ as C}from"./InputLabel-BTXevqr4.js";import{_ as $}from"./SearchInput-CdoSYJL3.js";import{_ as q}from"./Pagination-D56Hn3as.js";import{_ as z}from"./LoadingIcon-CLD0VpVl.js";import{_ as G,a as H}from"./SecondaryButton-BoI1NwE9.js";import{_ as J}from"./RedButton-D21iPtqa.js";import{_ as W}from"./FixedSelectionBox-Bk5LSyGJ.js";import{_ as X}from"./GridContainer-BC3u-41x.js";import{s as h}from"./ziggy-js-C7EU8ifa.js";import{i as Y,v as Z,b as ee,c as B,o as w,l as r,k as g,K as v,S as i,a as u,a4 as s,P as c,O as x,F as te}from"./@vue-BnW70ngI.js";import"./@intlify-xvnhHnag.js";import"./moment-C5S46NFB.js";/* empty css                                                    *//* empty css                                                                     */import"./app-EptGTPPo.js";import"./axios-t--hEgTQ.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./pinia-Ddsh4R0D.js";import"./@primevue-BllOwQ3c.js";import"./@primeuix-CKSY3gPt.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./izitoast-CYQMso0-.js";import"./deepmerge-CxfS31y9.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./PrimaryButton-DE9sqoJj.js";import"./SelectionBox-D4JR3fGi.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-CyTLADhX.js";import"./TextInput-DUNPEFms.js";import"./@headlessui-gOb5_P77.js";const se=["textContent"],re={class:"p-6 sm:mx-2"},oe={class:"flex items-stretch"},le={class:"mt-0 w-80 mr-8"},ae={class:"mt-0 w-80 mr-8"},ne={class:"mt-0 w-96 mr-8"},ie={class:"mt-0 w-80 mr-8"},me=["onClick"],ue=["textContent"],ce=["textContent"],de={class:"flex items-center justify-end p-3"},pe=["textContent"],gt={__name:"Index",props:{filters:Object,answers:Object},setup(n){const k=Y("$toast"),S=F(),{t:y}=E(),p=n,l=L({search:p.filters.search??"",user:p.filters.user??"",postSearch:p.filters.post??"",reported:p.filters.reported??"",limit:p.filters.limit??10}),e=Z({showSearchClearButton:(p.filters.search??"").trim().length>0,showPostSearchClearButton:(p.filters.post??"").trim().length>0,showUserSearchClearButton:(p.filters.user??"").trim().length>0,searching:!1,showModal:!1,open:!1,deleteId:null,deleting:!1,reportOptions:[{value:"yes",label:y("yes")},{value:"no",label:y("no")}],activePage:null,busy:ee(()=>e.activePage!==null||e.searching||l.processing)}),d=()=>{if(l.processing)return!1;l.transform(t=>(t.post=t.postSearch,delete t.postSearch,T(t))).get(h("postAnswer.list"),{preserveScroll:!0,onSuccess:()=>{}})},P=()=>{e.showSearchClearButton=!0,d()},_=()=>{e.showPostSearchClearButton=!0,d()},A=()=>{e.showUserSearchClearButton=!0,d()},V=()=>{l.search="",e.showSearchClearButton=!1,d()},I=()=>{l.postSearch="",e.showPostSearchClearButton=!1,d()},U=()=>{l.user="",e.showUserSearchClearButton=!1,d()},D=t=>{e.deleteId=t,e.showModal=!0,setTimeout(()=>e.open=!0,150)},b=async()=>{e.open=!1,setTimeout(()=>e.showModal=!1,150)},j=()=>{e.deleting||K.post(h("postAnswer.delete"),{id:e.deleteId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{e.deleting=!0},onSuccess:()=>{e.deleteId=null,S.props.jetstream.flash.message&&k.success(S.props.jetstream.flash.message),b()},onFinish:()=>{e.deleting=!1}})},M=t=>{e.activePage=t,e.searching=!0},N=t=>{l.limit=t,l.search="",l.user="",l.postSearch="",l.reported="",d()};return(t,a)=>(w(),B(te,null,[r(R,{title:t.$t("listPostAnswer")},{header:i(()=>[u("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:c(t.$t("listPostAnswer"))},null,8,se)]),default:i(()=>[u("div",re,[u("div",oe,[u("div",le,[r(C,{for:"search",value:t.$t("search")},null,8,["value"]),r($,{id:"search",class:"mt-1 block w-full",modelValue:s(l).search,"onUpdate:modelValue":a[0]||(a[0]=o=>s(l).search=o),placeholder:t.$t("answerSearch"),disabled:e.busy,"show-clear-button":e.showSearchClearButton,onInput:a[1]||(a[1]=o=>e.showSearchClearButton=!1),onClearSearch:V,onEnter:P},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),u("div",ae,[r(C,{for:"search-post",value:t.$t("post")},null,8,["value"]),r($,{id:"search-post",class:"mt-1 block w-full",modelValue:s(l).postSearch,"onUpdate:modelValue":a[2]||(a[2]=o=>s(l).postSearch=o),placeholder:t.$t("postSearch"),disabled:e.busy,"show-clear-button":e.showPostSearchClearButton,onInput:a[3]||(a[3]=o=>e.showPostSearchClearButton=!1),onClearSearch:I,onEnter:_},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),u("div",ne,[r(C,{for:"search-user",value:t.$t("answeredBy")},null,8,["value"]),r($,{id:"search-user",class:"mt-1 block w-full",modelValue:s(l).user,"onUpdate:modelValue":a[4]||(a[4]=o=>s(l).user=o),placeholder:t.$t("postUserSearch"),disabled:e.busy,"show-clear-button":e.showUserSearchClearButton,onInput:a[5]||(a[5]=o=>e.showUserSearchClearButton=!1),onClearSearch:U,onEnter:A},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),u("div",ie,[r(C,{for:"report-search",value:t.$t("reportStatus"),class:"mb-1"},null,8,["value"]),r(W,{modelValue:s(l).reported,"onUpdate:modelValue":a[6]||(a[6]=o=>s(l).reported=o),placeholder:t.$t("all"),disabled:e.busy,options:e.reportOptions,clearable:!0,onCleared:d,onSelected:d},null,8,["modelValue","placeholder","disabled","options"])])]),r(X,{loading:e.searching||s(l).processing},{default:i(()=>[r(s(Q),{value:n.answers.data},{empty:i(()=>[x(c(t.$t(n.filters.search&&n.filters.search!==""||n.filters.post&&n.filters.post!==""||n.filters.user&&n.filters.user!==""?"emptyResult":"emptyData")),1)]),default:i(()=>[r(s(m),{class:"number-column",header:t.$t("ID")},{body:i(({data:o})=>[r(s(f),{class:"hover:text-sky-600 hover:underline",textContent:c(o.answer_id),href:s(h)("postAnswer.detail",{postAnswer:o.answer_id})},null,8,["textContent","href"])]),_:1},8,["header"]),r(s(m),{class:"post-username-column",header:t.$t("answeredBy")},{body:i(({data:o})=>[r(s(f),{class:"hover:text-red-600 hover:underline",textContent:c(o.username),href:s(h)("postAnswer.list",{user:o.user_id})},null,8,["textContent","href"])]),_:1},8,["header"]),r(s(m),{class:"number-column extra",header:t.$t("createdByID")},{body:i(({data:o})=>[r(s(f),{class:"hover:text-red-600 hover:underline",textContent:c(o.user_id),href:s(h)("postAnswer.list",{user:o.user_id})},null,8,["textContent","href"])]),_:1},8,["header"]),r(s(m),{class:"title-flex-column",field:"content",header:t.$t("postAnswerContent")},null,8,["header"]),r(s(m),{class:"time-column",field:"tag",header:t.$t("answerCreatedAt")},{body:i(({data:o})=>[x(c(s(O)(o.created_at)),1)]),_:1},8,["header"]),r(s(m),{class:"number-column",field:"tag",header:t.$t("postID")},{body:i(({data:o})=>[r(s(f),{class:"hover:text-sky-600 hover:underline",textContent:c(o.post_id),href:s(h)("post.detail",{post:o.post_id})},null,8,["textContent","href"])]),_:1},8,["header"]),r(s(m),{class:"post-username-column",field:"post_username",header:t.$t("postCreatedBy")},null,8,["header"]),r(s(m),{class:"title-flex-column",field:"post_content",header:t.$t("postContent")},null,8,["header"]),r(s(m),{class:"count-column",field:"report_count",header:t.$t("reportCount")},null,8,["header"]),r(s(m),{class:"status-column",field:"type",header:t.$t("postAnswerType")},null,8,["header"]),r(s(m),{class:"status-column",field:"status_label",header:t.$t("status")},null,8,["header"]),r(s(m),{class:"action-column small"},{body:i(({data:o})=>[r(s(f),{href:s(h)("postAnswer.detail",{postAnswer:o.answer_id})},{default:i(()=>a[7]||(a[7]=[u("i",{class:"pi pi-info-circle text-gray-500 hover:text-sky-600"},null,-1)])),_:2},1032,["href"]),parseInt(o.status)!==0?(w(),B("i",{key:0,class:"pi pi-trash text-red-400 hover:text-red-600 hover:cursor-pointer",onClick:he=>D(o.answer_id)},null,8,me)):v("",!0)]),_:1})]),_:1},8,["value"])]),_:1},8,["loading"]),n.answers.data.length>0?(w(),g(q,{key:0,class:"mt-5 flex items-center justify-center mx-auto",links:n.answers.links,active:e.activePage,disabled:e.busy,limit:n.answers.per_page,total:n.answers.total,from:n.answers.from,to:n.answers.to,onProgress:M,onChangeLimit:N},null,8,["links","active","disabled","limit","total","from","to"])):v("",!0)])]),_:1},8,["title"]),e.showModal?(w(),g(H,{key:0,show:e.open,onClose:b},{default:i(()=>[u("div",{class:"pt-4 pb-3 px-4 font-semibold",textContent:c(t.$t("confirm"))},null,8,ue),u("div",{class:"border-t border-b p-4",textContent:c(t.$t("confirmDeletePostAnswerMessage"))},null,8,ce),u("div",de,[r(G,{class:"mr-3 text-sm",textContent:c(t.$t("cancel")),onClick:b},null,8,["textContent"]),r(J,{class:"text-sm overflow-hidden h-[34px]",onClick:j},{default:i(()=>[e.deleting?(w(),g(z,{key:0,class:"mr-2"})):v("",!0),u("span",{class:"text-sm text-white",textContent:c(t.$t("delete"))},null,8,pe)]),_:1})])]),_:1},8,["show"])):v("",!0)],64))}};export{gt as default};
