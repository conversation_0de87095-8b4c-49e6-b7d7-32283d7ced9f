var S=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function o(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var b=function(r){return s(r)&&!y(r)};function s(e){return!!e&&typeof e=="object"}function y(e){var r=Object.prototype.toString.call(e);return r==="[object RegExp]"||r==="[object Date]"||m(e)}var g=typeof Symbol=="function"&&Symbol.for,O=g?Symbol.for("react.element"):60103;function m(e){return e.$$typeof===O}function j(e){return Array.isArray(e)?[]:{}}function l(e,r){return r.clone!==!1&&r.isMergeableObject(e)?a(j(e),e,r):e}function d(e,r,n){return e.concat(r).map(function(c){return l(c,n)})}function p(e,r){if(!r.customMerge)return a;var n=r.customMerge(e);return typeof n=="function"?n:a}function M(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(r){return Object.propertyIsEnumerable.call(e,r)}):[]}function f(e){return Object.keys(e).concat(M(e))}function u(e,r){try{return r in e}catch{return!1}}function A(e,r){return u(e,r)&&!(Object.hasOwnProperty.call(e,r)&&Object.propertyIsEnumerable.call(e,r))}function E(e,r,n){var c={};return n.isMergeableObject(e)&&f(e).forEach(function(t){c[t]=l(e[t],n)}),f(r).forEach(function(t){A(e,t)||(u(e,t)&&n.isMergeableObject(r[t])?c[t]=p(t,n)(e[t],r[t],n):c[t]=l(r[t],n))}),c}function a(e,r,n){n=n||{},n.arrayMerge=n.arrayMerge||d,n.isMergeableObject=n.isMergeableObject||b,n.cloneUnlessOtherwiseSpecified=l;var c=Array.isArray(r),t=Array.isArray(e),i=c===t;return i?c?n.arrayMerge(e,r,n):E(e,r,n):l(r,n)}a.all=function(r,n){if(!Array.isArray(r))throw new Error("first argument should be an array");return r.reduce(function(c,t){return a(c,t,n)},{})};var w=a,h=w;const T=o(h);export{S as c,o as g,T as o};
