import{g as h}from"./index-DHV2tfOS.js";import{Q as A,N as g,i as b}from"./@inertiajs-BhKdJayA.js";import{t as N,p as V}from"./lodash-DBgjQQU6.js";import{d as T}from"./@element-plus-ccBf1-WH.js";import{_ as E}from"./AppLayout-m_I9gnvX.js";import{_ as F}from"./InputLabel-BTXevqr4.js";import{_ as z}from"./TextInput-C52bsWxF.js";import{_ as O}from"./Pagination-DDsmbrzN.js";import{_ as Q}from"./LoadingIcon-CesYxFkK.js";import{_ as M,a as R}from"./SecondaryButton-BWHXZF7Q.js";import{_ as W}from"./PrimaryButton-DE9sqoJj.js";import{i as K,v as L,c as r,o as a,l as i,k as $,K as m,S as f,a as t,a4 as c,R as _,P as e,F as p,M as C}from"./@vue-BnW70ngI.js";import"./moment-C5S46NFB.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./FixedSelectionBox-CwNS68U7.js";import"./SelectionBox-58uvzdoT.js";import"./@heroicons-BLousAGu.js";import"./@headlessui-gOb5_P77.js";const P=["textContent"],U={class:"py-6 px-6 sm:mx-2"},G={class:"flex items-center items-stretch"},H={class:"mt-0 w-80"},J={class:"w-full"},X={class:"text-left flex"},Y=["textContent"],Z=["textContent"],tt=["textContent"],et=["textContent"],nt=["textContent"],st=["textContent"],ot=["textContent"],lt=["textContent"],at=["textContent"],rt=["textContent"],it={class:"number-column"},ct=["textContent"],dt=["textContent"],ut=["textContent"],ht=["textContent"],mt=["textContent"],ft={class:"p-0 border-l",colspan:"2"},_t={key:0,class:"w-full h-full"},pt=["textContent"],xt=["textContent"],Ct={class:"p-0 border-l",colspan:"2"},vt={key:0,class:"w-full h-full"},yt=["textContent"],gt=["textContent"],bt={class:"border-l w-[56px]"},$t={key:1,class:"flex border-t"},kt=["textContent"],wt=["textContent"],St=["textContent"],It={class:"flex items-center justify-end px-3 py-3"},qt=["textContent"],ne={__name:"Index",props:{filters:Object,attachedItems:Object},setup(d){const k=K("$toast"),v=A(),o=L({searched:d.filters.search??"",searching:!1,confirmDeletion:!1,open:!1,deleteId:null,deleting:!1}),w=N(()=>g.get(route("attachedSurvey.list"),V({search:o.searched}),{preserveState:!0,onBefore:()=>{o.searching=!0},onFinish:()=>{o.searching=!1}}),1e3),x=()=>{o.open=!1,setTimeout(()=>o.confirmDeletion=!1,150)},S=n=>{o.deleteId=n,o.confirmDeletion=!0,setTimeout(()=>o.open=!0,150)},I=()=>{o.deleting||g.post(route("attachedSurvey.delete"),{id:o.deleteId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{o.deleting=!0},onSuccess:()=>{x(),o.deleteId=null,v.props.jetstream.flash.message&&k.success(v.props.jetstream.flash.message)},onFinish:()=>{o.deleting=!1}})},q=n=>{const l={};return n.forEach(s=>{l[s.question_id]||(l[s.question_id]={question_id:s.question_id,content:s.question_content,choices:[]}),l[s.question_id].choices.push({choice_id:s.choice_id,content:s.content})}),Object.keys(l).map(s=>l[s])};return(n,l)=>(a(),r(p,null,[i(E,{title:n.$t("listSurvey")},{header:f(()=>[t("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:e(n.$t("listAttachedSurvey"))},null,8,P),i(c(b),{class:"ml-auto flex items-center justify-center px-4 py-2 bg-sky-500 border border-transparent rounded-md font-semibold text-white uppercase transition ease-in-out duration-150 hover:bg-sky-600 focus:outline-none h-[38px]",textContent:e(n.$t("addNew")),href:n.route("attachedSurvey.create")},null,8,["textContent","href"])]),default:f(()=>[t("div",U,[t("div",G,[t("div",H,[i(F,{for:"search",value:n.$t("attachedSurveySearch")},null,8,["value"]),i(z,{class:"mt-1 block w-full",id:"search",modelValue:o.searched,"onUpdate:modelValue":l[0]||(l[0]=s=>o.searched=s),placeholder:"...",disabled:o.searching,onEnter:c(w)},null,8,["modelValue","disabled","onEnter"])])]),t("div",{class:_(["bg-white rounded-md shadow overflow-auto mt-5 flex flex-col",{"grid-loading":o.searching}])},[o.searching?(a(),$(Q,{key:0,class:"grid-loading-icon",size:24,color:"rgb(55 65 81)"})):m("",!0),t("table",J,[t("tr",X,[t("th",{class:"number-column",textContent:e(n.$t("number"))},null,8,Y),t("th",{class:"title-flex-column",textContent:e(n.$t("attachedSurveyTitle"))},null,8,Z),t("th",{class:"survey-id-column",textContent:e(n.$t("surveyID"))},null,8,tt),t("th",{class:"survey-title-column",textContent:e(n.$t("surveyTitle"))},null,8,et),t("th",{class:"attached-id-column",textContent:e(n.$t("surveyIDWasAttached"))},null,8,nt),t("th",{class:"survey-title-column",textContent:e(n.$t("surveyTitleWasAttached"))},null,8,st),t("th",{class:"question-id-column",textContent:e(n.$t("questionID"))},null,8,ot),t("th",{class:"question-content-column",textContent:e(n.$t("questionContent"))},null,8,lt),t("th",{class:"answer-id-column",textContent:e(n.$t("answerID"))},null,8,at),t("th",{class:"answer-content-column",textContent:e(n.$t("answerContent"))},null,8,rt),l[1]||(l[1]=t("th",{class:"w-[56px]"},null,-1))]),d.attachedItems.data.length>0?(a(!0),r(p,{key:0},C(d.attachedItems.data,(s,D)=>(a(),r("tr",{class:_(["flex border-t",[D%2===0?"bg-blue-50":""]])},[t("td",it,[i(c(b),{class:"hover:text-sky-600 hover:underline",textContent:e(s.attached_id),href:n.route("attachedSurvey.update",{attachedSurvey:s.attached_id})},null,8,["textContent","href"])]),t("td",{class:"title-flex-column",textContent:e(s.title)},null,8,ct),t("td",{class:"survey-id-column",textContent:e(c(h)(s.survey.survey_id,"S"))},null,8,dt),t("td",{class:"survey-title-column",textContent:e(s.survey.title)},null,8,ut),t("td",{class:"attached-id-column",textContent:e(c(h)(s.toSurvey.survey_id,"S"))},null,8,ht),t("td",{class:"survey-title-column",textContent:e(s.toSurvey.title)},null,8,mt),t("td",ft,[s.choices.length>0?(a(),r("table",_t,[(a(!0),r(p,null,C(q(s.choices),(u,j)=>(a(),r("tr",{class:_(["flex h-full",[j>0?"border-t":""]])},[t("td",{class:"question-id-column",textContent:e(c(h)(u.question_id,"Q"))},null,8,pt),t("td",{class:"question-content-column",textContent:e(u.content)},null,8,xt),t("td",Ct,[u.choices.length>0?(a(),r("table",vt,[(a(!0),r(p,null,C(u.choices,(y,B)=>(a(),r("tr",{class:_(["flex h-full",[B>0?"border-t":""]])},[t("td",{class:"answer-id-column",textContent:e(c(h)(y.choice_id,"A"))},null,8,yt),t("td",{class:"answer-content-column",textContent:e(y.content)},null,8,gt)],2))),256))])):m("",!0)])],2))),256))])):m("",!0)]),t("td",bt,[i(c(T),{class:"w-6 text-gray-500 transition ease-in-out duration-150 hover:cursor-pointer hover:text-red-500",onClick:u=>S(s.attached_id)},null,8,["onClick"])])],2))),256)):(a(),r("tr",$t,[t("td",{colspan:"11",textContent:e(n.$t(d.filters.search&&d.filters.search!==""?"emptyResult":"emptyData"))},null,8,kt)]))])],2),i(O,{class:"mt-5 flex items-center justify-center mx-auto",links:d.attachedItems.links},null,8,["links"])])]),_:1},8,["title"]),o.confirmDeletion?(a(),$(R,{key:0,show:o.open,closeable:!0,size:"lg","padding-vertical":"py-20",onClose:x},{default:f(()=>[t("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:e(n.$t("confirm"))},null,8,wt),t("div",{class:"border-t px-3 py-4 font-light",textContent:e(n.$t("attachedSurveyDeleteConfirmation"))},null,8,St),l[2]||(l[2]=t("div",{class:"border-t"},null,-1)),t("div",It,[i(M,{class:"mr-3 text-sm h-[38px]",textContent:e(n.$t("cancel")),onClick:x},null,8,["textContent"]),i(W,{class:"text-sm"},{default:f(()=>[t("span",{class:"text-sm",textContent:e(n.$t("delete")),onClick:I},null,8,qt)]),_:1})])]),_:1},8,["show"])):m("",!0)],64))}};export{ne as default};
