import{f as k,c as O}from"./index-BxmPUm2h.js";import{Q as A,T as F,i as p,N as L}from"./@inertiajs-Dt0-hqjZ.js";import{s as h}from"./ziggy-js-C7EU8ifa.js";import{s as q,a as c}from"./primevue-CrCPcMFN.js";import{_ as K}from"./AppLayout-_qQ0AdHn.js";import{_ as S}from"./InputLabel-BTXevqr4.js";import{_ as I}from"./SearchInput-CdoSYJL3.js";import{_ as Q}from"./Pagination-D56Hn3as.js";import{_ as R}from"./LoadingIcon-CLD0VpVl.js";import{_ as X,a as z}from"./SecondaryButton-BoI1NwE9.js";import{_ as G}from"./RedButton-D21iPtqa.js";import{_ as H}from"./GridContainer-BC3u-41x.js";import{i as J,v as W,b as Y,N as Z,c as f,o as d,l as o,k as y,K as v,S as l,a,a4 as s,P as n,O as $,F as B,X as C}from"./@vue-BnW70ngI.js";import"./moment-C5S46NFB.js";/* empty css                                                    *//* empty css                                                                     */import"./app-EptGTPPo.js";import"./axios-t--hEgTQ.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./pinia-Ddsh4R0D.js";import"./@primevue-BllOwQ3c.js";import"./@primeuix-CKSY3gPt.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./izitoast-CYQMso0-.js";import"./deepmerge-CxfS31y9.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./PrimaryButton-DE9sqoJj.js";import"./FixedSelectionBox-Bk5LSyGJ.js";import"./SelectionBox-D4JR3fGi.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-CyTLADhX.js";import"./TextInput-DUNPEFms.js";import"./@headlessui-gOb5_P77.js";const ee=["textContent"],te={class:"p-6 sm:mx-2"},se={class:"flex items-stretch"},re={class:"mt-0 w-80 mr-8"},oe={class:"mt-0 w-80 mr-8"},le=["textContent"],ae=["textContent"],ie={class:"pi pi-info-circle text-gray-500"},ne={class:"pi pi-pen-to-square text-gray-300"},ue={class:"pi pi-chart-bar text-sky-400"},me={class:"pi pi-database text-amber-400"},ce={class:"pi pi-trash text-red-400"},de={key:0,class:"pi pi-pen-to-square text-gray-300"},he=["onClick"],pe=["textContent"],fe=["textContent"],ve={class:"flex items-center justify-end px-3 py-3"},_e=["textContent"],kt={__name:"Index",props:{filters:Object,users:Object},setup(u){const D=J("$toast"),x=A(),_=u,m=F({id:_.filters.id??"",search:_.filters.search??"",limit:_.filters.limit??10}),r=W({showSearchClearButton:(_.filters.search??"").trim().length>0,showSearchIDClearButton:(_.filters.id??"").trim().length>0,searching:!1,showModal:!1,open:!1,deleteId:null,deleting:!1,activePage:null,busy:Y(()=>r.activePage!==null||r.searching||m.processing)}),g=()=>{if(m.processing)return!1;m.transform(e=>O(e)).get(h("user.list"),{preserveScroll:!0,onSuccess:()=>{}})},V=()=>{r.showSearchClearButton=!0,g()},N=()=>{m.search="",r.showSearchClearButton=!1,g()},P=()=>{r.showSearchIDClearButton=!0,g()},j=()=>{m.id="",r.showSearchIDClearButton=!1,g()},U=e=>{r.deleteId=e,r.showModal=!0,setTimeout(()=>r.open=!0,150)},w=()=>{r.open=!1,setTimeout(()=>r.showModal=!1,150)},M=()=>{r.deleting||L.post(h("user.delete"),{id:r.deleteId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{r.deleting=!0},onSuccess:()=>{r.deleteId=null,x.props.jetstream.flash.message&&D.success(x.props.jetstream.flash.message),w()},onFinish:()=>{r.deleting=!1}})},T=e=>{r.activePage=e,r.searching=!0},E=e=>{m.limit=e,m.id="",m.search="",g()};return(e,i)=>{const b=Z("tooltip");return d(),f(B,null,[o(K,{title:e.$t("listUser")},{header:l(()=>[a("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:n(e.$t("listUser"))},null,8,ee)]),default:l(()=>[a("div",te,[a("div",se,[a("div",re,[o(S,{for:"user-search",value:e.$t("userSearch")},null,8,["value"]),o(I,{id:"user-search",class:"mt-1 block w-full",modelValue:s(m).search,"onUpdate:modelValue":i[0]||(i[0]=t=>s(m).search=t),placeholder:e.$t("searchByNameOrPhone"),disabled:r.busy,"show-clear-button":r.showSearchClearButton,onInput:i[1]||(i[1]=t=>r.showSearchClearButton=!1),onClearSearch:N,onEnter:V},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),a("div",oe,[o(S,{for:"id-search",value:e.$t("ID")},null,8,["value"]),o(I,{id:"id-search",class:"mt-1 block w-full",modelValue:s(m).id,"onUpdate:modelValue":i[2]||(i[2]=t=>s(m).id=t),placeholder:e.$t("searchByID"),disabled:r.busy,"show-clear-button":r.showSearchIDClearButton,onInput:i[3]||(i[3]=t=>r.showSearchIDClearButton=!1),onClearSearch:j,onEnter:P},null,8,["modelValue","placeholder","disabled","show-clear-button"])])]),o(H,{loading:r.busy},{default:l(()=>[o(s(q),{value:u.users.data},{empty:l(()=>[$(n(e.$t(u.filters.search&&u.filters.search!==""||u.filters.id&&u.filters.id!==""?"emptyResult":"emptyData")),1)]),default:l(()=>[o(s(c),{class:"number-column",header:e.$t("ID")},{body:l(({data:t})=>[o(s(p),{class:"hover:text-sky-600 hover:underline",textContent:n(t.user_id),href:s(h)("user.detail",{user:t.user_id})},null,8,["textContent","href"])]),_:1},8,["header"]),o(s(c),{class:"time-column",header:e.$t("registerAt")},{body:l(({data:t})=>[$(n(s(k)(t.created_at)),1)]),_:1},8,["header"]),o(s(c),{class:"phone-column",field:"phone",header:e.$t("phone")},null,8,["header"]),o(s(c),{class:"username-column",field:"name",header:e.$t("username")},null,8,["header"]),o(s(c),{class:"role-column",header:e.$t("role")},{body:l(({data:t})=>[$(n(e.$t(t.role)),1)]),_:1},8,["header"]),o(s(c),{class:"count-column",header:e.$t("postCount")},{body:l(({data:t})=>[t.post_count?(d(),y(s(p),{key:0,class:"hover:text-red-600 hover:underline",textContent:n(t.post_count),href:s(h)("post.list",{user:t.user_id})},null,8,["textContent","href"])):(d(),f("span",{key:1,textContent:n(t.post_count)},null,8,le))]),_:1},8,["header"]),o(s(c),{class:"count-column",header:e.$t("answerCount")},{body:l(({data:t})=>[t.answer_count?(d(),y(s(p),{key:0,class:"hover:text-red-600 hover:underline",textContent:n(t.answer_count),href:s(h)("postAnswer.list",{user:t.user_id})},null,8,["textContent","href"])):(d(),f("span",{key:1,textContent:n(t.answer_count)},null,8,ae))]),_:1},8,["header"]),o(s(c),{class:"count-column",field:"comment_count",header:e.$t("commentCount")},null,8,["header"]),o(s(c),{class:"count-column",field:"best_answer_count",header:e.$t("bestAnswerCount")},null,8,["header"]),o(s(c),{class:"status-column",field:"status_label",header:e.$t("status")},null,8,["header"]),o(s(c),{class:"last-logged-time-column",header:e.$t("lastLoggedInTime")},{body:l(({data:t})=>[$(n(s(k)(t.last_logged_in_at)),1)]),_:1},8,["header"]),o(s(c),{class:"action-column extra"},{header:l(()=>[C(a("i",ie,null,512),[[b,e.$t("userInfo"),void 0,{top:!0}]]),C(a("i",ne,null,512),[[b,e.$t("edit"),void 0,{top:!0}]]),C(a("i",ue,null,512),[[b,e.$t("attribute"),void 0,{top:!0}]]),C(a("i",me,null,512),[[b,e.$t("feed"),void 0,{top:!0}]]),C(a("i",ce,null,512),[[b,e.$t("delete"),void 0,{top:!0}]])]),body:l(({data:t})=>[parseInt(t.status)!==0?(d(),f(B,{key:0},[o(s(p),{href:s(h)("user.detail",{user:t.user_id})},{default:l(()=>i[4]||(i[4]=[a("i",{class:"pi pi-info-circle text-gray-500 hover:text-sky-600"},null,-1)])),_:2},1032,["href"]),t.is_super_admin?v("",!0):(d(),f("i",de)),o(s(p),{href:s(h)("user.attribute",{user:t.user_id})},{default:l(()=>i[5]||(i[5]=[a("i",{class:"pi pi-chart-bar text-sky-400 hover:text-sky-600 hover:cursor-pointer"},null,-1)])),_:2},1032,["href"]),o(s(p),{href:s(h)("user.feed",{user:t.user_id})},{default:l(()=>i[6]||(i[6]=[a("i",{class:"pi pi-database text-amber-400 hover:text-amber-600 hover:cursor-pointer"},null,-1)])),_:2},1032,["href"]),t.is_super_admin?v("",!0):(d(),f("i",{key:1,class:"pi pi-trash text-red-400 hover:text-red-600 hover:cursor-pointer",onClick:ge=>U(t.user_id)},null,8,he))],64)):v("",!0)]),_:1})]),_:1},8,["value"])]),_:1},8,["loading"]),u.users.data.length>0?(d(),y(Q,{key:0,class:"mt-5 flex items-center justify-center mx-auto",links:u.users.links,active:r.activePage,disabled:r.busy,limit:u.users.per_page,total:u.users.total,from:u.users.from,to:u.users.to,onProgress:T,onChangeLimit:E},null,8,["links","active","disabled","limit","total","from","to"])):v("",!0)])]),_:1},8,["title"]),r.showModal?(d(),y(z,{key:0,show:r.open,onClose:w},{default:l(()=>[a("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:n(e.$t("confirm"))},null,8,pe),a("div",{class:"border-t border-b p-4",textContent:n(e.$t("confirmDeleteUserMessage"))},null,8,fe),a("div",ve,[o(X,{class:"mr-3 text-sm",textContent:n(e.$t("cancel")),onClick:w},null,8,["textContent"]),o(G,{class:"text-sm overflow-hidden h-[34px]",onClick:M},{default:l(()=>[r.deleting?(d(),y(R,{key:0,class:"mr-1"})):v("",!0),a("span",{class:"text-sm text-white",textContent:n(e.$t("delete"))},null,8,_e)]),_:1})])]),_:1},8,["show"])):v("",!0)],64)}}};export{kt as default};
