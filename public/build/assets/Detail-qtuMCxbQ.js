import{f as u}from"./index-k9QJQake.js";import{i as c}from"./@inertiajs-Dt0-hqjZ.js";import{_ as b}from"./AppLayout-CTb2MMqd.js";import{k as n,o as r,S as p,a as t,P as i,c as l,F as _,M as f,a4 as h}from"./@vue-BnW70ngI.js";import"./moment-C5S46NFB.js";/* empty css                                                    *//* empty css                                                                     */import"./app-65VXU7yX.js";import"./axios-t--hEgTQ.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./ziggy-js-C7EU8ifa.js";import"./pinia-Ddsh4R0D.js";import"./primevue-CrCPcMFN.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./izitoast-CYQMso0-.js";import"./deepmerge-CxfS31y9.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";import"./PrimaryButton-DE9sqoJj.js";const x=["textContent"],C={class:"max-w-7xl mx-auto p-6"},w={class:"bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden"},g={class:"p-datatable-table w-full w-border"},y={class:"text-left flex"},v=["textContent"],k={class:"text-left flex border-t"},B=["textContent"],D={class:"border-l flex-1"},I=["innerHTML"],$={key:2},vt={__name:"Detail",props:{post:Object},setup(a){const m=[{label:"ID",attribute:"post_id"},{label:"createdBy",attribute:"username"},{label:"createdByID",attribute:"user_id"},{label:"postContent",attribute:"content"},{label:"postCreatedAt",attribute:"created_at"},{label:"postType",attribute:"tag"},{label:"answerCount",attribute:"answer_count"},{label:"postStatus",attribute:"status_label"}],d=(e,s)=>{const o=e[s.attribute];return["postCreatedAt"].includes(s.label)?u(o):o};return(e,s)=>(r(),n(b,{title:e.$t("postInfo")},{header:p(()=>[t("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:i(e.$t("postInfo"))},null,8,x)]),default:p(()=>[t("div",C,[t("div",w,[t("table",g,[t("thead",null,[t("tr",y,[t("th",{colspan:"2",textContent:i(e.$t("generalInfo"))},null,8,v)])]),t("tbody",null,[(r(),l(_,null,f(m,o=>t("tr",k,[t("td",{class:"w-1/4",textContent:i(e.$t(o.label))},null,8,B),t("td",D,[o.label==="answerCount"?(r(),n(h(c),{key:0,class:"hover:text-red-600 hover:underline",textContent:i(a.post.answer_count),href:e.route("postAnswer.list",{post:a.post.post_id})},null,8,["textContent","href"])):o.label==="postContent"?(r(),l("div",{key:1,innerHTML:a.post.content},null,8,I)):(r(),l("div",$,i(d(a.post,o)),1))])])),64))])])])])]),_:1},8,["title"]))}};export{vt as default};
