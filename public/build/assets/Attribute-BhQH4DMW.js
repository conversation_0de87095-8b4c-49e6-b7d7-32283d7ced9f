import{_ as $}from"./AppLayout-m_I9gnvX.js";import{_ as f}from"./PrimaryButton-DE9sqoJj.js";import{c as y,d as p,e as _,f as v,g as C}from"./primevue-u0EmObz-.js";import{v as j,b as m,k as D,o,S as i,a as t,l as r,P as e,a4 as u,c as d,F as b,K as w,M as g,R as k}from"./@vue-BnW70ngI.js";import"./@inertiajs-BhKdJayA.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BWHXZF7Q.js";import"./@primeuix-CNwdBq9K.js";import"./@primevue-Bw51iWDD.js";const I=["textContent"],T={class:"max-w-7xl mx-auto py-6 px-6"},O={class:"bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden"},S={class:"w-full"},L={class:"text-left flex"},B=["textContent"],A={class:"text-left flex border-t odd:bg-blue-50 even:bg-white"},N=["textContent"],V=["textContent"],F={class:"text-left flex border-t odd:bg-blue-50 even:bg-white"},z=["textContent"],E=["textContent"],K={class:"w-full"},M={class:"text-left flex border-b"},P=["textContent"],R={class:"text-left flex border-b odd:bg-blue-50 even:bg-white"},q=["textContent"],G=["textContent"],H=["textContent"],J=["textContent"],Q={key:0,class:"mb-6 w-full flex justify-center"},U=["textContent"],W={class:"w-full"},X={class:"text-left flex"},Y=["textContent"],Z={class:"text-left flex border-t odd:bg-blue-50 even:bg-white"},tt=["textContent"],et=["textContent"],st={class:"text-left flex border-t odd:bg-blue-50 even:bg-white"},lt=["textContent"],nt=["textContent"],ot=["textContent"],at={class:"w-full"},dt={class:"text-left flex border-b"},it=["textContent"],rt={class:"text-left flex border-b odd:bg-blue-50 even:bg-white"},ct=["textContent"],ut=["textContent"],xt=["textContent"],bt=["textContent"],ht={key:0,class:"mb-6 w-full flex justify-center"},ft=["textContent"],_t={class:"w-full"},Ct={class:"text-left flex"},mt=["textContent"],wt={class:"text-left flex border-t odd:bg-blue-50 even:bg-white"},gt=["textContent"],kt=["textContent"],$t={class:"text-left flex border-t odd:bg-blue-50 even:bg-white"},yt=["textContent"],pt=["textContent"],vt=["textContent"],Rt={__name:"Attribute",props:{showToIndex:Number,attributes:Object,user:Object},setup(l){const h=l,c=j({showToIndex:h.showToIndex,showLikeDates:m(()=>{const s=[],a=Object.keys(h.attributes.like.data);for(let n=0;n<a.length;n++)n<c.showToIndex&&s.push(a[n]);return s}),showSimilarDates:m(()=>{const s=[],a=Object.keys(h.attributes.similar.data);for(let n=0;n<a.length;n++)n<c.showToIndex&&s.push(a[n]);return s})});return(s,a)=>(o(),D($,{title:s.$t("userAttribute")},{header:i(()=>[t("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:e(s.$t("userAttribute"))},null,8,I)]),default:i(()=>[t("div",T,[t("div",O,[t("table",S,[t("thead",null,[t("tr",L,[t("th",{colspan:"2",textContent:e(s.$t("userInfo"))},null,8,B)])]),t("tbody",null,[t("tr",A,[t("td",{class:"w-1/3",textContent:e(s.$t("ID"))},null,8,N),t("td",{class:"border-l flex-1",textContent:e(l.user.user_id)},null,8,V)]),t("tr",F,[t("td",{class:"w-1/3",textContent:e(s.$t("username"))},null,8,z),t("td",{class:"border-l flex-1",textContent:e(l.user.name)},null,8,E)])])])]),r(u(y),{value:"0",class:"bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden"},{default:i(()=>[r(u(p),null,{default:i(()=>[r(u(_),{value:"0",textContent:e(s.$t("tabSimilar"))},null,8,["textContent"]),r(u(_),{value:"1",textContent:e(s.$t("tabLike"))},null,8,["textContent"])]),_:1}),r(u(v),{class:"p-0"},{default:i(()=>[r(u(C),{value:"0"},{default:i(()=>[l.attributes.similar.ids.length>0?(o(),d(b,{key:0},[t("table",K,[t("thead",null,[t("tr",M,[t("th",{colspan:"2",textContent:e(s.$t("topSimilarByDay"))},null,8,P)])]),t("tbody",null,[t("tr",R,[t("td",{class:"w-1/3",textContent:e(s.$t("date"))},null,8,q),t("td",{class:"border-l flex-1",textContent:e(s.$t("ID"))},null,8,G)]),(o(!0),d(b,null,g(l.attributes.similar.data,(n,x)=>(o(),d("tr",{class:k(["text-left flex border-b odd:bg-blue-50 even:bg-white",c.showSimilarDates.includes(x)?"":"hidden"])},[t("td",{class:"w-1/3",textContent:e(x)},null,8,H),t("td",{class:"border-l flex-1",textContent:e(n.join(", "))},null,8,J)],2))),256))])]),Object.keys(l.attributes.similar.data).length>c.showToIndex?(o(),d("div",Q,[r(f,{class:"normal-case",onClick:a[0]||(a[0]=n=>c.showToIndex=Object.keys(l.attributes.similar.data).length)},{default:i(()=>[t("span",{class:"text-sm",textContent:e(s.$t("displayAll"))},null,8,U)]),_:1})])):w("",!0),t("table",W,[t("thead",null,[t("tr",X,[t("th",{colspan:"2",textContent:e(s.$t("topSimilar"))},null,8,Y)])]),t("tbody",null,[t("tr",Z,[t("td",{class:"w-1/3",textContent:e(s.$t("top50"))},null,8,tt),t("td",{class:"border-l flex-1",textContent:e(l.attributes.similar.ids.join(", "))},null,8,et)]),t("tr",st,[t("td",{class:"w-1/3",textContent:e(s.$t("viewedData"))},null,8,lt),t("td",{class:"border-l flex-1",textContent:e(l.attributes.similar.posts.join(", "))},null,8,nt)])])])],64)):(o(),d("div",{key:1,class:"p-3",textContent:e(s.$t("emptySimilarData"))},null,8,ot))]),_:1}),r(u(C),{value:"1"},{default:i(()=>[l.attributes.like.ids.length>0?(o(),d(b,{key:0},[t("table",at,[t("thead",null,[t("tr",dt,[t("th",{colspan:"2",textContent:e(s.$t("topLikeByDay"))},null,8,it)])]),t("tbody",null,[t("tr",rt,[t("td",{class:"w-1/3",textContent:e(s.$t("date"))},null,8,ct),t("td",{class:"border-l flex-1",textContent:e(s.$t("ID"))},null,8,ut)]),(o(!0),d(b,null,g(l.attributes.like.data,(n,x)=>(o(),d("tr",{class:k(["text-left flex border-b odd:bg-blue-50 even:bg-white",c.showLikeDates.includes(x)?"":"hidden"])},[t("td",{class:"w-1/3",textContent:e(x)},null,8,xt),t("td",{class:"border-l flex-1",textContent:e(n.join(", "))},null,8,bt)],2))),256))])]),Object.keys(l.attributes.like.data).length>c.showToIndex?(o(),d("div",ht,[r(f,{class:"normal-case",onClick:a[1]||(a[1]=n=>c.showToIndex=Object.keys(l.attributes.like.data).length)},{default:i(()=>[t("span",{class:"text-sm",textContent:e(s.$t("displayAll"))},null,8,ft)]),_:1})])):w("",!0),t("table",_t,[t("thead",null,[t("tr",Ct,[t("th",{colspan:"2",textContent:e(s.$t("topLike"))},null,8,mt)])]),t("tbody",null,[t("tr",wt,[t("td",{class:"w-1/3",textContent:e(s.$t("top15"))},null,8,gt),t("td",{class:"border-l flex-1",textContent:e(l.attributes.like.ids.join(", "))},null,8,kt)]),t("tr",$t,[t("td",{class:"w-1/3",textContent:e(s.$t("relatedData"))},null,8,yt),t("td",{class:"border-l flex-1",textContent:e(l.attributes.like.posts.join(", "))},null,8,pt)])])])],64)):(o(),d("div",{key:1,class:"p-3",textContent:e(s.$t("emptyLikedData"))},null,8,vt))]),_:1})]),_:1})]),_:1})])]),_:1},8,["title"]))}};export{Rt as default};
