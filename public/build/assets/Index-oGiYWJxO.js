import{u as K}from"./vue-i18n-DNS8h1FH.js";import{T}from"./@inertiajs-BhKdJayA.js";import{e as L,d as O}from"./@element-plus-ccBf1-WH.js";import{_ as R}from"./AppLayout-m_I9gnvX.js";import{_ as I}from"./Checkbox-BW6Lzxs4.js";import{_ as q}from"./ConfirmationModal-koF4JoqQ.js";import{_ as A}from"./DialogModal-CP7TKBlg.js";import{_ as P}from"./LoadingIcon-CesYxFkK.js";import{_ as y}from"./PrimaryButton-DE9sqoJj.js";import{_ as h}from"./SecondaryButton-BWHXZF7Q.js";import{_ as B}from"./InputLabel-BTXevqr4.js";import{_ as G}from"./TextInput-C52bsWxF.js";import{_ as H}from"./InputError-gQdwtcoE.js";import{i as J,r as _,c as m,o as i,l,S as r,a as t,P as o,F as b,M as S,k as w,K as v,a4 as a,R as F,O as x}from"./@vue-BnW70ngI.js";import"./@intlify-TnaUIxGf.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const Q=["textContent"],W={class:"max-w-7xl mx-auto p-6"},X={class:"bg-white rounded-md shadow overflow-x-auto"},Y={class:"w-full whitespace-nowrap"},Z={class:"text-left"},ee=["textContent"],te=["textContent"],se=["textContent"],ne=["textContent"],oe={class:"px-4 py-3 flex items-center justify-end"},le={key:1},ae=["textContent"],re={class:"mt-0"},ie={key:0,class:"mt-4"},ue={class:"grid grid-cols-2 gap-4 mt-4"},me={class:"flex items-center"},de=["textContent"],ce=["textContent"],pe={class:"grid grid-cols-2 gap-4"},fe={class:"flex items-center"},ve=["textContent"],ke=["textContent"],Ce=["textContent"],xe=["textContent"],$e=["textContent"],Oe={__name:"Index",props:{tokens:Array,availablePermissions:Array,defaultPermissions:Array},setup(d){const N=J("$toast"),{t:V}=K(),u=T({name:"",permissions:d.defaultPermissions}),$=_(!1),k=_(!1),D=()=>{k.value=!0},U=()=>{u.post(route("api-tokens.store"),{preserveScroll:!0,onSuccess:()=>{$.value=!0,k.value=!1,u.reset()}})},p=_(null),c=T({permissions:[]}),j=e=>{c.permissions=e.abilities,p.value=e},M=()=>{c.put(route("api-tokens.update",p.value),{preserveScroll:!0,preserveState:!0,onSuccess:()=>{p.value=null,N.success(V("tokenPermissionUpdatedSuccessfully"))}})},g=T({}),f=_(null),z=e=>{f.value=e},E=()=>{g.delete(route("api-tokens.destroy",f.value),{preserveScroll:!0,preserveState:!0,onSuccess:()=>{f.value=null,N.success(V("tokenDeletedSuccessfully"))}})};return(e,s)=>(i(),m(b,null,[l(R,{title:e.$t("API-Tokens")},{header:r(()=>[t("h2",{class:"font-semibold text-xl text-gray-800 leading-tight mr-auto",textContent:o(e.$t("API-Tokens"))},null,8,Q),l(y,{class:"text-sm py-1",textContent:o(e.$t("addNew")),onClick:D},null,8,["textContent"])]),default:r(()=>[t("div",W,[t("div",X,[t("table",Y,[t("thead",null,[t("tr",Z,[t("th",{class:"pt-4 pb-3 px-4",textContent:o(e.$t("tokenName"))},null,8,ee),t("th",{class:"pt-4 pb-3 px-4 w-60",textContent:o(e.$t("lastUsed"))},null,8,te),s[11]||(s[11]=t("th",{class:"w-40"},null,-1))])]),t("tbody",null,[d.tokens.length>0?(i(!0),m(b,{key:0},S(d.tokens,n=>(i(),m("tr",{key:n.id,class:"border-t hover:bg-gray-100 focus-within:bg-gray-100"},[t("td",{class:"px-4 py-3 text-left",textContent:o(n.name)},null,8,se),t("td",{class:"px-4 py-3 text-left",textContent:o(n.last_used_ago)},null,8,ne),t("td",oe,[d.availablePermissions.length>0?(i(),w(a(L),{key:0,class:"mr-3 w-5 mt-1 text-sky-400 transition hover:text-sky-700 hover:cursor-pointer",onClick:C=>j(n)},null,8,["onClick"])):v("",!0),l(a(O),{class:"w-5 mt-1 text-red-400 transition hover:text-red-700 hover:cursor-pointer",onClick:C=>z(n)},null,8,["onClick"])])]))),128)):(i(),m("tr",le,[t("td",{colspan:"3",class:"border-t px-4 py-5 text-center text-md",textContent:o(e.$t("emptyData"))},null,8,ae)]))])])])])]),_:1},8,["title"]),l(A,{show:k.value,onClose:s[3]||(s[3]=n=>k.value=!1)},{title:r(()=>[x(o(e.$t("createToken")),1)]),content:r(()=>[t("div",re,[l(B,{for:"name",value:e.$t("tokenName")},null,8,["value"]),l(G,{id:"name",class:"mt-1 block w-full",modelValue:a(u).name,"onUpdate:modelValue":s[0]||(s[0]=n=>a(u).name=n),type:"text",autofocus:""},null,8,["modelValue"]),l(H,{class:"mt-2",message:a(u).errors.name},null,8,["message"])]),d.availablePermissions.length>0?(i(),m("div",ie,[l(B,{for:"permissions",value:e.$t("permissions")},null,8,["value"]),t("div",ue,[(i(!0),m(b,null,S(d.availablePermissions,n=>(i(),m("div",{key:n},[t("label",me,[l(I,{checked:a(u).permissions,"onUpdate:checked":s[1]||(s[1]=C=>a(u).permissions=C),value:n},null,8,["checked","value"]),t("span",{class:"ms-2 text-gray-600",textContent:o(e.$t(n))},null,8,de)])]))),128))])])):v("",!0)]),footer:r(()=>[l(h,{onClick:s[2]||(s[2]=n=>k.value=!1),textContent:o(e.$t("cancel"))},null,8,["textContent"]),l(y,{class:F(["ms-3",{"opacity-25":a(u).processing}]),disabled:a(u).processing,onClick:U},{default:r(()=>[a(u).processing?(i(),w(P,{key:0,class:"mr-2"})):v("",!0),t("span",{class:"text-sm",textContent:o(e.$t("create"))},null,8,ce)]),_:1},8,["class","disabled"])]),_:1},8,["show"]),l(A,{show:p.value!=null,onClose:s[6]||(s[6]=n=>p.value=null)},{title:r(()=>[x(o(e.$t("API-TokenPermissions")),1)]),content:r(()=>[t("div",pe,[(i(!0),m(b,null,S(d.availablePermissions,n=>(i(),m("div",{key:n},[t("label",fe,[l(I,{checked:a(c).permissions,"onUpdate:checked":s[4]||(s[4]=C=>a(c).permissions=C),value:n},null,8,["checked","value"]),t("span",{class:"ms-2 text-gray-600",textContent:o(e.$t(n))},null,8,ve)])]))),128))])]),footer:r(()=>[l(h,{onClick:s[5]||(s[5]=n=>p.value=null),textContent:o(e.$t("cancel"))},null,8,["textContent"]),l(y,{class:F(["ms-3",{"opacity-25":a(c).processing}]),disabled:a(c).processing,onClick:M},{default:r(()=>[a(c).processing?(i(),w(P,{key:0,class:"mr-2"})):v("",!0),t("span",{class:"text-sm",textContent:o(e.$t("update"))},null,8,ke)]),_:1},8,["class","disabled"])]),_:1},8,["show"]),l(q,{show:f.value!=null,onClose:s[8]||(s[8]=n=>f.value=null)},{title:r(()=>[x(o(e.$t("deleteToken")),1)]),content:r(()=>[x(o(e.$t("deleteTokenMessage")),1)]),footer:r(()=>[l(h,{onClick:s[7]||(s[7]=n=>f.value=null),textContent:o(e.$t("cancel"))},null,8,["textContent"]),l(y,{class:F(["ms-3",{"opacity-25":a(g).processing}]),disabled:a(g).processing,onClick:E},{default:r(()=>[a(g).processing?(i(),w(P,{key:0,class:"mr-2"})):v("",!0),t("span",{class:"text-sm",textContent:o(e.$t("delete"))},null,8,Ce)]),_:1},8,["class","disabled"])]),_:1},8,["show"]),l(A,{show:$.value,onClose:s[10]||(s[10]=n=>$.value=!1)},{title:r(()=>[x(o(e.$t("API-Tokens")),1)]),content:r(()=>[t("div",{class:"mt-0",textContent:o(e.$t("tokenCopyMessage"))},null,8,xe),e.$page.props.jetstream.flash.token?(i(),m("div",{key:0,class:"mt-4 bg-gray-100 px-4 py-3 rounded-md font-mono text-sm text-gray-500 break-all",textContent:o(e.$page.props.jetstream.flash.token)},null,8,$e)):v("",!0)]),footer:r(()=>[l(h,{onClick:s[9]||(s[9]=n=>$.value=!1),textContent:o(e.$t("close"))},null,8,["textContent"])]),_:1},8,["show"])],64))}};export{Oe as default};
