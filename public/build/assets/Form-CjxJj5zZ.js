import{Q as j,T as B}from"./@inertiajs-Dt0-hqjZ.js";import{u as O}from"./vue-i18n-kWKo0idO.js";import{c as F}from"./@element-plus-CyTLADhX.js";import{_ as N}from"./AppLayout-CTb2MMqd.js";import{_ as P}from"./RedButton-D21iPtqa.js";import{_ as V}from"./LoadingIcon-CLD0VpVl.js";import{_ as q}from"./InputLabel-BTXevqr4.js";import{_ as z}from"./TextInput-DUNPEFms.js";import{_ as u}from"./InputError-gQdwtcoE.js";import{_ as K}from"./SecondaryButton-BoI1NwE9.js";import{_ as C}from"./SurveySelectionBox-82zS6qsI.js";import{_ as D}from"./FixedSelectionBox-Bk5LSyGJ.js";import{i as I,v as L,b as R,e as W,k as p,o as n,S as _,a as c,c as m,K as f,l,a4 as o,F as v,M as G,R as g,P as y}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@intlify-xvnhHnag.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./PrimaryButton-DE9sqoJj.js";/* empty css                                                    */import"./pinia-Ddsh4R0D.js";import"./index-k9QJQake.js";import"./moment-C5S46NFB.js";/* empty css                                                                     */import"./app-65VXU7yX.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./ziggy-js-C7EU8ifa.js";import"./primevue-CrCPcMFN.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./izitoast-CYQMso0-.js";import"./SelectionBox-D4JR3fGi.js";import"./@heroicons-BLousAGu.js";import"./@headlessui-gOb5_P77.js";const H=["textContent"],J=["textContent"],X={class:"max-w-7xl mx-auto py-6 px-6"},Y={class:"bg-white rounded-md shadow px-6 py-5 flex flex-col"},Z={class:"mt-0"},ee={class:"mt-5"},te={class:"mt-5"},oe={key:0,class:"mt-5"},se={class:"flex-1 mr-4 question-answer-select"},ae={class:"flex-1 ml-4 question-answer-select"},re={class:"ml-4 px-4 w-[56px]"},ut={__name:"Form",props:{title:String,attachedSurvey:Object},setup(h){const{t:E}=O(),T=h,b=I("$toast"),w=j(),e=B(T.attachedSurvey),i=L({loading:!1,clearData:!1,clearToSurveyData:!1,exclude:[],questions:[],questionOptions:R(()=>{const t=[];return i.questions.forEach(s=>{t.push({label:s.content,value:s.question_id})}),t})}),U=()=>{if(e.processing)return!1;e.errors={},e.transform(t=>{const s={};return s.attached_id=t.attached_id,s.title=t.title,s.survey_id=t.survey_id,s.to_survey_id=t.to_survey_id,s.choices=[],e.choices.forEach(a=>{(a.show??!1)&&s.choices.push({question_id:a.question_id,choice_id:a.choice_id})}),s}).post(route("attachedSurvey.store"),{onSuccess:()=>{e.attached_id||(e.reset(),i.clearData=!0,i.clearToSurveyData=!0),w.props.jetstream.flash.message&&b.success(w.props.jetstream.flash.message)}})},k=()=>{e.choices.forEach(t=>{t.show=!1}),e.to_survey_id="",i.clearToSurveyData=!0,S()},$=async()=>{if(i.loading)return!1;i.loading=!0,await window.axios.post(route("survey.listQuestion"),{survey_id:e.to_survey_id}).then(t=>{i.questions=t.data}).catch(()=>{b.error(E("commonErrorMessage"))}).finally(()=>{i.loading=!1})},A=t=>{const s=[],a=i.questions.find(r=>r.question_id===t);return a&&a.choices.forEach(r=>{s.push({value:r.choice_id,label:r.content})}),s},M=t=>{e.choices[t].show=!1,e.choices[t].question_id="",e.choices[t].choice_id=""},S=()=>{e.choices.push({question_id:"",choice_id:"",show:!0})},Q=(t,s)=>{},x=t=>{let s=0;for(let a=0;a<e.choices.length;a++)if(e.choices[a].show){if(a===t)break;s++}return s};return W(()=>{e.attached_id&&$()}),(t,s)=>(n(),p(N,{title:h.title},{header:_(()=>[c("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:y(h.title)},null,8,H),l(P,{class:g(["ml-auto",{"opacity-25":o(e).processing}]),disabled:o(e).processing,onClick:U},{default:_(()=>[o(e).processing?(n(),p(V,{key:0,class:"mr-2"})):f("",!0),c("span",{class:"text-sm",textContent:y(t.$t("save"))},null,8,J)]),_:1},8,["class","disabled"])]),default:_(()=>[c("div",X,[c("div",Y,[c("div",Z,[l(q,{for:"survey-title",value:t.$t("attachedSurveyTitle")},null,8,["value"]),l(z,{class:"mt-1 block w-full",id:"survey-title",modelValue:o(e).title,"onUpdate:modelValue":s[0]||(s[0]=a=>o(e).title=a),disabled:o(e).processing||i.loading,type:"text"},null,8,["modelValue","disabled"]),l(u,{message:o(e).errors.title},null,8,["message"])]),c("div",ee,[l(C,{class:"w-full",modelValue:o(e).survey_id,"onUpdate:modelValue":s[1]||(s[1]=a=>o(e).survey_id=a),label:t.$t("survey"),placeholder:t.$t("selectSurvey"),"clear-data":i.clearData,disabled:o(e).processing||i.loading,"enable-search":!0,"search-placeholder":t.$t("searchSurveyPlaceholder"),onSelected:k,onDataCleared:s[2]||(s[2]=a=>i.clearData=!1)},null,8,["modelValue","label","placeholder","clear-data","disabled","search-placeholder"]),l(u,{message:o(e).errors.survey_id},null,8,["message"])]),c("div",te,[l(C,{class:"w-full",modelValue:o(e).to_survey_id,"onUpdate:modelValue":s[3]||(s[3]=a=>o(e).to_survey_id=a),label:t.$t("surveyWasAttached"),placeholder:t.$t("selectSurvey"),"clear-data":i.clearToSurveyData,disabled:o(e).processing||i.loading||!o(e).survey_id,"enable-search":!0,"load-data":!1,exclude:[o(e).survey_id],"search-placeholder":t.$t("searchSurveyPlaceholder"),onSelected:$,onDataCleared:s[4]||(s[4]=a=>i.clearToSurveyData=!1)},null,8,["modelValue","label","placeholder","clear-data","disabled","exclude","search-placeholder"]),l(u,{message:o(e).errors.to_survey_id},null,8,["message"])]),o(e).to_survey_id?(n(),m("div",oe,[i.loading?(n(),p(V,{key:0,color:"#000000",size:20})):(n(),m(v,{key:1},[l(q,{for:"survey-title",value:t.$t("answer")},null,8,["value"]),(n(!0),m(v,null,G(o(e).choices,(a,r)=>(n(),m(v,null,[a.show?(n(),m("div",{class:g(["flex w-full items-stretch",{"mt-3 pt-2 border-t border-dashed":r>0}]),key:"choice_"+r},[c("div",se,[l(D,{modelValue:o(e).choices[r].question_id,"onUpdate:modelValue":d=>o(e).choices[r].question_id=d,placeholder:t.$t("selectQuestion"),disabled:o(e).processing,options:i.questionOptions,clearable:!1,onSelected:d=>Q(r,d)},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","options","onSelected"]),l(u,{message:o(e).errors["choices."+x(r)+".question_id"]},null,8,["message"])]),c("div",ae,[(n(),p(D,{key:"answer_choice_"+r,modelValue:o(e).choices[r].choice_id,"onUpdate:modelValue":d=>o(e).choices[r].choice_id=d,placeholder:t.$t("selectAnswer"),disabled:o(e).processing||!o(e).choices[r].question_id,options:A(o(e).choices[r].question_id),clearable:!1},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","options"])),l(u,{message:o(e).errors["choices."+x(r)+".choice_id"]},null,8,["message"])]),c("div",re,[l(o(F),{class:g(["w-5 text-gray-300 transition ease-in-out duration-150 mt-4",[o(e).choices.length>1?"hover:cursor-pointer hover:text-red-500":""]]),onClick:d=>M(r)},null,8,["class","onClick"])])],2)):f("",!0)],64))),256)),l(K,{class:"mt-3 text-sm h-[38px]",textContent:y(t.$t("addMoreAnswer")),onClick:S},null,8,["textContent"])],64))])):f("",!0)])])]),_:1},8,["title"]))}};export{ut as default};
