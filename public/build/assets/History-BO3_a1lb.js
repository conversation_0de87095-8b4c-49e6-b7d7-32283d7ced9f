import{f as D,t as k,c as U}from"./index-BxmPUm2h.js";import{T as I}from"./@inertiajs-Dt0-hqjZ.js";import{s as x}from"./ziggy-js-C7EU8ifa.js";import{b as E,s as H,a as m}from"./primevue-CrCPcMFN.js";import{_ as N}from"./AppLayout-_qQ0AdHn.js";import{_ as j}from"./Pagination-D56Hn3as.js";import{_ as p}from"./InputLabel-BTXevqr4.js";import{_ as g}from"./SearchInput-CdoSYJL3.js";import{_ as O}from"./GridContainer-BC3u-41x.js";import{v as L,b as T,k as v,o as h,S as d,a as c,l as o,K as w,a4 as r,c as z,O as b,P as f}from"./@vue-BnW70ngI.js";import"./moment-C5S46NFB.js";/* empty css                                                    *//* empty css                                                                     */import"./app-EptGTPPo.js";import"./axios-t--hEgTQ.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./pinia-Ddsh4R0D.js";import"./@primevue-BllOwQ3c.js";import"./@primeuix-CKSY3gPt.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./izitoast-CYQMso0-.js";import"./deepmerge-CxfS31y9.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";import"./PrimaryButton-DE9sqoJj.js";import"./FixedSelectionBox-Bk5LSyGJ.js";import"./SelectionBox-D4JR3fGi.js";import"./LoadingIcon-CLD0VpVl.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-CyTLADhX.js";import"./TextInput-DUNPEFms.js";import"./@headlessui-gOb5_P77.js";const A=["textContent"],K={class:"p-6 sm:mx-2"},R={class:"flex items-stretch"},q={class:"mt-0 w-80 mr-8"},F={class:"mt-0 w-80 mr-8"},G={class:"mt-0 w-80 mr-8"},J={class:"relative mt-1"},Ze={__name:"History",props:{filters:Object,logs:Object},setup(a){const u=a,s=I({user:u.filters.user??"",postSearch:u.filters.post??"",date:u.filters.date??"",limit:u.filters.limit??10}),t=L({searching:!1,showUserSearchClearButton:(u.filters.user??"").trim().length>0,showPostSearchClearButton:(u.filters.post??"").trim().length>0,activePage:null,busy:T(()=>t.activePage!==null||t.searching||s.processing)}),n=()=>{if(s.processing)return!1;s.transform(e=>(e.post=e.postSearch,delete e.postSearch,e.date&&(e.date=k(e.date)),U(e))).get(x("postViewHistory"),{preserveScroll:!0,onSuccess:()=>{}})},S=()=>{t.showPostSearchClearButton=!0,n()},y=()=>{t.showUserSearchClearButton=!0,n()},$=()=>{s.user="",t.showSearchClearButton=!1,n()},C=()=>{s.postSearch="",t.showPostSearchClearButton=!1,n()},B=()=>{s.date="",n()},V=e=>{t.activePage=e,t.searching=!0},P=e=>{s.limit=e,s.user="",s.postSearch="",s.date="",n()};return(e,l)=>(h(),v(N,{title:e.$t("postViewHistory")},{header:d(()=>[c("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:f(e.$t("postViewHistory"))},null,8,A)]),default:d(()=>[c("div",K,[c("div",R,[c("div",q,[o(p,{for:"user-search",value:e.$t("searchByViewedUser")},null,8,["value"]),o(g,{id:"user-search",class:"mt-1 block w-full",modelValue:r(s).user,"onUpdate:modelValue":l[0]||(l[0]=i=>r(s).user=i),placeholder:e.$t("searchByViewedUserPlaceholder"),disabled:t.busy,"show-clear-button":t.showUserSearchClearButton,onInput:l[1]||(l[1]=i=>t.showUserSearchClearButton=!1),onClearSearch:$,onEnter:y},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),c("div",F,[o(p,{for:"user-search",value:e.$t("post")},null,8,["value"]),o(g,{id:"user-search",class:"mt-1 block w-full",modelValue:r(s).postSearch,"onUpdate:modelValue":l[2]||(l[2]=i=>r(s).postSearch=i),placeholder:e.$t("postSearch"),disabled:t.busy,"show-clear-button":t.showPostSearchClearButton,onInput:l[3]||(l[3]=i=>t.showPostSearchClearButton=!1),onClearSearch:C,onEnter:S},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),c("div",G,[o(p,{for:"user-search",value:e.$t("viewedDate")},null,8,["value"]),c("div",J,[o(r(E),{modelValue:r(s).date,"onUpdate:modelValue":l[4]||(l[4]=i=>r(s).date=i),size:"small",disabled:t.busy,placeholder:e.$t("viewedDate"),"hide-on-date-time-select":!0,"max-date":new Date,onDateSelect:n},null,8,["modelValue","disabled","placeholder","max-date"]),r(s).date?(h(),z("i",{key:0,class:"absolute pi pi-times input-clear-icon text-gray-400 hover:text-red-400 hover:cursor-pointer",onClick:B})):w("",!0)])])]),o(O,{loading:t.busy},{default:d(()=>[o(r(H),{value:a.logs.data},{empty:d(()=>[b(f(e.$t(a.filters.user&&a.filters.user!==""||a.filters.post&&a.filters.post!==""||a.filters.date&&a.filters.date!==""?"emptyResult":"emptyData")),1)]),default:d(()=>[o(r(m),{class:"number-column",field:"id",header:e.$t("ID")},null,8,["header"]),o(r(m),{class:"number-column",field:"post_id",header:e.$t("postID")},null,8,["header"]),o(r(m),{class:"title-flex-column",field:"content",header:e.$t("postContent")},null,8,["header"]),o(r(m),{class:"count-column",field:"answer_count",header:e.$t("answerCount")},null,8,["header"]),o(r(m),{class:"number-column",field:"user_id",header:e.$t("viewedByID")},null,8,["header"]),o(r(m),{class:"history-username-column",field:"username",header:e.$t("viewedBy")},null,8,["header"]),o(r(m),{class:"time-column",header:e.$t("viewedAt")},{body:d(({data:i})=>[b(f(r(D)(i.viewed_at)),1)]),_:1},8,["header"]),o(r(m),{class:"px-5"})]),_:1},8,["value"])]),_:1},8,["loading"]),a.logs.data.length>0?(h(),v(j,{key:0,class:"mt-5 flex items-center justify-center mx-auto",links:a.logs.links,active:t.activePage,disabled:t.busy,limit:a.logs.per_page,total:a.logs.total,from:a.logs.from,to:a.logs.to,onProgress:V,onChangeLimit:P},null,8,["links","active","disabled","limit","total","from","to"])):w("",!0)])]),_:1},8,["title"]))}};export{Ze as default};
