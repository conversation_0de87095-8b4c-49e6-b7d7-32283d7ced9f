import{Q as M,T,i as _,N as V}from"./@inertiajs-Dt0-hqjZ.js";import{s as L,a as u}from"./primevue-CrCPcMFN.js";import{_ as j}from"./AppLayout-CTb2MMqd.js";import{_ as y}from"./InputLabel-BTXevqr4.js";import{_ as I}from"./SearchInput-CdoSYJL3.js";import{_ as N}from"./Pagination-D56Hn3as.js";import{_ as P}from"./LoadingIcon-CLD0VpVl.js";import{_ as D,a as O}from"./SecondaryButton-BoI1NwE9.js";import{_ as H}from"./RedButton-D21iPtqa.js";import{_ as Q}from"./FixedSelectionBox-Bk5LSyGJ.js";import{c as E}from"./index-k9QJQake.js";import{s as h}from"./ziggy-js-C7EU8ifa.js";import{_ as F}from"./GridContainer-BC3u-41x.js";import{i as R,v as U,b as z,c as A,o as p,l as s,k as g,K as b,S as i,a,a4 as l,R as K,O as G,P as c,F as J}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./PrimaryButton-DE9sqoJj.js";/* empty css                                                    */import"./SelectionBox-D4JR3fGi.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-CyTLADhX.js";import"./TextInput-DUNPEFms.js";import"./@headlessui-gOb5_P77.js";import"./moment-C5S46NFB.js";/* empty css                                                                     */import"./app-65VXU7yX.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./pinia-Ddsh4R0D.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./izitoast-CYQMso0-.js";const W={class:"flex-1 flex items-center"},X=["textContent"],Y={class:"p-6 sm:mx-2"},Z={class:"flex items-stretch"},tt={class:"mt-0 w-80 mr-8"},et={class:"mt-0 w-64 mr-8"},st=["innerHTML"],ot=["innerHTML"],lt=["src"],at=["onClick"],rt=["textContent"],it=["textContent"],nt={class:"flex items-center justify-end px-3 py-3"},mt=["textContent"],be={__name:"Index",props:{filters:Object,quests:Object},setup(r){const C=R("$toast"),$=M(),f=r,n=T({search:f.filters.search??"",status:f.filters.status??"",limit:f.filters.limit??10}),t=U({showSearchClearButton:(f.filters.search??"").trim().length>0,searching:!1,showModal:!1,open:!1,toggleId:null,toggling:!1,selectOptions:[{value:"enable",label:"アクティブ"},{value:"disable",label:"削除"}],activePage:null,busy:z(()=>t.activePage!==null||t.searching||n.processing)}),d=()=>{if(n.processing)return!1;n.transform(e=>E(e)).get(h("quest.list"),{preserveScroll:!0,onSuccess:()=>{}})},x=()=>{t.showSearchClearButton=!0,d()},w=()=>{n.search="",t.showSearchClearButton=!1,d()},q=e=>{t.toggleId=e,t.showModal=!0,setTimeout(()=>t.open=!0,150)},v=async()=>{t.open=!1,setTimeout(()=>t.showModal=!1,150)},k=()=>{t.toggling||V.post(h("quest.toggle"),{id:t.toggleId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{t.toggling=!0},onSuccess:()=>{t.toggleId=null,$.props.jetstream.flash.message&&C.success($.props.jetstream.flash.message),v()},onFinish:()=>{t.toggling=!1}})},S=e=>{t.activePage=e,t.searching=!0},B=e=>{n.limit=e,n.search="",n.status="",d()};return(e,m)=>(p(),A(J,null,[s(j,{title:e.$t("questList")},{header:i(()=>[a("div",W,[a("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:c(e.$t("questList"))},null,8,X),s(l(_),{class:"primary-button text-sm",href:l(h)("quest.form",{quest:""}),textContent:c(e.$t("addNew"))},null,8,["href","textContent"])])]),default:i(()=>[a("div",Y,[a("div",Z,[a("div",tt,[s(y,{for:"search",value:e.$t("search")},null,8,["value"]),s(I,{id:"search",class:"block w-full",modelValue:l(n).search,"onUpdate:modelValue":m[0]||(m[0]=o=>l(n).search=o),placeholder:e.$t("questSearch"),disabled:t.busy,"show-clear-button":t.showSearchClearButton,onInput:m[1]||(m[1]=o=>t.showSearchClearButton=!1),onClearSearch:w,onEnter:x},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),a("div",et,[s(y,{for:"status-search",value:e.$t("status")},null,8,["value"]),s(Q,{modelValue:l(n).status,"onUpdate:modelValue":m[2]||(m[2]=o=>l(n).status=o),placeholder:e.$t("all"),disabled:t.busy,options:t.selectOptions,clearable:!0,onCleared:d,onSelected:d},null,8,["modelValue","placeholder","disabled","options"])])]),s(F,{loading:t.busy},{default:i(()=>[s(l(L),{value:r.quests.data},{empty:i(()=>[G(c(e.$t(r.filters.search&&r.filters.search!==""||r.filters.status&&r.filters.status!==""?"emptyResult":"emptyData")),1)]),default:i(()=>[s(l(u),{class:"number-column small",field:"id",header:e.$t("ID")},null,8,["header"]),s(l(u),{class:"title-flex-column",field:"title",header:e.$t("title")},null,8,["header"]),s(l(u),{class:"title-flex-column",header:e.$t("questDescription")},{body:i(({data:o})=>[a("div",{innerHTML:o.description.replace(/(\r\n|\n|\r)/g,"<br />")},null,8,st)]),_:1},8,["header"]),s(l(u),{class:"status-column",header:e.$t("questAmount")},{body:i(({data:o})=>[a("div",{innerHTML:o.amount_label},null,8,ot)]),_:1},8,["header"]),s(l(u),{class:"status-column",header:e.$t("image")},{body:i(({data:o})=>[a("img",{class:"max-w-full max-h-full",src:o.image},null,8,lt)]),_:1},8,["header"]),s(l(u),{class:"status-column",field:"status_label",header:e.$t("status")},null,8,["header"]),s(l(u),{class:"action-column small"},{body:i(({data:o})=>[o.status!==0?(p(),g(l(_),{key:0,href:l(h)("quest.form",{quest:o.id})},{default:i(()=>m[3]||(m[3]=[a("i",{class:"pi pi-pen-to-square text-blue-400 hover:text-blue-600"},null,-1)])),_:2},1032,["href"])):b("",!0),a("i",{class:K(["pi hover:cursor-pointer",{"pi-eye text-sky-400 hover:text-sky-600":o.status,"pi-eye-slash text-red-400 hover:text-red-600":!o.status}]),onClick:ut=>q(o.id)},null,10,at)]),_:1})]),_:1},8,["value"])]),_:1},8,["loading"]),r.quests.data.length>0?(p(),g(N,{key:0,class:"mt-5 flex items-center justify-center mx-auto",links:r.quests.links,active:t.activePage,disabled:t.busy,limit:r.quests.per_page,total:r.quests.total,from:r.quests.from,to:r.quests.to,onProgress:S,onChangeLimit:B},null,8,["links","active","disabled","limit","total","from","to"])):b("",!0)])]),_:1},8,["title"]),t.showModal?(p(),g(O,{key:0,show:t.open,onClose:v},{default:i(()=>[a("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:c(e.$t("confirm"))},null,8,rt),a("div",{class:"border-t border-b p-4",textContent:c(e.$t("confirmToggleQuestMessage"))},null,8,it),a("div",nt,[s(D,{class:"mr-3 text-sm",textContent:c(e.$t("cancel")),onClick:v},null,8,["textContent"]),s(H,{class:"text-sm overflow-hidden h-[34px]",onClick:k},{default:i(()=>[t.toggling?(p(),g(P,{key:0,class:"mr-1"})):b("",!0),a("span",{class:"text-sm text-white",textContent:c(e.$t("confirm"))},null,8,mt)]),_:1})])]),_:1},8,["show"])):b("",!0)],64))}};export{be as default};
