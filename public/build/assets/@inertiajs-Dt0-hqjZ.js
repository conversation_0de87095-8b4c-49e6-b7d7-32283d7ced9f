import{a as J}from"./axios-t--hEgTQ.js";import{o as ae}from"./deepmerge-CxfS31y9.js";import{l as z}from"./qs-puzarlXf.js";import{u as E}from"./nprogress-CVH3SeWI.js";import{r as q,s as le,d as D,m as ce,q as K,h as j,v as G,j as pe,b as T}from"./@vue-BnW70ngI.js";import{a as S}from"./lodash.clonedeep-DcBkkazC.js";import{R as de}from"./lodash.isequal-SGFeuw-r.js";function ee(e,t){let r;return function(...i){clearTimeout(r),r=setTimeout(()=>e.apply(this,i),t)}}function k(e,t){return document.dispatchEvent(new CustomEvent(`inertia:${e}`,t))}var he=e=>k("before",{cancelable:!0,detail:{visit:e}}),ue=e=>k("error",{detail:{errors:e}}),me=e=>k("exception",{cancelable:!0,detail:{exception:e}}),Q=e=>k("finish",{detail:{visit:e}}),fe=e=>k("invalid",{cancelable:!0,detail:{response:e}}),L=e=>k("navigate",{detail:{page:e}}),ge=e=>k("progress",{detail:{progress:e}}),ye=e=>k("start",{detail:{visit:e}}),ve=e=>k("success",{detail:{page:e}});function B(e){return e instanceof File||e instanceof Blob||e instanceof FileList&&e.length>0||e instanceof FormData&&Array.from(e.values()).some(t=>B(t))||typeof e=="object"&&e!==null&&Object.values(e).some(t=>B(t))}function te(e,t=new FormData,r=null){e=e||{};for(let i in e)Object.prototype.hasOwnProperty.call(e,i)&&ie(t,re(r,i),e[i]);return t}function re(e,t){return e?e+"["+t+"]":t}function ie(e,t,r){if(Array.isArray(r))return Array.from(r.keys()).forEach(i=>ie(e,re(t,i.toString()),r[i]));if(r instanceof Date)return e.append(t,r.toISOString());if(r instanceof File)return e.append(t,r,r.name);if(r instanceof Blob)return e.append(t,r);if(typeof r=="boolean")return e.append(t,r?"1":"0");if(typeof r=="string")return e.append(t,r);if(typeof r=="number")return e.append(t,`${r}`);if(r==null)return e.append(t,"");te(r,e,t)}var be={modal:null,listener:null,show(e){typeof e=="object"&&(e=`All Inertia requests must receive a valid Inertia response, however a plain JSON response was received.<hr>${JSON.stringify(e)}`);let t=document.createElement("html");t.innerHTML=e,t.querySelectorAll("a").forEach(i=>i.setAttribute("target","_top")),this.modal=document.createElement("div"),this.modal.style.position="fixed",this.modal.style.width="100vw",this.modal.style.height="100vh",this.modal.style.padding="50px",this.modal.style.boxSizing="border-box",this.modal.style.backgroundColor="rgba(0, 0, 0, .6)",this.modal.style.zIndex=2e5,this.modal.addEventListener("click",()=>this.hide());let r=document.createElement("iframe");if(r.style.backgroundColor="white",r.style.borderRadius="5px",r.style.width="100%",r.style.height="100%",this.modal.appendChild(r),document.body.prepend(this.modal),document.body.style.overflow="hidden",!r.contentWindow)throw new Error("iframe not yet ready.");r.contentWindow.document.open(),r.contentWindow.document.write(t.outerHTML),r.contentWindow.document.close(),this.listener=this.hideOnEscape.bind(this),document.addEventListener("keydown",this.listener)},hide(){this.modal.outerHTML="",this.modal=null,document.body.style.overflow="visible",document.removeEventListener("keydown",this.listener)},hideOnEscape(e){e.keyCode===27&&this.hide()}};function $(e){return new URL(e.toString(),window.location.toString())}function se(e,t,r,i="brackets"){let n=/^https?:\/\//.test(t.toString()),o=n||t.toString().startsWith("/"),l=!o&&!t.toString().startsWith("#")&&!t.toString().startsWith("?"),f=t.toString().includes("?")||e==="get"&&Object.keys(r).length,y=t.toString().includes("#"),h=new URL(t.toString(),"http://localhost");return e==="get"&&Object.keys(r).length&&(h.search=z.stringify(ae(z.parse(h.search,{ignoreQueryPrefix:!0}),r),{encodeValuesOnly:!0,arrayFormat:i}),r={}),[[n?`${h.protocol}//${h.host}`:"",o?h.pathname:"",l?h.pathname.substring(1):"",f?h.search:"",y?h.hash:""].join(""),r]}function V(e){return e=new URL(e.href),e.hash="",e}var N=typeof window>"u",Y=!N&&/CriOS/.test(window.navigator.userAgent),Z=e=>{requestAnimationFrame(()=>{requestAnimationFrame(e)})},we=class{constructor(){this.visitId=null}init({initialPage:t,resolveComponent:r,swapComponent:i}){this.page=t,this.resolveComponent=r,this.swapComponent=i,this.setNavigationType(),this.clearRememberedStateOnReload(),this.isBackForwardVisit()?this.handleBackForwardVisit(this.page):this.isLocationVisit()?this.handleLocationVisit(this.page):this.handleInitialPageVisit(this.page),this.setupEventListeners()}setNavigationType(){this.navigationType=window.performance&&window.performance.getEntriesByType&&window.performance.getEntriesByType("navigation").length>0?window.performance.getEntriesByType("navigation")[0].type:"navigate"}clearRememberedStateOnReload(){var t;this.navigationType==="reload"&&((t=window.history.state)!=null&&t.rememberedState)&&delete window.history.state.rememberedState}handleInitialPageVisit(t){let r=window.location.hash;this.page.url.includes(r)||(this.page.url+=r),this.setPage(t,{preserveScroll:!0,preserveState:!0}).then(()=>L(t))}setupEventListeners(){window.addEventListener("popstate",this.handlePopstateEvent.bind(this)),document.addEventListener("scroll",ee(this.handleScrollEvent.bind(this),100),!0)}scrollRegions(){return document.querySelectorAll("[scroll-region]")}handleScrollEvent(t){typeof t.target.hasAttribute=="function"&&t.target.hasAttribute("scroll-region")&&this.saveScrollPositions()}saveScrollPositions(){this.replaceState({...this.page,scrollRegions:Array.from(this.scrollRegions()).map(t=>({top:t.scrollTop,left:t.scrollLeft}))})}resetScrollPositions(){Z(()=>{var t;window.scrollTo(0,0),this.scrollRegions().forEach(r=>{typeof r.scrollTo=="function"?r.scrollTo(0,0):(r.scrollTop=0,r.scrollLeft=0)}),this.saveScrollPositions(),window.location.hash&&((t=document.getElementById(window.location.hash.slice(1)))==null||t.scrollIntoView())})}restoreScrollPositions(){Z(()=>{this.page.scrollRegions&&this.scrollRegions().forEach((t,r)=>{let i=this.page.scrollRegions[r];if(i)typeof t.scrollTo=="function"?t.scrollTo(i.left,i.top):(t.scrollTop=i.top,t.scrollLeft=i.left);else return})})}isBackForwardVisit(){return window.history.state&&this.navigationType==="back_forward"}handleBackForwardVisit(t){window.history.state.version=t.version,this.setPage(window.history.state,{preserveScroll:!0,preserveState:!0}).then(()=>{this.restoreScrollPositions(),L(t)})}locationVisit(t,r){try{let i={preserveScroll:r};window.sessionStorage.setItem("inertiaLocationVisit",JSON.stringify(i)),window.location.href=t.href,V(window.location).href===V(t).href&&window.location.reload()}catch{return!1}}isLocationVisit(){try{return window.sessionStorage.getItem("inertiaLocationVisit")!==null}catch{return!1}}handleLocationVisit(t){var i,n;let r=JSON.parse(window.sessionStorage.getItem("inertiaLocationVisit")||"");window.sessionStorage.removeItem("inertiaLocationVisit"),t.url+=window.location.hash,t.rememberedState=((i=window.history.state)==null?void 0:i.rememberedState)??{},t.scrollRegions=((n=window.history.state)==null?void 0:n.scrollRegions)??[],this.setPage(t,{preserveScroll:r.preserveScroll,preserveState:!0}).then(()=>{r.preserveScroll&&this.restoreScrollPositions(),L(t)})}isLocationVisitResponse(t){return!!(t&&t.status===409&&t.headers["x-inertia-location"])}isInertiaResponse(t){return!!(t!=null&&t.headers["x-inertia"])}createVisitId(){return this.visitId={},this.visitId}cancelVisit(t,{cancelled:r=!1,interrupted:i=!1}){t&&!t.completed&&!t.cancelled&&!t.interrupted&&(t.cancelToken.abort(),t.onCancel(),t.completed=!1,t.cancelled=r,t.interrupted=i,Q(t),t.onFinish(t))}finishVisit(t){!t.cancelled&&!t.interrupted&&(t.completed=!0,t.cancelled=!1,t.interrupted=!1,Q(t),t.onFinish(t))}resolvePreserveOption(t,r){return typeof t=="function"?t(r):t==="errors"?Object.keys(r.props.errors||{}).length>0:t}cancel(){this.activeVisit&&this.cancelVisit(this.activeVisit,{cancelled:!0})}visit(t,{method:r="get",data:i={},replace:n=!1,preserveScroll:o=!1,preserveState:l=!1,only:f=[],except:y=[],headers:h={},errorBag:s="",forceFormData:a=!1,onCancelToken:c=()=>{},onBefore:d=()=>{},onStart:g=()=>{},onProgress:u=()=>{},onFinish:C=()=>{},onCancel:oe=()=>{},onSuccess:_=()=>{},onError:H=()=>{},queryStringArrayFormat:A="brackets"}={}){let P=typeof t=="string"?$(t):t;if((B(i)||a)&&!(i instanceof FormData)&&(i=te(i)),!(i instanceof FormData)){let[p,m]=se(r,P,i,A);P=$(p),i=m}let O={url:P,method:r,data:i,replace:n,preserveScroll:o,preserveState:l,only:f,except:y,headers:h,errorBag:s,forceFormData:a,queryStringArrayFormat:A,cancelled:!1,completed:!1,interrupted:!1};if(d(O)===!1||!he(O))return;this.activeVisit&&this.cancelVisit(this.activeVisit,{interrupted:!0}),this.saveScrollPositions();let U=this.createVisitId();this.activeVisit={...O,onCancelToken:c,onBefore:d,onStart:g,onProgress:u,onFinish:C,onCancel:oe,onSuccess:_,onError:H,queryStringArrayFormat:A,cancelToken:new AbortController},c({cancel:()=>{this.activeVisit&&this.cancelVisit(this.activeVisit,{cancelled:!0})}}),ye(O),g(O);let W=!!(f.length||y.length);J({method:r,url:V(P).href,data:r==="get"?{}:i,params:r==="get"?i:{},signal:this.activeVisit.cancelToken.signal,headers:{...h,Accept:"text/html, application/xhtml+xml","X-Requested-With":"XMLHttpRequest","X-Inertia":!0,...W?{"X-Inertia-Partial-Component":this.page.component}:{},...f.length?{"X-Inertia-Partial-Data":f.join(",")}:{},...y.length?{"X-Inertia-Partial-Except":y.join(",")}:{},...s&&s.length?{"X-Inertia-Error-Bag":s}:{},...this.page.version?{"X-Inertia-Version":this.page.version}:{}},onUploadProgress:p=>{i instanceof FormData&&(p.percentage=p.progress?Math.round(p.progress*100):0,ge(p),u(p))}}).then(p=>{var X;if(!this.isInertiaResponse(p))return Promise.reject({response:p});let m=p.data;W&&m.component===this.page.component&&(m.props={...this.page.props,...m.props}),o=this.resolvePreserveOption(o,m),l=this.resolvePreserveOption(l,m),l&&((X=window.history.state)!=null&&X.rememberedState)&&m.component===this.page.component&&(m.rememberedState=window.history.state.rememberedState);let x=P,F=$(m.url);return x.hash&&!F.hash&&V(x).href===F.href&&(F.hash=x.hash,m.url=F.href),this.setPage(m,{visitId:U,replace:n,preserveScroll:o,preserveState:l})}).then(()=>{let p=this.page.props.errors||{};if(Object.keys(p).length>0){let m=s?p[s]?p[s]:{}:p;return ue(m),H(m)}return ve(this.page),_(this.page)}).catch(p=>{if(this.isInertiaResponse(p.response))return this.setPage(p.response.data,{visitId:U});if(this.isLocationVisitResponse(p.response)){let m=$(p.response.headers["x-inertia-location"]),x=P;x.hash&&!m.hash&&V(x).href===m.href&&(m.hash=x.hash),this.locationVisit(m,o===!0)}else if(p.response)fe(p.response)&&be.show(p.response.data);else return Promise.reject(p)}).then(()=>{this.activeVisit&&this.finishVisit(this.activeVisit)}).catch(p=>{if(!J.isCancel(p)){let m=me(p);if(this.activeVisit&&this.finishVisit(this.activeVisit),m)return Promise.reject(p)}})}setPage(t,{visitId:r=this.createVisitId(),replace:i=!1,preserveScroll:n=!1,preserveState:o=!1}={}){return Promise.resolve(this.resolveComponent(t.component)).then(l=>{r===this.visitId&&(t.scrollRegions=this.page.scrollRegions||[],t.rememberedState=t.rememberedState||{},i=i||$(t.url).href===window.location.href,i?this.replaceState(t):this.pushState(t),this.swapComponent({component:l,page:t,preserveState:o}).then(()=>{n?this.restoreScrollPositions():this.resetScrollPositions(),i||L(t)}))})}pushState(t){this.page=t,Y?setTimeout(()=>window.history.pushState(t,"",t.url)):window.history.pushState(t,"",t.url)}replaceState(t){this.page=t,Y?setTimeout(()=>window.history.replaceState(t,"",t.url)):window.history.replaceState(t,"",t.url)}handlePopstateEvent(t){if(t.state!==null){let r=t.state,i=this.createVisitId();Promise.resolve(this.resolveComponent(r.component)).then(n=>{i===this.visitId&&(this.page=r,this.swapComponent({component:n,page:r,preserveState:!1}).then(()=>{this.restoreScrollPositions(),L(r)}))})}else{let r=$(this.page.url);r.hash=window.location.hash,this.replaceState({...this.page,url:r.href}),this.resetScrollPositions()}}get(t,r={},i={}){return this.visit(t,{...i,method:"get",data:r})}reload(t={}){return this.visit(window.location.href,{...t,preserveScroll:!0,preserveState:!0})}replace(t,r={}){return console.warn(`Inertia.replace() has been deprecated and will be removed in a future release. Please use Inertia.${r.method??"get"}() instead.`),this.visit(t,{preserveState:!0,...r,replace:!0})}post(t,r={},i={}){return this.visit(t,{preserveState:!0,...i,method:"post",data:r})}put(t,r={},i={}){return this.visit(t,{preserveState:!0,...i,method:"put",data:r})}patch(t,r={},i={}){return this.visit(t,{preserveState:!0,...i,method:"patch",data:r})}delete(t,r={}){return this.visit(t,{preserveState:!0,...r,method:"delete"})}remember(t,r="default"){var i;N||this.replaceState({...this.page,rememberedState:{...(i=this.page)==null?void 0:i.rememberedState,[r]:t}})}restore(t="default"){var r,i;if(!N)return(i=(r=window.history.state)==null?void 0:r.rememberedState)==null?void 0:i[t]}on(t,r){if(N)return()=>{};let i=n=>{let o=r(n);n.cancelable&&!n.defaultPrevented&&o===!1&&n.preventDefault()};return document.addEventListener(`inertia:${t}`,i),()=>document.removeEventListener(`inertia:${t}`,i)}},Se={buildDOMElement(e){let t=document.createElement("template");t.innerHTML=e;let r=t.content.firstChild;if(!e.startsWith("<script "))return r;let i=document.createElement("script");return i.innerHTML=r.innerHTML,r.getAttributeNames().forEach(n=>{i.setAttribute(n,r.getAttribute(n)||"")}),i},isInertiaManagedElement(e){return e.nodeType===Node.ELEMENT_NODE&&e.getAttribute("inertia")!==null},findMatchingElementIndex(e,t){let r=e.getAttribute("inertia");return r!==null?t.findIndex(i=>i.getAttribute("inertia")===r):-1},update:ee(function(e){let t=e.map(r=>this.buildDOMElement(r));Array.from(document.head.childNodes).filter(r=>this.isInertiaManagedElement(r)).forEach(r=>{var o,l;let i=this.findMatchingElementIndex(r,t);if(i===-1){(o=r==null?void 0:r.parentNode)==null||o.removeChild(r);return}let n=t.splice(i,1)[0];n&&!r.isEqualNode(n)&&((l=r==null?void 0:r.parentNode)==null||l.replaceChild(n,r))}),t.forEach(r=>document.head.appendChild(r))},1)};function Ee(e,t,r){let i={},n=0;function o(){let s=n+=1;return i[s]=[],s.toString()}function l(s){s===null||Object.keys(i).indexOf(s)===-1||(delete i[s],h())}function f(s,a=[]){s!==null&&Object.keys(i).indexOf(s)>-1&&(i[s]=a),h()}function y(){let s=t(""),a={...s?{title:`<title inertia="">${s}</title>`}:{}},c=Object.values(i).reduce((d,g)=>d.concat(g),[]).reduce((d,g)=>{if(g.indexOf("<")===-1)return d;if(g.indexOf("<title ")===0){let C=g.match(/(<title [^>]+>)(.*?)(<\/title>)/);return d.title=C?`${C[1]}${t(C[2])}${C[3]}`:g,d}let u=g.match(/ inertia="[^"]+"/);return u?d[u[0]]=g:d[Object.keys(d).length]=g,d},a);return Object.values(c)}function h(){e?r(y()):Se.update(y())}return h(),{forceUpdate:h,createProvider:function(){let s=o();return{update:a=>f(s,a),disconnect:()=>l(s)}}}}var ne=null;function ke(e){document.addEventListener("inertia:start",Ce.bind(null,e)),document.addEventListener("inertia:progress",xe),document.addEventListener("inertia:finish",Pe)}function Ce(e){ne=setTimeout(()=>E.start(),e)}function xe(e){var t;E.isStarted()&&((t=e.detail.progress)!=null&&t.percentage)&&E.set(Math.max(E.status,e.detail.progress.percentage/100*.9))}function Pe(e){if(clearTimeout(ne),E.isStarted())e.detail.visit.completed?E.done():e.detail.visit.interrupted?E.set(0):e.detail.visit.cancelled&&(E.done(),E.remove());else return}function Te(e){let t=document.createElement("style");t.type="text/css",t.textContent=`
    #nprogress {
      pointer-events: none;
    }

    #nprogress .bar {
      background: ${e};

      position: fixed;
      z-index: 1031;
      top: 0;
      left: 0;

      width: 100%;
      height: 2px;
    }

    #nprogress .peg {
      display: block;
      position: absolute;
      right: 0px;
      width: 100px;
      height: 100%;
      box-shadow: 0 0 10px ${e}, 0 0 5px ${e};
      opacity: 1.0;

      -webkit-transform: rotate(3deg) translate(0px, -4px);
          -ms-transform: rotate(3deg) translate(0px, -4px);
              transform: rotate(3deg) translate(0px, -4px);
    }

    #nprogress .spinner {
      display: block;
      position: fixed;
      z-index: 1031;
      top: 15px;
      right: 15px;
    }

    #nprogress .spinner-icon {
      width: 18px;
      height: 18px;
      box-sizing: border-box;

      border: solid 2px transparent;
      border-top-color: ${e};
      border-left-color: ${e};
      border-radius: 50%;

      -webkit-animation: nprogress-spinner 400ms linear infinite;
              animation: nprogress-spinner 400ms linear infinite;
    }

    .nprogress-custom-parent {
      overflow: hidden;
      position: relative;
    }

    .nprogress-custom-parent #nprogress .spinner,
    .nprogress-custom-parent #nprogress .bar {
      position: absolute;
    }

    @-webkit-keyframes nprogress-spinner {
      0%   { -webkit-transform: rotate(0deg); }
      100% { -webkit-transform: rotate(360deg); }
    }
    @keyframes nprogress-spinner {
      0%   { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `,document.head.appendChild(t)}function $e({delay:e=250,color:t="#29d",includeCSS:r=!0,showSpinner:i=!1}={}){ke(e),E.configure({showSpinner:i}),r&&Te(t)}function Oe(e){let t=e.currentTarget.tagName.toLowerCase()==="a";return!(e.target&&(e==null?void 0:e.target).isContentEditable||e.defaultPrevented||t&&e.altKey||t&&e.ctrlKey||t&&e.metaKey||t&&e.shiftKey||t&&"button"in e&&e.button!==0)}var w=new we,Le={created(){if(!this.$options.remember)return;Array.isArray(this.$options.remember)&&(this.$options.remember={data:this.$options.remember}),typeof this.$options.remember=="string"&&(this.$options.remember={data:[this.$options.remember]}),typeof this.$options.remember.data=="string"&&(this.$options.remember={data:[this.$options.remember.data]});let e=this.$options.remember.key instanceof Function?this.$options.remember.key.call(this):this.$options.remember.key,t=w.restore(e),r=this.$options.remember.data.filter(n=>!(this[n]!==null&&typeof this[n]=="object"&&this[n].__rememberable===!1)),i=n=>this[n]!==null&&typeof this[n]=="object"&&typeof this[n].__remember=="function"&&typeof this[n].__restore=="function";r.forEach(n=>{this[n]!==void 0&&t!==void 0&&t[n]!==void 0&&(i(n)?this[n].__restore(t[n]):this[n]=t[n]),this.$watch(n,()=>{w.remember(r.reduce((o,l)=>({...o,[l]:S(i(l)?this[l].__remember():this[l])}),{}),e)},{immediate:!0,deep:!0})})}},Ve=Le;function je(e,t){let r=typeof e=="string"?e:null,i=typeof e=="string"?t:e,n=r?w.restore(r):null,o=typeof i=="object"?S(i):S(i()),l=null,f=null,y=s=>s,h=G({...n?n.data:S(o),isDirty:!1,errors:n?n.errors:{},hasErrors:!1,processing:!1,progress:null,wasSuccessful:!1,recentlySuccessful:!1,data(){return Object.keys(o).reduce((s,a)=>(s[a]=this[a],s),{})},transform(s){return y=s,this},defaults(s,a){if(typeof i=="function")throw new Error("You cannot call `defaults()` when using a function to define your form data.");return typeof s>"u"?(o=this.data(),this.isDirty=!1):o=Object.assign({},S(o),typeof s=="string"?{[s]:a}:s),this},reset(...s){let a=typeof i=="object"?S(o):S(i()),c=S(a);return s.length===0?(o=c,Object.assign(this,a)):Object.keys(a).filter(d=>s.includes(d)).forEach(d=>{o[d]=c[d],this[d]=a[d]}),this},setError(s,a){return Object.assign(this.errors,typeof s=="string"?{[s]:a}:s),this.hasErrors=Object.keys(this.errors).length>0,this},clearErrors(...s){return this.errors=Object.keys(this.errors).reduce((a,c)=>({...a,...s.length>0&&!s.includes(c)?{[c]:this.errors[c]}:{}}),{}),this.hasErrors=Object.keys(this.errors).length>0,this},submit(s,a,c={}){let d=y(this.data()),g={...c,onCancelToken:u=>{if(l=u,c.onCancelToken)return c.onCancelToken(u)},onBefore:u=>{if(this.wasSuccessful=!1,this.recentlySuccessful=!1,clearTimeout(f),c.onBefore)return c.onBefore(u)},onStart:u=>{if(this.processing=!0,c.onStart)return c.onStart(u)},onProgress:u=>{if(this.progress=u,c.onProgress)return c.onProgress(u)},onSuccess:async u=>{this.processing=!1,this.progress=null,this.clearErrors(),this.wasSuccessful=!0,this.recentlySuccessful=!0,f=setTimeout(()=>this.recentlySuccessful=!1,2e3);let C=c.onSuccess?await c.onSuccess(u):null;return o=S(this.data()),this.isDirty=!1,C},onError:u=>{if(this.processing=!1,this.progress=null,this.clearErrors().setError(u),c.onError)return c.onError(u)},onCancel:()=>{if(this.processing=!1,this.progress=null,c.onCancel)return c.onCancel()},onFinish:u=>{if(this.processing=!1,this.progress=null,l=null,c.onFinish)return c.onFinish(u)}};s==="delete"?w.delete(a,{...g,data:d}):w[s](a,d,g)},get(s,a){this.submit("get",s,a)},post(s,a){this.submit("post",s,a)},put(s,a){this.submit("put",s,a)},patch(s,a){this.submit("patch",s,a)},delete(s,a){this.submit("delete",s,a)},cancel(){l&&l.cancel()},__rememberable:r===null,__remember(){return{data:this.data(),errors:this.errors}},__restore(s){Object.assign(this,s.data),this.setError(s.errors)}});return pe(h,s=>{h.isDirty=!de(h.data(),o),r&&w.remember(S(s.__remember()),r)},{immediate:!0,deep:!0}),h}var v=q(null),b=q(null),R=le(null),I=q(null),M=null,Fe=D({name:"Inertia",props:{initialPage:{type:Object,required:!0},initialComponent:{type:Object,required:!1},resolveComponent:{type:Function,required:!1},titleCallback:{type:Function,required:!1,default:e=>e},onHeadUpdate:{type:Function,required:!1,default:()=>()=>{}}},setup({initialPage:e,initialComponent:t,resolveComponent:r,titleCallback:i,onHeadUpdate:n}){v.value=t?K(t):null,b.value=e,I.value=null;let o=typeof window>"u";return M=Ee(o,i,n),o||(w.init({initialPage:e,resolveComponent:r,swapComponent:async l=>{v.value=K(l.component),b.value=l.page,I.value=l.preserveState?I.value:Date.now()}}),w.on("navigate",()=>M.forceUpdate())),()=>{if(v.value){v.value.inheritAttrs=!!v.value.inheritAttrs;let l=j(v.value,{...b.value.props,key:I.value});return R.value&&(v.value.layout=R.value,R.value=null),v.value.layout?typeof v.value.layout=="function"?v.value.layout(j,l):(Array.isArray(v.value.layout)?v.value.layout:[v.value.layout]).concat(l).reverse().reduce((f,y)=>(y.inheritAttrs=!!y.inheritAttrs,j(y,{...b.value.props},()=>f))):l}}}}),Ie=Fe,Ne={install(e){w.form=je,Object.defineProperty(e.config.globalProperties,"$inertia",{get:()=>w}),Object.defineProperty(e.config.globalProperties,"$page",{get:()=>b.value}),Object.defineProperty(e.config.globalProperties,"$headManager",{get:()=>M}),e.mixin(Ve)}};function Xe(){return G({props:T(()=>{var e;return(e=b.value)==null?void 0:e.props}),url:T(()=>{var e;return(e=b.value)==null?void 0:e.url}),component:T(()=>{var e;return(e=b.value)==null?void 0:e.component}),version:T(()=>{var e;return(e=b.value)==null?void 0:e.version}),scrollRegions:T(()=>{var e;return(e=b.value)==null?void 0:e.scrollRegions}),rememberedState:T(()=>{var e;return(e=b.value)==null?void 0:e.rememberedState})})}async function Je({id:e="app",resolve:t,setup:r,title:i,progress:n={},page:o,render:l}){let f=typeof window>"u",y=f?null:document.getElementById(e),h=o||JSON.parse(y.dataset.page),s=d=>Promise.resolve(t(d)).then(g=>g.default||g),a=[],c=await s(h.component).then(d=>r({el:y,App:Ie,props:{initialPage:h,initialComponent:d,resolveComponent:s,titleCallback:i,onHeadUpdate:f?g=>a=g:null},plugin:Ne}));if(!f&&n&&$e(n),f){let d=await l(ce({render:()=>j("div",{id:e,"data-page":JSON.stringify(h),innerHTML:c?l(c):""})}));return{head:a,body:d}}}var Ae=D({props:{title:{type:String,required:!1}},data(){return{provider:this.$headManager.createProvider()}},beforeUnmount(){this.provider.disconnect()},methods:{isUnaryTag(e){return["area","base","br","col","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"].indexOf(e.type)>-1},renderTagStart(e){e.props=e.props||{},e.props.inertia=e.props["head-key"]!==void 0?e.props["head-key"]:"";let t=Object.keys(e.props).reduce((r,i)=>{let n=e.props[i];return["key","head-key"].includes(i)?r:n===""?r+` ${i}`:r+` ${i}="${n}"`},"");return`<${e.type}${t}>`},renderTagChildren(e){return typeof e.children=="string"?e.children:e.children.reduce((t,r)=>t+this.renderTag(r),"")},isFunctionNode(e){return typeof e.type=="function"},isComponentNode(e){return typeof e.type=="object"},isCommentNode(e){return/(comment|cmt)/i.test(e.type.toString())},isFragmentNode(e){return/(fragment|fgt|symbol\(\))/i.test(e.type.toString())},isTextNode(e){return/(text|txt)/i.test(e.type.toString())},renderTag(e){if(this.isTextNode(e))return e.children;if(this.isFragmentNode(e)||this.isCommentNode(e))return"";let t=this.renderTagStart(e);return e.children&&(t+=this.renderTagChildren(e)),this.isUnaryTag(e)||(t+=`</${e.type}>`),t},addTitleElement(e){return this.title&&!e.find(t=>t.startsWith("<title"))&&e.push(`<title inertia>${this.title}</title>`),e},renderNodes(e){return this.addTitleElement(e.flatMap(t=>this.resolveNode(t)).map(t=>this.renderTag(t)).filter(t=>t))},resolveNode(e){return this.isFunctionNode(e)?this.resolveNode(e.type()):this.isComponentNode(e)?(console.warn("Using components in the <Head> component is not supported."),[]):this.isTextNode(e)&&e.children?e:this.isFragmentNode(e)&&e.children?e.children.flatMap(t=>this.resolveNode(t)):this.isCommentNode(e)?[]:e}},render(){this.provider.update(this.renderNodes(this.$slots.default?this.$slots.default():[]))}}),ze=Ae,Re=D({name:"Link",props:{as:{type:String,default:"a"},data:{type:Object,default:()=>({})},href:{type:String,required:!0},method:{type:String,default:"get"},replace:{type:Boolean,default:!1},preserveScroll:{type:Boolean,default:!1},preserveState:{type:Boolean,default:null},only:{type:Array,default:()=>[]},except:{type:Array,default:()=>[]},headers:{type:Object,default:()=>({})},queryStringArrayFormat:{type:String,default:"brackets"}},setup(e,{slots:t,attrs:r}){return()=>{let i=e.as.toLowerCase(),n=e.method.toLowerCase(),[o,l]=se(n,e.href||"",e.data,e.queryStringArrayFormat);return i==="a"&&n!=="get"&&console.warn(`Creating POST/PUT/PATCH/DELETE <a> links is discouraged as it causes "Open Link in New Tab/Window" accessibility issues.

Please specify a more appropriate element using the "as" attribute. For example:

<Link href="${o}" method="${n}" as="button">...</Link>`),j(e.as,{...r,...i==="a"?{href:o}:{},onClick:f=>{Oe(f)&&(f.preventDefault(),w.visit(o,{data:l,method:n,replace:e.replace,preserveScroll:e.preserveScroll,preserveState:e.preserveState??n!=="get",only:e.only,except:e.except,headers:e.headers,onCancelToken:r.onCancelToken||(()=>({})),onBefore:r.onBefore||(()=>({})),onStart:r.onStart||(()=>({})),onProgress:r.onProgress||(()=>({})),onFinish:r.onFinish||(()=>({})),onCancel:r.onCancel||(()=>({})),onSuccess:r.onSuccess||(()=>({})),onError:r.onError||(()=>({}))}))}},t)}}}),Ke=Re;export{w as N,Xe as Q,je as T,ze as Z,Ke as i,Je as j};
