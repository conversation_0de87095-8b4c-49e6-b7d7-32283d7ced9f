import{g as _}from"./index-DHV2tfOS.js";import{Q as B,N as v,i as b}from"./@inertiajs-BhKdJayA.js";import{t as N,p as V}from"./lodash-DBgjQQU6.js";import{d as F}from"./@element-plus-ccBf1-WH.js";import{_ as T}from"./AppLayout-m_I9gnvX.js";import{_ as z}from"./InputLabel-BTXevqr4.js";import{_ as E}from"./TextInput-C52bsWxF.js";import{_ as L}from"./Pagination-DDsmbrzN.js";import{_ as P}from"./LoadingIcon-CesYxFkK.js";import{_ as M,a as O}from"./SecondaryButton-BWHXZF7Q.js";import{_ as Q}from"./PrimaryButton-DE9sqoJj.js";import{i as R,v as A,c as l,o,l as r,k as g,K as $,S as m,a as t,a4 as u,R as h,P as e,F as f,M as x}from"./@vue-BnW70ngI.js";import"./moment-C5S46NFB.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./FixedSelectionBox-CwNS68U7.js";import"./SelectionBox-58uvzdoT.js";import"./@heroicons-BLousAGu.js";import"./@headlessui-gOb5_P77.js";const K=["textContent"],U={class:"py-6 px-6 sm:mx-2"},G={class:"flex items-center items-stretch"},H={class:"mt-0 w-80"},J={class:"w-full survey-table"},W={class:"text-left flex"},X=["textContent"],Y=["textContent"],Z=["textContent"],tt=["textContent"],et=["textContent"],st=["textContent"],nt=["textContent"],ot=["textContent"],lt=["textContent"],rt={class:"number-column"},it=["textContent"],at={class:"border-l p-0 flex-1",colspan:"7"},ct={class:"w-full h-full"},ut=["textContent"],dt=["textContent"],mt=["textContent"],ht=["textContent"],ft=["textContent"],pt={class:"p-0 border-l",colspan:"2"},_t={class:"w-full h-full"},xt=["textContent"],Ct=["textContent"],yt={key:1,class:"flex h-full"},vt={class:"border-l w-[56px]"},bt={key:1,class:"flex border-t"},gt=["textContent"],$t=["textContent"],wt=["textContent"],kt={class:"flex items-center justify-end px-3 py-3"},St=["textContent"],te={__name:"Index",props:{filters:Object,surveys:Object},setup(d){const w=R("$toast"),C=B(),n=A({searched:d.filters.search??"",searching:!1,confirmDeletion:!1,open:!1,deleteId:null,deleting:!1}),k=N(()=>v.get(route("survey.list"),V({search:n.searched}),{preserveState:!0,onBefore:()=>{n.searching=!0},onFinish:()=>{n.searching=!1}}),1e3),p=()=>{n.open=!1,setTimeout(()=>n.confirmDeletion=!1,150)},S=s=>{n.deleteId=s,n.confirmDeletion=!0,setTimeout(()=>n.open=!0,150)},D=()=>{n.deleting||v.post(route("survey.delete"),{id:n.deleteId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{n.deleting=!0},onSuccess:()=>{p(),n.deleteId=null,C.props.jetstream.flash.message&&w.success(C.props.jetstream.flash.message)},onFinish:()=>{n.deleting=!1}})};return(s,i)=>(o(),l(f,null,[r(T,{title:s.$t("listSurvey")},{header:m(()=>[t("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:e(s.$t("listSurvey"))},null,8,K),r(u(b),{class:"ml-auto flex items-center justify-center px-4 py-2 bg-sky-500 border border-transparent rounded-md font-semibold text-white uppercase transition ease-in-out duration-150 hover:bg-sky-600 focus:outline-none h-[38px]",textContent:e(s.$t("addNew")),href:s.route("survey.create")},null,8,["textContent","href"])]),default:m(()=>[t("div",U,[t("div",G,[t("div",H,[r(z,{for:"search",value:s.$t("surveySearch")},null,8,["value"]),r(E,{class:"mt-1 block w-full",id:"search",modelValue:n.searched,"onUpdate:modelValue":i[0]||(i[0]=c=>n.searched=c),placeholder:s.$t("enterSurveySearch"),disabled:n.searching,onEnter:u(k)},null,8,["modelValue","placeholder","disabled","onEnter"])])]),t("div",{class:h(["bg-white rounded-md shadow overflow-auto mt-5 flex flex-col",{"grid-loading":n.searching}])},[n.searching?(o(),g(P,{key:0,class:"grid-loading-icon",size:24,color:"rgb(55 65 81)"})):$("",!0),t("table",J,[t("tr",W,[t("th",{class:"number-column",textContent:e(s.$t("ID"))},null,8,X),t("th",{class:"survey-title-column",textContent:e(s.$t("surveyTitle"))},null,8,Y),t("th",{class:"question-id-column",textContent:e(s.$t("questionID"))},null,8,Z),t("th",{class:"title-flex-column",textContent:e(s.$t("questionContent"))},null,8,tt),t("th",{class:"question-type-column",textContent:e(s.$t("questionType"))},null,8,et),t("th",{class:"question-public-column",textContent:e(s.$t("questionPublic"))},null,8,st),t("th",{class:"question-point-column",textContent:e(s.$t("questionPoint"))},null,8,nt),t("th",{class:"answer-id-column",textContent:e(s.$t("answerID"))},null,8,ot),t("th",{class:"answer-content-column",textContent:e(s.$t("answerContent"))},null,8,lt),i[1]||(i[1]=t("th",{class:"w-[56px]"},null,-1))]),d.surveys.data.length>0?(o(!0),l(f,{key:0},x(d.surveys.data,(c,q)=>(o(),l("tr",{class:h(["flex border-t",[q%2?"bg-blue-50":""]])},[t("td",rt,[r(u(b),{class:"hover:text-sky-600 hover:underline",textContent:e(u(_)(c.survey_id,"S")),href:s.route("survey.update",{survey:c.survey_id})},null,8,["textContent","href"])]),t("td",{class:"survey-title-column",textContent:e(c.title)},null,8,it),t("td",at,[t("table",ct,[(o(!0),l(f,null,x(c.questions,(a,I)=>(o(),l("tr",{class:h(["flex h-full",[I>0?"border-t":""]])},[t("td",{class:"question-id-column",textContent:e(u(_)(a.question_id,"Q"))},null,8,ut),t("td",{class:"title-flex-column",textContent:e(a.content)},null,8,dt),t("td",{class:"question-type-column",textContent:e(a.typeLabel)},null,8,mt),t("td",{class:"question-public-column",textContent:e(a.publicLabel)},null,8,ht),t("td",{class:"question-point-column",textContent:e(a.point)},null,8,ft),t("td",pt,[t("table",_t,[a.choices.length>0?(o(!0),l(f,{key:0},x(a.choices,(y,j)=>(o(),l("tr",{class:h(["flex h-full",[j>0?"border-t":""]])},[t("td",{class:"answer-id-column",textContent:e(u(_)(y.choice_id,"A"))},null,8,xt),t("td",{class:"answer-content-column",textContent:e(y.content)},null,8,Ct)],2))),256)):(o(),l("tr",yt,i[2]||(i[2]=[t("td",{class:"answer-id-column"},null,-1),t("td",{class:"answer-content-column"},null,-1)])))])])],2))),256))])]),t("td",vt,[r(u(F),{class:"w-6 text-gray-500 transition ease-in-out duration-150 hover:cursor-pointer hover:text-red-500",onClick:a=>S(c.survey_id)},null,8,["onClick"])])],2))),256)):(o(),l("tr",bt,[t("td",{colspan:"10",textContent:e(s.$t(d.filters.search&&d.filters.search!==""?"emptyResult":"emptyData"))},null,8,gt)]))])],2),r(L,{class:"mt-5 flex items-center justify-center mx-auto",links:d.surveys.links},null,8,["links"])])]),_:1},8,["title"]),n.confirmDeletion?(o(),g(O,{key:0,show:n.open,closeable:!0,size:"lg","padding-vertical":"py-20",onClose:p},{default:m(()=>[t("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:e(s.$t("confirm"))},null,8,$t),t("div",{class:"border-t px-3 py-4 font-light",textContent:e(s.$t("surveyDeleteConfirmation"))},null,8,wt),i[3]||(i[3]=t("div",{class:"border-t"},null,-1)),t("div",kt,[r(M,{class:"mr-3 text-sm h-[38px]",textContent:e(s.$t("cancel")),onClick:p},null,8,["textContent"]),r(Q,{class:"text-sm"},{default:m(()=>[t("span",{class:"text-sm",textContent:e(s.$t("delete")),onClick:D},null,8,St)]),_:1})])]),_:1},8,["show"])):$("",!0)],64))}};export{te as default};
