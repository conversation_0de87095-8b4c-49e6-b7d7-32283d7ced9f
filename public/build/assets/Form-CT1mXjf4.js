import{Q as j,T as B}from"./@inertiajs-BhKdJayA.js";import{u as O}from"./vue-i18n-DNS8h1FH.js";import{c as F}from"./@element-plus-ccBf1-WH.js";import{_ as N}from"./AppLayout-m_I9gnvX.js";import{_ as P}from"./RedButton-D21iPtqa.js";import{_ as V}from"./LoadingIcon-CesYxFkK.js";import{_ as q}from"./InputLabel-BTXevqr4.js";import{_ as z}from"./TextInput-C52bsWxF.js";import{_ as u}from"./InputError-gQdwtcoE.js";import{_ as K}from"./SecondaryButton-BWHXZF7Q.js";import{_ as C}from"./SurveySelectionBox-PZfoWyYA.js";import{_ as D}from"./FixedSelectionBox-CwNS68U7.js";import{i as I,v as L,b as R,e as W,k as h,o as n,S as _,a as c,c as m,K as f,l as i,a4 as t,F as v,M as G,R as g,P as y}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./PrimaryButton-DE9sqoJj.js";import"./pinia-BB7AK9rL.js";import"./index-DHV2tfOS.js";import"./moment-C5S46NFB.js";import"./SelectionBox-58uvzdoT.js";import"./@heroicons-BLousAGu.js";import"./lodash-DBgjQQU6.js";import"./@headlessui-gOb5_P77.js";const H=["textContent"],J=["textContent"],X={class:"max-w-7xl mx-auto py-6 px-6"},Y={class:"bg-white rounded-md shadow px-6 py-5 flex flex-col"},Z={class:"mt-0"},ee={class:"mt-5"},se={class:"mt-5"},te={key:0,class:"mt-5"},oe={class:"flex-1 mr-4 question-answer-select"},ae={class:"flex-1 ml-4 question-answer-select"},le={class:"ml-4 px-4 w-[56px]"},Qe={__name:"Form",props:{title:String,attachedSurvey:Object},setup(p){const{t:E}=O(),T=p,b=I("$toast"),w=j(),e=B(T.attachedSurvey),r=L({loading:!1,clearData:!1,clearToSurveyData:!1,exclude:[],questions:[],questionOptions:R(()=>{const s=[];return r.questions.forEach(o=>{s.push({label:o.content,value:o.question_id})}),s})}),U=()=>{if(e.processing)return!1;e.errors={},e.transform(s=>{const o={};return o.attached_id=s.attached_id,o.title=s.title,o.survey_id=s.survey_id,o.to_survey_id=s.to_survey_id,o.choices=[],e.choices.forEach(a=>{(a.show??!1)&&o.choices.push({question_id:a.question_id,choice_id:a.choice_id})}),o}).post(route("attachedSurvey.store"),{onSuccess:()=>{e.attached_id||(e.reset(),r.clearData=!0,r.clearToSurveyData=!0),w.props.jetstream.flash.message&&b.success(w.props.jetstream.flash.message)}})},k=()=>{e.choices.forEach(s=>{s.show=!1}),e.to_survey_id="",r.clearToSurveyData=!0,S()},$=async()=>{if(r.loading)return!1;r.loading=!0,await window.axios.post(route("survey.listQuestion"),{survey_id:e.to_survey_id}).then(s=>{r.questions=s.data}).catch(()=>{b.error(E("commonErrorMessage"))}).finally(()=>{r.loading=!1})},A=s=>{const o=[],a=r.questions.find(l=>l.question_id===s);return a&&a.choices.forEach(l=>{o.push({value:l.choice_id,label:l.content})}),o},M=s=>{e.choices[s].show=!1,e.choices[s].question_id="",e.choices[s].choice_id=""},S=()=>{e.choices.push({question_id:"",choice_id:"",show:!0})},Q=(s,o)=>{},x=s=>{let o=0;for(let a=0;a<e.choices.length;a++)if(e.choices[a].show){if(a===s)break;o++}return o};return W(()=>{e.attached_id&&$()}),(s,o)=>(n(),h(N,{title:p.title},{header:_(()=>[c("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:y(p.title)},null,8,H),i(P,{class:g(["ml-auto",{"opacity-25":t(e).processing}]),disabled:t(e).processing,onClick:U},{default:_(()=>[t(e).processing?(n(),h(V,{key:0,class:"mr-2"})):f("",!0),c("span",{class:"text-sm",textContent:y(s.$t("save"))},null,8,J)]),_:1},8,["class","disabled"])]),default:_(()=>[c("div",X,[c("div",Y,[c("div",Z,[i(q,{for:"survey-title",value:s.$t("attachedSurveyTitle")},null,8,["value"]),i(z,{class:"mt-1 block w-full",id:"survey-title",modelValue:t(e).title,"onUpdate:modelValue":o[0]||(o[0]=a=>t(e).title=a),disabled:t(e).processing||r.loading,type:"text"},null,8,["modelValue","disabled"]),i(u,{message:t(e).errors.title},null,8,["message"])]),c("div",ee,[i(C,{class:"w-full",modelValue:t(e).survey_id,"onUpdate:modelValue":o[1]||(o[1]=a=>t(e).survey_id=a),label:s.$t("survey"),placeholder:s.$t("selectSurvey"),"clear-data":r.clearData,disabled:t(e).processing||r.loading,"enable-search":!0,"search-placeholder":s.$t("searchSurveyPlaceholder"),onSelected:k,onDataCleared:o[2]||(o[2]=a=>r.clearData=!1)},null,8,["modelValue","label","placeholder","clear-data","disabled","search-placeholder"]),i(u,{message:t(e).errors.survey_id},null,8,["message"])]),c("div",se,[i(C,{class:"w-full",modelValue:t(e).to_survey_id,"onUpdate:modelValue":o[3]||(o[3]=a=>t(e).to_survey_id=a),label:s.$t("surveyWasAttached"),placeholder:s.$t("selectSurvey"),"clear-data":r.clearToSurveyData,disabled:t(e).processing||r.loading||!t(e).survey_id,"enable-search":!0,"load-data":!1,exclude:[t(e).survey_id],"search-placeholder":s.$t("searchSurveyPlaceholder"),onSelected:$,onDataCleared:o[4]||(o[4]=a=>r.clearToSurveyData=!1)},null,8,["modelValue","label","placeholder","clear-data","disabled","exclude","search-placeholder"]),i(u,{message:t(e).errors.to_survey_id},null,8,["message"])]),t(e).to_survey_id?(n(),m("div",te,[r.loading?(n(),h(V,{key:0,color:"#000000",size:20})):(n(),m(v,{key:1},[i(q,{for:"survey-title",value:s.$t("answer")},null,8,["value"]),(n(!0),m(v,null,G(t(e).choices,(a,l)=>(n(),m(v,null,[a.show?(n(),m("div",{class:g(["flex w-full items-stretch",{"mt-3 pt-2 border-t border-dashed":l>0}]),key:"choice_"+l},[c("div",oe,[i(D,{modelValue:t(e).choices[l].question_id,"onUpdate:modelValue":d=>t(e).choices[l].question_id=d,placeholder:s.$t("selectQuestion"),disabled:t(e).processing,options:r.questionOptions,clearable:!1,onSelected:d=>Q(l,d)},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","options","onSelected"]),i(u,{message:t(e).errors["choices."+x(l)+".question_id"]},null,8,["message"])]),c("div",ae,[(n(),h(D,{key:"answer_choice_"+l,modelValue:t(e).choices[l].choice_id,"onUpdate:modelValue":d=>t(e).choices[l].choice_id=d,placeholder:s.$t("selectAnswer"),disabled:t(e).processing||!t(e).choices[l].question_id,options:A(t(e).choices[l].question_id),clearable:!1},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","options"])),i(u,{message:t(e).errors["choices."+x(l)+".choice_id"]},null,8,["message"])]),c("div",le,[i(t(F),{class:g(["w-5 text-gray-300 transition ease-in-out duration-150 mt-4",[t(e).choices.length>1?"hover:cursor-pointer hover:text-red-500":""]]),onClick:d=>M(l)},null,8,["class","onClick"])])],2)):f("",!0)],64))),256)),i(K,{class:"mt-3 text-sm h-[38px]",textContent:y(s.$t("addMoreAnswer")),onClick:S},null,8,["textContent"])],64))])):f("",!0)])])]),_:1},8,["title"]))}};export{Qe as default};
