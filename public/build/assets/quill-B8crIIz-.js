var zs=Object.defineProperty;var Vs=(i,t,e)=>t in i?zs(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var v=(i,t,e)=>Vs(i,typeof t!="symbol"?t+"":t,e);import{E as $,T as te,I as Ks,S as L,A as Zs,B as rs,L as _,P as Ht,R as ls,a as Gs,b as os,C as as,c as me,d as tt,e as pt}from"./parchment-DI0xVRB_.js";import{g as Ws}from"./deepmerge-4BhuujTU.js";import{l as cs}from"./lodash.clonedeep-DCKevjwB.js";import{l as us}from"./lodash.isequal-BCJU-oNO.js";import{E as Xs}from"./eventemitter3-mWFy3unY.js";import{i as pe,c as ht,m as it}from"./lodash-es-BvFElN8u.js";var ee={exports:{}},M=-1,I=1,R=0;function wt(i,t,e,s,n){if(i===t)return i?[[R,i]]:[];if(e!=null){var r=ln(i,t,e);if(r)return r}var l=be(i,t),o=i.substring(0,l);i=i.substring(l),t=t.substring(l),l=Ft(i,t);var a=i.substring(i.length-l);i=i.substring(0,i.length-l),t=t.substring(0,t.length-l);var c=Ys(i,t);return o&&c.unshift([R,o]),a&&c.push([R,a]),ye(c,n),s&&tn(c),c}function Ys(i,t){var e;if(!i)return[[I,t]];if(!t)return[[M,i]];var s=i.length>t.length?i:t,n=i.length>t.length?t:i,r=s.indexOf(n);if(r!==-1)return e=[[I,s.substring(0,r)],[R,n],[I,s.substring(r+n.length)]],i.length>t.length&&(e[0][0]=e[2][0]=M),e;if(n.length===1)return[[M,i],[I,t]];var l=Js(i,t);if(l){var o=l[0],a=l[1],c=l[2],f=l[3],m=l[4],u=wt(o,c),h=wt(a,f);return u.concat([[R,m]],h)}return Qs(i,t)}function Qs(i,t){for(var e=i.length,s=t.length,n=Math.ceil((e+s)/2),r=n,l=2*n,o=new Array(l),a=new Array(l),c=0;c<l;c++)o[c]=-1,a[c]=-1;o[r+1]=0,a[r+1]=0;for(var f=e-s,m=f%2!==0,u=0,h=0,d=0,p=0,y=0;y<n;y++){for(var b=-y+u;b<=y-h;b+=2){var E=r+b,x;b===-y||b!==y&&o[E-1]<o[E+1]?x=o[E+1]:x=o[E-1]+1;for(var A=x-b;x<e&&A<s&&i.charAt(x)===t.charAt(A);)x++,A++;if(o[E]=x,x>e)h+=2;else if(A>s)u+=2;else if(m){var N=r+f-b;if(N>=0&&N<l&&a[N]!==-1){var k=e-a[N];if(x>=k)return Ce(i,t,x,A)}}}for(var S=-y+d;S<=y-p;S+=2){var N=r+S,k;S===-y||S!==y&&a[N-1]<a[N+1]?k=a[N+1]:k=a[N-1]+1;for(var T=k-S;k<e&&T<s&&i.charAt(e-k-1)===t.charAt(s-T-1);)k++,T++;if(a[N]=k,k>e)p+=2;else if(T>s)d+=2;else if(!m){var E=r+f-S;if(E>=0&&E<l&&o[E]!==-1){var x=o[E],A=r+x-E;if(k=e-k,x>=k)return Ce(i,t,x,A)}}}}return[[M,i],[I,t]]}function Ce(i,t,e,s){var n=i.substring(0,e),r=t.substring(0,s),l=i.substring(e),o=t.substring(s),a=wt(n,r),c=wt(l,o);return a.concat(c)}function be(i,t){if(!i||!t||i.charAt(0)!==t.charAt(0))return 0;for(var e=0,s=Math.min(i.length,t.length),n=s,r=0;e<n;)i.substring(r,n)==t.substring(r,n)?(e=n,r=e):s=n,n=Math.floor((s-e)/2+e);return hs(i.charCodeAt(n-1))&&n--,n}function Oe(i,t){var e=i.length,s=t.length;if(e==0||s==0)return 0;e>s?i=i.substring(e-s):e<s&&(t=t.substring(0,e));var n=Math.min(e,s);if(i==t)return n;for(var r=0,l=1;;){var o=i.substring(n-l),a=t.indexOf(o);if(a==-1)return r;l+=a,(a==0||i.substring(n-l)==t.substring(0,l))&&(r=l,l++)}}function Ft(i,t){if(!i||!t||i.slice(-1)!==t.slice(-1))return 0;for(var e=0,s=Math.min(i.length,t.length),n=s,r=0;e<n;)i.substring(i.length-n,i.length-r)==t.substring(t.length-n,t.length-r)?(e=n,r=e):s=n,n=Math.floor((s-e)/2+e);return fs(i.charCodeAt(i.length-n))&&n--,n}function Js(i,t){var e=i.length>t.length?i:t,s=i.length>t.length?t:i;if(e.length<4||s.length*2<e.length)return null;function n(h,d,p){for(var y=h.substring(p,p+Math.floor(h.length/4)),b=-1,E="",x,A,N,k;(b=d.indexOf(y,b+1))!==-1;){var S=be(h.substring(p),d.substring(b)),T=Ft(h.substring(0,p),d.substring(0,b));E.length<T+S&&(E=d.substring(b-T,b)+d.substring(b,b+S),x=h.substring(0,p-T),A=h.substring(p+S),N=d.substring(0,b-T),k=d.substring(b+S))}return E.length*2>=h.length?[x,A,N,k,E]:null}var r=n(e,s,Math.ceil(e.length/4)),l=n(e,s,Math.ceil(e.length/2)),o;if(!r&&!l)return null;l?r?o=r[4].length>l[4].length?r:l:o=l:o=r;var a,c,f,m;i.length>t.length?(a=o[0],c=o[1],f=o[2],m=o[3]):(f=o[0],m=o[1],a=o[2],c=o[3]);var u=o[4];return[a,c,f,m,u]}function tn(i){for(var t=!1,e=[],s=0,n=null,r=0,l=0,o=0,a=0,c=0;r<i.length;)i[r][0]==R?(e[s++]=r,l=a,o=c,a=0,c=0,n=i[r][1]):(i[r][0]==I?a+=i[r][1].length:c+=i[r][1].length,n&&n.length<=Math.max(l,o)&&n.length<=Math.max(a,c)&&(i.splice(e[s-1],0,[M,n]),i[e[s-1]+1][0]=I,s--,s--,r=s>0?e[s-1]:-1,l=0,o=0,a=0,c=0,n=null,t=!0)),r++;for(t&&ye(i),nn(i),r=1;r<i.length;){if(i[r-1][0]==M&&i[r][0]==I){var f=i[r-1][1],m=i[r][1],u=Oe(f,m),h=Oe(m,f);u>=h?(u>=f.length/2||u>=m.length/2)&&(i.splice(r,0,[R,m.substring(0,u)]),i[r-1][1]=f.substring(0,f.length-u),i[r+1][1]=m.substring(u),r++):(h>=f.length/2||h>=m.length/2)&&(i.splice(r,0,[R,f.substring(0,h)]),i[r-1][0]=I,i[r-1][1]=m.substring(0,m.length-h),i[r+1][0]=M,i[r+1][1]=f.substring(h),r++),r++}r++}}var Ie=/[^a-zA-Z0-9]/,Me=/\s/,Be=/[\r\n]/,en=/\n\r?\n$/,sn=/^\r?\n\r?\n/;function nn(i){function t(h,d){if(!h||!d)return 6;var p=h.charAt(h.length-1),y=d.charAt(0),b=p.match(Ie),E=y.match(Ie),x=b&&p.match(Me),A=E&&y.match(Me),N=x&&p.match(Be),k=A&&y.match(Be),S=N&&h.match(en),T=k&&d.match(sn);return S||T?5:N||k?4:b&&!x&&A?3:x||A?2:b||E?1:0}for(var e=1;e<i.length-1;){if(i[e-1][0]==R&&i[e+1][0]==R){var s=i[e-1][1],n=i[e][1],r=i[e+1][1],l=Ft(s,n);if(l){var o=n.substring(n.length-l);s=s.substring(0,s.length-l),n=o+n.substring(0,n.length-l),r=o+r}for(var a=s,c=n,f=r,m=t(s,n)+t(n,r);n.charAt(0)===r.charAt(0);){s+=n.charAt(0),n=n.substring(1)+r.charAt(0),r=r.substring(1);var u=t(s,n)+t(n,r);u>=m&&(m=u,a=s,c=n,f=r)}i[e-1][1]!=a&&(a?i[e-1][1]=a:(i.splice(e-1,1),e--),i[e][1]=c,f?i[e+1][1]=f:(i.splice(e+1,1),e--))}e++}}function ye(i,t){i.push([R,""]);for(var e=0,s=0,n=0,r="",l="",o;e<i.length;){if(e<i.length-1&&!i[e][1]){i.splice(e,1);continue}switch(i[e][0]){case I:n++,l+=i[e][1],e++;break;case M:s++,r+=i[e][1],e++;break;case R:var a=e-n-s-1;if(t){if(a>=0&&gs(i[a][1])){var c=i[a][1].slice(-1);if(i[a][1]=i[a][1].slice(0,-1),r=c+r,l=c+l,!i[a][1]){i.splice(a,1),e--;var f=a-1;i[f]&&i[f][0]===I&&(n++,l=i[f][1]+l,f--),i[f]&&i[f][0]===M&&(s++,r=i[f][1]+r,f--),a=f}}if(ds(i[e][1])){var c=i[e][1].charAt(0);i[e][1]=i[e][1].slice(1),r+=c,l+=c}}if(e<i.length-1&&!i[e][1]){i.splice(e,1);break}if(r.length>0||l.length>0){r.length>0&&l.length>0&&(o=be(l,r),o!==0&&(a>=0?i[a][1]+=l.substring(0,o):(i.splice(0,0,[R,l.substring(0,o)]),e++),l=l.substring(o),r=r.substring(o)),o=Ft(l,r),o!==0&&(i[e][1]=l.substring(l.length-o)+i[e][1],l=l.substring(0,l.length-o),r=r.substring(0,r.length-o)));var m=n+s;r.length===0&&l.length===0?(i.splice(e-m,m),e=e-m):r.length===0?(i.splice(e-m,m,[I,l]),e=e-m+1):l.length===0?(i.splice(e-m,m,[M,r]),e=e-m+1):(i.splice(e-m,m,[M,r],[I,l]),e=e-m+2)}e!==0&&i[e-1][0]===R?(i[e-1][1]+=i[e][1],i.splice(e,1)):e++,n=0,s=0,r="",l="";break}}i[i.length-1][1]===""&&i.pop();var u=!1;for(e=1;e<i.length-1;)i[e-1][0]===R&&i[e+1][0]===R&&(i[e][1].substring(i[e][1].length-i[e-1][1].length)===i[e-1][1]?(i[e][1]=i[e-1][1]+i[e][1].substring(0,i[e][1].length-i[e-1][1].length),i[e+1][1]=i[e-1][1]+i[e+1][1],i.splice(e-1,1),u=!0):i[e][1].substring(0,i[e+1][1].length)==i[e+1][1]&&(i[e-1][1]+=i[e+1][1],i[e][1]=i[e][1].substring(i[e+1][1].length)+i[e+1][1],i.splice(e+1,1),u=!0)),e++;u&&ye(i,t)}function hs(i){return i>=55296&&i<=56319}function fs(i){return i>=56320&&i<=57343}function ds(i){return fs(i.charCodeAt(0))}function gs(i){return hs(i.charCodeAt(i.length-1))}function rn(i){for(var t=[],e=0;e<i.length;e++)i[e][1].length>0&&t.push(i[e]);return t}function Vt(i,t,e,s){return gs(i)||ds(s)?null:rn([[R,i],[M,t],[I,e],[R,s]])}function ln(i,t,e){var s=typeof e=="number"?{index:e,length:0}:e.oldRange,n=typeof e=="number"?null:e.newRange,r=i.length,l=t.length;if(s.length===0&&(n===null||n.length===0)){var o=s.index,a=i.slice(0,o),c=i.slice(o),f=n?n.index:null;t:{var m=o+l-r;if(f!==null&&f!==m||m<0||m>l)break t;var u=t.slice(0,m),h=t.slice(m);if(h!==c)break t;var d=Math.min(o,m),p=a.slice(0,d),y=u.slice(0,d);if(p!==y)break t;var b=a.slice(d),E=u.slice(d);return Vt(p,b,E,c)}t:{if(f!==null&&f!==o)break t;var x=o,u=t.slice(0,x),h=t.slice(x);if(u!==a)break t;var A=Math.min(r-x,l-x),N=c.slice(c.length-A),k=h.slice(h.length-A);if(N!==k)break t;var b=c.slice(0,c.length-A),E=h.slice(0,h.length-A);return Vt(a,b,E,N)}}if(s.length>0&&n&&n.length===0)t:{var p=i.slice(0,s.index),N=i.slice(s.index+s.length),d=p.length,A=N.length;if(l<d+A)break t;var y=t.slice(0,d),k=t.slice(l-A);if(p!==y||N!==k)break t;var b=i.slice(d,r-A),E=t.slice(d,l-A);return Vt(p,b,E,N)}return null}function $t(i,t,e,s){return wt(i,t,e,s,!0)}$t.INSERT=I;$t.DELETE=M;$t.EQUAL=R;var on=$t,ve={};Object.defineProperty(ve,"__esModule",{value:!0});const an=cs,cn=us;var se;(function(i){function t(r={},l={},o=!1){typeof r!="object"&&(r={}),typeof l!="object"&&(l={});let a=an(l);o||(a=Object.keys(a).reduce((c,f)=>(a[f]!=null&&(c[f]=a[f]),c),{}));for(const c in r)r[c]!==void 0&&l[c]===void 0&&(a[c]=r[c]);return Object.keys(a).length>0?a:void 0}i.compose=t;function e(r={},l={}){typeof r!="object"&&(r={}),typeof l!="object"&&(l={});const o=Object.keys(r).concat(Object.keys(l)).reduce((a,c)=>(cn(r[c],l[c])||(a[c]=l[c]===void 0?null:l[c]),a),{});return Object.keys(o).length>0?o:void 0}i.diff=e;function s(r={},l={}){r=r||{};const o=Object.keys(l).reduce((a,c)=>(l[c]!==r[c]&&r[c]!==void 0&&(a[c]=l[c]),a),{});return Object.keys(r).reduce((a,c)=>(r[c]!==l[c]&&l[c]===void 0&&(a[c]=null),a),o)}i.invert=s;function n(r,l,o=!1){if(typeof r!="object")return l;if(typeof l!="object")return;if(!o)return l;const a=Object.keys(l).reduce((c,f)=>(r[f]===void 0&&(c[f]=l[f]),c),{});return Object.keys(a).length>0?a:void 0}i.transform=n})(se||(se={}));ve.default=se;var jt={};Object.defineProperty(jt,"__esModule",{value:!0});var ne;(function(i){function t(e){return typeof e.delete=="number"?e.delete:typeof e.retain=="number"?e.retain:typeof e.retain=="object"&&e.retain!==null?1:typeof e.insert=="string"?e.insert.length:1}i.length=t})(ne||(ne={}));jt.default=ne;var Ee={};Object.defineProperty(Ee,"__esModule",{value:!0});const De=jt;class un{constructor(t){this.ops=t,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(t){t||(t=1/0);const e=this.ops[this.index];if(e){const s=this.offset,n=De.default.length(e);if(t>=n-s?(t=n-s,this.index+=1,this.offset=0):this.offset+=t,typeof e.delete=="number")return{delete:t};{const r={};return e.attributes&&(r.attributes=e.attributes),typeof e.retain=="number"?r.retain=t:typeof e.retain=="object"&&e.retain!==null?r.retain=e.retain:typeof e.insert=="string"?r.insert=e.insert.substr(s,t):r.insert=e.insert,r}}else return{retain:1/0}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?De.default.length(this.ops[this.index])-this.offset:1/0}peekType(){const t=this.ops[this.index];return t?typeof t.delete=="number"?"delete":typeof t.retain=="number"||typeof t.retain=="object"&&t.retain!==null?"retain":"insert":"retain"}rest(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);{const t=this.offset,e=this.index,s=this.next(),n=this.ops.slice(this.index);return this.offset=t,this.index=e,[s].concat(n)}}else return[]}}Ee.default=un;(function(i,t){Object.defineProperty(t,"__esModule",{value:!0}),t.AttributeMap=t.OpIterator=t.Op=void 0;const e=on,s=cs,n=us,r=ve;t.AttributeMap=r.default;const l=jt;t.Op=l.default;const o=Ee;t.OpIterator=o.default;const a="\0",c=(m,u)=>{if(typeof m!="object"||m===null)throw new Error(`cannot retain a ${typeof m}`);if(typeof u!="object"||u===null)throw new Error(`cannot retain a ${typeof u}`);const h=Object.keys(m)[0];if(!h||h!==Object.keys(u)[0])throw new Error(`embed types not matched: ${h} != ${Object.keys(u)[0]}`);return[h,m[h],u[h]]};class f{constructor(u){Array.isArray(u)?this.ops=u:u!=null&&Array.isArray(u.ops)?this.ops=u.ops:this.ops=[]}static registerEmbed(u,h){this.handlers[u]=h}static unregisterEmbed(u){delete this.handlers[u]}static getHandler(u){const h=this.handlers[u];if(!h)throw new Error(`no handlers for embed type "${u}"`);return h}insert(u,h){const d={};return typeof u=="string"&&u.length===0?this:(d.insert=u,h!=null&&typeof h=="object"&&Object.keys(h).length>0&&(d.attributes=h),this.push(d))}delete(u){return u<=0?this:this.push({delete:u})}retain(u,h){if(typeof u=="number"&&u<=0)return this;const d={retain:u};return h!=null&&typeof h=="object"&&Object.keys(h).length>0&&(d.attributes=h),this.push(d)}push(u){let h=this.ops.length,d=this.ops[h-1];if(u=s(u),typeof d=="object"){if(typeof u.delete=="number"&&typeof d.delete=="number")return this.ops[h-1]={delete:d.delete+u.delete},this;if(typeof d.delete=="number"&&u.insert!=null&&(h-=1,d=this.ops[h-1],typeof d!="object"))return this.ops.unshift(u),this;if(n(u.attributes,d.attributes)){if(typeof u.insert=="string"&&typeof d.insert=="string")return this.ops[h-1]={insert:d.insert+u.insert},typeof u.attributes=="object"&&(this.ops[h-1].attributes=u.attributes),this;if(typeof u.retain=="number"&&typeof d.retain=="number")return this.ops[h-1]={retain:d.retain+u.retain},typeof u.attributes=="object"&&(this.ops[h-1].attributes=u.attributes),this}}return h===this.ops.length?this.ops.push(u):this.ops.splice(h,0,u),this}chop(){const u=this.ops[this.ops.length-1];return u&&typeof u.retain=="number"&&!u.attributes&&this.ops.pop(),this}filter(u){return this.ops.filter(u)}forEach(u){this.ops.forEach(u)}map(u){return this.ops.map(u)}partition(u){const h=[],d=[];return this.forEach(p=>{(u(p)?h:d).push(p)}),[h,d]}reduce(u,h){return this.ops.reduce(u,h)}changeLength(){return this.reduce((u,h)=>h.insert?u+l.default.length(h):h.delete?u-h.delete:u,0)}length(){return this.reduce((u,h)=>u+l.default.length(h),0)}slice(u=0,h=1/0){const d=[],p=new o.default(this.ops);let y=0;for(;y<h&&p.hasNext();){let b;y<u?b=p.next(u-y):(b=p.next(h-y),d.push(b)),y+=l.default.length(b)}return new f(d)}compose(u){const h=new o.default(this.ops),d=new o.default(u.ops),p=[],y=d.peek();if(y!=null&&typeof y.retain=="number"&&y.attributes==null){let E=y.retain;for(;h.peekType()==="insert"&&h.peekLength()<=E;)E-=h.peekLength(),p.push(h.next());y.retain-E>0&&d.next(y.retain-E)}const b=new f(p);for(;h.hasNext()||d.hasNext();)if(d.peekType()==="insert")b.push(d.next());else if(h.peekType()==="delete")b.push(h.next());else{const E=Math.min(h.peekLength(),d.peekLength()),x=h.next(E),A=d.next(E);if(A.retain){const N={};if(typeof x.retain=="number")N.retain=typeof A.retain=="number"?E:A.retain;else if(typeof A.retain=="number")x.retain==null?N.insert=x.insert:N.retain=x.retain;else{const S=x.retain==null?"insert":"retain",[T,Rt,Ps]=c(x[S],A.retain),_s=f.getHandler(T);N[S]={[T]:_s.compose(Rt,Ps,S==="retain")}}const k=r.default.compose(x.attributes,A.attributes,typeof x.retain=="number");if(k&&(N.attributes=k),b.push(N),!d.hasNext()&&n(b.ops[b.ops.length-1],N)){const S=new f(h.rest());return b.concat(S).chop()}}else typeof A.delete=="number"&&(typeof x.retain=="number"||typeof x.retain=="object"&&x.retain!==null)&&b.push(A)}return b.chop()}concat(u){const h=new f(this.ops.slice());return u.ops.length>0&&(h.push(u.ops[0]),h.ops=h.ops.concat(u.ops.slice(1))),h}diff(u,h){if(this.ops===u.ops)return new f;const d=[this,u].map(x=>x.map(A=>{if(A.insert!=null)return typeof A.insert=="string"?A.insert:a;const N=x===u?"on":"with";throw new Error("diff() called "+N+" non-document")}).join("")),p=new f,y=e(d[0],d[1],h,!0),b=new o.default(this.ops),E=new o.default(u.ops);return y.forEach(x=>{let A=x[1].length;for(;A>0;){let N=0;switch(x[0]){case e.INSERT:N=Math.min(E.peekLength(),A),p.push(E.next(N));break;case e.DELETE:N=Math.min(A,b.peekLength()),b.next(N),p.delete(N);break;case e.EQUAL:N=Math.min(b.peekLength(),E.peekLength(),A);const k=b.next(N),S=E.next(N);n(k.insert,S.insert)?p.retain(N,r.default.diff(k.attributes,S.attributes)):p.push(S).delete(N);break}A-=N}}),p.chop()}eachLine(u,h=`
`){const d=new o.default(this.ops);let p=new f,y=0;for(;d.hasNext();){if(d.peekType()!=="insert")return;const b=d.peek(),E=l.default.length(b)-d.peekLength(),x=typeof b.insert=="string"?b.insert.indexOf(h,E)-E:-1;if(x<0)p.push(d.next());else if(x>0)p.push(d.next(x));else{if(u(p,d.next(1).attributes||{},y)===!1)return;y+=1,p=new f}}p.length()>0&&u(p,{},y)}invert(u){const h=new f;return this.reduce((d,p)=>{if(p.insert)h.delete(l.default.length(p));else{if(typeof p.retain=="number"&&p.attributes==null)return h.retain(p.retain),d+p.retain;if(p.delete||typeof p.retain=="number"){const y=p.delete||p.retain;return u.slice(d,d+y).forEach(E=>{p.delete?h.push(E):p.retain&&p.attributes&&h.retain(l.default.length(E),r.default.invert(p.attributes,E.attributes))}),d+y}else if(typeof p.retain=="object"&&p.retain!==null){const y=u.slice(d,d+1),b=new o.default(y.ops).next(),[E,x,A]=c(p.retain,b.insert),N=f.getHandler(E);return h.retain({[E]:N.invert(x,A)},r.default.invert(p.attributes,b.attributes)),d+1}}return d},0),h.chop()}transform(u,h=!1){if(h=!!h,typeof u=="number")return this.transformPosition(u,h);const d=u,p=new o.default(this.ops),y=new o.default(d.ops),b=new f;for(;p.hasNext()||y.hasNext();)if(p.peekType()==="insert"&&(h||y.peekType()!=="insert"))b.retain(l.default.length(p.next()));else if(y.peekType()==="insert")b.push(y.next());else{const E=Math.min(p.peekLength(),y.peekLength()),x=p.next(E),A=y.next(E);if(x.delete)continue;if(A.delete)b.push(A);else{const N=x.retain,k=A.retain;let S=typeof k=="object"&&k!==null?k:E;if(typeof N=="object"&&N!==null&&typeof k=="object"&&k!==null){const T=Object.keys(N)[0];if(T===Object.keys(k)[0]){const Rt=f.getHandler(T);Rt&&(S={[T]:Rt.transform(N[T],k[T],h)})}}b.retain(S,r.default.transform(x.attributes,A.attributes,h))}}return b.chop()}transformPosition(u,h=!1){h=!!h;const d=new o.default(this.ops);let p=0;for(;d.hasNext()&&p<=u;){const y=d.peekLength(),b=d.peekType();if(d.next(),b==="delete"){u-=Math.min(y,u-p);continue}else b==="insert"&&(p<u||!h)&&(u+=y);p+=y}return u}}f.Op=l.default,f.OpIterator=o.default,f.AttributeMap=r.default,f.handlers={},t.default=f,i.exports=f,i.exports.default=f})(ee,ee.exports);var F=ee.exports;const w=Ws(F);class K extends ${static value(){}optimize(){(this.prev||this.next)&&this.remove()}length(){return 0}value(){return""}}K.blotName="break";K.tagName="BR";let V=class extends te{};const hn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Pt(i){return i.replace(/[&<>"']/g,t=>hn[t])}const G=class G extends Ks{static compare(t,e){const s=G.order.indexOf(t),n=G.order.indexOf(e);return s>=0||n>=0?s-n:t===e?0:t<e?-1:1}formatAt(t,e,s,n){if(G.compare(this.statics.blotName,s)<0&&this.scroll.query(s,L.BLOT)){const r=this.isolate(t,e);n&&r.wrap(s,n)}else super.formatAt(t,e,s,n)}optimize(t){if(super.optimize(t),this.parent instanceof G&&G.compare(this.statics.blotName,this.parent.statics.blotName)>0){const e=this.parent.isolate(this.offset(),this.length());this.moveChildren(e),e.wrap(this)}}};v(G,"allowedChildren",[G,K,$,V]),v(G,"order",["cursor","inline","link","underline","strike","italic","bold","script","code"]);let X=G;const Ue=1;class C extends rs{constructor(){super(...arguments);v(this,"cache",{})}delta(){return this.cache.delta==null&&(this.cache.delta=ms(this)),this.cache.delta}deleteAt(e,s){super.deleteAt(e,s),this.cache={}}formatAt(e,s,n,r){s<=0||(this.scroll.query(n,L.BLOCK)?e+s===this.length()&&this.format(n,r):super.formatAt(e,Math.min(s,this.length()-e-1),n,r),this.cache={})}insertAt(e,s,n){if(n!=null){super.insertAt(e,s,n),this.cache={};return}if(s.length===0)return;const r=s.split(`
`),l=r.shift();l.length>0&&(e<this.length()-1||this.children.tail==null?super.insertAt(Math.min(e,this.length()-1),l):this.children.tail.insertAt(this.children.tail.length(),l),this.cache={});let o=this;r.reduce((a,c)=>(o=o.split(a,!0),o.insertAt(0,c),c.length),e+l.length)}insertBefore(e,s){const{head:n}=this.children;super.insertBefore(e,s),n instanceof K&&n.remove(),this.cache={}}length(){return this.cache.length==null&&(this.cache.length=super.length()+Ue),this.cache.length}moveChildren(e,s){super.moveChildren(e,s),this.cache={}}optimize(e){super.optimize(e),this.cache={}}path(e){return super.path(e,!0)}removeChild(e){super.removeChild(e),this.cache={}}split(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(s&&(e===0||e>=this.length()-Ue)){const r=this.clone();return e===0?(this.parent.insertBefore(r,this),this):(this.parent.insertBefore(r,this.next),r)}const n=super.split(e,s);return this.cache={},n}}C.blotName="block";C.tagName="P";C.defaultChild=K;C.allowedChildren=[K,X,$,V];class H extends ${attach(){super.attach(),this.attributes=new Zs(this.domNode)}delta(){return new w().insert(this.value(),{...this.formats(),...this.attributes.values()})}format(t,e){const s=this.scroll.query(t,L.BLOCK_ATTRIBUTE);s!=null&&this.attributes.attribute(s,e)}formatAt(t,e,s,n){this.format(s,n)}insertAt(t,e,s){if(s!=null){super.insertAt(t,e,s);return}const n=e.split(`
`),r=n.pop(),l=n.map(a=>{const c=this.scroll.create(C.blotName);return c.insertAt(0,a),c}),o=this.split(t);l.forEach(a=>{this.parent.insertBefore(a,o)}),r&&this.parent.insertBefore(this.scroll.create("text",r),o)}}H.scope=L.BLOCK_BLOT;function ms(i){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return i.descendants(_).reduce((e,s)=>s.length()===0?e:e.insert(s.value(),D(s,{},t)),new w).insert(`
`,D(i))}function D(i){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return i==null||("formats"in i&&typeof i.formats=="function"&&(t={...t,...i.formats()},e&&delete t["code-token"]),i.parent==null||i.parent.statics.blotName==="scroll"||i.parent.statics.scope!==i.statics.scope)?t:D(i.parent,t,e)}const B=class B extends ${static value(){}constructor(t,e,s){super(t,e),this.selection=s,this.textNode=document.createTextNode(B.CONTENTS),this.domNode.appendChild(this.textNode),this.savedLength=0}detach(){this.parent!=null&&this.parent.removeChild(this)}format(t,e){if(this.savedLength!==0){super.format(t,e);return}let s=this,n=0;for(;s!=null&&s.statics.scope!==L.BLOCK_BLOT;)n+=s.offset(s.parent),s=s.parent;s!=null&&(this.savedLength=B.CONTENTS.length,s.optimize(),s.formatAt(n,B.CONTENTS.length,t,e),this.savedLength=0)}index(t,e){return t===this.textNode?0:super.index(t,e)}length(){return this.savedLength}position(){return[this.textNode,this.textNode.data.length]}remove(){super.remove(),this.parent=null}restore(){if(this.selection.composing||this.parent==null)return null;const t=this.selection.getNativeRange();for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);const e=this.prev instanceof V?this.prev:null,s=e?e.length():0,n=this.next instanceof V?this.next:null,r=n?n.text:"",{textNode:l}=this,o=l.data.split(B.CONTENTS).join("");l.data=B.CONTENTS;let a;if(e)a=e,(o||n)&&(e.insertAt(e.length(),o+r),n&&n.remove());else if(n)a=n,n.insertAt(0,o);else{const c=document.createTextNode(o);a=this.scroll.create(c),this.parent.insertBefore(a,this)}if(this.remove(),t){const c=(u,h)=>e&&u===e.domNode?h:u===l?s+h-1:n&&u===n.domNode?s+o.length+h:null,f=c(t.start.node,t.start.offset),m=c(t.end.node,t.end.offset);if(f!==null&&m!==null)return{startNode:a.domNode,startOffset:f,endNode:a.domNode,endOffset:m}}return null}update(t,e){if(t.some(s=>s.type==="characterData"&&s.target===this.textNode)){const s=this.restore();s&&(e.range=s)}}optimize(t){super.optimize(t);let{parent:e}=this;for(;e;){if(e.domNode.tagName==="A"){this.savedLength=B.CONTENTS.length,e.isolate(this.offset(e),this.length()).unwrap(),this.savedLength=0;break}e=e.parent}}value(){return""}};v(B,"blotName","cursor"),v(B,"className","ql-cursor"),v(B,"tagName","span"),v(B,"CONTENTS","\uFEFF");let dt=B;const ie=new WeakMap,re=["error","warn","log","info"];let le="warn";function ps(i){if(le&&re.indexOf(i)<=re.indexOf(le)){for(var t=arguments.length,e=new Array(t>1?t-1:0),s=1;s<t;s++)e[s-1]=arguments[s];console[i](...e)}}function et(i){return re.reduce((t,e)=>(t[e]=ps.bind(console,e,i),t),{})}et.level=i=>{le=i};ps.level=et.level;const Kt=et("quill:events"),fn=["selectionchange","mousedown","mouseup","click"];fn.forEach(i=>{document.addEventListener(i,function(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];Array.from(document.querySelectorAll(".ql-container")).forEach(n=>{const r=ie.get(n);r&&r.emitter&&r.emitter.handleDOM(...e)})})});class q extends Xs{constructor(){super(),this.domListeners={},this.on("error",Kt.error)}emit(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];return Kt.log.call(Kt,...e),super.emit(...e)}handleDOM(t){for(var e=arguments.length,s=new Array(e>1?e-1:0),n=1;n<e;n++)s[n-1]=arguments[n];(this.domListeners[t.type]||[]).forEach(r=>{let{node:l,handler:o}=r;(t.target===l||l.contains(t.target))&&o(t,...s)})}listenDOM(t,e,s){this.domListeners[t]||(this.domListeners[t]=[]),this.domListeners[t].push({node:e,handler:s})}}v(q,"events",{EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SCROLL_EMBED_UPDATE:"scroll-embed-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change",COMPOSITION_BEFORE_START:"composition-before-start",COMPOSITION_START:"composition-start",COMPOSITION_BEFORE_END:"composition-before-end",COMPOSITION_END:"composition-end"}),v(q,"sources",{API:"api",SILENT:"silent",USER:"user"});const Zt=et("quill:selection");class rt{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;this.index=t,this.length=e}}class dn{constructor(t,e){this.emitter=e,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new rt(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,()=>{!this.mouseDown&&!this.composing&&setTimeout(this.update.bind(this,q.sources.USER),1)}),this.emitter.on(q.events.SCROLL_BEFORE_UPDATE,()=>{if(!this.hasFocus())return;const s=this.getNativeRange();s!=null&&s.start.node!==this.cursor.textNode&&this.emitter.once(q.events.SCROLL_UPDATE,(n,r)=>{try{this.root.contains(s.start.node)&&this.root.contains(s.end.node)&&this.setNativeRange(s.start.node,s.start.offset,s.end.node,s.end.offset);const l=r.some(o=>o.type==="characterData"||o.type==="childList"||o.type==="attributes"&&o.target===this.root);this.update(l?q.sources.SILENT:n)}catch{}})}),this.emitter.on(q.events.SCROLL_OPTIMIZE,(s,n)=>{if(n.range){const{startNode:r,startOffset:l,endNode:o,endOffset:a}=n.range;this.setNativeRange(r,l,o,a),this.update(q.sources.SILENT)}}),this.update(q.sources.SILENT)}handleComposition(){this.emitter.on(q.events.COMPOSITION_BEFORE_START,()=>{this.composing=!0}),this.emitter.on(q.events.COMPOSITION_END,()=>{if(this.composing=!1,this.cursor.parent){const t=this.cursor.restore();if(!t)return;setTimeout(()=>{this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)},1)}})}handleDragging(){this.emitter.listenDOM("mousedown",document.body,()=>{this.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,()=>{this.mouseDown=!1,this.update(q.sources.USER)})}focus(){this.hasFocus()||(this.root.focus({preventScroll:!0}),this.setRange(this.savedRange))}format(t,e){this.scroll.update();const s=this.getNativeRange();if(!(s==null||!s.native.collapsed||this.scroll.query(t,L.BLOCK))){if(s.start.node!==this.cursor.textNode){const n=this.scroll.find(s.start.node,!1);if(n==null)return;if(n instanceof _){const r=n.split(s.start.offset);n.parent.insertBefore(this.cursor,r)}else n.insertBefore(this.cursor,s.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}getBounds(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;const s=this.scroll.length();t=Math.min(t,s-1),e=Math.min(t+e,s-1)-t;let n,[r,l]=this.scroll.leaf(t);if(r==null)return null;if(e>0&&l===r.length()){const[f]=this.scroll.leaf(t+1);if(f){const[m]=this.scroll.line(t),[u]=this.scroll.line(t+1);m===u&&(r=f,l=0)}}[n,l]=r.position(l,!0);const o=document.createRange();if(e>0)return o.setStart(n,l),[r,l]=this.scroll.leaf(t+e),r==null?null:([n,l]=r.position(l,!0),o.setEnd(n,l),o.getBoundingClientRect());let a="left",c;if(n instanceof Text){if(!n.data.length)return null;l<n.data.length?(o.setStart(n,l),o.setEnd(n,l+1)):(o.setStart(n,l-1),o.setEnd(n,l),a="right"),c=o.getBoundingClientRect()}else{if(!(r.domNode instanceof Element))return null;c=r.domNode.getBoundingClientRect(),l>0&&(a="right")}return{bottom:c.top+c.height,height:c.height,left:c[a],right:c[a],top:c.top,width:0}}getNativeRange(){const t=document.getSelection();if(t==null||t.rangeCount<=0)return null;const e=t.getRangeAt(0);if(e==null)return null;const s=this.normalizeNative(e);return Zt.info("getNativeRange",s),s}getRange(){const t=this.scroll.domNode;if("isConnected"in t&&!t.isConnected)return[null,null];const e=this.getNativeRange();return e==null?[null,null]:[this.normalizedToRange(e),e]}hasFocus(){return document.activeElement===this.root||document.activeElement!=null&&Gt(this.root,document.activeElement)}normalizedToRange(t){const e=[[t.start.node,t.start.offset]];t.native.collapsed||e.push([t.end.node,t.end.offset]);const s=e.map(l=>{const[o,a]=l,c=this.scroll.find(o,!0),f=c.offset(this.scroll);return a===0?f:c instanceof _?f+c.index(o,a):f+c.length()}),n=Math.min(Math.max(...s),this.scroll.length()-1),r=Math.min(n,...s);return new rt(r,n-r)}normalizeNative(t){if(!Gt(this.root,t.startContainer)||!t.collapsed&&!Gt(this.root,t.endContainer))return null;const e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach(s=>{let{node:n,offset:r}=s;for(;!(n instanceof Text)&&n.childNodes.length>0;)if(n.childNodes.length>r)n=n.childNodes[r],r=0;else if(n.childNodes.length===r)n=n.lastChild,n instanceof Text?r=n.data.length:n.childNodes.length>0?r=n.childNodes.length:r=n.childNodes.length+1;else break;s.node=n,s.offset=r}),e}rangeToNative(t){const e=this.scroll.length(),s=(n,r)=>{n=Math.min(e-1,n);const[l,o]=this.scroll.leaf(n);return l?l.position(o,r):[null,-1]};return[...s(t.index,!1),...s(t.index+t.length,!0)]}setNativeRange(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:t,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:e,r=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(Zt.info("setNativeRange",t,e,s,n),t!=null&&(this.root.parentNode==null||t.parentNode==null||s.parentNode==null))return;const l=document.getSelection();if(l!=null)if(t!=null){this.hasFocus()||this.root.focus({preventScroll:!0});const{native:o}=this.getNativeRange()||{};if(o==null||r||t!==o.startContainer||e!==o.startOffset||s!==o.endContainer||n!==o.endOffset){t instanceof Element&&t.tagName==="BR"&&(e=Array.from(t.parentNode.childNodes).indexOf(t),t=t.parentNode),s instanceof Element&&s.tagName==="BR"&&(n=Array.from(s.parentNode.childNodes).indexOf(s),s=s.parentNode);const a=document.createRange();a.setStart(t,e),a.setEnd(s,n),l.removeAllRanges(),l.addRange(a)}}else l.removeAllRanges(),this.root.blur()}setRange(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:q.sources.API;if(typeof e=="string"&&(s=e,e=!1),Zt.info("setRange",t),t!=null){const n=this.rangeToNative(t);this.setNativeRange(...n,e)}else this.setNativeRange(null);this.update(s)}update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:q.sources.USER;const e=this.lastRange,[s,n]=this.getRange();if(this.lastRange=s,this.lastNative=n,this.lastRange!=null&&(this.savedRange=this.lastRange),!pe(e,this.lastRange)){if(!this.composing&&n!=null&&n.native.collapsed&&n.start.node!==this.cursor.textNode){const l=this.cursor.restore();l&&this.setNativeRange(l.startNode,l.startOffset,l.endNode,l.endOffset)}const r=[q.events.SELECTION_CHANGE,ht(this.lastRange),ht(e),t];this.emitter.emit(q.events.EDITOR_CHANGE,...r),t!==q.sources.SILENT&&this.emitter.emit(...r)}}}function Gt(i,t){try{t.parentNode}catch{return!1}return i.contains(t)}const gn=/^[ -~]*$/;class mn{constructor(t){this.scroll=t,this.delta=this.getDelta()}applyDelta(t){this.scroll.update();let e=this.scroll.length();this.scroll.batchStart();const s=He(t),n=new w;return bn(s.ops.slice()).reduce((l,o)=>{const a=F.Op.length(o);let c=o.attributes||{},f=!1,m=!1;if(o.insert!=null){if(n.retain(a),typeof o.insert=="string"){const d=o.insert;m=!d.endsWith(`
`)&&(e<=l||!!this.scroll.descendant(H,l)[0]),this.scroll.insertAt(l,d);const[p,y]=this.scroll.line(l);let b=it({},D(p));if(p instanceof C){const[E]=p.descendant(_,y);E&&(b=it(b,D(E)))}c=F.AttributeMap.diff(b,c)||{}}else if(typeof o.insert=="object"){const d=Object.keys(o.insert)[0];if(d==null)return l;const p=this.scroll.query(d,L.INLINE)!=null;if(p)(e<=l||this.scroll.descendant(H,l)[0])&&(m=!0);else if(l>0){const[y,b]=this.scroll.descendant(_,l-1);y instanceof V?y.value()[b]!==`
`&&(f=!0):y instanceof $&&y.statics.scope===L.INLINE_BLOT&&(f=!0)}if(this.scroll.insertAt(l,d,o.insert[d]),p){const[y]=this.scroll.descendant(_,l);if(y){const b=it({},D(y));c=F.AttributeMap.diff(b,c)||{}}}}e+=a}else if(n.push(o),o.retain!==null&&typeof o.retain=="object"){const d=Object.keys(o.retain)[0];if(d==null)return l;this.scroll.updateEmbedAt(l,d,o.retain[d])}Object.keys(c).forEach(d=>{this.scroll.formatAt(l,a,d,c[d])});const u=f?1:0,h=m?1:0;return e+=u+h,n.retain(u),n.delete(h),l+a+u+h},0),n.reduce((l,o)=>typeof o.delete=="number"?(this.scroll.deleteAt(l,o.delete),l):l+F.Op.length(o),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(s)}deleteText(t,e){return this.scroll.deleteAt(t,e),this.update(new w().retain(t).delete(e))}formatLine(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.scroll.update(),Object.keys(s).forEach(r=>{this.scroll.lines(t,Math.max(e,1)).forEach(l=>{l.format(r,s[r])})}),this.scroll.optimize();const n=new w().retain(t).retain(e,ht(s));return this.update(n)}formatText(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};Object.keys(s).forEach(r=>{this.scroll.formatAt(t,e,r,s[r])});const n=new w().retain(t).retain(e,ht(s));return this.update(n)}getContents(t,e){return this.delta.slice(t,t+e)}getDelta(){return this.scroll.lines().reduce((t,e)=>t.concat(e.delta()),new w)}getFormat(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,s=[],n=[];e===0?this.scroll.path(t).forEach(o=>{const[a]=o;a instanceof C?s.push(a):a instanceof _&&n.push(a)}):(s=this.scroll.lines(t,e),n=this.scroll.descendants(_,t,e));const[r,l]=[s,n].map(o=>{const a=o.shift();if(a==null)return{};let c=D(a);for(;Object.keys(c).length>0;){const f=o.shift();if(f==null)return c;c=pn(D(f),c)}return c});return{...r,...l}}getHTML(t,e){const[s,n]=this.scroll.line(t);if(s){const r=s.length();return s.length()>=n+e&&!(n===0&&e===r)?xt(s,n,e,!0):xt(this.scroll,t,e,!0)}return""}getText(t,e){return this.getContents(t,e).filter(s=>typeof s.insert=="string").map(s=>s.insert).join("")}insertContents(t,e){const s=He(e),n=new w().retain(t).concat(s);return this.scroll.insertContents(t,s),this.update(n)}insertEmbed(t,e,s){return this.scroll.insertAt(t,e,s),this.update(new w().retain(t).insert({[e]:s}))}insertText(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return e=e.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(t,e),Object.keys(s).forEach(n=>{this.scroll.formatAt(t,e.length,n,s[n])}),this.update(new w().retain(t).insert(e,ht(s)))}isBlank(){if(this.scroll.children.length===0)return!0;if(this.scroll.children.length>1)return!1;const t=this.scroll.children.head;if((t==null?void 0:t.statics.blotName)!==C.blotName)return!1;const e=t;return e.children.length>1?!1:e.children.head instanceof K}removeFormat(t,e){const s=this.getText(t,e),[n,r]=this.scroll.line(t+e);let l=0,o=new w;n!=null&&(l=n.length()-r,o=n.delta().slice(r,r+l-1).insert(`
`));const c=this.getContents(t,e+l).diff(new w().insert(s).concat(o)),f=new w().retain(t).concat(c);return this.applyDelta(f)}update(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0;const n=this.delta;if(e.length===1&&e[0].type==="characterData"&&e[0].target.data.match(gn)&&this.scroll.find(e[0].target)){const r=this.scroll.find(e[0].target),l=D(r),o=r.offset(this.scroll),a=e[0].oldValue.replace(dt.CONTENTS,""),c=new w().insert(a),f=new w().insert(r.value()),m=s&&{oldRange:Fe(s.oldRange,-o),newRange:Fe(s.newRange,-o)};t=new w().retain(o).concat(c.diff(f,m)).reduce((h,d)=>d.insert?h.insert(d.insert,l):h.push(d),new w),this.delta=n.compose(t)}else this.delta=this.getDelta(),(!t||!pe(n.compose(t),this.delta))&&(t=n.diff(this.delta,s));return t}}function ct(i,t,e){if(i.length===0){const[h]=Wt(e.pop());return t<=0?`</li></${h}>`:`</li></${h}>${ct([],t-1,e)}`}const[{child:s,offset:n,length:r,indent:l,type:o},...a]=i,[c,f]=Wt(o);if(l>t)return e.push(o),l===t+1?`<${c}><li${f}>${xt(s,n,r)}${ct(a,l,e)}`:`<${c}><li>${ct(i,t+1,e)}`;const m=e[e.length-1];if(l===t&&o===m)return`</li><li${f}>${xt(s,n,r)}${ct(a,l,e)}`;const[u]=Wt(e.pop());return`</li></${u}>${ct(i,t-1,e)}`}function xt(i,t,e){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if("html"in i&&typeof i.html=="function")return i.html(t,e);if(i instanceof V)return Pt(i.value().slice(t,t+e)).replaceAll(" ","&nbsp;");if(i instanceof Ht){if(i.statics.blotName==="list-container"){const c=[];return i.children.forEachAt(t,e,(f,m,u)=>{const h="formats"in f&&typeof f.formats=="function"?f.formats():{};c.push({child:f,offset:m,length:u,indent:h.indent||0,type:h.list})}),ct(c,-1,[])}const n=[];if(i.children.forEachAt(t,e,(c,f,m)=>{n.push(xt(c,f,m))}),s||i.statics.blotName==="list")return n.join("");const{outerHTML:r,innerHTML:l}=i.domNode,[o,a]=r.split(`>${l}<`);return o==="<table"?`<table style="border: 1px solid #000;">${n.join("")}<${a}`:`${o}>${n.join("")}<${a}`}return i.domNode instanceof Element?i.domNode.outerHTML:""}function pn(i,t){return Object.keys(t).reduce((e,s)=>{if(i[s]==null)return e;const n=t[s];return n===i[s]?e[s]=n:Array.isArray(n)?n.indexOf(i[s])<0?e[s]=n.concat([i[s]]):e[s]=n:e[s]=[n,i[s]],e},{})}function Wt(i){const t=i==="ordered"?"ol":"ul";switch(i){case"checked":return[t,' data-list="checked"'];case"unchecked":return[t,' data-list="unchecked"'];default:return[t,""]}}function He(i){return i.reduce((t,e)=>{if(typeof e.insert=="string"){const s=e.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return t.insert(s,e.attributes)}return t.push(e)},new w)}function Fe(i,t){let{index:e,length:s}=i;return new rt(e+t,s)}function bn(i){const t=[];return i.forEach(e=>{typeof e.insert=="string"?e.insert.split(`
`).forEach((n,r)=>{r&&t.push({insert:`
`,attributes:e.attributes}),n&&t.push({insert:n,attributes:e.attributes})}):t.push(e)}),t}class Z{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.quill=t,this.options=e}}v(Z,"DEFAULTS",{});const Ct="\uFEFF";class qe extends ${constructor(t,e){super(t,e),this.contentNode=document.createElement("span"),this.contentNode.setAttribute("contenteditable","false"),Array.from(this.domNode.childNodes).forEach(s=>{this.contentNode.appendChild(s)}),this.leftGuard=document.createTextNode(Ct),this.rightGuard=document.createTextNode(Ct),this.domNode.appendChild(this.leftGuard),this.domNode.appendChild(this.contentNode),this.domNode.appendChild(this.rightGuard)}index(t,e){return t===this.leftGuard?0:t===this.rightGuard?1:super.index(t,e)}restore(t){let e=null,s;const n=t.data.split(Ct).join("");if(t===this.leftGuard)if(this.prev instanceof V){const r=this.prev.length();this.prev.insertAt(r,n),e={startNode:this.prev.domNode,startOffset:r+n.length}}else s=document.createTextNode(n),this.parent.insertBefore(this.scroll.create(s),this),e={startNode:s,startOffset:n.length};else t===this.rightGuard&&(this.next instanceof V?(this.next.insertAt(0,n),e={startNode:this.next.domNode,startOffset:n.length}):(s=document.createTextNode(n),this.parent.insertBefore(this.scroll.create(s),this.next),e={startNode:s,startOffset:n.length}));return t.data=Ct,e}update(t,e){t.forEach(s=>{if(s.type==="characterData"&&(s.target===this.leftGuard||s.target===this.rightGuard)){const n=this.restore(s.target);n&&(e.range=n)}})}}class yn{constructor(t,e){v(this,"isComposing",!1);this.scroll=t,this.emitter=e,this.setupListeners()}setupListeners(){this.scroll.domNode.addEventListener("compositionstart",t=>{this.isComposing||this.handleCompositionStart(t)}),this.scroll.domNode.addEventListener("compositionend",t=>{this.isComposing&&queueMicrotask(()=>{this.handleCompositionEnd(t)})})}handleCompositionStart(t){const e=t.target instanceof Node?this.scroll.find(t.target,!0):null;e&&!(e instanceof qe)&&(this.emitter.emit(q.events.COMPOSITION_BEFORE_START,t),this.scroll.batchStart(),this.emitter.emit(q.events.COMPOSITION_START,t),this.isComposing=!0)}handleCompositionEnd(t){this.emitter.emit(q.events.COMPOSITION_BEFORE_END,t),this.scroll.batchEnd(),this.emitter.emit(q.events.COMPOSITION_END,t),this.isComposing=!1}}const Et=class Et{constructor(t,e){v(this,"modules",{});this.quill=t,this.options=e}init(){Object.keys(this.options.modules).forEach(t=>{this.modules[t]==null&&this.addModule(t)})}addModule(t){const e=this.quill.constructor.import(`modules/${t}`);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}};v(Et,"DEFAULTS",{modules:{}}),v(Et,"themes",{default:Et});let gt=Et;const vn=i=>i.parentElement||i.getRootNode().host||null,En=i=>{const t=i.getBoundingClientRect(),e="offsetWidth"in i&&Math.abs(t.width)/i.offsetWidth||1,s="offsetHeight"in i&&Math.abs(t.height)/i.offsetHeight||1;return{top:t.top,right:t.left+i.clientWidth*e,bottom:t.top+i.clientHeight*s,left:t.left}},Ot=i=>{const t=parseInt(i,10);return Number.isNaN(t)?0:t},$e=(i,t,e,s,n,r)=>i<e&&t>s?0:i<e?-(e-i+n):t>s?t-i>s-e?i+n-e:t-s+r:0,qn=(i,t)=>{var r,l,o;const e=i.ownerDocument;let s=t,n=i;for(;n;){const a=n===e.body,c=a?{top:0,right:((r=window.visualViewport)==null?void 0:r.width)??e.documentElement.clientWidth,bottom:((l=window.visualViewport)==null?void 0:l.height)??e.documentElement.clientHeight,left:0}:En(n),f=getComputedStyle(n),m=$e(s.left,s.right,c.left,c.right,Ot(f.scrollPaddingLeft),Ot(f.scrollPaddingRight)),u=$e(s.top,s.bottom,c.top,c.bottom,Ot(f.scrollPaddingTop),Ot(f.scrollPaddingBottom));if(m||u)if(a)(o=e.defaultView)==null||o.scrollBy(m,u);else{const{scrollLeft:h,scrollTop:d}=n;u&&(n.scrollTop+=u),m&&(n.scrollLeft+=m);const p=n.scrollLeft-h,y=n.scrollTop-d;s={left:s.left-p,top:s.top-y,right:s.right-p,bottom:s.bottom-y}}n=a||f.position==="fixed"?null:vn(n)}},wn=100,xn=["block","break","cursor","inline","scroll","text"],Nn=(i,t,e)=>{const s=new ls;return xn.forEach(n=>{const r=t.query(n);r&&s.register(r)}),i.forEach(n=>{let r=t.query(n);r||e.error(`Cannot register "${n}" specified in "formats" config. Are you sure it was registered?`);let l=0;for(;r;)if(s.register(r),r="blotName"in r?r.requiredContainer??null:null,l+=1,l>wn){e.error(`Cycle detected in registering blot requiredContainer: "${n}"`);break}}),s},ft=et("quill"),It=new ls;Ht.uiClass="ql-ui";const P=class P{static debug(t){t===!0&&(t="log"),et.level(t)}static find(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return ie.get(t)||It.find(t,e)}static import(t){return this.imports[t]==null&&ft.error(`Cannot import ${t}. Are you sure it was registered?`),this.imports[t]}static register(){if(typeof(arguments.length<=0?void 0:arguments[0])!="string"){const t=arguments.length<=0?void 0:arguments[0],e=!!(!(arguments.length<=1)&&arguments[1]),s="attrName"in t?t.attrName:t.blotName;typeof s=="string"?this.register(`formats/${s}`,t,e):Object.keys(t).forEach(n=>{this.register(n,t[n],e)})}else{const t=arguments.length<=0?void 0:arguments[0],e=arguments.length<=1?void 0:arguments[1],s=!!(!(arguments.length<=2)&&arguments[2]);this.imports[t]!=null&&!s&&ft.warn(`Overwriting ${t} with`,e),this.imports[t]=e,(t.startsWith("blots/")||t.startsWith("formats/"))&&e&&typeof e!="boolean"&&e.blotName!=="abstract"&&It.register(e),typeof e.register=="function"&&e.register(It)}}constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.options=An(t,e),this.container=this.options.container,this.container==null){ft.error("Invalid Quill container",t);return}this.options.debug&&P.debug(this.options.debug);const s=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",ie.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.emitter=new q;const n=os.blotName,r=this.options.registry.query(n);if(!r||!("blotName"in r))throw new Error(`Cannot initialize Quill without "${n}" blot`);if(this.scroll=new r(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new mn(this.scroll),this.selection=new dn(this.scroll,this.emitter),this.composition=new yn(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.addModule("input"),this.theme.addModule("uiNode"),this.theme.init(),this.emitter.on(q.events.EDITOR_CHANGE,l=>{l===q.events.TEXT_CHANGE&&this.root.classList.toggle("ql-blank",this.editor.isBlank())}),this.emitter.on(q.events.SCROLL_UPDATE,(l,o)=>{const a=this.selection.lastRange,[c]=this.selection.getRange(),f=a&&c?{oldRange:a,newRange:c}:void 0;j.call(this,()=>this.editor.update(null,o,f),l)}),this.emitter.on(q.events.SCROLL_EMBED_UPDATE,(l,o)=>{const a=this.selection.lastRange,[c]=this.selection.getRange(),f=a&&c?{oldRange:a,newRange:c}:void 0;j.call(this,()=>{const m=new w().retain(l.offset(this)).retain({[l.statics.blotName]:o});return this.editor.update(m,[],f)},P.sources.USER)}),s){const l=this.clipboard.convert({html:`${s}<p><br></p>`,text:`
`});this.setContents(l)}this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}addContainer(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof t=="string"){const s=t;t=document.createElement("div"),t.classList.add(s)}return this.container.insertBefore(t,e),t}blur(){this.selection.setRange(null)}deleteText(t,e,s){return[t,e,,s]=Y(t,e,s),j.call(this,()=>this.editor.deleteText(t,e),s,t,-1*e)}disable(){this.enable(!1)}editReadOnly(t){this.allowReadOnlyEdits=!0;const e=t();return this.allowReadOnlyEdits=!1,e}enable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}focus(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.selection.focus(),t.preventScroll||this.scrollSelectionIntoView()}format(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:q.sources.API;return j.call(this,()=>{const n=this.getSelection(!0);let r=new w;if(n==null)return r;if(this.scroll.query(t,L.BLOCK))r=this.editor.formatLine(n.index,n.length,{[t]:e});else{if(n.length===0)return this.selection.format(t,e),r;r=this.editor.formatText(n.index,n.length,{[t]:e})}return this.setSelection(n,q.sources.SILENT),r},s)}formatLine(t,e,s,n,r){let l;return[t,e,l,r]=Y(t,e,s,n,r),j.call(this,()=>this.editor.formatLine(t,e,l),r,t,0)}formatText(t,e,s,n,r){let l;return[t,e,l,r]=Y(t,e,s,n,r),j.call(this,()=>this.editor.formatText(t,e,l),r,t,0)}getBounds(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,s=null;if(typeof t=="number"?s=this.selection.getBounds(t,e):s=this.selection.getBounds(t.index,t.length),!s)return null;const n=this.container.getBoundingClientRect();return{bottom:s.bottom-n.top,height:s.height,left:s.left-n.left,right:s.right-n.left,top:s.top-n.top,width:s.width}}getContents(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-t;return[t,e]=Y(t,e),this.editor.getContents(t,e)}getFormat(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof t=="number"?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}getIndex(t){return t.offset(this.scroll)}getLength(){return this.scroll.length()}getLeaf(t){return this.scroll.leaf(t)}getLine(t){return this.scroll.line(t)}getLines(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof t!="number"?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}getModule(t){return this.theme.modules[t]}getSelection(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1)&&this.focus(),this.update(),this.selection.getRange()[0]}getSemanticHTML(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return typeof t=="number"&&(e=e??this.getLength()-t),[t,e]=Y(t,e),this.editor.getHTML(t,e)}getText(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return typeof t=="number"&&(e=e??this.getLength()-t),[t,e]=Y(t,e),this.editor.getText(t,e)}hasFocus(){return this.selection.hasFocus()}insertEmbed(t,e,s){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:P.sources.API;return j.call(this,()=>this.editor.insertEmbed(t,e,s),n,t)}insertText(t,e,s,n,r){let l;return[t,,l,r]=Y(t,0,s,n,r),j.call(this,()=>this.editor.insertText(t,e,l),r,t,e.length)}isEnabled(){return this.scroll.isEnabled()}off(){return this.emitter.off(...arguments)}on(){return this.emitter.on(...arguments)}once(){return this.emitter.once(...arguments)}removeFormat(t,e,s){return[t,e,,s]=Y(t,e,s),j.call(this,()=>this.editor.removeFormat(t,e),s,t)}scrollRectIntoView(t){qn(this.root,t)}scrollIntoView(){console.warn("Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead."),this.scrollSelectionIntoView()}scrollSelectionIntoView(){const t=this.selection.lastRange,e=t&&this.selection.getBounds(t.index,t.length);e&&this.scrollRectIntoView(e)}setContents(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:q.sources.API;return j.call(this,()=>{t=new w(t);const s=this.getLength(),n=this.editor.deleteText(0,s),r=this.editor.insertContents(0,t),l=this.editor.deleteText(this.getLength()-1,1);return n.compose(r).compose(l)},e)}setSelection(t,e,s){t==null?this.selection.setRange(null,e||P.sources.API):([t,e,,s]=Y(t,e,s),this.selection.setRange(new rt(Math.max(0,t),e),s),s!==q.sources.SILENT&&this.scrollSelectionIntoView())}setText(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:q.sources.API;const s=new w().insert(t);return this.setContents(s,e)}update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:q.sources.USER;const e=this.scroll.update(t);return this.selection.update(t),e}updateContents(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:q.sources.API;return j.call(this,()=>(t=new w(t),this.editor.applyDelta(t)),e,!0)}};v(P,"DEFAULTS",{bounds:null,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0},placeholder:"",readOnly:!1,registry:It,theme:"default"}),v(P,"events",q.events),v(P,"sources",q.sources),v(P,"version","2.0.3"),v(P,"imports",{delta:w,parchment:Gs,"core/module":Z,"core/theme":gt});let g=P;function je(i){return typeof i=="string"?document.querySelector(i):i}function Xt(i){return Object.entries(i??{}).reduce((t,e)=>{let[s,n]=e;return{...t,[s]:n===!0?{}:n}},{})}function Pe(i){return Object.fromEntries(Object.entries(i).filter(t=>t[1]!==void 0))}function An(i,t){const e=je(i);if(!e)throw new Error("Invalid Quill container");const n=!t.theme||t.theme===g.DEFAULTS.theme?gt:g.import(`themes/${t.theme}`);if(!n)throw new Error(`Invalid theme ${t.theme}. Did you register it?`);const{modules:r,...l}=g.DEFAULTS,{modules:o,...a}=n.DEFAULTS;let c=Xt(t.modules);c!=null&&c.toolbar&&c.toolbar.constructor!==Object&&(c={...c,toolbar:{container:c.toolbar}});const f=it({},Xt(r),Xt(o),c),m={...l,...Pe(a),...Pe(t)};let u=t.registry;return u?t.formats&&ft.warn('Ignoring "formats" option because "registry" is specified'):u=t.formats?Nn(t.formats,m.registry,ft):m.registry,{...m,registry:u,container:e,theme:n,modules:Object.entries(f).reduce((h,d)=>{let[p,y]=d;if(!y)return h;const b=g.import(`modules/${p}`);return b==null?(ft.error(`Cannot load ${p} module. Are you sure you registered it?`),h):{...h,[p]:it({},b.DEFAULTS||{},y)}},{}),bounds:je(m.bounds)}}function j(i,t,e,s){if(!this.isEnabled()&&t===q.sources.USER&&!this.allowReadOnlyEdits)return new w;let n=e==null?null:this.getSelection();const r=this.editor.delta,l=i();if(n!=null&&(e===!0&&(e=n.index),s==null?n=_e(n,l,t):s!==0&&(n=_e(n,e,s,t)),this.setSelection(n,q.sources.SILENT)),l.length()>0){const o=[q.events.TEXT_CHANGE,l,r,t];this.emitter.emit(q.events.EDITOR_CHANGE,...o),t!==q.sources.SILENT&&this.emitter.emit(...o)}return l}function Y(i,t,e,s,n){let r={};return typeof i.index=="number"&&typeof i.length=="number"?typeof t!="number"?(n=s,s=e,e=t,t=i.length,i=i.index):(t=i.length,i=i.index):typeof t!="number"&&(n=s,s=e,e=t,t=0),typeof e=="object"?(r=e,n=s):typeof e=="string"&&(s!=null?r[e]=s:n=e),n=n||q.sources.API,[i,t,r,n]}function _e(i,t,e,s){const n=typeof e=="number"?e:0;if(i==null)return null;let r,l;return t&&typeof t.transformPosition=="function"?[r,l]=[i.index,i.index+i.length].map(o=>t.transformPosition(o,s!==q.sources.USER)):[r,l]=[i.index,i.index+i.length].map(o=>o<t||o===t&&s===q.sources.USER?o:n>=0?o+n:Math.max(t,o+n)),new rt(r,l-r)}class lt extends as{}function ze(i){return i instanceof C||i instanceof H}function Ve(i){return typeof i.updateContent=="function"}class ut extends os{constructor(t,e,s){let{emitter:n}=s;super(t,e),this.emitter=n,this.batch=!1,this.optimize(),this.enable(),this.domNode.addEventListener("dragstart",r=>this.handleDragStart(r))}batchStart(){Array.isArray(this.batch)||(this.batch=[])}batchEnd(){if(!this.batch)return;const t=this.batch;this.batch=!1,this.update(t)}emitMount(t){this.emitter.emit(q.events.SCROLL_BLOT_MOUNT,t)}emitUnmount(t){this.emitter.emit(q.events.SCROLL_BLOT_UNMOUNT,t)}emitEmbedUpdate(t,e){this.emitter.emit(q.events.SCROLL_EMBED_UPDATE,t,e)}deleteAt(t,e){const[s,n]=this.line(t),[r]=this.line(t+e);if(super.deleteAt(t,e),r!=null&&s!==r&&n>0){if(s instanceof H||r instanceof H){this.optimize();return}const l=r.children.head instanceof K?null:r.children.head;s.moveChildren(r,l),s.remove()}this.optimize()}enable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",t?"true":"false")}formatAt(t,e,s,n){super.formatAt(t,e,s,n),this.optimize()}insertAt(t,e,s){if(t>=this.length())if(s==null||this.scroll.query(e,L.BLOCK)==null){const n=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(n),s==null&&e.endsWith(`
`)?n.insertAt(0,e.slice(0,-1),s):n.insertAt(0,e,s)}else{const n=this.scroll.create(e,s);this.appendChild(n)}else super.insertAt(t,e,s);this.optimize()}insertBefore(t,e){if(t.statics.scope===L.INLINE_BLOT){const s=this.scroll.create(this.statics.defaultChild.blotName);s.appendChild(t),super.insertBefore(s,e)}else super.insertBefore(t,e)}insertContents(t,e){const s=this.deltaToRenderBlocks(e.concat(new w().insert(`
`))),n=s.pop();if(n==null)return;this.batchStart();const r=s.shift();if(r){const a=r.type==="block"&&(r.delta.length()===0||!this.descendant(H,t)[0]&&t<this.length()),c=r.type==="block"?r.delta:new w().insert({[r.key]:r.value});Yt(this,t,c);const f=r.type==="block"?1:0,m=t+c.length()+f;a&&this.insertAt(m-1,`
`);const u=D(this.line(t)[0]),h=F.AttributeMap.diff(u,r.attributes)||{};Object.keys(h).forEach(d=>{this.formatAt(m-1,1,d,h[d])}),t=m}let[l,o]=this.children.find(t);if(s.length&&(l&&(l=l.split(o),o=0),s.forEach(a=>{if(a.type==="block"){const c=this.createBlock(a.attributes,l||void 0);Yt(c,0,a.delta)}else{const c=this.create(a.key,a.value);this.insertBefore(c,l||void 0),Object.keys(a.attributes).forEach(f=>{c.format(f,a.attributes[f])})}})),n.type==="block"&&n.delta.length()){const a=l?l.offset(l.scroll)+o:this.length();Yt(this,a,n.delta)}this.batchEnd(),this.optimize()}isEnabled(){return this.domNode.getAttribute("contenteditable")==="true"}leaf(t){const e=this.path(t).pop();if(!e)return[null,-1];const[s,n]=e;return s instanceof _?[s,n]:[null,-1]}line(t){return t===this.length()?this.line(t-1):this.descendant(ze,t)}lines(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;const s=(n,r,l)=>{let o=[],a=l;return n.children.forEachAt(r,l,(c,f,m)=>{ze(c)?o.push(c):c instanceof as&&(o=o.concat(s(c,f,a))),a-=m}),o};return s(this,t,e)}optimize(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch||(super.optimize(t,e),t.length>0&&this.emitter.emit(q.events.SCROLL_OPTIMIZE,t,e))}path(t){return super.path(t).slice(1)}remove(){}update(t){if(this.batch){Array.isArray(t)&&(this.batch=this.batch.concat(t));return}let e=q.sources.USER;typeof t=="string"&&(e=t),Array.isArray(t)||(t=this.observer.takeRecords()),t=t.filter(s=>{let{target:n}=s;const r=this.find(n,!0);return r&&!Ve(r)}),t.length>0&&this.emitter.emit(q.events.SCROLL_BEFORE_UPDATE,e,t),super.update(t.concat([])),t.length>0&&this.emitter.emit(q.events.SCROLL_UPDATE,e,t)}updateEmbedAt(t,e,s){const[n]=this.descendant(r=>r instanceof H,t);n&&n.statics.blotName===e&&Ve(n)&&n.updateContent(s)}handleDragStart(t){t.preventDefault()}deltaToRenderBlocks(t){const e=[];let s=new w;return t.forEach(n=>{const r=n==null?void 0:n.insert;if(r)if(typeof r=="string"){const l=r.split(`
`);l.slice(0,-1).forEach(a=>{s.insert(a,n.attributes),e.push({type:"block",delta:s,attributes:n.attributes??{}}),s=new w});const o=l[l.length-1];o&&s.insert(o,n.attributes)}else{const l=Object.keys(r)[0];if(!l)return;this.query(l,L.INLINE)?s.push(n):(s.length()&&e.push({type:"block",delta:s,attributes:{}}),s=new w,e.push({type:"blockEmbed",key:l,value:r[l],attributes:n.attributes??{}}))}}),s.length()&&e.push({type:"block",delta:s,attributes:{}}),e}createBlock(t,e){let s;const n={};Object.entries(t).forEach(o=>{let[a,c]=o;this.query(a,L.BLOCK&L.BLOT)!=null?s=a:n[a]=c});const r=this.create(s||this.statics.defaultChild.blotName,s?t[s]:void 0);this.insertBefore(r,e||void 0);const l=r.length();return Object.entries(n).forEach(o=>{let[a,c]=o;r.formatAt(0,l,a,c)}),r}}v(ut,"blotName","scroll"),v(ut,"className","ql-editor"),v(ut,"tagName","DIV"),v(ut,"defaultChild",C),v(ut,"allowedChildren",[C,H,lt]);function Yt(i,t,e){e.reduce((s,n)=>{const r=F.Op.length(n);let l=n.attributes||{};if(n.insert!=null){if(typeof n.insert=="string"){const o=n.insert;i.insertAt(s,o);const[a]=i.descendant(_,s),c=D(a);l=F.AttributeMap.diff(c,l)||{}}else if(typeof n.insert=="object"){const o=Object.keys(n.insert)[0];if(o==null)return s;if(i.insertAt(s,o,n.insert[o]),i.scroll.query(o,L.INLINE)!=null){const[c]=i.descendant(_,s),f=D(c);l=F.AttributeMap.diff(f,l)||{}}}}return Object.keys(l).forEach(o=>{i.formatAt(s,r,o,l[o])}),s+r},t)}const we={scope:L.BLOCK,whitelist:["right","center","justify"]},kn=new me("align","align",we),bs=new tt("align","ql-align",we),ys=new pt("align","text-align",we);class vs extends pt{value(t){let e=super.value(t);return e.startsWith("rgb(")?(e=e.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),`#${e.split(",").map(n=>`00${parseInt(n,10).toString(16)}`.slice(-2)).join("")}`):e}}const Ln=new tt("color","ql-color",{scope:L.INLINE}),xe=new vs("color","color",{scope:L.INLINE}),Sn=new tt("background","ql-bg",{scope:L.INLINE}),Ne=new vs("background","background-color",{scope:L.INLINE});class ot extends lt{static create(t){const e=super.create(t);return e.setAttribute("spellcheck","false"),e}code(t,e){return this.children.map(s=>s.length()<=1?"":s.domNode.innerText).join(`
`).slice(t,t+e)}html(t,e){return`<pre>
${Pt(this.code(t,e))}
</pre>`}}class O extends C{static register(){g.register(ot)}}v(O,"TAB","  ");class Ae extends X{}Ae.blotName="code";Ae.tagName="CODE";O.blotName="code-block";O.className="ql-code-block";O.tagName="DIV";ot.blotName="code-block-container";ot.className="ql-code-block-container";ot.tagName="DIV";ot.allowedChildren=[O];O.allowedChildren=[V,K,dt];O.requiredContainer=ot;const ke={scope:L.BLOCK,whitelist:["rtl"]},Es=new me("direction","dir",ke),qs=new tt("direction","ql-direction",ke),ws=new pt("direction","direction",ke),xs={scope:L.INLINE,whitelist:["serif","monospace"]},Ns=new tt("font","ql-font",xs);class Tn extends pt{value(t){return super.value(t).replace(/["']/g,"")}}const As=new Tn("font","font-family",xs),ks=new tt("size","ql-size",{scope:L.INLINE,whitelist:["small","large","huge"]}),Ls=new pt("size","font-size",{scope:L.INLINE,whitelist:["10px","18px","32px"]}),Rn=et("quill:keyboard"),Cn=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey";class _t extends Z{static match(t,e){return["altKey","ctrlKey","metaKey","shiftKey"].some(s=>!!e[s]!==t[s]&&e[s]!==null)?!1:e.key===t.key||e.key===t.which}constructor(t,e){super(t,e),this.bindings={},Object.keys(this.options.bindings).forEach(s=>{this.options.bindings[s]&&this.addBinding(this.options.bindings[s])}),this.addBinding({key:"Enter",shiftKey:null},this.handleEnter),this.addBinding({key:"Enter",metaKey:null,ctrlKey:null,altKey:null},()=>{}),/Firefox/i.test(navigator.userAgent)?(this.addBinding({key:"Backspace"},{collapsed:!0},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0},this.handleDelete)):(this.addBinding({key:"Backspace"},{collapsed:!0,prefix:/^.?$/},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0,suffix:/^.?$/},this.handleDelete)),this.addBinding({key:"Backspace"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Delete"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},this.handleBackspace),this.listen()}addBinding(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const n=In(t);if(n==null){Rn.warn("Attempted to add invalid keyboard binding",n);return}typeof e=="function"&&(e={handler:e}),typeof s=="function"&&(s={handler:s}),(Array.isArray(n.key)?n.key:[n.key]).forEach(l=>{const o={...n,key:l,...e,...s};this.bindings[o.key]=this.bindings[o.key]||[],this.bindings[o.key].push(o)})}listen(){this.quill.root.addEventListener("keydown",t=>{if(t.defaultPrevented||t.isComposing||t.keyCode===229&&(t.key==="Enter"||t.key==="Backspace"))return;const n=(this.bindings[t.key]||[]).concat(this.bindings[t.which]||[]).filter(b=>_t.match(t,b));if(n.length===0)return;const r=g.find(t.target,!0);if(r&&r.scroll!==this.quill.scroll)return;const l=this.quill.getSelection();if(l==null||!this.quill.hasFocus())return;const[o,a]=this.quill.getLine(l.index),[c,f]=this.quill.getLeaf(l.index),[m,u]=l.length===0?[c,f]:this.quill.getLeaf(l.index+l.length),h=c instanceof te?c.value().slice(0,f):"",d=m instanceof te?m.value().slice(u):"",p={collapsed:l.length===0,empty:l.length===0&&o.length()<=1,format:this.quill.getFormat(l),line:o,offset:a,prefix:h,suffix:d,event:t};n.some(b=>{if(b.collapsed!=null&&b.collapsed!==p.collapsed||b.empty!=null&&b.empty!==p.empty||b.offset!=null&&b.offset!==p.offset)return!1;if(Array.isArray(b.format)){if(b.format.every(E=>p.format[E]==null))return!1}else if(typeof b.format=="object"&&!Object.keys(b.format).every(E=>b.format[E]===!0?p.format[E]!=null:b.format[E]===!1?p.format[E]==null:pe(b.format[E],p.format[E])))return!1;return b.prefix!=null&&!b.prefix.test(p.prefix)||b.suffix!=null&&!b.suffix.test(p.suffix)?!1:b.handler.call(this,l,p,b)!==!0})&&t.preventDefault()})}handleBackspace(t,e){const s=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;if(t.index===0||this.quill.getLength()<=1)return;let n={};const[r]=this.quill.getLine(t.index);let l=new w().retain(t.index-s).delete(s);if(e.offset===0){const[o]=this.quill.getLine(t.index-1);if(o&&!(o.statics.blotName==="block"&&o.length()<=1)){const c=r.formats(),f=this.quill.getFormat(t.index-1,1);if(n=F.AttributeMap.diff(c,f)||{},Object.keys(n).length>0){const m=new w().retain(t.index+r.length()-2).retain(1,n);l=l.compose(m)}}}this.quill.updateContents(l,g.sources.USER),this.quill.focus()}handleDelete(t,e){const s=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(t.index>=this.quill.getLength()-s)return;let n={};const[r]=this.quill.getLine(t.index);let l=new w().retain(t.index).delete(s);if(e.offset>=r.length()-1){const[o]=this.quill.getLine(t.index+1);if(o){const a=r.formats(),c=this.quill.getFormat(t.index,1);n=F.AttributeMap.diff(a,c)||{},Object.keys(n).length>0&&(l=l.retain(o.length()-1).retain(1,n))}}this.quill.updateContents(l,g.sources.USER),this.quill.focus()}handleDeleteRange(t){Le({range:t,quill:this.quill}),this.quill.focus()}handleEnter(t,e){const s=Object.keys(e.format).reduce((r,l)=>(this.quill.scroll.query(l,L.BLOCK)&&!Array.isArray(e.format[l])&&(r[l]=e.format[l]),r),{}),n=new w().retain(t.index).delete(t.length).insert(`
`,s);this.quill.updateContents(n,g.sources.USER),this.quill.setSelection(t.index+1,g.sources.SILENT),this.quill.focus()}}const On={bindings:{bold:Qt("bold"),italic:Qt("italic"),underline:Qt("underline"),indent:{key:"Tab",format:["blockquote","indent","list"],handler(i,t){return t.collapsed&&t.offset!==0?!0:(this.quill.format("indent","+1",g.sources.USER),!1)}},outdent:{key:"Tab",shiftKey:!0,format:["blockquote","indent","list"],handler(i,t){return t.collapsed&&t.offset!==0?!0:(this.quill.format("indent","-1",g.sources.USER),!1)}},"outdent backspace":{key:"Backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler(i,t){t.format.indent!=null?this.quill.format("indent","-1",g.sources.USER):t.format.list!=null&&this.quill.format("list",!1,g.sources.USER)}},"indent code-block":Ke(!0),"outdent code-block":Ke(!1),"remove tab":{key:"Tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler(i){this.quill.deleteText(i.index-1,1,g.sources.USER)}},tab:{key:"Tab",handler(i,t){if(t.format.table)return!0;this.quill.history.cutoff();const e=new w().retain(i.index).delete(i.length).insert("	");return this.quill.updateContents(e,g.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(i.index+1,g.sources.SILENT),!1}},"blockquote empty enter":{key:"Enter",collapsed:!0,format:["blockquote"],empty:!0,handler(){this.quill.format("blockquote",!1,g.sources.USER)}},"list empty enter":{key:"Enter",collapsed:!0,format:["list"],empty:!0,handler(i,t){const e={list:!1};t.format.indent&&(e.indent=!1),this.quill.formatLine(i.index,i.length,e,g.sources.USER)}},"checklist enter":{key:"Enter",collapsed:!0,format:{list:"checked"},handler(i){const[t,e]=this.quill.getLine(i.index),s={...t.formats(),list:"checked"},n=new w().retain(i.index).insert(`
`,s).retain(t.length()-e-1).retain(1,{list:"unchecked"});this.quill.updateContents(n,g.sources.USER),this.quill.setSelection(i.index+1,g.sources.SILENT),this.quill.scrollSelectionIntoView()}},"header enter":{key:"Enter",collapsed:!0,format:["header"],suffix:/^$/,handler(i,t){const[e,s]=this.quill.getLine(i.index),n=new w().retain(i.index).insert(`
`,t.format).retain(e.length()-s-1).retain(1,{header:null});this.quill.updateContents(n,g.sources.USER),this.quill.setSelection(i.index+1,g.sources.SILENT),this.quill.scrollSelectionIntoView()}},"table backspace":{key:"Backspace",format:["table"],collapsed:!0,offset:0,handler(){}},"table delete":{key:"Delete",format:["table"],collapsed:!0,suffix:/^$/,handler(){}},"table enter":{key:"Enter",shiftKey:null,format:["table"],handler(i){const t=this.quill.getModule("table");if(t){const[e,s,n,r]=t.getTable(i),l=Mn(e,s,n,r);if(l==null)return;let o=e.offset();if(l<0){const a=new w().retain(o).insert(`
`);this.quill.updateContents(a,g.sources.USER),this.quill.setSelection(i.index+1,i.length,g.sources.SILENT)}else if(l>0){o+=e.length();const a=new w().retain(o).insert(`
`);this.quill.updateContents(a,g.sources.USER),this.quill.setSelection(o,g.sources.USER)}}}},"table tab":{key:"Tab",shiftKey:null,format:["table"],handler(i,t){const{event:e,line:s}=t,n=s.offset(this.quill.scroll);e.shiftKey?this.quill.setSelection(n-1,g.sources.USER):this.quill.setSelection(n+s.length(),g.sources.USER)}},"list autofill":{key:" ",shiftKey:null,collapsed:!0,format:{"code-block":!1,blockquote:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler(i,t){if(this.quill.scroll.query("list")==null)return!0;const{length:e}=t.prefix,[s,n]=this.quill.getLine(i.index);if(n>e)return!0;let r;switch(t.prefix.trim()){case"[]":case"[ ]":r="unchecked";break;case"[x]":r="checked";break;case"-":case"*":r="bullet";break;default:r="ordered"}this.quill.insertText(i.index," ",g.sources.USER),this.quill.history.cutoff();const l=new w().retain(i.index-n).delete(e+1).retain(s.length()-2-n).retain(1,{list:r});return this.quill.updateContents(l,g.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(i.index-e,g.sources.SILENT),!1}},"code exit":{key:"Enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler(i){const[t,e]=this.quill.getLine(i.index);let s=2,n=t;for(;n!=null&&n.length()<=1&&n.formats()["code-block"];)if(n=n.prev,s-=1,s<=0){const r=new w().retain(i.index+t.length()-e-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(r,g.sources.USER),this.quill.setSelection(i.index-1,g.sources.SILENT),!1}return!0}},"embed left":Mt("ArrowLeft",!1),"embed left shift":Mt("ArrowLeft",!0),"embed right":Mt("ArrowRight",!1),"embed right shift":Mt("ArrowRight",!0),"table down":Ze(!1),"table up":Ze(!0)}};_t.DEFAULTS=On;function Ke(i){return{key:"Tab",shiftKey:!i,format:{"code-block":!0},handler(t,e){let{event:s}=e;const n=this.quill.scroll.query("code-block"),{TAB:r}=n;if(t.length===0&&!s.shiftKey){this.quill.insertText(t.index,r,g.sources.USER),this.quill.setSelection(t.index+r.length,g.sources.SILENT);return}const l=t.length===0?this.quill.getLines(t.index,1):this.quill.getLines(t);let{index:o,length:a}=t;l.forEach((c,f)=>{i?(c.insertAt(0,r),f===0?o+=r.length:a+=r.length):c.domNode.textContent.startsWith(r)&&(c.deleteAt(0,r.length),f===0?o-=r.length:a-=r.length)}),this.quill.update(g.sources.USER),this.quill.setSelection(o,a,g.sources.SILENT)}}}function Mt(i,t){return{key:i,shiftKey:t,altKey:null,[i==="ArrowLeft"?"prefix":"suffix"]:/^$/,handler(s){let{index:n}=s;i==="ArrowRight"&&(n+=s.length+1);const[r]=this.quill.getLeaf(n);return r instanceof $?(i==="ArrowLeft"?t?this.quill.setSelection(s.index-1,s.length+1,g.sources.USER):this.quill.setSelection(s.index-1,g.sources.USER):t?this.quill.setSelection(s.index,s.length+1,g.sources.USER):this.quill.setSelection(s.index+s.length+1,g.sources.USER),!1):!0}}}function Qt(i){return{key:i[0],shortKey:!0,handler(t,e){this.quill.format(i,!e.format[i],g.sources.USER)}}}function Ze(i){return{key:i?"ArrowUp":"ArrowDown",collapsed:!0,format:["table"],handler(t,e){const s=i?"prev":"next",n=e.line,r=n.parent[s];if(r!=null){if(r.statics.blotName==="table-row"){let l=r.children.head,o=n;for(;o.prev!=null;)o=o.prev,l=l.next;const a=l.offset(this.quill.scroll)+Math.min(e.offset,l.length()-1);this.quill.setSelection(a,0,g.sources.USER)}}else{const l=n.table()[s];l!=null&&(i?this.quill.setSelection(l.offset(this.quill.scroll)+l.length()-1,0,g.sources.USER):this.quill.setSelection(l.offset(this.quill.scroll),0,g.sources.USER))}return!1}}}function In(i){if(typeof i=="string"||typeof i=="number")i={key:i};else if(typeof i=="object")i=ht(i);else return null;return i.shortKey&&(i[Cn]=i.shortKey,delete i.shortKey),i}function Le(i){let{quill:t,range:e}=i;const s=t.getLines(e);let n={};if(s.length>1){const r=s[0].formats(),l=s[s.length-1].formats();n=F.AttributeMap.diff(l,r)||{}}t.deleteText(e,g.sources.USER),Object.keys(n).length>0&&t.formatLine(e.index,1,n,g.sources.USER),t.setSelection(e.index,g.sources.SILENT)}function Mn(i,t,e,s){return t.prev==null&&t.next==null?e.prev==null&&e.next==null?s===0?-1:1:e.prev==null?-1:1:t.prev==null?-1:t.next==null?1:null}const Bn=/font-weight:\s*normal/,Dn=["P","OL","UL"],Ge=i=>i&&Dn.includes(i.tagName),Un=i=>{Array.from(i.querySelectorAll("br")).filter(t=>Ge(t.previousElementSibling)&&Ge(t.nextElementSibling)).forEach(t=>{var e;(e=t.parentNode)==null||e.removeChild(t)})},Hn=i=>{Array.from(i.querySelectorAll('b[style*="font-weight"]')).filter(t=>{var e;return(e=t.getAttribute("style"))==null?void 0:e.match(Bn)}).forEach(t=>{var s;const e=i.createDocumentFragment();e.append(...t.childNodes),(s=t.parentNode)==null||s.replaceChild(e,t)})};function Fn(i){i.querySelector('[id^="docs-internal-guid-"]')&&(Hn(i),Un(i))}const $n=/\bmso-list:[^;]*ignore/i,jn=/\bmso-list:[^;]*\bl(\d+)/i,Pn=/\bmso-list:[^;]*\blevel(\d+)/i,_n=(i,t)=>{const e=i.getAttribute("style"),s=e==null?void 0:e.match(jn);if(!s)return null;const n=Number(s[1]),r=e==null?void 0:e.match(Pn),l=r?Number(r[1]):1,o=new RegExp(`@list l${n}:level${l}\\s*\\{[^\\}]*mso-level-number-format:\\s*([\\w-]+)`,"i"),a=t.match(o),c=a&&a[1]==="bullet"?"bullet":"ordered";return{id:n,indent:l,type:c,element:i}},zn=i=>{var l,o;const t=Array.from(i.querySelectorAll("[style*=mso-list]")),e=[],s=[];t.forEach(a=>{(a.getAttribute("style")||"").match($n)?e.push(a):s.push(a)}),e.forEach(a=>{var c;return(c=a.parentNode)==null?void 0:c.removeChild(a)});const n=i.documentElement.innerHTML,r=s.map(a=>_n(a,n)).filter(a=>a);for(;r.length;){const a=[];let c=r.shift();for(;c;)a.push(c),c=r.length&&((l=r[0])==null?void 0:l.element)===c.element.nextElementSibling&&r[0].id===c.id?r.shift():null;const f=document.createElement("ul");a.forEach(h=>{const d=document.createElement("li");d.setAttribute("data-list",h.type),h.indent>1&&d.setAttribute("class",`ql-indent-${h.indent-1}`),d.innerHTML=h.element.innerHTML,f.appendChild(d)});const m=(o=a[0])==null?void 0:o.element,{parentNode:u}=m??{};m&&(u==null||u.replaceChild(f,m)),a.slice(1).forEach(h=>{let{element:d}=h;u==null||u.removeChild(d)})}};function Vn(i){i.documentElement.getAttribute("xmlns:w")==="urn:schemas-microsoft-com:office:word"&&zn(i)}const Kn=[Vn,Fn],Zn=i=>{i.documentElement&&Kn.forEach(t=>{t(i)})},Gn=et("quill:clipboard"),Wn=[[Node.TEXT_NODE,oi],[Node.TEXT_NODE,Xe],["br",ti],[Node.ELEMENT_NODE,Xe],[Node.ELEMENT_NODE,Jn],[Node.ELEMENT_NODE,Qn],[Node.ELEMENT_NODE,ri],["li",ni],["ol, ul",ii],["pre",ei],["tr",li],["b",Jt("bold")],["i",Jt("italic")],["strike",Jt("strike")],["style",si]],Xn=[kn,Es].reduce((i,t)=>(i[t.keyName]=t,i),{}),We=[ys,Ne,xe,ws,As,Ls].reduce((i,t)=>(i[t.keyName]=t,i),{});class Ss extends Z{constructor(t,e){super(t,e),this.quill.root.addEventListener("copy",s=>this.onCaptureCopy(s,!1)),this.quill.root.addEventListener("cut",s=>this.onCaptureCopy(s,!0)),this.quill.root.addEventListener("paste",this.onCapturePaste.bind(this)),this.matchers=[],Wn.concat(this.options.matchers??[]).forEach(s=>{let[n,r]=s;this.addMatcher(n,r)})}addMatcher(t,e){this.matchers.push([t,e])}convert(t){let{html:e,text:s}=t,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(n[O.blotName])return new w().insert(s||"",{[O.blotName]:n[O.blotName]});if(!e)return new w().insert(s||"",n);const r=this.convertHTML(e);return kt(r,`
`)&&(r.ops[r.ops.length-1].attributes==null||n.table)?r.compose(new w().retain(r.length()-1).delete(1)):r}normalizeHTML(t){Zn(t)}convertHTML(t){const e=new DOMParser().parseFromString(t,"text/html");this.normalizeHTML(e);const s=e.body,n=new WeakMap,[r,l]=this.prepareMatching(s,n);return Se(this.quill.scroll,s,r,l,n)}dangerouslyPasteHTML(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:g.sources.API;if(typeof t=="string"){const n=this.convert({html:t,text:""});this.quill.setContents(n,e),this.quill.setSelection(0,g.sources.SILENT)}else{const n=this.convert({html:e,text:""});this.quill.updateContents(new w().retain(t).concat(n),s),this.quill.setSelection(t+n.length(),g.sources.SILENT)}}onCaptureCopy(t){var l,o;let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(t.defaultPrevented)return;t.preventDefault();const[s]=this.quill.selection.getRange();if(s==null)return;const{html:n,text:r}=this.onCopy(s,e);(l=t.clipboardData)==null||l.setData("text/plain",r),(o=t.clipboardData)==null||o.setData("text/html",n),e&&Le({range:s,quill:this.quill})}normalizeURIList(t){return t.split(/\r?\n/).filter(e=>e[0]!=="#").join(`
`)}onCapturePaste(t){var l,o,a,c,f;if(t.defaultPrevented||!this.quill.isEnabled())return;t.preventDefault();const e=this.quill.getSelection(!0);if(e==null)return;const s=(l=t.clipboardData)==null?void 0:l.getData("text/html");let n=(o=t.clipboardData)==null?void 0:o.getData("text/plain");if(!s&&!n){const m=(a=t.clipboardData)==null?void 0:a.getData("text/uri-list");m&&(n=this.normalizeURIList(m))}const r=Array.from(((c=t.clipboardData)==null?void 0:c.files)||[]);if(!s&&r.length>0){this.quill.uploader.upload(e,r);return}if(s&&r.length>0){const m=new DOMParser().parseFromString(s,"text/html");if(m.body.childElementCount===1&&((f=m.body.firstElementChild)==null?void 0:f.tagName)==="IMG"){this.quill.uploader.upload(e,r);return}}this.onPaste(e,{html:s,text:n})}onCopy(t){const e=this.quill.getText(t);return{html:this.quill.getSemanticHTML(t),text:e}}onPaste(t,e){let{text:s,html:n}=e;const r=this.quill.getFormat(t.index),l=this.convert({text:s,html:n},r);Gn.log("onPaste",l,{text:s,html:n});const o=new w().retain(t.index).delete(t.length).concat(l);this.quill.updateContents(o,g.sources.USER),this.quill.setSelection(o.length()-t.length,g.sources.SILENT),this.quill.scrollSelectionIntoView()}prepareMatching(t,e){const s=[],n=[];return this.matchers.forEach(r=>{const[l,o]=r;switch(l){case Node.TEXT_NODE:n.push(o);break;case Node.ELEMENT_NODE:s.push(o);break;default:Array.from(t.querySelectorAll(l)).forEach(a=>{if(e.has(a)){const c=e.get(a);c==null||c.push(o)}else e.set(a,[o])});break}}),[s,n]}}v(Ss,"DEFAULTS",{matchers:[]});function at(i,t,e,s){return s.query(t)?i.reduce((n,r)=>{if(!r.insert)return n;if(r.attributes&&r.attributes[t])return n.push(r);const l=e?{[t]:e}:{};return n.insert(r.insert,{...l,...r.attributes})},new w):i}function kt(i,t){let e="";for(let s=i.ops.length-1;s>=0&&e.length<t.length;--s){const n=i.ops[s];if(typeof n.insert!="string")break;e=n.insert+e}return e.slice(-1*t.length)===t}function st(i,t){if(!(i instanceof Element))return!1;const e=t.query(i);return e&&e.prototype instanceof $?!1:["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].includes(i.tagName.toLowerCase())}function Yn(i,t){return i.previousElementSibling&&i.nextElementSibling&&!st(i.previousElementSibling,t)&&!st(i.nextElementSibling,t)}const Bt=new WeakMap;function Ts(i){return i==null?!1:(Bt.has(i)||(i.tagName==="PRE"?Bt.set(i,!0):Bt.set(i,Ts(i.parentNode))),Bt.get(i))}function Se(i,t,e,s,n){return t.nodeType===t.TEXT_NODE?s.reduce((r,l)=>l(t,r,i),new w):t.nodeType===t.ELEMENT_NODE?Array.from(t.childNodes||[]).reduce((r,l)=>{let o=Se(i,l,e,s,n);return l.nodeType===t.ELEMENT_NODE&&(o=e.reduce((a,c)=>c(l,a,i),o),o=(n.get(l)||[]).reduce((a,c)=>c(l,a,i),o)),r.concat(o)},new w):new w}function Jt(i){return(t,e,s)=>at(e,i,!0,s)}function Qn(i,t,e){const s=me.keys(i),n=tt.keys(i),r=pt.keys(i),l={};return s.concat(n).concat(r).forEach(o=>{let a=e.query(o,L.ATTRIBUTE);a!=null&&(l[a.attrName]=a.value(i),l[a.attrName])||(a=Xn[o],a!=null&&(a.attrName===o||a.keyName===o)&&(l[a.attrName]=a.value(i)||void 0),a=We[o],a!=null&&(a.attrName===o||a.keyName===o)&&(a=We[o],l[a.attrName]=a.value(i)||void 0))}),Object.entries(l).reduce((o,a)=>{let[c,f]=a;return at(o,c,f,e)},t)}function Jn(i,t,e){const s=e.query(i);if(s==null)return t;if(s.prototype instanceof $){const n={},r=s.value(i);if(r!=null)return n[s.blotName]=r,new w().insert(n,s.formats(i,e))}else if(s.prototype instanceof rs&&!kt(t,`
`)&&t.insert(`
`),"blotName"in s&&"formats"in s&&typeof s.formats=="function")return at(t,s.blotName,s.formats(i,e),e);return t}function ti(i,t){return kt(t,`
`)||t.insert(`
`),t}function ei(i,t,e){const s=e.query("code-block"),n=s&&"formats"in s&&typeof s.formats=="function"?s.formats(i,e):!0;return at(t,"code-block",n,e)}function si(){return new w}function ni(i,t,e){const s=e.query(i);if(s==null||s.blotName!=="list"||!kt(t,`
`))return t;let n=-1,r=i.parentNode;for(;r!=null;)["OL","UL"].includes(r.tagName)&&(n+=1),r=r.parentNode;return n<=0?t:t.reduce((l,o)=>o.insert?o.attributes&&typeof o.attributes.indent=="number"?l.push(o):l.insert(o.insert,{indent:n,...o.attributes||{}}):l,new w)}function ii(i,t,e){const s=i;let n=s.tagName==="OL"?"ordered":"bullet";const r=s.getAttribute("data-checked");return r&&(n=r==="true"?"checked":"unchecked"),at(t,"list",n,e)}function Xe(i,t,e){if(!kt(t,`
`)){if(st(i,e)&&(i.childNodes.length>0||i instanceof HTMLParagraphElement))return t.insert(`
`);if(t.length()>0&&i.nextSibling){let s=i.nextSibling;for(;s!=null;){if(st(s,e))return t.insert(`
`);const n=e.query(s);if(n&&n.prototype instanceof H)return t.insert(`
`);s=s.firstChild}}}return t}function ri(i,t,e){var r;const s={},n=i.style||{};return n.fontStyle==="italic"&&(s.italic=!0),n.textDecoration==="underline"&&(s.underline=!0),n.textDecoration==="line-through"&&(s.strike=!0),((r=n.fontWeight)!=null&&r.startsWith("bold")||parseInt(n.fontWeight,10)>=700)&&(s.bold=!0),t=Object.entries(s).reduce((l,o)=>{let[a,c]=o;return at(l,a,c,e)},t),parseFloat(n.textIndent||0)>0?new w().insert("	").concat(t):t}function li(i,t,e){var n,r;const s=((n=i.parentElement)==null?void 0:n.tagName)==="TABLE"?i.parentElement:(r=i.parentElement)==null?void 0:r.parentElement;if(s!=null){const o=Array.from(s.querySelectorAll("tr")).indexOf(i)+1;return at(t,"table",o,e)}return t}function oi(i,t,e){var n;let s=i.data;if(((n=i.parentElement)==null?void 0:n.tagName)==="O:P")return t.insert(s.trim());if(!Ts(i)){if(s.trim().length===0&&s.includes(`
`)&&!Yn(i,e))return t;s=s.replace(/[^\S\u00a0]/g," "),s=s.replace(/ {2,}/g," "),(i.previousSibling==null&&i.parentElement!=null&&st(i.parentElement,e)||i.previousSibling instanceof Element&&st(i.previousSibling,e))&&(s=s.replace(/^ /,"")),(i.nextSibling==null&&i.parentElement!=null&&st(i.parentElement,e)||i.nextSibling instanceof Element&&st(i.nextSibling,e))&&(s=s.replace(/ $/,"")),s=s.replaceAll(" "," ")}return t.insert(s)}class Rs extends Z{constructor(e,s){super(e,s);v(this,"lastRecorded",0);v(this,"ignoreChange",!1);v(this,"stack",{undo:[],redo:[]});v(this,"currentRange",null);this.quill.on(g.events.EDITOR_CHANGE,(n,r,l,o)=>{n===g.events.SELECTION_CHANGE?r&&o!==g.sources.SILENT&&(this.currentRange=r):n===g.events.TEXT_CHANGE&&(this.ignoreChange||(!this.options.userOnly||o===g.sources.USER?this.record(r,l):this.transform(r)),this.currentRange=oe(this.currentRange,r))}),this.quill.keyboard.addBinding({key:"z",shortKey:!0},this.undo.bind(this)),this.quill.keyboard.addBinding({key:["z","Z"],shortKey:!0,shiftKey:!0},this.redo.bind(this)),/Win/i.test(navigator.platform)&&this.quill.keyboard.addBinding({key:"y",shortKey:!0},this.redo.bind(this)),this.quill.root.addEventListener("beforeinput",n=>{n.inputType==="historyUndo"?(this.undo(),n.preventDefault()):n.inputType==="historyRedo"&&(this.redo(),n.preventDefault())})}change(e,s){if(this.stack[e].length===0)return;const n=this.stack[e].pop();if(!n)return;const r=this.quill.getContents(),l=n.delta.invert(r);this.stack[s].push({delta:l,range:oe(n.range,l)}),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(n.delta,g.sources.USER),this.ignoreChange=!1,this.restoreSelection(n)}clear(){this.stack={undo:[],redo:[]}}cutoff(){this.lastRecorded=0}record(e,s){if(e.ops.length===0)return;this.stack.redo=[];let n=e.invert(s),r=this.currentRange;const l=Date.now();if(this.lastRecorded+this.options.delay>l&&this.stack.undo.length>0){const o=this.stack.undo.pop();o&&(n=n.compose(o.delta),r=o.range)}else this.lastRecorded=l;n.length()!==0&&(this.stack.undo.push({delta:n,range:r}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}redo(){this.change("redo","undo")}transform(e){Ye(this.stack.undo,e),Ye(this.stack.redo,e)}undo(){this.change("undo","redo")}restoreSelection(e){if(e.range)this.quill.setSelection(e.range,g.sources.USER);else{const s=ci(this.quill.scroll,e.delta);this.quill.setSelection(s,g.sources.USER)}}}v(Rs,"DEFAULTS",{delay:1e3,maxStack:100,userOnly:!1});function Ye(i,t){let e=t;for(let s=i.length-1;s>=0;s-=1){const n=i[s];i[s]={delta:e.transform(n.delta,!0),range:n.range&&oe(n.range,e)},e=n.delta.transform(e),i[s].delta.length()===0&&i.splice(s,1)}}function ai(i,t){const e=t.ops[t.ops.length-1];return e==null?!1:e.insert!=null?typeof e.insert=="string"&&e.insert.endsWith(`
`):e.attributes!=null?Object.keys(e.attributes).some(s=>i.query(s,L.BLOCK)!=null):!1}function ci(i,t){const e=t.reduce((n,r)=>n+(r.delete||0),0);let s=t.length()-e;return ai(i,t)&&(s-=1),s}function oe(i,t){if(!i)return i;const e=t.transformPosition(i.index),s=t.transformPosition(i.index+i.length);return{index:e,length:s-e}}class Cs extends Z{constructor(t,e){super(t,e),t.root.addEventListener("drop",s=>{var l;s.preventDefault();let n=null;if(document.caretRangeFromPoint)n=document.caretRangeFromPoint(s.clientX,s.clientY);else if(document.caretPositionFromPoint){const o=document.caretPositionFromPoint(s.clientX,s.clientY);n=document.createRange(),n.setStart(o.offsetNode,o.offset),n.setEnd(o.offsetNode,o.offset)}const r=n&&t.selection.normalizeNative(n);if(r){const o=t.selection.normalizedToRange(r);(l=s.dataTransfer)!=null&&l.files&&this.upload(o,s.dataTransfer.files)}})}upload(t,e){const s=[];Array.from(e).forEach(n=>{var r;n&&((r=this.options.mimetypes)!=null&&r.includes(n.type))&&s.push(n)}),s.length>0&&this.options.handler.call(this,t,s)}}Cs.DEFAULTS={mimetypes:["image/png","image/jpeg"],handler(i,t){if(!this.quill.scroll.query("image"))return;const e=t.map(s=>new Promise(n=>{const r=new FileReader;r.onload=()=>{n(r.result)},r.readAsDataURL(s)}));Promise.all(e).then(s=>{const n=s.reduce((r,l)=>r.insert({image:l}),new w().retain(i.index).delete(i.length));this.quill.updateContents(n,q.sources.USER),this.quill.setSelection(i.index+s.length,q.sources.SILENT)})}};const ui=["insertText","insertReplacementText"];class hi extends Z{constructor(t,e){super(t,e),t.root.addEventListener("beforeinput",s=>{this.handleBeforeInput(s)}),/Android/i.test(navigator.userAgent)||t.on(g.events.COMPOSITION_BEFORE_START,()=>{this.handleCompositionStart()})}deleteRange(t){Le({range:t,quill:this.quill})}replaceText(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";if(t.length===0)return!1;if(e){const s=this.quill.getFormat(t.index,1);this.deleteRange(t),this.quill.updateContents(new w().retain(t.index).insert(e,s),g.sources.USER)}else this.deleteRange(t);return this.quill.setSelection(t.index+e.length,0,g.sources.SILENT),!0}handleBeforeInput(t){if(this.quill.composition.isComposing||t.defaultPrevented||!ui.includes(t.inputType))return;const e=t.getTargetRanges?t.getTargetRanges()[0]:null;if(!e||e.collapsed===!0)return;const s=fi(t);if(s==null)return;const n=this.quill.selection.normalizeNative(e),r=n?this.quill.selection.normalizedToRange(n):null;r&&this.replaceText(r,s)&&t.preventDefault()}handleCompositionStart(){const t=this.quill.getSelection();t&&this.replaceText(t)}}function fi(i){var t;return typeof i.data=="string"?i.data:(t=i.dataTransfer)!=null&&t.types.includes("text/plain")?i.dataTransfer.getData("text/plain"):null}const di=/Mac/i.test(navigator.platform),gi=100,mi=i=>!!(i.key==="ArrowLeft"||i.key==="ArrowRight"||i.key==="ArrowUp"||i.key==="ArrowDown"||i.key==="Home"||di&&i.key==="a"&&i.ctrlKey===!0);class pi extends Z{constructor(e,s){super(e,s);v(this,"isListening",!1);v(this,"selectionChangeDeadline",0);this.handleArrowKeys(),this.handleNavigationShortcuts()}handleArrowKeys(){this.quill.keyboard.addBinding({key:["ArrowLeft","ArrowRight"],offset:0,shiftKey:null,handler(e,s){let{line:n,event:r}=s;if(!(n instanceof Ht)||!n.uiNode)return!0;const l=getComputedStyle(n.domNode).direction==="rtl";return l&&r.key!=="ArrowRight"||!l&&r.key!=="ArrowLeft"?!0:(this.quill.setSelection(e.index-1,e.length+(r.shiftKey?1:0),g.sources.USER),!1)}})}handleNavigationShortcuts(){this.quill.root.addEventListener("keydown",e=>{!e.defaultPrevented&&mi(e)&&this.ensureListeningToSelectionChange()})}ensureListeningToSelectionChange(){if(this.selectionChangeDeadline=Date.now()+gi,this.isListening)return;this.isListening=!0;const e=()=>{this.isListening=!1,Date.now()<=this.selectionChangeDeadline&&this.handleSelectionChange()};document.addEventListener("selectionchange",e,{once:!0})}handleSelectionChange(){const e=document.getSelection();if(!e)return;const s=e.getRangeAt(0);if(s.collapsed!==!0||s.startOffset!==0)return;const n=this.quill.scroll.find(s.startContainer);if(!(n instanceof Ht)||!n.uiNode)return;const r=document.createRange();r.setStartAfter(n.uiNode),r.setEndAfter(n.uiNode),e.removeAllRanges(),e.addRange(r)}}g.register({"blots/block":C,"blots/block/embed":H,"blots/break":K,"blots/container":lt,"blots/cursor":dt,"blots/embed":qe,"blots/inline":X,"blots/scroll":ut,"blots/text":V,"modules/clipboard":Ss,"modules/history":Rs,"modules/keyboard":_t,"modules/uploader":Cs,"modules/input":hi,"modules/uiNode":pi});class bi extends tt{add(t,e){let s=0;if(e==="+1"||e==="-1"){const n=this.value(t)||0;s=e==="+1"?n+1:n-1}else typeof e=="number"&&(s=e);return s===0?(this.remove(t),!0):super.add(t,s.toString())}canAdd(t,e){return super.canAdd(t,e)||super.canAdd(t,parseInt(e,10))}value(t){return parseInt(super.value(t),10)||void 0}}const yi=new bi("indent","ql-indent",{scope:L.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});class ae extends C{}v(ae,"blotName","blockquote"),v(ae,"tagName","blockquote");class ce extends C{static formats(t){return this.tagName.indexOf(t.tagName)+1}}v(ce,"blotName","header"),v(ce,"tagName",["H1","H2","H3","H4","H5","H6"]);class Lt extends lt{}Lt.blotName="list-container";Lt.tagName="OL";class St extends C{static create(t){const e=super.create();return e.setAttribute("data-list",t),e}static formats(t){return t.getAttribute("data-list")||void 0}static register(){g.register(Lt)}constructor(t,e){super(t,e);const s=e.ownerDocument.createElement("span"),n=r=>{if(!t.isEnabled())return;const l=this.statics.formats(e,t);l==="checked"?(this.format("list","unchecked"),r.preventDefault()):l==="unchecked"&&(this.format("list","checked"),r.preventDefault())};s.addEventListener("mousedown",n),s.addEventListener("touchstart",n),this.attachUI(s)}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-list",e):super.format(t,e)}}St.blotName="list";St.tagName="LI";Lt.allowedChildren=[St];St.requiredContainer=Lt;class Nt extends X{static create(){return super.create()}static formats(){return!0}optimize(t){super.optimize(t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}v(Nt,"blotName","bold"),v(Nt,"tagName",["STRONG","B"]);class ue extends Nt{}v(ue,"blotName","italic"),v(ue,"tagName",["EM","I"]);class nt extends X{static create(t){const e=super.create(t);return e.setAttribute("href",this.sanitize(t)),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e}static formats(t){return t.getAttribute("href")}static sanitize(t){return Os(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}format(t,e){t!==this.statics.blotName||!e?super.format(t,e):this.domNode.setAttribute("href",this.constructor.sanitize(e))}}v(nt,"blotName","link"),v(nt,"tagName","A"),v(nt,"SANITIZED_URL","about:blank"),v(nt,"PROTOCOL_WHITELIST",["http","https","mailto","tel","sms"]);function Os(i,t){const e=document.createElement("a");e.href=i;const s=e.href.slice(0,e.href.indexOf(":"));return t.indexOf(s)>-1}class he extends X{static create(t){return t==="super"?document.createElement("sup"):t==="sub"?document.createElement("sub"):super.create(t)}static formats(t){if(t.tagName==="SUB")return"sub";if(t.tagName==="SUP")return"super"}}v(he,"blotName","script"),v(he,"tagName",["SUB","SUP"]);class fe extends Nt{}v(fe,"blotName","strike"),v(fe,"tagName",["S","STRIKE"]);class de extends X{}v(de,"blotName","underline"),v(de,"tagName","U");class Dt extends qe{static create(t){if(window.katex==null)throw new Error("Formula module requires KaTeX.");const e=super.create(t);return typeof t=="string"&&(window.katex.render(t,e,{throwOnError:!1,errorColor:"#f00"}),e.setAttribute("data-value",t)),e}static value(t){return t.getAttribute("data-value")}html(){const{formula:t}=this.value();return`<span>${t}</span>`}}v(Dt,"blotName","formula"),v(Dt,"className","ql-formula"),v(Dt,"tagName","SPAN");const Qe=["alt","height","width"];class ge extends ${static create(t){const e=super.create(t);return typeof t=="string"&&e.setAttribute("src",this.sanitize(t)),e}static formats(t){return Qe.reduce((e,s)=>(t.hasAttribute(s)&&(e[s]=t.getAttribute(s)),e),{})}static match(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}static sanitize(t){return Os(t,["http","https","data"])?t:"//:0"}static value(t){return t.getAttribute("src")}format(t,e){Qe.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}}v(ge,"blotName","image"),v(ge,"tagName","IMG");const Je=["height","width"];class Ut extends H{static create(t){const e=super.create(t);return e.setAttribute("frameborder","0"),e.setAttribute("allowfullscreen","true"),e.setAttribute("src",this.sanitize(t)),e}static formats(t){return Je.reduce((e,s)=>(t.hasAttribute(s)&&(e[s]=t.getAttribute(s)),e),{})}static sanitize(t){return nt.sanitize(t)}static value(t){return t.getAttribute("src")}format(t,e){Je.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}html(){const{video:t}=this.value();return`<a href="${t}">${t}</a>`}}v(Ut,"blotName","video"),v(Ut,"className","ql-video"),v(Ut,"tagName","IFRAME");const yt=new tt("code-token","hljs",{scope:L.INLINE});class Q extends X{static formats(t,e){for(;t!=null&&t!==e.domNode;){if(t.classList&&t.classList.contains(O.className))return super.formats(t,e);t=t.parentNode}}constructor(t,e,s){super(t,e,s),yt.add(this.domNode,s)}format(t,e){t!==Q.blotName?super.format(t,e):e?yt.add(this.domNode,e):(yt.remove(this.domNode),this.domNode.classList.remove(this.statics.className))}optimize(){super.optimize(...arguments),yt.value(this.domNode)||this.unwrap()}}Q.blotName="code-token";Q.className="ql-token";class U extends O{static create(t){const e=super.create(t);return typeof t=="string"&&e.setAttribute("data-language",t),e}static formats(t){return t.getAttribute("data-language")||"plain"}static register(){}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-language",e):super.format(t,e)}replaceWith(t,e){return this.formatAt(0,this.length(),Q.blotName,!1),super.replaceWith(t,e)}}class vt extends ot{attach(){super.attach(),this.forceNext=!1,this.scroll.emitMount(this)}format(t,e){t===U.blotName&&(this.forceNext=!0,this.children.forEach(s=>{s.format(t,e)}))}formatAt(t,e,s,n){s===U.blotName&&(this.forceNext=!0),super.formatAt(t,e,s,n)}highlight(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.children.head==null)return;const n=`${Array.from(this.domNode.childNodes).filter(l=>l!==this.uiNode).map(l=>l.textContent).join(`
`)}
`,r=U.formats(this.children.head.domNode);if(e||this.forceNext||this.cachedText!==n){if(n.trim().length>0||this.cachedText==null){const l=this.children.reduce((a,c)=>a.concat(ms(c,!1)),new w),o=t(n,r);l.diff(o).reduce((a,c)=>{let{retain:f,attributes:m}=c;return f?(m&&Object.keys(m).forEach(u=>{[U.blotName,Q.blotName].includes(u)&&this.formatAt(a,f,u,m[u])}),a+f):a},0)}this.cachedText=n,this.forceNext=!1}}html(t,e){const[s]=this.children.find(t);return`<pre data-language="${s?U.formats(s.domNode):"plain"}">
${Pt(this.code(t,e))}
</pre>`}optimize(t){if(super.optimize(t),this.parent!=null&&this.children.head!=null&&this.uiNode!=null){const e=U.formats(this.children.head.domNode);e!==this.uiNode.value&&(this.uiNode.value=e)}}}vt.allowedChildren=[U];U.requiredContainer=vt;U.allowedChildren=[Q,dt,V,K];const vi=(i,t,e)=>{if(typeof i.versionString=="string"){const s=i.versionString.split(".")[0];if(parseInt(s,10)>=11)return i.highlight(e,{language:t}).value}return i.highlight(t,e).value};class Is extends Z{static register(){g.register(Q,!0),g.register(U,!0),g.register(vt,!0)}constructor(t,e){if(super(t,e),this.options.hljs==null)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");this.languages=this.options.languages.reduce((s,n)=>{let{key:r}=n;return s[r]=!0,s},{}),this.highlightBlot=this.highlightBlot.bind(this),this.initListener(),this.initTimer()}initListener(){this.quill.on(g.events.SCROLL_BLOT_MOUNT,t=>{if(!(t instanceof vt))return;const e=this.quill.root.ownerDocument.createElement("select");this.options.languages.forEach(s=>{let{key:n,label:r}=s;const l=e.ownerDocument.createElement("option");l.textContent=r,l.setAttribute("value",n),e.appendChild(l)}),e.addEventListener("change",()=>{t.format(U.blotName,e.value),this.quill.root.focus(),this.highlight(t,!0)}),t.uiNode==null&&(t.attachUI(e),t.children.head&&(e.value=U.formats(t.children.head.domNode)))})}initTimer(){let t=null;this.quill.on(g.events.SCROLL_OPTIMIZE,()=>{t&&clearTimeout(t),t=setTimeout(()=>{this.highlight(),t=null},this.options.interval)})}highlight(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.quill.selection.composing)return;this.quill.update(g.sources.USER);const s=this.quill.getSelection();(t==null?this.quill.scroll.descendants(vt):[t]).forEach(r=>{r.highlight(this.highlightBlot,e)}),this.quill.update(g.sources.SILENT),s!=null&&this.quill.setSelection(s,g.sources.SILENT)}highlightBlot(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"plain";if(e=this.languages[e]?e:"plain",e==="plain")return Pt(t).split(`
`).reduce((n,r,l)=>(l!==0&&n.insert(`
`,{[O.blotName]:e}),n.insert(r)),new w);const s=this.quill.root.ownerDocument.createElement("div");return s.classList.add(O.className),s.innerHTML=vi(this.options.hljs,e,t),Se(this.quill.scroll,s,[(n,r)=>{const l=yt.value(n);return l?r.compose(new w().retain(r.length(),{[Q.blotName]:l})):r}],[(n,r)=>n.data.split(`
`).reduce((l,o,a)=>(a!==0&&l.insert(`
`,{[O.blotName]:e}),l.insert(o)),r)],new WeakMap)}}Is.DEFAULTS={hljs:window.hljs,interval:1e3,languages:[{key:"plain",label:"Plain"},{key:"bash",label:"Bash"},{key:"cpp",label:"C++"},{key:"cs",label:"C#"},{key:"css",label:"CSS"},{key:"diff",label:"Diff"},{key:"xml",label:"HTML/XML"},{key:"java",label:"Java"},{key:"javascript",label:"JavaScript"},{key:"markdown",label:"Markdown"},{key:"php",label:"PHP"},{key:"python",label:"Python"},{key:"ruby",label:"Ruby"},{key:"sql",label:"SQL"}]};const qt=class qt extends C{static create(t){const e=super.create();return t?e.setAttribute("data-row",t):e.setAttribute("data-row",Te()),e}static formats(t){if(t.hasAttribute("data-row"))return t.getAttribute("data-row")}cellOffset(){return this.parent?this.parent.children.indexOf(this):-1}format(t,e){t===qt.blotName&&e?this.domNode.setAttribute("data-row",e):super.format(t,e)}row(){return this.parent}rowOffset(){return this.row()?this.row().rowOffset():-1}table(){return this.row()&&this.row().table()}};v(qt,"blotName","table"),v(qt,"tagName","TD");let z=qt;class J extends lt{checkMerge(){if(super.checkMerge()&&this.next.children.head!=null){const t=this.children.head.formats(),e=this.children.tail.formats(),s=this.next.children.head.formats(),n=this.next.children.tail.formats();return t.table===e.table&&t.table===s.table&&t.table===n.table}return!1}optimize(t){super.optimize(t),this.children.forEach(e=>{if(e.next==null)return;const s=e.formats(),n=e.next.formats();if(s.table!==n.table){const r=this.splitAfter(e);r&&r.optimize(),this.prev&&this.prev.optimize()}})}rowOffset(){return this.parent?this.parent.children.indexOf(this):-1}table(){return this.parent&&this.parent.parent}}v(J,"blotName","table-row"),v(J,"tagName","TR");class W extends lt{}v(W,"blotName","table-body"),v(W,"tagName","TBODY");class mt extends lt{balanceCells(){const t=this.descendants(J),e=t.reduce((s,n)=>Math.max(n.children.length,s),0);t.forEach(s=>{new Array(e-s.children.length).fill(0).forEach(()=>{let n;s.children.head!=null&&(n=z.formats(s.children.head.domNode));const r=this.scroll.create(z.blotName,n);s.appendChild(r),r.optimize()})})}cells(t){return this.rows().map(e=>e.children.at(t))}deleteColumn(t){const[e]=this.descendant(W);e==null||e.children.head==null||e.children.forEach(s=>{const n=s.children.at(t);n!=null&&n.remove()})}insertColumn(t){const[e]=this.descendant(W);e==null||e.children.head==null||e.children.forEach(s=>{const n=s.children.at(t),r=z.formats(s.children.head.domNode),l=this.scroll.create(z.blotName,r);s.insertBefore(l,n)})}insertRow(t){const[e]=this.descendant(W);if(e==null||e.children.head==null)return;const s=Te(),n=this.scroll.create(J.blotName);e.children.head.children.forEach(()=>{const l=this.scroll.create(z.blotName,s);n.appendChild(l)});const r=e.children.at(t);e.insertBefore(n,r)}rows(){const t=this.children.head;return t==null?[]:t.children.map(e=>e)}}v(mt,"blotName","table-container"),v(mt,"tagName","TABLE");mt.allowedChildren=[W];W.requiredContainer=mt;W.allowedChildren=[J];J.requiredContainer=W;J.allowedChildren=[z];z.requiredContainer=J;function Te(){return`row-${Math.random().toString(36).slice(2,6)}`}class Ei extends Z{static register(){g.register(z),g.register(J),g.register(W),g.register(mt)}constructor(){super(...arguments),this.listenBalanceCells()}balanceTables(){this.quill.scroll.descendants(mt).forEach(t=>{t.balanceCells()})}deleteColumn(){const[t,,e]=this.getTable();e!=null&&(t.deleteColumn(e.cellOffset()),this.quill.update(g.sources.USER))}deleteRow(){const[,t]=this.getTable();t!=null&&(t.remove(),this.quill.update(g.sources.USER))}deleteTable(){const[t]=this.getTable();if(t==null)return;const e=t.offset();t.remove(),this.quill.update(g.sources.USER),this.quill.setSelection(e,g.sources.SILENT)}getTable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.quill.getSelection();if(t==null)return[null,null,null,-1];const[e,s]=this.quill.getLine(t.index);if(e==null||e.statics.blotName!==z.blotName)return[null,null,null,-1];const n=e.parent;return[n.parent.parent,n,e,s]}insertColumn(t){const e=this.quill.getSelection();if(!e)return;const[s,n,r]=this.getTable(e);if(r==null)return;const l=r.cellOffset();s.insertColumn(l+t),this.quill.update(g.sources.USER);let o=n.rowOffset();t===0&&(o+=1),this.quill.setSelection(e.index+o,e.length,g.sources.SILENT)}insertColumnLeft(){this.insertColumn(0)}insertColumnRight(){this.insertColumn(1)}insertRow(t){const e=this.quill.getSelection();if(!e)return;const[s,n,r]=this.getTable(e);if(r==null)return;const l=n.rowOffset();s.insertRow(l+t),this.quill.update(g.sources.USER),t>0?this.quill.setSelection(e,g.sources.SILENT):this.quill.setSelection(e.index+n.children.length,e.length,g.sources.SILENT)}insertRowAbove(){this.insertRow(0)}insertRowBelow(){this.insertRow(1)}insertTable(t,e){const s=this.quill.getSelection();if(s==null)return;const n=new Array(t).fill(0).reduce(r=>{const l=new Array(e).fill(`
`).join("");return r.insert(l,{table:Te()})},new w().retain(s.index));this.quill.updateContents(n,g.sources.USER),this.quill.setSelection(s.index,g.sources.SILENT),this.balanceTables()}listenBalanceCells(){this.quill.on(g.events.SCROLL_OPTIMIZE,t=>{t.some(e=>["TD","TR","TBODY","TABLE"].includes(e.target.tagName)?(this.quill.once(g.events.TEXT_CHANGE,(s,n,r)=>{r===g.sources.USER&&this.balanceTables()}),!0):!1)})}}const ts=et("quill:toolbar");class Re extends Z{constructor(t,e){var s,n;if(super(t,e),Array.isArray(this.options.container)){const r=document.createElement("div");r.setAttribute("role","toolbar"),qi(r,this.options.container),(n=(s=t.container)==null?void 0:s.parentNode)==null||n.insertBefore(r,t.container),this.container=r}else typeof this.options.container=="string"?this.container=document.querySelector(this.options.container):this.container=this.options.container;if(!(this.container instanceof HTMLElement)){ts.error("Container required for toolbar",this.options);return}this.container.classList.add("ql-toolbar"),this.controls=[],this.handlers={},this.options.handlers&&Object.keys(this.options.handlers).forEach(r=>{var o;const l=(o=this.options.handlers)==null?void 0:o[r];l&&this.addHandler(r,l)}),Array.from(this.container.querySelectorAll("button, select")).forEach(r=>{this.attach(r)}),this.quill.on(g.events.EDITOR_CHANGE,()=>{const[r]=this.quill.selection.getRange();this.update(r)})}addHandler(t,e){this.handlers[t]=e}attach(t){let e=Array.from(t.classList).find(n=>n.indexOf("ql-")===0);if(!e)return;if(e=e.slice(3),t.tagName==="BUTTON"&&t.setAttribute("type","button"),this.handlers[e]==null&&this.quill.scroll.query(e)==null){ts.warn("ignoring attaching to nonexistent format",e,t);return}const s=t.tagName==="SELECT"?"change":"click";t.addEventListener(s,n=>{let r;if(t.tagName==="SELECT"){if(t.selectedIndex<0)return;const o=t.options[t.selectedIndex];o.hasAttribute("selected")?r=!1:r=o.value||!1}else t.classList.contains("ql-active")?r=!1:r=t.value||!t.hasAttribute("value"),n.preventDefault();this.quill.focus();const[l]=this.quill.selection.getRange();if(this.handlers[e]!=null)this.handlers[e].call(this,r);else if(this.quill.scroll.query(e).prototype instanceof $){if(r=prompt(`Enter ${e}`),!r)return;this.quill.updateContents(new w().retain(l.index).delete(l.length).insert({[e]:r}),g.sources.USER)}else this.quill.format(e,r,g.sources.USER);this.update(l)}),this.controls.push([e,t])}update(t){const e=t==null?{}:this.quill.getFormat(t);this.controls.forEach(s=>{const[n,r]=s;if(r.tagName==="SELECT"){let l=null;if(t==null)l=null;else if(e[n]==null)l=r.querySelector("option[selected]");else if(!Array.isArray(e[n])){let o=e[n];typeof o=="string"&&(o=o.replace(/"/g,'\\"')),l=r.querySelector(`option[value="${o}"]`)}l==null?(r.value="",r.selectedIndex=-1):l.selected=!0}else if(t==null)r.classList.remove("ql-active"),r.setAttribute("aria-pressed","false");else if(r.hasAttribute("value")){const l=e[n],o=l===r.getAttribute("value")||l!=null&&l.toString()===r.getAttribute("value")||l==null&&!r.getAttribute("value");r.classList.toggle("ql-active",o),r.setAttribute("aria-pressed",o.toString())}else{const l=e[n]!=null;r.classList.toggle("ql-active",l),r.setAttribute("aria-pressed",l.toString())}})}}Re.DEFAULTS={};function es(i,t,e){const s=document.createElement("button");s.setAttribute("type","button"),s.classList.add(`ql-${t}`),s.setAttribute("aria-pressed","false"),e!=null?(s.value=e,s.setAttribute("aria-label",`${t}: ${e}`)):s.setAttribute("aria-label",t),i.appendChild(s)}function qi(i,t){Array.isArray(t[0])||(t=[t]),t.forEach(e=>{const s=document.createElement("span");s.classList.add("ql-formats"),e.forEach(n=>{if(typeof n=="string")es(s,n);else{const r=Object.keys(n)[0],l=n[r];Array.isArray(l)?wi(s,r,l):es(s,r,l)}}),i.appendChild(s)})}function wi(i,t,e){const s=document.createElement("select");s.classList.add(`ql-${t}`),e.forEach(n=>{const r=document.createElement("option");n!==!1?r.setAttribute("value",String(n)):r.setAttribute("selected","selected"),s.appendChild(r)}),i.appendChild(s)}Re.DEFAULTS={container:null,handlers:{clean(){const i=this.quill.getSelection();if(i!=null)if(i.length===0){const t=this.quill.getFormat();Object.keys(t).forEach(e=>{this.quill.scroll.query(e,L.INLINE)!=null&&this.quill.format(e,!1,g.sources.USER)})}else this.quill.removeFormat(i.index,i.length,g.sources.USER)},direction(i){const{align:t}=this.quill.getFormat();i==="rtl"&&t==null?this.quill.format("align","right",g.sources.USER):!i&&t==="right"&&this.quill.format("align",!1,g.sources.USER),this.quill.format("direction",i,g.sources.USER)},indent(i){const t=this.quill.getSelection(),e=this.quill.getFormat(t),s=parseInt(e.indent||0,10);if(i==="+1"||i==="-1"){let n=i==="+1"?1:-1;e.direction==="rtl"&&(n*=-1),this.quill.format("indent",s+n,g.sources.USER)}},link(i){i===!0&&(i=prompt("Enter link URL:")),this.quill.format("link",i,g.sources.USER)},list(i){const t=this.quill.getSelection(),e=this.quill.getFormat(t);i==="check"?e.list==="checked"||e.list==="unchecked"?this.quill.format("list",!1,g.sources.USER):this.quill.format("list","unchecked",g.sources.USER):this.quill.format("list",i,g.sources.USER)}}};const xi='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="13" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="9" y1="4" y2="4"/></svg>',Ni='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="14" x2="4" y1="14" y2="14"/><line class="ql-stroke" x1="12" x2="6" y1="4" y2="4"/></svg>',Ai='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="5" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="9" y1="4" y2="4"/></svg>',ki='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="3" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="3" y1="4" y2="4"/></svg>',Li='<svg viewbox="0 0 18 18"><g class="ql-fill ql-color-label"><polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"/><rect height="1" width="1" x="4" y="4"/><polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"/><rect height="1" width="1" x="2" y="6"/><rect height="1" width="1" x="3" y="5"/><rect height="1" width="1" x="4" y="7"/><polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"/><rect height="1" width="1" x="2" y="12"/><rect height="1" width="1" x="2" y="9"/><rect height="1" width="1" x="2" y="15"/><polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"/><rect height="1" width="1" x="3" y="8"/><path d="M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z"/><path d="M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z"/><path d="M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z"/><rect height="1" width="1" x="12" y="2"/><rect height="1" width="1" x="11" y="3"/><path d="M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z"/><rect height="1" width="1" x="2" y="3"/><rect height="1" width="1" x="6" y="2"/><rect height="1" width="1" x="3" y="2"/><rect height="1" width="1" x="5" y="3"/><rect height="1" width="1" x="9" y="2"/><rect height="1" width="1" x="15" y="14"/><polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"/><rect height="1" width="1" x="13" y="7"/><rect height="1" width="1" x="15" y="5"/><rect height="1" width="1" x="14" y="6"/><rect height="1" width="1" x="15" y="8"/><rect height="1" width="1" x="14" y="9"/><path d="M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z"/><rect height="1" width="1" x="14" y="3"/><polygon points="12 6.868 12 6 11.62 6 12 6.868"/><rect height="1" width="1" x="15" y="2"/><rect height="1" width="1" x="12" y="5"/><rect height="1" width="1" x="13" y="4"/><polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"/><rect height="1" width="1" x="9" y="14"/><rect height="1" width="1" x="8" y="15"/><path d="M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z"/><rect height="1" width="1" x="5" y="15"/><path d="M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z"/><rect height="1" width="1" x="11" y="15"/><path d="M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z"/><rect height="1" width="1" x="14" y="15"/><rect height="1" width="1" x="15" y="11"/></g><polyline class="ql-stroke" points="5.5 13 9 5 12.5 13"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="11" y2="11"/></svg>',Si='<svg viewbox="0 0 18 18"><rect class="ql-fill ql-stroke" height="3" width="3" x="4" y="5"/><rect class="ql-fill ql-stroke" height="3" width="3" x="11" y="5"/><path class="ql-even ql-fill ql-stroke" d="M7,8c0,4.031-3,5-3,5"/><path class="ql-even ql-fill ql-stroke" d="M14,8c0,4.031-3,5-3,5"/></svg>',Ti='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z"/><path class="ql-stroke" d="M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z"/></svg>',Ri='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="5" x2="13" y1="3" y2="3"/><line class="ql-stroke" x1="6" x2="9.35" y1="12" y2="3"/><line class="ql-stroke" x1="11" x2="15" y1="11" y2="15"/><line class="ql-stroke" x1="15" x2="11" y1="11" y2="15"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="7" x="2" y="14"/></svg>',ss='<svg viewbox="0 0 18 18"><polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"/><polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"/><line class="ql-stroke" x1="10" x2="8" y1="5" y2="13"/></svg>',Ci='<svg viewbox="0 0 18 18"><line class="ql-color-label ql-stroke ql-transparent" x1="3" x2="15" y1="15" y2="15"/><polyline class="ql-stroke" points="5.5 11 9 3 12.5 11"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="9" y2="9"/></svg>',Oi='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"/><line class="ql-stroke ql-fill" x1="15" x2="11" y1="4" y2="4"/><path class="ql-fill" d="M11,3a3,3,0,0,0,0,6h1V3H11Z"/><rect class="ql-fill" height="11" width="1" x="11" y="4"/><rect class="ql-fill" height="11" width="1" x="13" y="4"/></svg>',Ii='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"/><line class="ql-stroke ql-fill" x1="9" x2="5" y1="4" y2="4"/><path class="ql-fill" d="M5,3A3,3,0,0,0,5,9H6V3H5Z"/><rect class="ql-fill" height="11" width="1" x="5" y="4"/><rect class="ql-fill" height="11" width="1" x="7" y="4"/></svg>',Mi='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z"/><rect class="ql-fill" height="1.6" rx="0.8" ry="0.8" width="5" x="5.15" y="6.2"/><path class="ql-fill" d="M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z"/></svg>',Bi='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z"/></svg>',Di='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',Ui='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',Hi='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z"/></svg>',Fi='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',$i='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z"/></svg>',ji='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="13" y1="4" y2="4"/><line class="ql-stroke" x1="5" x2="11" y1="14" y2="14"/><line class="ql-stroke" x1="8" x2="10" y1="14" y2="4"/></svg>',Pi='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="10" width="12" x="3" y="4"/><circle class="ql-fill" cx="6" cy="7" r="1"/><polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"/></svg>',_i='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"/></svg>',zi='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="5 7 5 11 3 9 5 7"/></svg>',Vi='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="11" y1="7" y2="11"/><path class="ql-even ql-stroke" d="M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z"/><path class="ql-even ql-stroke" d="M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z"/></svg>',Ki='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="6" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="6" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="6" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="3" y1="4" y2="4"/><line class="ql-stroke" x1="3" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="3" y1="14" y2="14"/></svg>',Zi='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="9" x2="15" y1="4" y2="4"/><polyline class="ql-stroke" points="3 4 4 5 6 3"/><line class="ql-stroke" x1="9" x2="15" y1="14" y2="14"/><polyline class="ql-stroke" points="3 14 4 15 6 13"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="3 9 4 10 6 8"/></svg>',Gi='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="7" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="7" x2="15" y1="14" y2="14"/><line class="ql-stroke ql-thin" x1="2.5" x2="4.5" y1="5.5" y2="5.5"/><path class="ql-fill" d="M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z"/><path class="ql-stroke ql-thin" d="M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156"/><path class="ql-stroke ql-thin" d="M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109"/></svg>',Wi='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z"/><path class="ql-fill" d="M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z"/></svg>',Xi='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z"/><path class="ql-fill" d="M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z"/></svg>',Yi='<svg viewbox="0 0 18 18"><line class="ql-stroke ql-thin" x1="15.5" x2="2.5" y1="8.5" y2="9.5"/><path class="ql-fill" d="M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z"/><path class="ql-fill" d="M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z"/></svg>',Qi='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="2" width="3" x="5" y="5"/><rect class="ql-fill" height="2" width="4" x="9" y="5"/><g class="ql-fill ql-transparent"><rect height="2" width="3" x="5" y="8"/><rect height="2" width="4" x="9" y="8"/><rect height="2" width="3" x="5" y="11"/><rect height="2" width="4" x="9" y="11"/></g></svg>',Ji='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="12" x="3" y="15"/></svg>',tr='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="12" width="1" x="5" y="3"/><rect class="ql-fill" height="12" width="1" x="12" y="3"/><rect class="ql-fill" height="2" width="8" x="5" y="8"/><rect class="ql-fill" height="1" width="3" x="3" y="5"/><rect class="ql-fill" height="1" width="3" x="3" y="7"/><rect class="ql-fill" height="1" width="3" x="3" y="10"/><rect class="ql-fill" height="1" width="3" x="3" y="12"/><rect class="ql-fill" height="1" width="3" x="12" y="5"/><rect class="ql-fill" height="1" width="3" x="12" y="7"/><rect class="ql-fill" height="1" width="3" x="12" y="10"/><rect class="ql-fill" height="1" width="3" x="12" y="12"/></svg>',At={align:{"":xi,center:Ni,right:Ai,justify:ki},background:Li,blockquote:Si,bold:Ti,clean:Ri,code:ss,"code-block":ss,color:Ci,direction:{"":Oi,rtl:Ii},formula:Mi,header:{1:Bi,2:Di,3:Ui,4:Hi,5:Fi,6:$i},italic:ji,image:Pi,indent:{"+1":_i,"-1":zi},link:Vi,list:{bullet:Ki,check:Zi,ordered:Gi},script:{sub:Wi,super:Xi},strike:Yi,table:Qi,underline:Ji,video:tr},er='<svg viewbox="0 0 18 18"><polygon class="ql-stroke" points="7 11 9 13 11 11 7 11"/><polygon class="ql-stroke" points="7 7 9 5 11 7 7 7"/></svg>';let ns=0;function is(i,t){i.setAttribute(t,`${i.getAttribute(t)!=="true"}`)}class zt{constructor(t){this.select=t,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",()=>{this.togglePicker()}),this.label.addEventListener("keydown",e=>{switch(e.key){case"Enter":this.togglePicker();break;case"Escape":this.escape(),e.preventDefault();break}}),this.select.addEventListener("change",this.update.bind(this))}togglePicker(){this.container.classList.toggle("ql-expanded"),is(this.label,"aria-expanded"),is(this.options,"aria-hidden")}buildItem(t){const e=document.createElement("span");e.tabIndex="0",e.setAttribute("role","button"),e.classList.add("ql-picker-item");const s=t.getAttribute("value");return s&&e.setAttribute("data-value",s),t.textContent&&e.setAttribute("data-label",t.textContent),e.addEventListener("click",()=>{this.selectItem(e,!0)}),e.addEventListener("keydown",n=>{switch(n.key){case"Enter":this.selectItem(e,!0),n.preventDefault();break;case"Escape":this.escape(),n.preventDefault();break}}),e}buildLabel(){const t=document.createElement("span");return t.classList.add("ql-picker-label"),t.innerHTML=er,t.tabIndex="0",t.setAttribute("role","button"),t.setAttribute("aria-expanded","false"),this.container.appendChild(t),t}buildOptions(){const t=document.createElement("span");t.classList.add("ql-picker-options"),t.setAttribute("aria-hidden","true"),t.tabIndex="-1",t.id=`ql-picker-options-${ns}`,ns+=1,this.label.setAttribute("aria-controls",t.id),this.options=t,Array.from(this.select.options).forEach(e=>{const s=this.buildItem(e);t.appendChild(s),e.selected===!0&&this.selectItem(s)}),this.container.appendChild(t)}buildPicker(){Array.from(this.select.attributes).forEach(t=>{this.container.setAttribute(t.name,t.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}escape(){this.close(),setTimeout(()=>this.label.focus(),1)}close(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}selectItem(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const s=this.container.querySelector(".ql-selected");t!==s&&(s!=null&&s.classList.remove("ql-selected"),t!=null&&(t.classList.add("ql-selected"),this.select.selectedIndex=Array.from(t.parentNode.children).indexOf(t),t.hasAttribute("data-value")?this.label.setAttribute("data-value",t.getAttribute("data-value")):this.label.removeAttribute("data-value"),t.hasAttribute("data-label")?this.label.setAttribute("data-label",t.getAttribute("data-label")):this.label.removeAttribute("data-label"),e&&(this.select.dispatchEvent(new Event("change")),this.close())))}update(){let t;if(this.select.selectedIndex>-1){const s=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];t=this.select.options[this.select.selectedIndex],this.selectItem(s)}else this.selectItem(null);const e=t!=null&&t!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",e)}}class Ms extends zt{constructor(t,e){super(t),this.label.innerHTML=e,this.container.classList.add("ql-color-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).slice(0,7).forEach(s=>{s.classList.add("ql-primary")})}buildItem(t){const e=super.buildItem(t);return e.style.backgroundColor=t.getAttribute("value")||"",e}selectItem(t,e){super.selectItem(t,e);const s=this.label.querySelector(".ql-color-label"),n=t&&t.getAttribute("data-value")||"";s&&(s.tagName==="line"?s.style.stroke=n:s.style.fill=n)}}class Bs extends zt{constructor(t,e){super(t),this.container.classList.add("ql-icon-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).forEach(s=>{s.innerHTML=e[s.getAttribute("data-value")||""]}),this.defaultItem=this.container.querySelector(".ql-selected"),this.selectItem(this.defaultItem)}selectItem(t,e){super.selectItem(t,e);const s=t||this.defaultItem;if(s!=null){if(this.label.innerHTML===s.innerHTML)return;this.label.innerHTML=s.innerHTML}}}const sr=i=>{const{overflowY:t}=getComputedStyle(i,null);return t!=="visible"&&t!=="clip"};class Ds{constructor(t,e){this.quill=t,this.boundsContainer=e||document.body,this.root=t.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,sr(this.quill.root)&&this.quill.root.addEventListener("scroll",()=>{this.root.style.marginTop=`${-1*this.quill.root.scrollTop}px`}),this.hide()}hide(){this.root.classList.add("ql-hidden")}position(t){const e=t.left+t.width/2-this.root.offsetWidth/2,s=t.bottom+this.quill.root.scrollTop;this.root.style.left=`${e}px`,this.root.style.top=`${s}px`,this.root.classList.remove("ql-flip");const n=this.boundsContainer.getBoundingClientRect(),r=this.root.getBoundingClientRect();let l=0;if(r.right>n.right&&(l=n.right-r.right,this.root.style.left=`${e+l}px`),r.left<n.left&&(l=n.left-r.left,this.root.style.left=`${e+l}px`),r.bottom>n.bottom){const o=r.bottom-r.top,a=t.bottom-t.top+o;this.root.style.top=`${s-a}px`,this.root.classList.add("ql-flip")}return l}show(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}const nr=[!1,"center","right","justify"],ir=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],rr=[!1,"serif","monospace"],lr=["1","2","3",!1],or=["small",!1,"large","huge"];class Tt extends gt{constructor(t,e){super(t,e);const s=n=>{if(!document.body.contains(t.root)){document.body.removeEventListener("click",s);return}this.tooltip!=null&&!this.tooltip.root.contains(n.target)&&document.activeElement!==this.tooltip.textbox&&!this.quill.hasFocus()&&this.tooltip.hide(),this.pickers!=null&&this.pickers.forEach(r=>{r.container.contains(n.target)||r.close()})};t.emitter.listenDOM("click",document.body,s)}addModule(t){const e=super.addModule(t);return t==="toolbar"&&this.extendToolbar(e),e}buildButtons(t,e){Array.from(t).forEach(s=>{(s.getAttribute("class")||"").split(/\s+/).forEach(r=>{if(r.startsWith("ql-")&&(r=r.slice(3),e[r]!=null))if(r==="direction")s.innerHTML=e[r][""]+e[r].rtl;else if(typeof e[r]=="string")s.innerHTML=e[r];else{const l=s.value||"";l!=null&&e[r][l]&&(s.innerHTML=e[r][l])}})})}buildPickers(t,e){this.pickers=Array.from(t).map(n=>{if(n.classList.contains("ql-align")&&(n.querySelector("option")==null&&bt(n,nr),typeof e.align=="object"))return new Bs(n,e.align);if(n.classList.contains("ql-background")||n.classList.contains("ql-color")){const r=n.classList.contains("ql-background")?"background":"color";return n.querySelector("option")==null&&bt(n,ir,r==="background"?"#ffffff":"#000000"),new Ms(n,e[r])}return n.querySelector("option")==null&&(n.classList.contains("ql-font")?bt(n,rr):n.classList.contains("ql-header")?bt(n,lr):n.classList.contains("ql-size")&&bt(n,or)),new zt(n)});const s=()=>{this.pickers.forEach(n=>{n.update()})};this.quill.on(q.events.EDITOR_CHANGE,s)}}Tt.DEFAULTS=it({},gt.DEFAULTS,{modules:{toolbar:{handlers:{formula(){this.quill.theme.tooltip.edit("formula")},image(){let i=this.container.querySelector("input.ql-image[type=file]");i==null&&(i=document.createElement("input"),i.setAttribute("type","file"),i.setAttribute("accept",this.quill.uploader.options.mimetypes.join(", ")),i.classList.add("ql-image"),i.addEventListener("change",()=>{const t=this.quill.getSelection(!0);this.quill.uploader.upload(t,i.files),i.value=""}),this.container.appendChild(i)),i.click()},video(){this.quill.theme.tooltip.edit("video")}}}}});class Us extends Ds{constructor(t,e){super(t,e),this.textbox=this.root.querySelector('input[type="text"]'),this.listen()}listen(){this.textbox.addEventListener("keydown",t=>{t.key==="Enter"?(this.save(),t.preventDefault()):t.key==="Escape"&&(this.cancel(),t.preventDefault())})}cancel(){this.hide(),this.restoreFocus()}edit(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),this.textbox==null)return;e!=null?this.textbox.value=e:t!==this.root.getAttribute("data-mode")&&(this.textbox.value="");const s=this.quill.getBounds(this.quill.selection.savedRange);s!=null&&this.position(s),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute(`data-${t}`)||""),this.root.setAttribute("data-mode",t)}restoreFocus(){this.quill.focus({preventScroll:!0})}save(){let{value:t}=this.textbox;switch(this.root.getAttribute("data-mode")){case"link":{const{scrollTop:e}=this.quill.root;this.linkRange?(this.quill.formatText(this.linkRange,"link",t,q.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",t,q.sources.USER)),this.quill.root.scrollTop=e;break}case"video":t=ar(t);case"formula":{if(!t)break;const e=this.quill.getSelection(!0);if(e!=null){const s=e.index+e.length;this.quill.insertEmbed(s,this.root.getAttribute("data-mode"),t,q.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(s+1," ",q.sources.USER),this.quill.setSelection(s+2,q.sources.USER)}break}}this.textbox.value="",this.hide()}}function ar(i){let t=i.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||i.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return t?`${t[1]||"https"}://www.youtube.com/embed/${t[2]}?showinfo=0`:(t=i.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?`${t[1]||"https"}://player.vimeo.com/video/${t[2]}/`:i}function bt(i,t){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;t.forEach(s=>{const n=document.createElement("option");s===e?n.setAttribute("selected","selected"):n.setAttribute("value",String(s)),i.appendChild(n)})}const cr=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]];class Hs extends Us{constructor(t,e){super(t,e),this.quill.on(q.events.EDITOR_CHANGE,(s,n,r,l)=>{if(s===q.events.SELECTION_CHANGE)if(n!=null&&n.length>0&&l===q.sources.USER){this.show(),this.root.style.left="0px",this.root.style.width="",this.root.style.width=`${this.root.offsetWidth}px`;const o=this.quill.getLines(n.index,n.length);if(o.length===1){const a=this.quill.getBounds(n);a!=null&&this.position(a)}else{const a=o[o.length-1],c=this.quill.getIndex(a),f=Math.min(a.length()-1,n.index+n.length-c),m=this.quill.getBounds(new rt(c,f));m!=null&&this.position(m)}}else document.activeElement!==this.textbox&&this.quill.hasFocus()&&this.hide()})}listen(){super.listen(),this.root.querySelector(".ql-close").addEventListener("click",()=>{this.root.classList.remove("ql-editing")}),this.quill.on(q.events.SCROLL_OPTIMIZE,()=>{setTimeout(()=>{if(this.root.classList.contains("ql-hidden"))return;const t=this.quill.getSelection();if(t!=null){const e=this.quill.getBounds(t);e!=null&&this.position(e)}},1)})}cancel(){this.show()}position(t){const e=super.position(t),s=this.root.querySelector(".ql-tooltip-arrow");return s.style.marginLeft="",e!==0&&(s.style.marginLeft=`${-1*e-s.offsetWidth/2}px`),e}}v(Hs,"TEMPLATE",['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""));class Fs extends Tt{constructor(t,e){e.modules.toolbar!=null&&e.modules.toolbar.container==null&&(e.modules.toolbar.container=cr),super(t,e),this.quill.container.classList.add("ql-bubble")}extendToolbar(t){this.tooltip=new Hs(this.quill,this.options.bounds),t.container!=null&&(this.tooltip.root.appendChild(t.container),this.buildButtons(t.container.querySelectorAll("button"),At),this.buildPickers(t.container.querySelectorAll("select"),At))}}Fs.DEFAULTS=it({},Tt.DEFAULTS,{modules:{toolbar:{handlers:{link(i){i?this.quill.theme.tooltip.edit():this.quill.format("link",!1,g.sources.USER)}}}}});const ur=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]];class $s extends Us{constructor(){super(...arguments);v(this,"preview",this.root.querySelector("a.ql-preview"))}listen(){super.listen(),this.root.querySelector("a.ql-action").addEventListener("click",e=>{this.root.classList.contains("ql-editing")?this.save():this.edit("link",this.preview.textContent),e.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",e=>{if(this.linkRange!=null){const s=this.linkRange;this.restoreFocus(),this.quill.formatText(s,"link",!1,q.sources.USER),delete this.linkRange}e.preventDefault(),this.hide()}),this.quill.on(q.events.SELECTION_CHANGE,(e,s,n)=>{if(e!=null){if(e.length===0&&n===q.sources.USER){const[r,l]=this.quill.scroll.descendant(nt,e.index);if(r!=null){this.linkRange=new rt(e.index-l,r.length());const o=nt.formats(r.domNode);this.preview.textContent=o,this.preview.setAttribute("href",o),this.show();const a=this.quill.getBounds(this.linkRange);a!=null&&this.position(a);return}}else delete this.linkRange;this.hide()}})}show(){super.show(),this.root.removeAttribute("data-mode")}}v($s,"TEMPLATE",['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""));class js extends Tt{constructor(t,e){e.modules.toolbar!=null&&e.modules.toolbar.container==null&&(e.modules.toolbar.container=ur),super(t,e),this.quill.container.classList.add("ql-snow")}extendToolbar(t){t.container!=null&&(t.container.classList.add("ql-snow"),this.buildButtons(t.container.querySelectorAll("button"),At),this.buildPickers(t.container.querySelectorAll("select"),At),this.tooltip=new $s(this.quill,this.options.bounds),t.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"k",shortKey:!0},(e,s)=>{t.handlers.link.call(t,!s.format.link)}))}}js.DEFAULTS=it({},Tt.DEFAULTS,{modules:{toolbar:{handlers:{link(i){if(i){const t=this.quill.getSelection();if(t==null||t.length===0)return;let e=this.quill.getText(t);/^\S+@\S+\.\S+$/.test(e)&&e.indexOf("mailto:")!==0&&(e=`mailto:${e}`);const{tooltip:s}=this.quill.theme;s.edit("link",e)}else this.quill.format("link",!1,g.sources.USER)}}}}});g.register({"attributors/attribute/direction":Es,"attributors/class/align":bs,"attributors/class/background":Sn,"attributors/class/color":Ln,"attributors/class/direction":qs,"attributors/class/font":Ns,"attributors/class/size":ks,"attributors/style/align":ys,"attributors/style/background":Ne,"attributors/style/color":xe,"attributors/style/direction":ws,"attributors/style/font":As,"attributors/style/size":Ls},!0);g.register({"formats/align":bs,"formats/direction":qs,"formats/indent":yi,"formats/background":Ne,"formats/color":xe,"formats/font":Ns,"formats/size":ks,"formats/blockquote":ae,"formats/code-block":O,"formats/header":ce,"formats/list":St,"formats/bold":Nt,"formats/code":Ae,"formats/italic":ue,"formats/link":nt,"formats/script":he,"formats/strike":fe,"formats/underline":de,"formats/formula":Dt,"formats/image":ge,"formats/video":Ut,"modules/syntax":Is,"modules/table":Ei,"modules/toolbar":Re,"themes/bubble":Fs,"themes/snow":js,"ui/icons":At,"ui/picker":zt,"ui/icon-picker":Bs,"ui/color-picker":Ms,"ui/tooltip":Ds},!0);export{g as Q};
