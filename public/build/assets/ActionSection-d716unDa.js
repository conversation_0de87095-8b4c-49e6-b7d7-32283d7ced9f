import{_ as d}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{c as i,o as n,a as s,J as t,l as a,S as c}from"./@vue-BnW70ngI.js";const l={},r={class:"md:col-span-1 flex justify-between"},_={class:"px-4 sm:px-0"},m={class:"text-lg font-medium text-gray-900"},p={class:"mt-1 text-sm text-gray-600"},h={class:"px-4 sm:px-0"};function f(e,o){return n(),i("div",r,[s("div",_,[s("h3",m,[t(e.$slots,"title")]),s("p",p,[t(e.$slots,"description")])]),s("div",h,[t(e.$slots,"aside")])])}const $=d(l,[["render",f]]),u={class:"md:grid md:grid-cols-3 md:gap-6"},x={class:"mt-5 md:mt-0 md:col-span-2"},g={class:"px-4 py-5 sm:p-6 bg-white shadow sm:rounded-lg"},y={__name:"ActionSection",setup(e){return(o,v)=>(n(),i("div",u,[a($,null,{title:c(()=>[t(o.$slots,"title")]),description:c(()=>[t(o.$slots,"description")]),_:3}),s("div",x,[s("div",g,[t(o.$slots,"content")])])]))}};export{$ as S,y as _};
