import{T as b,i as g}from"./@inertiajs-Dt0-hqjZ.js";import{_ as w}from"./AppLayout-DKZEmXIb.js";import{_ as $}from"./RedButton-D21iPtqa.js";import{_ as C}from"./LoadingIcon-CesYxFkK.js";import{_ as p}from"./TextInput-C52bsWxF.js";import{_ as V}from"./TextAreaInput-y-SlU-FI.js";import{_ as m}from"./InputError-gQdwtcoE.js";import{_ as h}from"./ImageInput-BV1wAASf.js";import{_ as v}from"./FixedSelectionBox-CkXOgkaT.js";import{i as y,k as u,o as c,S as d,a as l,l as r,P as a,a4 as t,K as q}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";import"./PrimaryButton-DE9sqoJj.js";import"./SelectionBox-CzAgH5wz.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-CyTLADhX.js";import"./@headlessui-gOb5_P77.js";const k={class:"flex-1 flex items-center"},S=["textContent"],U=["textContent"],j={class:"max-w-7xl mx-auto py-6 px-6"},B={class:"bg-white rounded-md shadow flex flex-col overflow-auto px-5 pt-3 pb-4"},D={class:"flex flex-col pb-2"},F=["textContent"],N={class:"flex pb-2"},T={class:"flex flex-col flex-1 mr-3"},O=["textContent"],Q={class:"flex pb-2"},z={class:"flex flex-col flex-1 mr-3"},A=["textContent"],K={class:"flex items-center"},P=["textContent"],E={class:"flex flex-col flex-1 ml-3"},G=["textContent"],H={class:"flex flex-col"},I=["textContent"],Te={__name:"Form",props:{quest:Object,types:Object,action:String},setup(f){const n=f,x=y("$toast"),e=b(n.quest),_=()=>{if(e.processing)return!1;e.errors={},e.transform(s=>(typeof s.image=="string"&&delete s.image,s)).post(route("quest.store"),{forceFormData:!0,preserveScroll:!0,preserveState:!0,onSuccess:s=>{s.props.jetstream.flash.message&&x.success(s.props.jetstream.flash.message),n.action==="create"&&(window.location=route("quest.list"))}})};return(s,o)=>(c(),u(w,{title:s.$t(n.action+"Quest")},{header:d(()=>[l("div",k,[l("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:a(s.$t(n.action+"Quest"))},null,8,S),r(t(g),{class:"primary-button text-sm mr-3",href:s.route("quest.list"),textContent:a(s.$t("list"))},null,8,["href","textContent"]),r($,{class:"normal-case",disabled:t(e).processing,onClick:_},{default:d(()=>[t(e).processing?(c(),u(C,{key:0,class:"mr-2"})):q("",!0),l("span",{class:"text-sm",textContent:a(s.$t("save"))},null,8,U)]),_:1},8,["disabled"])])]),default:d(()=>[l("div",j,[l("div",B,[l("div",D,[l("label",{textContent:a(s.$t("title")),class:"w-full"},null,8,F),r(p,{class:"block w-full",type:"text",modelValue:t(e).title,"onUpdate:modelValue":o[0]||(o[0]=i=>t(e).title=i),disabled:t(e).processing},null,8,["modelValue","disabled"]),r(m,{class:"w-full mt-1",message:t(e).errors.title},null,8,["message"])]),l("div",N,[l("div",T,[l("label",{textContent:a(s.$t("questType")),class:"w-full mb-1"},null,8,O),r(v,{modelValue:t(e).type,"onUpdate:modelValue":o[1]||(o[1]=i=>t(e).type=i),placeholder:s.$t("questType"),disabled:n.action==="update"||t(e).processing,options:n.types},null,8,["modelValue","placeholder","disabled","options"]),r(m,{class:"w-full mt-1",message:t(e).errors.type},null,8,["message"])]),r(h,{class:"flex-1 ml-3",label:s.$t("image"),error:t(e).errors.image,disabled:t(e).processing,modelValue:t(e).image,"onUpdate:modelValue":[o[2]||(o[2]=i=>t(e).image=i),o[3]||(o[3]=i=>t(e).image=i)]},null,8,["label","error","disabled","modelValue"])]),l("div",Q,[l("div",z,[l("label",{textContent:a(s.$t("questAmount")),class:"w-full mb-1"},null,8,A),l("div",K,[r(p,{class:"block w-full",type:"text",modelValue:t(e).amount,"onUpdate:modelValue":o[4]||(o[4]=i=>t(e).amount=i),disabled:t(e).processing},null,8,["modelValue","disabled"]),l("div",{class:"ml-2 w-[100px]",textContent:a(s.$t("quest.unit.coin"))},null,8,P)]),r(m,{class:"w-full mt-1",message:t(e).errors.amount},null,8,["message"])]),l("div",E,[l("label",{textContent:a(s.$t("sort")),class:"w-full mb-1"},null,8,G),r(p,{class:"block w-full",type:"text",modelValue:t(e).sort,"onUpdate:modelValue":o[5]||(o[5]=i=>t(e).sort=i),disabled:t(e).processing},null,8,["modelValue","disabled"]),r(m,{class:"w-full mt-1",message:t(e).errors.sort},null,8,["message"])])]),l("div",H,[l("label",{textContent:a(s.$t("questDescription")),class:"w-full mb-1"},null,8,I),r(V,{class:"block w-full resize-none h-[150px]",type:"text",modelValue:t(e).description,"onUpdate:modelValue":o[6]||(o[6]=i=>t(e).description=i),disabled:t(e).processing},null,8,["modelValue","disabled"]),r(m,{class:"w-full mt-1",message:t(e).errors.description},null,8,["message"])])])])]),_:1},8,["title"]))}};export{Te as default};
