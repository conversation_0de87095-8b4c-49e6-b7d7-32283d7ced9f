import{_ as e}from"./AppLayout-m_I9gnvX.js";import i from"./UpdateProfileInformationForm-_FQQkdu0.js";import{k as p,o as a,S as o,a as r,l as m,P as s}from"./@vue-BnW70ngI.js";import"./@inertiajs-BhKdJayA.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BWHXZF7Q.js";import"./PrimaryButton-DE9sqoJj.js";import"./InputError-gQdwtcoE.js";import"./InputLabel-BTXevqr4.js";import"./LoadingIcon-CesYxFkK.js";import"./TextInput-C52bsWxF.js";const l=["textContent"],n={class:"max-w-7xl mx-auto py-10 px-6"},j={__name:"Show",setup(_){return(t,c)=>(a(),p(e,{title:t.$t("profile")},{header:o(()=>[r("h2",{class:"text-xl text-gray-800 leading-tight",textContent:s(t.$t("updateProfile"))},null,8,l)]),default:o(()=>[r("div",n,[m(i,{user:t.$page.props.auth.user},null,8,["user"])])]),_:1},8,["title"]))}};export{j as default};
