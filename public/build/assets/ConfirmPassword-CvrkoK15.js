import{r as l,c as p,o as d,l as o,a4 as a,S as t,a as e,_ as u,R as f,O as c,F as _}from"./@vue-BnW70ngI.js";import{T as w,Z as g}from"./@inertiajs-BhKdJayA.js";import{A as b,a as x}from"./AuthenticationCardLogo-C3QjX6Mv.js";import{_ as v}from"./InputError-gQdwtcoE.js";import{_ as y}from"./InputLabel-BTXevqr4.js";import{_ as C}from"./PrimaryButton-DE9sqoJj.js";import{_ as V}from"./TextInput-C52bsWxF.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const k={class:"flex justify-end mt-4"},O={__name:"ConfirmPassword",setup(A){const s=w({password:""}),i=l(null),m=()=>{s.post(route("password.confirm"),{onFinish:()=>{s.reset(),i.value.focus()}})};return($,r)=>(d(),p(_,null,[o(a(g),{title:"Secure Area"}),o(x,null,{logo:t(()=>[o(b)]),default:t(()=>[r[2]||(r[2]=e("div",{class:"mb-4 text-sm text-gray-600"}," This is a secure area of the application. Please confirm your password before continuing. ",-1)),e("form",{onSubmit:u(m,["prevent"])},[e("div",null,[o(y,{for:"password",value:"Password"}),o(V,{id:"password",ref_key:"passwordInput",ref:i,modelValue:a(s).password,"onUpdate:modelValue":r[0]||(r[0]=n=>a(s).password=n),type:"password",class:"mt-1 block w-full",required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),o(v,{class:"mt-2",message:a(s).errors.password},null,8,["message"])]),e("div",k,[o(C,{class:f(["ms-4",{"opacity-25":a(s).processing}]),disabled:a(s).processing},{default:t(()=>r[1]||(r[1]=[c(" Confirm ")])),_:1},8,["class","disabled"])])],32)]),_:1})],64))}};export{O as default};
