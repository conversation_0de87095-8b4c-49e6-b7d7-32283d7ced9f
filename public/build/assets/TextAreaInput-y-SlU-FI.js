import{v as n,j as u,X as i,a7 as p,c,o as m}from"./@vue-BnW70ngI.js";const f=["disabled","placeholder"],b={__name:"TextAreaInput",props:{modelValue:{default:"",validator(e){return e===null||typeof e=="string"||typeof e=="number"}},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:""},clearData:{type:Boolean,default:!1}},emits:["update:modelValue","dataCleared"],setup(e,{emit:r}){const d=e,s=r,a=n({text:d.modelValue});return u(()=>d.clearData,l=>{l===!0&&(a.text="",s("dataCleared"))}),(l,t)=>i((m(),c("textarea",{class:"input-area","onUpdate:modelValue":t[0]||(t[0]=o=>a.text=o),disabled:e.disabled,placeholder:e.placeholder,onInput:t[1]||(t[1]=o=>l.$emit("update:modelValue",o.target.value)),autocomplete:"off"},null,40,f)),[[p,a.text]])}};export{b as _};
