import{u as b}from"./vue-i18n-kWKo0idO.js";import{f as c}from"./index-DHV2tfOS.js";import{i as _}from"./@inertiajs-Dt0-hqjZ.js";import{_ as f}from"./AppLayout-DKZEmXIb.js";import{k as i,o as s,S as n,a as t,P as r,c as p,F as m,M as h,a4 as x,O as w}from"./@vue-BnW70ngI.js";import"./@intlify-xvnhHnag.js";import"./moment-C5S46NFB.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";import"./PrimaryButton-DE9sqoJj.js";const C=["textContent"],g={class:"max-w-7xl mx-auto p-6"},I={class:"bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden"},D={class:"w-full"},v={class:"text-left flex"},y=["textContent"],k={class:"text-left flex border-t even:bg-blue-50 odd:bg-white"},A=["textContent"],B={class:"border-l flex-1"},mt={__name:"Detail",props:{answer:Object},setup(a){const{t:$}=b(),d=[{label:"ID",attribute:"answer_id"},{label:"postID",attribute:"post_id"},{label:"userPostID",attribute:"post_userID"},{label:"postCreatedBy",attribute:"post_username"},{label:"postContent",attribute:"post_content"},{label:"userAnswerID",attribute:"user_id"},{label:"answeredBy",attribute:"username"},{label:"postAnswerContent",attribute:"content"},{label:"answerCreatedAt",attribute:"created_at"},{label:"status",attribute:"status_label"}],u=(e,l)=>{const o=e[l.attribute];return["postCreatedAt"].includes(l.label)?c(o):o};return(e,l)=>(s(),i(f,{title:e.$t("postAnswerInfo")},{header:n(()=>[t("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:r(e.$t("postAnswerInfo"))},null,8,C)]),default:n(()=>[t("div",g,[t("div",I,[t("table",D,[t("thead",null,[t("tr",v,[t("th",{colspan:"2",textContent:r(e.$t("generalInfo"))},null,8,y)])]),t("tbody",null,[(s(),p(m,null,h(d,o=>t("tr",k,[t("td",{class:"w-1/4",textContent:r(e.$t(o.label))},null,8,A),t("td",B,[o.label==="postID"?(s(),i(x(_),{key:0,class:"hover:text-red-600 hover:underline",textContent:r(a.answer.post_id),href:e.route("post.detail",{post:a.answer.post_id})},null,8,["textContent","href"])):(s(),p(m,{key:1},[w(r(u(a.answer,o)),1)],64))])])),64))])])])])]),_:1},8,["title"]))}};export{mt as default};
