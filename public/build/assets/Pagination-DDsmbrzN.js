import{v as x,c as l,o as i,a as r,K as h,l as p,P as b,a4 as m,F as d,M as y,R as o,k as L,_ as M}from"./@vue-BnW70ngI.js";import{i as w}from"./@inertiajs-BhKdJayA.js";import{_ as k}from"./FixedSelectionBox-CwNS68U7.js";import{u as C}from"./vue-i18n-DNS8h1FH.js";const H={class:"w-full flex items-center"},N={class:"mr-auto flex items-center text-sm"},T=["textContent"],$=["textContent"],V={key:0,class:"flex flex-wrap items-end -mb-1 ml-2"},B=["innerHTML"],P=["innerHTML"],F=["innerHTML"],I={__name:"Pagination",props:{links:Array,active:Number|null,disabled:Boolean,limit:Number|null,total:Number|0,from:Number|0,to:Number|0},emits:["progress","changeLimit"],setup(e,{emit:g}){const{t:s}=C(),a=x({limit:e.limit||10,limitOptions:[{value:10,label:s("10")},{value:20,label:s("20")},{value:50,label:s("50")},{value:100,label:s("100")},{value:200,label:s("200")}]}),f=g,v=()=>{f("changeLimit",a.limit)};return(c,u)=>(i(),l("div",H,[r("div",N,[r("span",{textContent:b(m(s)("display")),class:"mr-1.5"},null,8,T),p(k,{class:"w-[54px] pagination-limit-wrapper",modelValue:a.limit,"onUpdate:modelValue":u[0]||(u[0]=t=>a.limit=t),disabled:e.disabled,options:a.limitOptions,clearable:!1,onSelected:v},null,8,["modelValue","disabled","options"]),r("span",{textContent:b(m(s)("itemPerPage",{total:e.total,from:e.from,to:e.to})),class:"ml-1.5"},null,8,$)]),e.links.length>3?(i(),l("div",V,[(i(!0),l(d,null,y(e.links,(t,n)=>(i(),l(d,null,[t.url===null?(i(),l("div",{key:n,class:o(["mr-1 px-3.5 py-2.5 text-gray-400 text-sm leading-4 transition",{"border rounded":t.label!=="..."}]),innerHTML:t.label},null,10,B)):t.active?(i(),l("div",{key:`active-${n}`,class:o(["mr-1 px-3.5 py-2.5 text-sm leading-4 border focus:border-sky-500 rounded transition",{"bg-sky-500 text-white font-semibold":!e.active,"bg-gray-100/90":e.active}]),innerHTML:t.label},null,10,P)):(i(),l(d,{key:2},[e.disabled?(i(),l("div",{key:`pagination-disabled-${n}`,class:o(["mr-1 px-3.5 py-2.5 text-gray-400 text-sm leading-4 transition border rounded",{"bg-sky-500 text-white font-semibold":e.active===n,"bg-gray-100/90":e.active!==n}]),innerHTML:t.label},null,10,F)):(i(),L(m(w),{key:`link-${n}`,class:o(["mr-1 px-3.5 py-2.5 focus:text-sky-500 text-sm leading-4 hover:bg-sky-100 border focus:border-sky-500 rounded bg-white transition",{"bg-white":t.active}]),href:t.url,innerHTML:t.label,onClick:M(S=>c.$emit("progress",n),["prevent"]),"on-progress":()=>console.log("a"),"on-finish":()=>c.$emit("progress",null)},null,8,["class","href","innerHTML","onClick","on-progress","on-finish"]))],64))],64))),256))])):h("",!0)]))}};export{I as _};
