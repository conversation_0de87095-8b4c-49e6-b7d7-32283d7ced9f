import{i as T,m as B,a as O,b as E,D as Ve,c as y,d as Q,e as j,u as ae,f as ue,g as Da,h as D,j as Z,k as C,l as ce,n as ie,o as Sa,p,q as Ye,r as je,s as oe,A as Wa,t as Va,v as Ua,N as wa,w as fa,x as ga,y as Be,C as xa,z as Xe,B as Je,M as ze,E as Ke,F as qe,G as Qe,H as Ze,I as Ha,J as $a,K as Ga,L as Ya,O as ea,P as ja,Q as Ba,R as K,S as Xa,T as Ja,U as za,V as Ka,W as qa}from"./@intlify-TnaUIxGf.js";import{B as Qa,r as q,s as ba,b as W,j as Ce,D as Za,d as Ue,h as va,F as da,y as te,i as et,e as at,g as tt,a1 as lt,l as nt,a2 as rt}from"./@vue-BnW70ngI.js";/*!
  * vue-i18n v9.14.4
  * (c) 2025 ka<PERSON>ya kawa<PERSON>
  * Released under the MIT License.
  */const st="9.14.4";function ct(){typeof __VUE_I18N_FULL_INSTALL__!="boolean"&&(K().__VUE_I18N_FULL_INSTALL__=!0),typeof __VUE_I18N_LEGACY_API__!="boolean"&&(K().__VUE_I18N_LEGACY_API__=!0),typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(K().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(K().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(K().__INTLIFY_PROD_DEVTOOLS__=!1)}const ot=Ja.__EXTEND_POINT__,H=ga(ot);H(),H(),H(),H(),H(),H(),H(),H(),H();const Ea=xa.__EXTEND_POINT__,M=ga(Ea),N={UNEXPECTED_RETURN_TYPE:Ea,INVALID_ARGUMENT:M(),MUST_BE_CALL_SETUP_TOP:M(),NOT_INSTALLED:M(),NOT_AVAILABLE_IN_LEGACY_MODE:M(),REQUIRED_VALUE:M(),INVALID_VALUE:M(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:M(),NOT_INSTALLED_WITH_PROVIDE:M(),UNEXPECTED_ERROR:M(),NOT_COMPATIBLE_LEGACY_VUE_I18N:M(),BRIDGE_SUPPORT_VUE_2_ONLY:M(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:M(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:M(),__EXTEND_POINT__:M()};function P(e,...s){return Da(e,null,void 0)}const Ae=B("__translateVNode"),pe=B("__datetimeParts"),De=B("__numberParts"),Ia=B("__setPluralRules"),La=B("__injectWithOption"),Se=B("__dispose");function le(e){if(!p(e)||oe(e))return e;for(const s in e)if(ie(e,s))if(!s.includes("."))p(e[s])&&le(e[s]);else{const t=s.split("."),n=t.length-1;let c=e,a=!1;for(let l=0;l<n;l++){if(t[l]==="__proto__")throw new Error(`unsafe key: ${t[l]}`);if(t[l]in c||(c[t[l]]=C()),!p(c[t[l]])){a=!0;break}c=c[t[l]]}if(a||(oe(c)?Wa.includes(t[n])||delete e[s]:(c[t[n]]=e[s],delete e[s])),!oe(c)){const l=c[t[n]];p(l)&&le(l)}}return e}function _e(e,s){const{messages:t,__i18n:n,messageResolver:c,flatJson:a}=s,l=O(t)?t:y(n)?C():{[e]:C()};if(y(n)&&n.forEach(i=>{if("locale"in i&&"resource"in i){const{locale:_,resource:m}=i;_?(l[_]=l[_]||C(),ce(m,l[_])):ce(m,l)}else E(i)&&ce(JSON.parse(i),l)}),c==null&&a)for(const i in l)ie(l,i)&&le(l[i]);return l}function Ta(e){return e.type}function Fa(e,s,t){let n=p(s.messages)?s.messages:C();"__i18nGlobal"in t&&(n=_e(e.locale.value,{messages:n,__i18n:t.__i18nGlobal}));const c=Object.keys(n);c.length&&c.forEach(a=>{e.mergeLocaleMessage(a,n[a])});{if(p(s.datetimeFormats)){const a=Object.keys(s.datetimeFormats);a.length&&a.forEach(l=>{e.mergeDateTimeFormat(l,s.datetimeFormats[l])})}if(p(s.numberFormats)){const a=Object.keys(s.numberFormats);a.length&&a.forEach(l=>{e.mergeNumberFormat(l,s.numberFormats[l])})}}}function aa(e){return nt(rt,null,e,0)}const ta="__INTLIFY_META__",la=()=>[],ut=()=>!1;let na=0;function ra(e){return(s,t,n,c)=>e(t,n,te()||void 0,c)}const it=()=>{const e=te();let s=null;return e&&(s=Ta(e)[ta])?{[ta]:s}:null};function we(e={},s){const{__root:t,__injectWithOption:n}=e,c=t===void 0,a=e.flatJson,l=ue?q:ba,i=!!e.translateExistCompatible;let _=T(e.inheritLocale)?e.inheritLocale:!0;const m=l(t&&_?t.locale.value:E(e.locale)?e.locale:Ve),b=l(t&&_?t.fallbackLocale.value:E(e.fallbackLocale)||y(e.fallbackLocale)||O(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:m.value),v=l(_e(m.value,e)),g=l(O(e.datetimeFormats)?e.datetimeFormats:{[m.value]:{}}),d=l(O(e.numberFormats)?e.numberFormats:{[m.value]:{}});let R=t?t.missingWarn:T(e.missingWarn)||Q(e.missingWarn)?e.missingWarn:!0,h=t?t.fallbackWarn:T(e.fallbackWarn)||Q(e.fallbackWarn)?e.fallbackWarn:!0,V=t?t.fallbackRoot:T(e.fallbackRoot)?e.fallbackRoot:!0,A=!!e.fallbackFormat,X=j(e.missing)?e.missing:null,U=j(e.missing)?ra(e.missing):null,w=j(e.postTranslation)?e.postTranslation:null,J=t?t.warnHtmlMessage:T(e.warnHtmlMessage)?e.warnHtmlMessage:!0,$=!!e.escapeParameter;const ee=t?t.modifiers:O(e.modifiers)?e.modifiers:{};let S=e.pluralRules||t&&t.pluralRules,f;f=(()=>{c&&Be(null);const r={version:st,locale:m.value,fallbackLocale:b.value,messages:v.value,modifiers:ee,pluralRules:S,missing:U===null?void 0:U,missingWarn:R,fallbackWarn:h,fallbackFormat:A,unresolving:!0,postTranslation:w===null?void 0:w,warnHtmlMessage:J,escapeParameter:$,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};r.datetimeFormats=g.value,r.numberFormats=d.value,r.__datetimeFormatters=O(f)?f.__datetimeFormatters:void 0,r.__numberFormatters=O(f)?f.__numberFormatters:void 0;const o=Sa(r);return c&&Be(o),o})(),ae(f,m.value,b.value);function me(){return[m.value,b.value,v.value,g.value,d.value]}const fe=W({get:()=>m.value,set:r=>{m.value=r,f.locale=m.value}}),ge=W({get:()=>b.value,set:r=>{b.value=r,f.fallbackLocale=b.value,ae(f,m.value,r)}}),be=W(()=>v.value),ve=W(()=>g.value),G=W(()=>d.value);function de(){return j(w)?w:null}function Ee(r){w=r,f.postTranslation=r}function Ie(){return X}function Le(r){r!==null&&(U=ra(r)),X=r,f.missing=U}const x=(r,o,L,F,Y,se)=>{me();let z;try{__INTLIFY_PROD_DEVTOOLS__&&Va(it()),c||(f.fallbackContext=t?Ua():void 0),z=r(f)}finally{__INTLIFY_PROD_DEVTOOLS__,c||(f.fallbackContext=void 0)}if(L!=="translate exists"&&Z(z)&&z===wa||L==="translate exists"&&!z){const[pa,At]=o();return t&&V?F(t):Y(pa)}else{if(se(z))return z;throw P(N.UNEXPECTED_RETURN_TYPE)}};function re(...r){return x(o=>Reflect.apply(Je,null,[o,...r]),()=>Xe(...r),"translate",o=>Reflect.apply(o.t,o,[...r]),o=>o,o=>E(o))}function Te(...r){const[o,L,F]=r;if(F&&!p(F))throw P(N.INVALID_ARGUMENT);return re(o,L,D({resolvedMessage:!0},F||{}))}function Fe(...r){return x(o=>Reflect.apply(qe,null,[o,...r]),()=>Ke(...r),"datetime format",o=>Reflect.apply(o.d,o,[...r]),()=>ze,o=>E(o))}function Oe(...r){return x(o=>Reflect.apply(Ze,null,[o,...r]),()=>Qe(...r),"number format",o=>Reflect.apply(o.n,o,[...r]),()=>ze,o=>E(o))}function Ne(r){return r.map(o=>E(o)||Z(o)||T(o)?aa(String(o)):o)}const Re={normalize:Ne,interpolate:r=>r,type:"vnode"};function ke(...r){return x(o=>{let L;const F=o;try{F.processor=Re,L=Reflect.apply(Je,null,[F,...r])}finally{F.processor=null}return L},()=>Xe(...r),"translate",o=>o[Ae](...r),o=>[aa(o)],o=>y(o))}function Pe(...r){return x(o=>Reflect.apply(Ze,null,[o,...r]),()=>Qe(...r),"number format",o=>o[De](...r),la,o=>E(o)||y(o))}function he(...r){return x(o=>Reflect.apply(qe,null,[o,...r]),()=>Ke(...r),"datetime format",o=>o[pe](...r),la,o=>E(o)||y(o))}function Me(r){S=r,f.pluralRules=S}function ye(r,o){return x(()=>{if(!r)return!1;const L=E(o)?o:m.value,F=Ge(L),Y=f.messageResolver(F,r);return i?Y!=null:oe(Y)||Ha(Y)||E(Y)},()=>[r],"translate exists",L=>Reflect.apply(L.te,L,[r,o]),ut,L=>T(L))}function u(r){let o=null;const L=fa(f,b.value,m.value);for(let F=0;F<L.length;F++){const Y=v.value[L[F]]||{},se=f.messageResolver(Y,r);if(se!=null){o=se;break}}return o}function I(r){const o=u(r);return o??(t?t.tm(r)||{}:{})}function Ge(r){return v.value[r]||{}}function Ra(r,o){if(a){const L={[r]:o};for(const F in L)ie(L,F)&&le(L[F]);o=L[r]}v.value[r]=o,f.messages=v.value}function ka(r,o){v.value[r]=v.value[r]||{};const L={[r]:o};if(a)for(const F in L)ie(L,F)&&le(L[F]);o=L[r],ce(o,v.value[r]),f.messages=v.value}function Pa(r){return g.value[r]||{}}function ha(r,o){g.value[r]=o,f.datetimeFormats=g.value,Ye(f,r,o)}function Ma(r,o){g.value[r]=D(g.value[r]||{},o),f.datetimeFormats=g.value,Ye(f,r,o)}function ya(r){return d.value[r]||{}}function Ca(r,o){d.value[r]=o,f.numberFormats=d.value,je(f,r,o)}function Aa(r,o){d.value[r]=D(d.value[r]||{},o),f.numberFormats=d.value,je(f,r,o)}na++,t&&ue&&(Ce(t.locale,r=>{_&&(m.value=r,f.locale=r,ae(f,m.value,b.value))}),Ce(t.fallbackLocale,r=>{_&&(b.value=r,f.fallbackLocale=r,ae(f,m.value,b.value))}));const k={id:na,locale:fe,fallbackLocale:ge,get inheritLocale(){return _},set inheritLocale(r){_=r,r&&t&&(m.value=t.locale.value,b.value=t.fallbackLocale.value,ae(f,m.value,b.value))},get availableLocales(){return Object.keys(v.value).sort()},messages:be,get modifiers(){return ee},get pluralRules(){return S||{}},get isGlobal(){return c},get missingWarn(){return R},set missingWarn(r){R=r,f.missingWarn=R},get fallbackWarn(){return h},set fallbackWarn(r){h=r,f.fallbackWarn=h},get fallbackRoot(){return V},set fallbackRoot(r){V=r},get fallbackFormat(){return A},set fallbackFormat(r){A=r,f.fallbackFormat=A},get warnHtmlMessage(){return J},set warnHtmlMessage(r){J=r,f.warnHtmlMessage=r},get escapeParameter(){return $},set escapeParameter(r){$=r,f.escapeParameter=r},t:re,getLocaleMessage:Ge,setLocaleMessage:Ra,mergeLocaleMessage:ka,getPostTranslationHandler:de,setPostTranslationHandler:Ee,getMissingHandler:Ie,setMissingHandler:Le,[Ia]:Me};return k.datetimeFormats=ve,k.numberFormats=G,k.rt=Te,k.te=ye,k.tm=I,k.d=Fe,k.n=Oe,k.getDateTimeFormat=Pa,k.setDateTimeFormat=ha,k.mergeDateTimeFormat=Ma,k.getNumberFormat=ya,k.setNumberFormat=Ca,k.mergeNumberFormat=Aa,k[La]=n,k[Ae]=ke,k[pe]=he,k[De]=Pe,k}function _t(e){const s=E(e.locale)?e.locale:Ve,t=E(e.fallbackLocale)||y(e.fallbackLocale)||O(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:s,n=j(e.missing)?e.missing:void 0,c=T(e.silentTranslationWarn)||Q(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,a=T(e.silentFallbackWarn)||Q(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,l=T(e.fallbackRoot)?e.fallbackRoot:!0,i=!!e.formatFallbackMessages,_=O(e.modifiers)?e.modifiers:{},m=e.pluralizationRules,b=j(e.postTranslation)?e.postTranslation:void 0,v=E(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,g=!!e.escapeParameterHtml,d=T(e.sync)?e.sync:!0;let R=e.messages;if(O(e.sharedMessages)){const $=e.sharedMessages;R=Object.keys($).reduce((S,f)=>{const ne=S[f]||(S[f]={});return D(ne,$[f]),S},R||{})}const{__i18n:h,__root:V,__injectWithOption:A}=e,X=e.datetimeFormats,U=e.numberFormats,w=e.flatJson,J=e.translateExistCompatible;return{locale:s,fallbackLocale:t,messages:R,flatJson:w,datetimeFormats:X,numberFormats:U,missing:n,missingWarn:c,fallbackWarn:a,fallbackRoot:l,fallbackFormat:i,modifiers:_,pluralRules:m,postTranslation:b,warnHtmlMessage:v,escapeParameter:g,messageResolver:e.messageResolver,inheritLocale:d,translateExistCompatible:J,__i18n:h,__root:V,__injectWithOption:A}}function We(e={},s){{const t=we(_t(e)),{__extender:n}=e,c={id:t.id,get locale(){return t.locale.value},set locale(a){t.locale.value=a},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(a){t.fallbackLocale.value=a},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate(){return[]}}},set formatter(a){},get missing(){return t.getMissingHandler()},set missing(a){t.setMissingHandler(a)},get silentTranslationWarn(){return T(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(a){t.missingWarn=T(a)?!a:a},get silentFallbackWarn(){return T(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(a){t.fallbackWarn=T(a)?!a:a},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(a){t.fallbackFormat=a},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(a){t.setPostTranslationHandler(a)},get sync(){return t.inheritLocale},set sync(a){t.inheritLocale=a},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(a){t.warnHtmlMessage=a!=="off"},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(a){t.escapeParameter=a},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(a){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...a){const[l,i,_]=a,m={};let b=null,v=null;if(!E(l))throw P(N.INVALID_ARGUMENT);const g=l;return E(i)?m.locale=i:y(i)?b=i:O(i)&&(v=i),y(_)?b=_:O(_)&&(v=_),Reflect.apply(t.t,t,[g,b||v||{},m])},rt(...a){return Reflect.apply(t.rt,t,[...a])},tc(...a){const[l,i,_]=a,m={plural:1};let b=null,v=null;if(!E(l))throw P(N.INVALID_ARGUMENT);const g=l;return E(i)?m.locale=i:Z(i)?m.plural=i:y(i)?b=i:O(i)&&(v=i),E(_)?m.locale=_:y(_)?b=_:O(_)&&(v=_),Reflect.apply(t.t,t,[g,b||v||{},m])},te(a,l){return t.te(a,l)},tm(a){return t.tm(a)},getLocaleMessage(a){return t.getLocaleMessage(a)},setLocaleMessage(a,l){t.setLocaleMessage(a,l)},mergeLocaleMessage(a,l){t.mergeLocaleMessage(a,l)},d(...a){return Reflect.apply(t.d,t,[...a])},getDateTimeFormat(a){return t.getDateTimeFormat(a)},setDateTimeFormat(a,l){t.setDateTimeFormat(a,l)},mergeDateTimeFormat(a,l){t.mergeDateTimeFormat(a,l)},n(...a){return Reflect.apply(t.n,t,[...a])},getNumberFormat(a){return t.getNumberFormat(a)},setNumberFormat(a,l){t.setNumberFormat(a,l)},mergeNumberFormat(a,l){t.mergeNumberFormat(a,l)},getChoiceIndex(a,l){return-1}};return c.__extender=n,c}}const xe={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function mt({slots:e},s){return s.length===1&&s[0]==="default"?(e.default?e.default():[]).reduce((n,c)=>[...n,...c.type===da?c.children:[c]],[]):s.reduce((t,n)=>{const c=e[n];return c&&(t[n]=c()),t},C())}function Oa(e){return da}const ft=Ue({name:"i18n-t",props:D({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>Z(e)||!isNaN(e)}},xe),setup(e,s){const{slots:t,attrs:n}=s,c=e.i18n||He({useScope:e.scope,__useComponent:!0});return()=>{const a=Object.keys(t).filter(v=>v!=="_"),l=C();e.locale&&(l.locale=e.locale),e.plural!==void 0&&(l.plural=E(e.plural)?+e.plural:e.plural);const i=mt(s,a),_=c[Ae](e.keypath,i,l),m=D(C(),n),b=E(e.tag)||p(e.tag)?e.tag:Oa();return va(b,m,_)}}}),sa=ft;function gt(e){return y(e)&&!E(e[0])}function Na(e,s,t,n){const{slots:c,attrs:a}=s;return()=>{const l={part:!0};let i=C();e.locale&&(l.locale=e.locale),E(e.format)?l.key=e.format:p(e.format)&&(E(e.format.key)&&(l.key=e.format.key),i=Object.keys(e.format).reduce((g,d)=>t.includes(d)?D(C(),g,{[d]:e.format[d]}):g,C()));const _=n(e.value,l,i);let m=[l.key];y(_)?m=_.map((g,d)=>{const R=c[g.type],h=R?R({[g.type]:g.value,index:d,parts:_}):[g.value];return gt(h)&&(h[0].key=`${g.type}-${d}`),h}):E(_)&&(m=[_]);const b=D(C(),a),v=E(e.tag)||p(e.tag)?e.tag:Oa();return va(v,b,m)}}const bt=Ue({name:"i18n-n",props:D({value:{type:Number,required:!0},format:{type:[String,Object]}},xe),setup(e,s){const t=e.i18n||He({useScope:e.scope,__useComponent:!0});return Na(e,s,$a,(...n)=>t[De](...n))}}),ca=bt,vt=Ue({name:"i18n-d",props:D({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},xe),setup(e,s){const t=e.i18n||He({useScope:e.scope,__useComponent:!0});return Na(e,s,Ga,(...n)=>t[pe](...n))}}),oa=vt;function dt(e,s){const t=e;if(e.mode==="composition")return t.__getInstance(s)||e.global;{const n=t.__getInstance(s);return n!=null?n.__composer:e.global.__composer}}function Et(e){const s=l=>{const{instance:i,modifiers:_,value:m}=l;if(!i||!i.$)throw P(N.UNEXPECTED_ERROR);const b=dt(e,i.$),v=ua(m);return[Reflect.apply(b.t,b,[...ia(v)]),b]};return{created:(l,i)=>{const[_,m]=s(i);ue&&e.global===m&&(l.__i18nWatcher=Ce(m.locale,()=>{i.instance&&i.instance.$forceUpdate()})),l.__composer=m,l.textContent=_},unmounted:l=>{ue&&l.__i18nWatcher&&(l.__i18nWatcher(),l.__i18nWatcher=void 0,delete l.__i18nWatcher),l.__composer&&(l.__composer=void 0,delete l.__composer)},beforeUpdate:(l,{value:i})=>{if(l.__composer){const _=l.__composer,m=ua(i);l.textContent=Reflect.apply(_.t,_,[...ia(m)])}},getSSRProps:l=>{const[i]=s(l);return{textContent:i}}}}function ua(e){if(E(e))return{path:e};if(O(e)){if(!("path"in e))throw P(N.REQUIRED_VALUE,"path");return e}else throw P(N.INVALID_VALUE)}function ia(e){const{path:s,locale:t,args:n,choice:c,plural:a}=e,l={},i=n||{};return E(t)&&(l.locale=t),Z(c)&&(l.plural=c),Z(a)&&(l.plural=a),[s,i,l]}function It(e,s,...t){const n=O(t[0])?t[0]:{},c=!!n.useI18nComponentName;(T(n.globalInstall)?n.globalInstall:!0)&&([c?"i18n":sa.name,"I18nT"].forEach(l=>e.component(l,sa)),[ca.name,"I18nN"].forEach(l=>e.component(l,ca)),[oa.name,"I18nD"].forEach(l=>e.component(l,oa))),e.directive("t",Et(s))}function Lt(e,s,t){return{beforeCreate(){const n=te();if(!n)throw P(N.UNEXPECTED_ERROR);const c=this.$options;if(c.i18n){const a=c.i18n;if(c.__i18n&&(a.__i18n=c.__i18n),a.__root=s,this===this.$root)this.$i18n=_a(e,a);else{a.__injectWithOption=!0,a.__extender=t.__vueI18nExtend,this.$i18n=We(a);const l=this.$i18n;l.__extender&&(l.__disposer=l.__extender(this.$i18n))}}else if(c.__i18n)if(this===this.$root)this.$i18n=_a(e,c);else{this.$i18n=We({__i18n:c.__i18n,__injectWithOption:!0,__extender:t.__vueI18nExtend,__root:s});const a=this.$i18n;a.__extender&&(a.__disposer=a.__extender(this.$i18n))}else this.$i18n=e;c.__i18nGlobal&&Fa(s,c,c),this.$t=(...a)=>this.$i18n.t(...a),this.$rt=(...a)=>this.$i18n.rt(...a),this.$tc=(...a)=>this.$i18n.tc(...a),this.$te=(a,l)=>this.$i18n.te(a,l),this.$d=(...a)=>this.$i18n.d(...a),this.$n=(...a)=>this.$i18n.n(...a),this.$tm=a=>this.$i18n.tm(a),t.__setInstance(n,this.$i18n)},mounted(){},unmounted(){const n=te();if(!n)throw P(N.UNEXPECTED_ERROR);const c=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,c.__disposer&&(c.__disposer(),delete c.__disposer,delete c.__extender),t.__deleteInstance(n),delete this.$i18n}}}function _a(e,s){e.locale=s.locale||e.locale,e.fallbackLocale=s.fallbackLocale||e.fallbackLocale,e.missing=s.missing||e.missing,e.silentTranslationWarn=s.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=s.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=s.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=s.postTranslation||e.postTranslation,e.warnHtmlInMessage=s.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=s.escapeParameterHtml||e.escapeParameterHtml,e.sync=s.sync||e.sync,e.__composer[Ia](s.pluralizationRules||e.pluralizationRules);const t=_e(e.locale,{messages:s.messages,__i18n:s.__i18n});return Object.keys(t).forEach(n=>e.mergeLocaleMessage(n,t[n])),s.datetimeFormats&&Object.keys(s.datetimeFormats).forEach(n=>e.mergeDateTimeFormat(n,s.datetimeFormats[n])),s.numberFormats&&Object.keys(s.numberFormats).forEach(n=>e.mergeNumberFormat(n,s.numberFormats[n])),e}const Tt=B("global-vue-i18n");function St(e={},s){const t=__VUE_I18N_LEGACY_API__&&T(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,n=T(e.globalInjection)?e.globalInjection:!0,c=__VUE_I18N_LEGACY_API__&&t?!!e.allowComposition:!0,a=new Map,[l,i]=Ft(e,t),_=B("");function m(g){return a.get(g)||null}function b(g,d){a.set(g,d)}function v(g){a.delete(g)}{const g={get mode(){return __VUE_I18N_LEGACY_API__&&t?"legacy":"composition"},get allowComposition(){return c},async install(d,...R){if(d.__VUE_I18N_SYMBOL__=_,d.provide(d.__VUE_I18N_SYMBOL__,g),O(R[0])){const A=R[0];g.__composerExtend=A.__composerExtend,g.__vueI18nExtend=A.__vueI18nExtend}let h=null;!t&&n&&(h=Ct(d,g.global)),__VUE_I18N_FULL_INSTALL__&&It(d,g,...R),__VUE_I18N_LEGACY_API__&&t&&d.mixin(Lt(i,i.__composer,g));const V=d.unmount;d.unmount=()=>{h&&h(),g.dispose(),V()}},get global(){return i},dispose(){l.stop()},__instances:a,__getInstance:m,__setInstance:b,__deleteInstance:v};return g}}function He(e={}){const s=te();if(s==null)throw P(N.MUST_BE_CALL_SETUP_TOP);if(!s.isCE&&s.appContext.app!=null&&!s.appContext.app.__VUE_I18N_SYMBOL__)throw P(N.NOT_INSTALLED);const t=Ot(s),n=Rt(t),c=Ta(s),a=Nt(e,c);if(__VUE_I18N_LEGACY_API__&&t.mode==="legacy"&&!e.__useComponent){if(!t.allowComposition)throw P(N.NOT_AVAILABLE_IN_LEGACY_MODE);return Mt(s,a,n,e)}if(a==="global")return Fa(n,e,c),n;if(a==="parent"){let _=kt(t,s,e.__useComponent);return _==null&&(_=n),_}const l=t;let i=l.__getInstance(s);if(i==null){const _=D({},e);"__i18n"in c&&(_.__i18n=c.__i18n),n&&(_.__root=n),i=we(_),l.__composerExtend&&(i[Se]=l.__composerExtend(i)),ht(l,s,i),l.__setInstance(s,i)}return i}function Ft(e,s,t){const n=Qa();{const c=__VUE_I18N_LEGACY_API__&&s?n.run(()=>We(e)):n.run(()=>we(e));if(c==null)throw P(N.UNEXPECTED_ERROR);return[n,c]}}function Ot(e){{const s=et(e.isCE?Tt:e.appContext.app.__VUE_I18N_SYMBOL__);if(!s)throw P(e.isCE?N.NOT_INSTALLED_WITH_PROVIDE:N.UNEXPECTED_ERROR);return s}}function Nt(e,s){return Ya(e)?"__i18n"in s?"local":"global":e.useScope?e.useScope:"local"}function Rt(e){return e.mode==="composition"?e.global:e.global.__composer}function kt(e,s,t=!1){let n=null;const c=s.root;let a=Pt(s,t);for(;a!=null;){const l=e;if(e.mode==="composition")n=l.__getInstance(a);else if(__VUE_I18N_LEGACY_API__){const i=l.__getInstance(a);i!=null&&(n=i.__composer,t&&n&&!n[La]&&(n=null))}if(n!=null||c===a)break;a=a.parent}return n}function Pt(e,s=!1){return e==null?null:s&&e.vnode.ctx||e.parent}function ht(e,s,t){at(()=>{},s),tt(()=>{const n=t;e.__deleteInstance(s);const c=n[Se];c&&(c(),delete n[Se])},s)}function Mt(e,s,t,n={}){const c=s==="local",a=ba(null);if(c&&e.proxy&&!(e.proxy.$options.i18n||e.proxy.$options.__i18n))throw P(N.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const l=T(n.inheritLocale)?n.inheritLocale:!E(n.locale),i=q(!c||l?t.locale.value:E(n.locale)?n.locale:Ve),_=q(!c||l?t.fallbackLocale.value:E(n.fallbackLocale)||y(n.fallbackLocale)||O(n.fallbackLocale)||n.fallbackLocale===!1?n.fallbackLocale:i.value),m=q(_e(i.value,n)),b=q(O(n.datetimeFormats)?n.datetimeFormats:{[i.value]:{}}),v=q(O(n.numberFormats)?n.numberFormats:{[i.value]:{}}),g=c?t.missingWarn:T(n.missingWarn)||Q(n.missingWarn)?n.missingWarn:!0,d=c?t.fallbackWarn:T(n.fallbackWarn)||Q(n.fallbackWarn)?n.fallbackWarn:!0,R=c?t.fallbackRoot:T(n.fallbackRoot)?n.fallbackRoot:!0,h=!!n.fallbackFormat,V=j(n.missing)?n.missing:null,A=j(n.postTranslation)?n.postTranslation:null,X=c?t.warnHtmlMessage:T(n.warnHtmlMessage)?n.warnHtmlMessage:!0,U=!!n.escapeParameter,w=c?t.modifiers:O(n.modifiers)?n.modifiers:{},J=n.pluralRules||c&&t.pluralRules;function $(){return[i.value,_.value,m.value,b.value,v.value]}const ee=W({get:()=>a.value?a.value.locale.value:i.value,set:u=>{a.value&&(a.value.locale.value=u),i.value=u}}),S=W({get:()=>a.value?a.value.fallbackLocale.value:_.value,set:u=>{a.value&&(a.value.fallbackLocale.value=u),_.value=u}}),f=W(()=>a.value?a.value.messages.value:m.value),ne=W(()=>b.value),me=W(()=>v.value);function fe(){return a.value?a.value.getPostTranslationHandler():A}function ge(u){a.value&&a.value.setPostTranslationHandler(u)}function be(){return a.value?a.value.getMissingHandler():V}function ve(u){a.value&&a.value.setMissingHandler(u)}function G(u){return $(),u()}function de(...u){return a.value?G(()=>Reflect.apply(a.value.t,null,[...u])):G(()=>"")}function Ee(...u){return a.value?Reflect.apply(a.value.rt,null,[...u]):""}function Ie(...u){return a.value?G(()=>Reflect.apply(a.value.d,null,[...u])):G(()=>"")}function Le(...u){return a.value?G(()=>Reflect.apply(a.value.n,null,[...u])):G(()=>"")}function x(u){return a.value?a.value.tm(u):{}}function re(u,I){return a.value?a.value.te(u,I):!1}function Te(u){return a.value?a.value.getLocaleMessage(u):{}}function Fe(u,I){a.value&&(a.value.setLocaleMessage(u,I),m.value[u]=I)}function Oe(u,I){a.value&&a.value.mergeLocaleMessage(u,I)}function Ne(u){return a.value?a.value.getDateTimeFormat(u):{}}function $e(u,I){a.value&&(a.value.setDateTimeFormat(u,I),b.value[u]=I)}function Re(u,I){a.value&&a.value.mergeDateTimeFormat(u,I)}function ke(u){return a.value?a.value.getNumberFormat(u):{}}function Pe(u,I){a.value&&(a.value.setNumberFormat(u,I),v.value[u]=I)}function he(u,I){a.value&&a.value.mergeNumberFormat(u,I)}const Me={get id(){return a.value?a.value.id:-1},locale:ee,fallbackLocale:S,messages:f,datetimeFormats:ne,numberFormats:me,get inheritLocale(){return a.value?a.value.inheritLocale:l},set inheritLocale(u){a.value&&(a.value.inheritLocale=u)},get availableLocales(){return a.value?a.value.availableLocales:Object.keys(m.value)},get modifiers(){return a.value?a.value.modifiers:w},get pluralRules(){return a.value?a.value.pluralRules:J},get isGlobal(){return a.value?a.value.isGlobal:!1},get missingWarn(){return a.value?a.value.missingWarn:g},set missingWarn(u){a.value&&(a.value.missingWarn=u)},get fallbackWarn(){return a.value?a.value.fallbackWarn:d},set fallbackWarn(u){a.value&&(a.value.missingWarn=u)},get fallbackRoot(){return a.value?a.value.fallbackRoot:R},set fallbackRoot(u){a.value&&(a.value.fallbackRoot=u)},get fallbackFormat(){return a.value?a.value.fallbackFormat:h},set fallbackFormat(u){a.value&&(a.value.fallbackFormat=u)},get warnHtmlMessage(){return a.value?a.value.warnHtmlMessage:X},set warnHtmlMessage(u){a.value&&(a.value.warnHtmlMessage=u)},get escapeParameter(){return a.value?a.value.escapeParameter:U},set escapeParameter(u){a.value&&(a.value.escapeParameter=u)},t:de,getPostTranslationHandler:fe,setPostTranslationHandler:ge,getMissingHandler:be,setMissingHandler:ve,rt:Ee,d:Ie,n:Le,tm:x,te:re,getLocaleMessage:Te,setLocaleMessage:Fe,mergeLocaleMessage:Oe,getDateTimeFormat:Ne,setDateTimeFormat:$e,mergeDateTimeFormat:Re,getNumberFormat:ke,setNumberFormat:Pe,mergeNumberFormat:he};function ye(u){u.locale.value=i.value,u.fallbackLocale.value=_.value,Object.keys(m.value).forEach(I=>{u.mergeLocaleMessage(I,m.value[I])}),Object.keys(b.value).forEach(I=>{u.mergeDateTimeFormat(I,b.value[I])}),Object.keys(v.value).forEach(I=>{u.mergeNumberFormat(I,v.value[I])}),u.escapeParameter=U,u.fallbackFormat=h,u.fallbackRoot=R,u.fallbackWarn=d,u.missingWarn=g,u.warnHtmlMessage=X}return lt(()=>{if(e.proxy==null||e.proxy.$i18n==null)throw P(N.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const u=a.value=e.proxy.$i18n.__composer;s==="global"?(i.value=u.locale.value,_.value=u.fallbackLocale.value,m.value=u.messages.value,b.value=u.datetimeFormats.value,v.value=u.numberFormats.value):c&&ye(u)}),Me}const yt=["locale","fallbackLocale","availableLocales"],ma=["t","rt","d","n","tm","te"];function Ct(e,s){const t=Object.create(null);return yt.forEach(c=>{const a=Object.getOwnPropertyDescriptor(s,c);if(!a)throw P(N.UNEXPECTED_ERROR);const l=Za(a.value)?{get(){return a.value.value},set(i){a.value.value=i}}:{get(){return a.get&&a.get()}};Object.defineProperty(t,c,l)}),e.config.globalProperties.$i18n=t,ma.forEach(c=>{const a=Object.getOwnPropertyDescriptor(s,c);if(!a||!a.value)throw P(N.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${c}`,a)}),()=>{delete e.config.globalProperties.$i18n,ma.forEach(c=>{delete e.config.globalProperties[`$${c}`]})}}ct();__INTLIFY_JIT_COMPILATION__?ea(za):ea(Ka);ja(qa);Ba(fa);if(__INTLIFY_PROD_DEVTOOLS__){const e=K();e.__INTLIFY__=!0,Xa(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}export{St as c,He as u};
