import{i as v,r as c,c as g,o as i,a as t,l as r,a4 as o,S as V,k as y,K as $,P as k,R as P,_ as b}from"./@vue-BnW70ngI.js";import{u as x}from"./vue-i18n-DNS8h1FH.js";import{T as S}from"./@inertiajs-BhKdJayA.js";import{_ as n}from"./InputError-gQdwtcoE.js";import{_ as d}from"./InputLabel-BTXevqr4.js";import{_ as B}from"./LoadingIcon-CesYxFkK.js";import{_ as C}from"./PrimaryButton-DE9sqoJj.js";import{_ as m}from"./TextInput-C52bsWxF.js";import"./@intlify-TnaUIxGf.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";const I={class:"mt-0"},U={class:"mt-4"},h={class:"mt-4"},N={class:"flex items-center justify-end text-end mt-5"},j=["textContent"],W={__name:"UpdatePasswordForm",setup(E){const f=v("$toast"),{t:w}=x(),p=c(null),u=c(null),s=S({current_password:"",password:"",password_confirmation:""}),_=()=>{s.put(route("user-password.update"),{errorBag:"updatePassword",preserveScroll:!0,onSuccess:()=>{s.reset(),f.success(w("passwordUpdatedSuccessfully"))},onError:()=>{s.errors.password&&(s.reset("password","password_confirmation"),p.value.focus()),s.errors.current_password&&(s.reset("current_password"),u.value.focus())}})};return(l,e)=>(i(),g("form",{class:"px-6 py-5 bg-white shadow rounded-md mx-auto sm:max-w-xl",onSubmit:b(_,["prevent"]),autocomplete:"off"},[t("div",I,[r(d,{for:"current_password",value:l.$t("currentPassword")},null,8,["value"]),r(m,{id:"current_password",class:"mt-1 block w-full",ref_key:"currentPasswordInput",ref:u,modelValue:o(s).current_password,"onUpdate:modelValue":e[0]||(e[0]=a=>o(s).current_password=a),type:"password",autocomplete:"off"},null,8,["modelValue"]),r(n,{class:"mt-2",message:o(s).errors.current_password},null,8,["message"])]),t("div",U,[r(d,{for:"password",value:l.$t("newPassword")},null,8,["value"]),r(m,{id:"password",ref_key:"passwordInput",ref:p,modelValue:o(s).password,"onUpdate:modelValue":e[1]||(e[1]=a=>o(s).password=a),type:"password",class:"mt-1 block w-full",autocomplete:"off"},null,8,["modelValue"]),r(n,{class:"mt-2",message:o(s).errors.password},null,8,["message"])]),t("div",h,[r(d,{for:"password_confirmation",value:l.$t("confirmPassword")},null,8,["value"]),r(m,{id:"password_confirmation",modelValue:o(s).password_confirmation,"onUpdate:modelValue":e[2]||(e[2]=a=>o(s).password_confirmation=a),type:"password",class:"mt-1 block w-full",autocomplete:"off"},null,8,["modelValue"]),r(n,{class:"mt-2",message:o(s).errors.password_confirmation},null,8,["message"])]),t("div",N,[r(C,{class:P({"opacity-25":o(s).processing}),disabled:o(s).processing},{default:V(()=>[o(s).processing?(i(),y(B,{key:0,class:"mr-2"})):$("",!0),t("span",{textContent:k(l.$t("save"))},null,8,j)]),_:1},8,["class","disabled"])])],32))}};export{W as default};
