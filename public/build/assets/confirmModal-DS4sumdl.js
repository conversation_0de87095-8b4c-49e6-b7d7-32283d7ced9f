import{b as y,r as T,k as v,o as i,c as f,K as w,a as n,R as d,P as h,O as B,T as M,a3 as j,h as S}from"./@vue-BnW70ngI.js";import{_ as z}from"./LoadingIcon-CLD0VpVl.js";/* empty css                                                                     */import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{i as I}from"./app-EptGTPPo.js";const L={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},_={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},N={class:"sm:flex sm:items-start"},P={class:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left"},V={class:"text-lg leading-6 font-medium text-gray-900",id:"modal-title"},D={class:"mt-2"},F={class:"text-gray-500"},K={class:"mt-5 sm:mt-4 sm:flex sm:flex-row-reverse"},A=["disabled"],O=["disabled"],R={__name:"ConfirmModal",props:{show:{type:Boolean,default:!1},title:{type:String},message:{type:String},confirmText:{type:String},cancelText:{type:String},type:{type:String,default:"warning",validator:e=>["danger","warning","info","success"].includes(e)},showIcon:{type:Boolean,default:!0}},emits:["confirm","cancel","close"],setup(e,{emit:r}){const l=e,s=r,b=y(()=>{const t={danger:"border-l-4 border-red-500",warning:"border-l-4 border-yellow-500",info:"border-l-4 border-sky-500",success:"border-l-4 border-green-500"};return t[l.type]||t.warning}),x=y(()=>{const t={danger:"bg-red-100",warning:"bg-yellow-100",info:"bg-sky-100",success:"bg-green-100"};return t[l.type]||t.warning}),m=y(()=>{const t={danger:"text-red-600",warning:"text-yellow-600",info:"text-sky-600",success:"text-green-600"};return t[l.type]||t.warning}),p=y(()=>{const t={danger:"bg-red-600 hover:bg-red-700 focus:ring-red-500",warning:"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",info:"bg-sky-600 hover:bg-sky-700 focus:ring-sky-500",success:"bg-green-600 hover:bg-green-700 focus:ring-green-500"};return t[l.type]||t.warning}),a=T(!1),u=()=>{a.value||(a.value=!0,s("confirm"))},c=()=>{a.value||s("cancel")},g=t=>{t.key==="Escape"&&c()};return typeof window<"u"&&document.addEventListener("keydown",g),(t,o)=>(i(),v(M,{to:"body"},[e.show?(i(),f("div",L,[n("div",_,[n("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:c}),o[4]||(o[4]=n("span",{class:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true"},"​",-1)),n("div",{class:d(["inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6 -translate-y-48",b.value])},[n("div",N,[e.showIcon?(i(),f("div",{key:0,class:d(["mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10",x.value])},[e.type==="danger"?(i(),f("svg",{key:0,class:d(["h-6 w-6",m.value]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},o[0]||(o[0]=[n("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"},null,-1)]),2)):e.type==="warning"?(i(),f("svg",{key:1,class:d(["h-6 w-6",m.value]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},o[1]||(o[1]=[n("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]),2)):e.type==="info"?(i(),f("svg",{key:2,class:d(["h-6 w-6",m.value]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},o[2]||(o[2]=[n("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]),2)):e.type==="success"?(i(),f("svg",{key:3,class:d(["h-6 w-6",m.value]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},o[3]||(o[3]=[n("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"},null,-1)]),2)):w("",!0)],2)):w("",!0),n("div",P,[n("h3",V,h(e.title),1),n("div",D,[n("p",F,h(e.message),1)])])]),n("div",K,[n("button",{type:"button",class:d(["w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none sm:ml-3 sm:w-auto sm:text-sm transition-colors duration-200",p.value]),disabled:a.value,onClick:u},[a.value?(i(),v(z,{key:0,color:"#ffffff",class:"mr-2"})):w("",!0),B(" "+h(e.confirmText),1)],10,A),n("button",{type:"button",class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:w-auto sm:text-sm transition-colors duration-200",disabled:a.value,onClick:c},h(e.cancelText),9,O)])],2)])])):w("",!0)]))}},$=E(R,[["__scopeId","data-v-f33cfcb1"]]),k=(e,r=e)=>{try{return I.global.t(e)}catch{return r}},C=(e={})=>new Promise(r=>{const{title:l,message:s,confirmText:b,cancelText:x,type:m="warning",showIcon:p=!0,onConfirm:a=null,onCancel:u=null}=e,c=document.createElement("div");document.body.appendChild(c);const g=j({render(){return S($,{show:!0,title:l,message:s,confirmText:b,cancelText:x,type:m,showIcon:p,onConfirm:async()=>{a&&await new Promise(async o=>{await a(o)}),t(),r(!0)},onCancel:()=>{t(),u&&u(),r(!1)},onClose:()=>{t(),u&&u(),r(!1)}})}}),t=()=>{g.unmount(),document.body.removeChild(c)};g.mount(c)}),U=(e,r=null,l=null)=>{const s=l||k;return C({title:s("deleteConfirmation"),message:e,confirmText:s("delete"),cancelText:s("cancel"),type:"danger",onConfirm:r})},W=(e,r=null,l=null)=>{const s=l||k;return C({title:s("saveChanges"),message:e,confirmText:s("save"),cancelText:s("cancel"),type:"info",onConfirm:r})};export{W as a,U as c};
