import{f as p}from"./index-DHV2tfOS.js";import{i as b}from"./@inertiajs-BhKdJayA.js";import{_ as m}from"./AppLayout-m_I9gnvX.js";import{k as i,o as a,S as d,a as t,P as s,c as r,F as _,M as f,a4 as h}from"./@vue-BnW70ngI.js";import"./moment-C5S46NFB.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BWHXZF7Q.js";import"./PrimaryButton-DE9sqoJj.js";const x=["textContent"],C={class:"max-w-7xl mx-auto p-6"},w={class:"bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden"},g={class:"p-datatable-table w-full w-border"},y={class:"text-left flex"},v=["textContent"],k={class:"text-left flex border-t"},B=["textContent"],D={class:"border-l flex-1"},I=["innerHTML"],$={key:2},J={__name:"Detail",props:{post:Object},setup(l){const u=[{label:"ID",attribute:"post_id"},{label:"createdBy",attribute:"username"},{label:"createdByID",attribute:"user_id"},{label:"postContent",attribute:"content"},{label:"postCreatedAt",attribute:"created_at"},{label:"postType",attribute:"tag"},{label:"answerCount",attribute:"answer_count"},{label:"postStatus",attribute:"status_label"}],c=(e,n)=>{const o=e[n.attribute];return["postCreatedAt"].includes(n.label)?p(o):o};return(e,n)=>(a(),i(m,{title:e.$t("postInfo")},{header:d(()=>[t("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:s(e.$t("postInfo"))},null,8,x)]),default:d(()=>[t("div",C,[t("div",w,[t("table",g,[t("thead",null,[t("tr",y,[t("th",{colspan:"2",textContent:s(e.$t("generalInfo"))},null,8,v)])]),t("tbody",null,[(a(),r(_,null,f(u,o=>t("tr",k,[t("td",{class:"w-1/4",textContent:s(e.$t(o.label))},null,8,B),t("td",D,[o.label==="answerCount"?(a(),i(h(b),{key:0,class:"hover:text-red-600 hover:underline",textContent:s(l.post.answer_count),href:e.route("postAnswer.list",{post:l.post.post_id})},null,8,["textContent","href"])):o.label==="postContent"?(a(),r("div",{key:1,innerHTML:l.post.content},null,8,I)):(a(),r("div",$,s(c(l.post,o)),1))])])),64))])])])])]),_:1},8,["title"]))}};export{J as default};
