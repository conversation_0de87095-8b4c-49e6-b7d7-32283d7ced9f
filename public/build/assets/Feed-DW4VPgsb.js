import{f as y}from"./index-BxmPUm2h.js";import{_ as b}from"./AppLayout-_qQ0AdHn.js";import{_ as f}from"./PrimaryButton-DE9sqoJj.js";import{_ as p}from"./LoadingIcon-CLD0VpVl.js";import{v as $,k as l,o as n,S as i,a as e,c as m,K as c,R as k,P as s,F as w,M as N,a4 as T,l as x}from"./@vue-BnW70ngI.js";import"./moment-C5S46NFB.js";/* empty css                                                    *//* empty css                                                                     */import"./app-EptGTPPo.js";import"./axios-t--hEgTQ.js";import"./@inertiajs-Dt0-hqjZ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./ziggy-js-C7EU8ifa.js";import"./pinia-Ddsh4R0D.js";import"./primevue-CrCPcMFN.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./izitoast-CYQMso0-.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";const v={class:"flex-1 flex items-center"},M=["textContent"],P=["textContent"],B={class:"mx-auto py-6 px-6"},D={class:"w-full feed-table"},F={class:"text-left flex"},L=["textContent"],E=["textContent"],H=["innerHTML"],q=["textContent"],A=["textContent"],S=["textContent"],V={class:"flex border-t"},z=["textContent"],j=["textContent"],I=["innerHTML"],K=["textContent"],O=["textContent"],R=["textContent"],G={key:1,class:"flex border-t"},J=["textContent"],Q={key:0,class:"mt-6"},U=["textContent"],Rt={__name:"Feed",props:{posts:Array,hasNextPage:Boolean,user:Object},setup(r){const d=r,t=$({refreshing:!1,loading:!1,hasNextPage:d.hasNextPage,posts:[],next:0});d.posts.forEach(o=>t.posts.push(o));const u=async()=>{if(t.loading)return!1;t.loading=!0,await window.axios.get(route("user.feed",{user:d.user.user_id}),{params:{next:t.next,axios:!0}}).then(o=>{t.hasNextPage=o.data.hasNextPage,o.data.posts.forEach(h=>t.posts.push(h))}).catch(o=>{}).finally(()=>{t.loading=!1,t.refreshing=!1})},_=async()=>{if(t.loading)return!1;t.posts=[],t.refreshing=!0,t.next=0,await u()},g=async()=>{if(t.loading)return!1;t.refreshing=!1,t.next=1,await u()};return(o,h)=>(n(),l(b,{title:o.$t("userFeed")},{header:i(()=>[e("div",v,[e("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:s(o.$t("userFeedExtra",{name:r.user.name.length?r.user.name:"N/A",id:r.user.user_id}))},null,8,M),x(f,{class:"normal-case",disabled:t.loading,onClick:_},{default:i(()=>[t.refreshing?(n(),l(p,{key:0,class:"mr-2"})):c("",!0),e("span",{class:"text-sm",textContent:s(o.$t("refresh"))},null,8,P)]),_:1},8,["disabled"])])]),default:i(()=>[e("div",B,[e("div",{class:k(["bg-white rounded-md shadow flex flex-col overflow-auto",{"grid-loading":t.loading}])},[t.loading?(n(),l(p,{key:0,class:"grid-loading-icon",size:24,color:"rgb(55 65 81)"})):c("",!0),e("table",D,[e("tbody",null,[e("tr",F,[e("th",{class:"number-column extra",textContent:s(o.$t("STT"))},null,8,L),e("th",{class:"number-column",textContent:s(o.$t("postID"))},null,8,E),e("th",{class:"title-flex-column",innerHTML:o.$t("postContent")},null,8,H),e("th",{class:"post-type-column",textContent:s(o.$t("postType"))},null,8,q),e("th",{class:"time-column",textContent:s(o.$t("postCreatedAt"))},null,8,A),e("th",{class:"post-type-column",textContent:s(o.$t("qaType"))},null,8,S)]),t.posts.length>0?(n(!0),m(w,{key:0},N(t.posts,(a,C)=>(n(),m("tr",V,[e("td",{class:"number-column extra",textContent:s(C+1)},null,8,z),e("td",{class:"number-column",textContent:s(a.post_id)},null,8,j),e("td",{class:"title-flex-column",innerHTML:a.content},null,8,I),e("td",{class:"post-type-column",textContent:s(a.tag)},null,8,K),e("td",{class:"time-column",textContent:s(T(y)(a.created_at))},null,8,O),e("td",{class:"post-type-column",textContent:s(o.$t("qaType."+a.qa_type))},null,8,R)]))),256)):(n(),m("tr",G,[e("td",{colspan:"6",textContent:s(o.$t(t.loading?"loading":"emptyData"))},null,8,J)]))])])],2),t.hasNextPage?(n(),m("div",Q,[x(f,{class:"normal-case mx-auto",disabled:t.loading,onClick:g},{default:i(()=>[t.loading&&!t.refreshing?(n(),l(p,{key:0,class:"mr-2"})):c("",!0),e("span",{class:"text-sm",textContent:s(o.$t("loadMore"))},null,8,U)]),_:1},8,["disabled"])])):c("",!0)])]),_:1},8,["title"]))}};export{Rt as default};
