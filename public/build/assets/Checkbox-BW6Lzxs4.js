import{b as u,X as n,a6 as d,c as p,o as i}from"./@vue-BnW70ngI.js";const k=["value"],h={__name:"Checkbox",props:{checked:{type:[<PERSON><PERSON><PERSON>,<PERSON><PERSON>an],default:!1},value:{type:String,default:null}},emits:["update:checked"],setup(e,{emit:c}){const s=c,l=e,t=u({get(){return l.checked},set(o){s("update:checked",o)}});return(o,a)=>n((i(),p("input",{class:"rounded border-gray-300 text-sky-600 shadow-sm focus:ring-sky-500","onUpdate:modelValue":a[0]||(a[0]=r=>t.value=r),type:"checkbox",value:e.value},null,8,k)),[[d,t.value]])}};export{h as _};
