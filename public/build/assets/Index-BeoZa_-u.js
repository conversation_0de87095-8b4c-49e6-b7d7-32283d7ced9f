import{f as I,c as P}from"./index-BxmPUm2h.js";import{Q as M,T as O,i as $,N as T}from"./@inertiajs-Dt0-hqjZ.js";import{s as E,a as d}from"./primevue-CrCPcMFN.js";import{_ as F}from"./AppLayout-_qQ0AdHn.js";import{_ as C}from"./InputLabel-BTXevqr4.js";import{_ as L}from"./SearchInput-CdoSYJL3.js";import{_ as U}from"./Pagination-D56Hn3as.js";import{_ as q}from"./LoadingIcon-CLD0VpVl.js";import{_ as A,a as K}from"./SecondaryButton-BoI1NwE9.js";import{_ as Q}from"./RedButton-D21iPtqa.js";import{_ as R}from"./FixedSelectionBox-Bk5LSyGJ.js";import{s as p}from"./ziggy-js-C7EU8ifa.js";import{_ as z}from"./GridContainer-BC3u-41x.js";import{i as G,v as H,b as J,c as _,o as f,l as s,k as v,K as w,S as n,a,a4 as o,O as x,P as m,F as y}from"./@vue-BnW70ngI.js";import"./moment-C5S46NFB.js";/* empty css                                                    *//* empty css                                                                     */import"./app-EptGTPPo.js";import"./axios-t--hEgTQ.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./pinia-Ddsh4R0D.js";import"./@primevue-BllOwQ3c.js";import"./@primeuix-CKSY3gPt.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./izitoast-CYQMso0-.js";import"./deepmerge-CxfS31y9.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./PrimaryButton-DE9sqoJj.js";import"./SelectionBox-D4JR3fGi.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-CyTLADhX.js";import"./TextInput-DUNPEFms.js";import"./@headlessui-gOb5_P77.js";const W={class:"flex-1 flex items-center"},X=["textContent"],Y={class:"p-6 sm:mx-2"},Z={class:"flex items-stretch"},ee={class:"mt-0 w-80 mr-8"},te={class:"mt-0 w-64 mr-8"},se=["href","textContent"],oe=["onClick"],ae=["textContent"],le=["textContent"],re={class:"flex items-center justify-end px-3 py-3"},ie=["textContent"],ht={__name:"Index",props:{filters:Object,news:Object},setup(l){const k=G("$toast"),g=M(),h=l,i=O({search:h.filters.search??"",status:h.filters.status??"",limit:h.filters.limit??10}),e=H({showSearchClearButton:(h.filters.search??"").trim().length>0,searching:!1,showModal:!1,open:!1,deleteId:null,deleting:!1,selectOptions:[{value:"enable",label:"アクティブ"},{value:"disable",label:"削除"}],activePage:null,busy:J(()=>e.activePage!==null||e.searching||i.processing)}),u=()=>{if(i.processing)return!1;i.transform(t=>P(t)).get(p("news.list"),{preserveScroll:!0,onSuccess:()=>{}})},S=()=>{e.showSearchClearButton=!0,u()},N=()=>{i.search="",e.showSearchClearButton=!1,u()},B=t=>{e.deleteId=t,e.showModal=!0,setTimeout(()=>e.open=!0,150)},b=async()=>{e.open=!1,setTimeout(()=>e.showModal=!1,150)},V=()=>{e.deleting||T.post(p("news.delete"),{id:e.deleteId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{e.deleting=!0},onSuccess:()=>{e.deleteId=null,g.props.jetstream.flash.message&&k.success(g.props.jetstream.flash.message),b()},onFinish:()=>{e.deleting=!1}})},j=t=>{e.activePage=t,e.searching=!0},D=t=>{i.limit=t,i.search="",i.status="",u()};return(t,c)=>(f(),_(y,null,[s(F,{title:t.$t("listNews")},{header:n(()=>[a("div",W,[a("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:m(t.$t("listNews"))},null,8,X),s(o($),{class:"primary-button text-sm",href:o(p)("news.form",{news:""}),textContent:m(t.$t("addNew"))},null,8,["href","textContent"])])]),default:n(()=>[a("div",Y,[a("div",Z,[a("div",ee,[s(C,{for:"search",value:t.$t("search")},null,8,["value"]),s(L,{id:"search",class:"mt-1 block w-full",modelValue:o(i).search,"onUpdate:modelValue":c[0]||(c[0]=r=>o(i).search=r),placeholder:t.$t("newsSearch"),disabled:e.busy,"show-clear-button":e.showSearchClearButton,onInput:c[1]||(c[1]=r=>e.showSearchClearButton=!1),onClearSearch:N,onEnter:S},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),a("div",te,[s(C,{for:"status-search",value:t.$t("status"),class:"mb-1"},null,8,["value"]),s(R,{modelValue:o(i).status,"onUpdate:modelValue":c[2]||(c[2]=r=>o(i).status=r),placeholder:t.$t("all"),disabled:e.busy,options:e.selectOptions,clearable:!0,onCleared:u,onSelected:u},null,8,["modelValue","placeholder","disabled","options"])])]),s(z,{loading:e.busy},{default:n(()=>[s(o(E),{value:l.news.data},{empty:n(()=>[x(m(t.$t(l.filters.search&&l.filters.search!==""||l.filters.status&&l.filters.status!==""?"emptyResult":"emptyData")),1)]),default:n(()=>[s(o(d),{class:"number-column small",field:"news_id",header:t.$t("ID")},null,8,["header"]),s(o(d),{class:"time-column",header:t.$t("newsCreatedAt")},{body:n(({data:r})=>[x(m(o(I)(r.created_at)),1)]),_:1},8,["header"]),s(o(d),{class:"title-flex-column",field:"title",header:t.$t("newsTitle")},null,8,["header"]),s(o(d),{class:"number-column",header:t.$t("url")},{body:n(({data:r})=>[a("a",{class:"text-blue-600 hover:text-blue-700 hover:underline",href:o(p)("news.detail",{news:r.news_id}),target:"_blank",textContent:m(t.$t("view"))},null,8,se)]),_:1},8,["header"]),s(o(d),{class:"status-column",field:"status_label",header:t.$t("status")},null,8,["header"]),s(o(d),{class:"action-column small"},{body:n(({data:r})=>[r.status!==0?(f(),_(y,{key:0},[s(o($),{href:o(p)("news.form",{news:r.news_id})},{default:n(()=>c[3]||(c[3]=[a("i",{class:"pi pi-pen-to-square text-blue-400 hover:text-blue-600"},null,-1)])),_:2},1032,["href"]),a("i",{class:"pi pi-trash text-red-400 hover:text-red-600 hover:cursor-pointer",onClick:ne=>B(r.news_id)},null,8,oe)],64)):w("",!0)]),_:1})]),_:1},8,["value"])]),_:1},8,["loading"]),l.news.data.length>0?(f(),v(U,{key:0,class:"mt-5 flex items-center justify-center mx-auto",links:l.news.links,active:e.activePage,disabled:e.busy,limit:l.news.per_page,total:l.news.total,from:l.news.from,to:l.news.to,onProgress:j,onChangeLimit:D},null,8,["links","active","disabled","limit","total","from","to"])):w("",!0)])]),_:1},8,["title"]),e.showModal?(f(),v(K,{key:0,show:e.open,onClose:b},{default:n(()=>[a("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:m(t.$t("confirm"))},null,8,ae),a("div",{class:"border-t border-b p-4",textContent:m(t.$t("confirmDeleteNewsMessage"))},null,8,le),a("div",re,[s(A,{class:"mr-3 text-sm",textContent:m(t.$t("cancel")),onClick:b},null,8,["textContent"]),s(Q,{class:"text-sm overflow-hidden h-[34px]",onClick:V},{default:n(()=>[e.deleting?(f(),v(q,{key:0,class:"mr-1"})):w("",!0),a("span",{class:"text-sm text-white",textContent:m(t.$t("delete"))},null,8,ie)]),_:1})])]),_:1},8,["show"])):w("",!0)],64))}};export{ht as default};
