const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Index-BxmaaYfS.js","assets/vue-i18n-kWKo0idO.js","assets/@intlify-xvnhHnag.js","assets/@vue-BnW70ngI.js","assets/@inertiajs-Dt0-hqjZ.js","assets/axios-t--hEgTQ.js","assets/deepmerge-CxfS31y9.js","assets/call-bind-apply-helpers-B4ICrQ1R.js","assets/function-bind-CHqF18-c.js","assets/es-errors-CFxpeikN.js","assets/qs-puzarlXf.js","assets/side-channel-DG-5PZt1.js","assets/object-inspect-Cfg_CA0t.js","assets/side-channel-list-BvdnDMxL.js","assets/side-channel-map-ru-_NPG8.js","assets/get-intrinsic-BFhK1_aj.js","assets/es-object-atoms-Ditt1eQ6.js","assets/math-intrinsics-Cv-yPkyD.js","assets/gopd-fcd2-aIC.js","assets/es-define-property-bDCdrV83.js","assets/has-symbols-BaUvM3gb.js","assets/get-proto-D3FFaEao.js","assets/dunder-proto-Cj7W6A2l.js","assets/hasown-DwiY0sux.js","assets/call-bound-C_1-0vVo.js","assets/side-channel-weakmap-CGy7gfKF.js","assets/nprogress-CVH3SeWI.js","assets/lodash.clonedeep-DcBkkazC.js","assets/lodash.isequal-SGFeuw-r.js","assets/@element-plus-CyTLADhX.js","assets/AppLayout-_qQ0AdHn.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/SecondaryButton-BoI1NwE9.js","assets/PrimaryButton-DE9sqoJj.js","assets/AuthenticationCardLogo-Bk9nzujc.css","assets/Checkbox-BW6Lzxs4.js","assets/ConfirmationModal-ClaGRyF5.js","assets/DialogModal-LfgJQ09a.js","assets/LoadingIcon-CLD0VpVl.js","assets/LoadingIcon-CiCW7nnq.css","assets/InputLabel-BTXevqr4.js","assets/TextInput-DUNPEFms.js","assets/InputError-gQdwtcoE.js","assets/ApiTokenManager-DiSn4APj.js","assets/ActionMessage-yNeSLSLA.js","assets/ActionSection-d716unDa.js","assets/DangerButton-C49GvHso.js","assets/Index-DTGpuJbl.js","assets/lodash-Bx_YDCCc.js","assets/ziggy-js-C7EU8ifa.js","assets/primevue-CrCPcMFN.js","assets/@primeuix-CKSY3gPt.js","assets/@primevue-BllOwQ3c.js","assets/SearchInput-CdoSYJL3.js","assets/Pagination-D56Hn3as.js","assets/FixedSelectionBox-Bk5LSyGJ.js","assets/SelectionBox-D4JR3fGi.js","assets/@heroicons-BLousAGu.js","assets/@headlessui-gOb5_P77.js","assets/TextAreaInput-DHjed6qD.js","assets/Form-BKk0RWcz.js","assets/RedButton-D21iPtqa.js","assets/SurveySelectionBox-xjDrcmsD.js","assets/pinia-Ddsh4R0D.js","assets/index-BxmPUm2h.js","assets/moment-C5S46NFB.js","assets/ConfirmModal-Bee9A7JT.css","assets/laravel-vite-plugin-DEL3ZhID.js","assets/@vueup-DIjuzNyW.js","assets/quill-D-mw74c0.js","assets/quill-delta-D18WSM5Q.js","assets/fast-diff-DNDSwfiB.js","assets/@vueup-CrSYVOAc.css","assets/izitoast-CYQMso0-.js","assets/Index-BSNcjAa6.js","assets/ConfirmPassword-Bg3WfLWg.js","assets/AuthenticationCardLogo-B-NI73cE.js","assets/ForgotPassword-a4Z8wGOe.js","assets/Login-tVFDvVGW.js","assets/Register-COfWF3Kf.js","assets/ResetPassword-DNvf_p_e.js","assets/TwoFactorChallenge-Ny93PZzX.js","assets/VerifyEmail-Bp3spFE7.js","assets/Setting-DO2tnMv5.js","assets/UnlockPost-Bl0QjLHu.js","assets/confirmModal-DS4sumdl.js","assets/UnlockPost-DTY9Cz7e.css","assets/CommunityFormContent-D4B2O9XM.js","assets/CommunityFormContent.vue_vue_type_script_setup_true_lang-Dl2CgMFy.js","assets/ImageInput-BV1wAASf.js","assets/Form-D-q25kiI.js","assets/Index-l_Mr0kHA.js","assets/GridContainer-BC3u-41x.js","assets/Error-DzrmWfTG.js","assets/Detail-DTbaUjn6.js","assets/Form-CWx1Bw4l.js","assets/Index-BeoZa_-u.js","assets/CommunityFormModal-Cm9J6GGm.js","assets/Detail-v2J2Cdf-.js","assets/Form-Dtbk6W_1.js","assets/Form-HQe54YOH.css","assets/History-BO3_a1lb.js","assets/Index-CTC7Rhsf.js","assets/Detail-BQsbFEKB.js","assets/Index-DBEOG3wS.js","assets/Form-DevAWlwC.js","assets/Index-DhCjjOiw.js","assets/ChangePassword-hJv5iVnS.js","assets/UpdatePasswordForm-C-XBt4ZQ.js","assets/DeleteUserForm-i7qyxAmA.js","assets/LogoutOtherBrowserSessionsForm-B4Ipbiu7.js","assets/TwoFactorAuthenticationForm-6SUwYxoT.js","assets/UpdateProfileInformationForm-DoqQHZAw.js","assets/Show-BxB7ebM0.js","assets/Form-DDAzErHK.js","assets/Index-CUHwZHLR.js","assets/Form-i4svYnka.js","assets/Index-u_jjbVsY.js","assets/Sort-Bsj7HNGi.js","assets/Attribute-UfrEzqz_.js","assets/Detail-DOo2Euzb.js","assets/Feed-DW4VPgsb.js","assets/Index-B3rlTfWi.js"])))=>i.map(i=>d[i]);
import{a as y}from"./axios-t--hEgTQ.js";import{a3 as w,h as P}from"./@vue-BnW70ngI.js";import{j as v}from"./@inertiajs-Dt0-hqjZ.js";import{r as T}from"./laravel-vite-plugin-DEL3ZhID.js";import{o as $}from"./ziggy-js-C7EU8ifa.js";import{c as _}from"./pinia-Ddsh4R0D.js";import{T as D}from"./primevue-CrCPcMFN.js";import{P as A,M as I}from"./@primevue-BllOwQ3c.js";import"./@vueup-DIjuzNyW.js";import{c as f}from"./vue-i18n-kWKo0idO.js";import{i as S}from"./izitoast-CYQMso0-.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@primeuix-CKSY3gPt.js";import"./quill-D-mw74c0.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./@intlify-xvnhHnag.js";const C="modulepreload",E=function(o){return"/build/"+o},p={},t=function(s,e,r){let m=Promise.resolve();if(e&&e.length>0){document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),i=(n==null?void 0:n.nonce)||(n==null?void 0:n.getAttribute("nonce"));m=Promise.allSettled(e.map(c=>{if(c=E(c),c in p)return;p[c]=!0;const u=c.endsWith(".css"),h=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${h}`))return;const a=document.createElement("link");if(a.rel=u?"stylesheet":C,u||(a.as="script"),a.crossOrigin="",a.href=c,i&&a.setAttribute("nonce",i),document.head.appendChild(a),u)return new Promise((d,g)=>{a.addEventListener("load",d),a.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${c}`)))})}))}function l(n){const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=n,window.dispatchEvent(i),!i.defaultPrevented)throw n}return m.then(n=>{for(const i of n||[])i.status==="rejected"&&l(i.reason);return s().catch(l)})},L=Intl.DateTimeFormat().resolvedOptions().timeZone;window.axios=y.create({baseURL:"/",withCredentials:!0,headers:{Accept:"application/json","X-TIMEZONE":L}});window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";const k="HonNe Console",b="Email",N="Mật khẩu",q="Mật khẩu hiện tại",x="Mật khẩu mới",B="Xác nhận mật khẩu",O="Nhớ mật khẩu",M="Đăng nhập",R="Danh sách khảo sát",V="Thông tin cá nhân",F="Cập nhật thông tin cá nhân",U="Tên người dùng",Q="Thay đổi mật khẩu",W="Đăng xuất",z="Hủy",H="Lưu",j="Xác nhận",K="Bạn có thực sự muốn đăng xuất không?",X="Thay đổi mật khẩu thành công.",Y="Thêm mới",Z="Danh sách",G="Tiêu đề khảo sát",J="Câu hỏi",tt="Loại câu hỏi",et="Chế độ công khai",st="Nội dung câu hỏi",ot="Điểm thưởng",nt="Thêm câu hỏi",rt="Lựa chọn trả lời",it="Tùy chọn {index}",ct="+ Thêm tùy chọn",at="Checkbox",ut="Pulldown",mt="Tự luận",lt="Tự luận có sao",pt="Không công khai",ht="Công khai trên nền tảng",dt="Công khai trên ứng dụng",gt="Công khai trên cả nền tảng và ứng dụng",yt="Tìm kiếm khảo sát",wt="Nhập tiêu đề khảo sát...",Pt="Không có dữ liệu.",vt="Không tìm thấy kết quả theo yêu cầu.",Tt="ID",$t="Survey ID",_t="Question ID",Dt="Answer ID",At="Lựa chọn trả lời",It="Thứ tự khảo sát",ft="No.",St="Chọn khảo sát...",Ct="Nhập để tìm khảo sát...",Et="Danh sách khảo sát chèn",Lt="Tìm kiếm khảo sát chèn",kt="Tiêu đề khảo sát chèn",bt="Tiêu đề khảo sát được chèn",Nt="Bạn có thực sự muốn xóa dữ liệu này không?",qt="Khảo sát",xt="Khảo sát được chèn",Bt="Câu trả lời",Ot="Có lỗi xảy ra, xin vui lòng thử lại!",Mt="Chọn câu hỏi...",Rt="Chọn câu trả lời...",Vt="Bạn đã chọn câu hỏi này rồi.",Ft="Bạn có thực sự muốn xóa khảo sát này không?",Ut="ID được chèn",Qt="Tìm kiếm người dùng",Wt="Danh sách người dùng",zt="Thời gian đăng ký",Ht="Số ĐT",jt="Tên người dùng",Kt="Vai trò",Xt="Tổng điểm tích lũy",Yt="Điểm hiện tại",Zt="Thời gian bắt đầu khảo sát",Gt="Ngày giờ trả lời cuối cùng",Jt="Trạng thái",te="Người dùng",ee="Quản trị viên",se="Chưa hoàn thành",oe="Hoàn tất trả lời",ne="Đang trả lời",re="Tìm theo tên hoặc số điện thoại...",ie="Có lỗi xảy ra",ce="Có lỗi xảy ra xin vui lòng thử lại sau.",ae="Thông tin người dùng",ue="Thông tin chung",me="ID người dùng",le="Số câu hỏi",pe="Số câu trả lời",he="Số comment",de="Thời gian đăng nhập lần cuối",ge="Bạn có thực sự muốn xoá người dùng này không?",ye="Danh sách câu hỏi",we="Tìm kiếm",Pe="Tìm kiếm theo ID hoặc tên",ve="Người tạo",Te="User ID",$e="Nội dung câu hỏi",_e="Phân loại",De="Thời gian hỏi",Ae="Trạng thái câu hỏi",Ie="Thông tin câu hỏi",fe="Bạn có thực sự muốn xoá câu hỏi này không?",Se="Danh sách câu trả lời",Ce="Câu hỏi",Ee="Tìm theo ID hoặc nội dung câu trả lời",Le="Tìm theo ID hoặc nội dung câu hỏi",ke="Nội dung câu trả lời",be="Thời gian trả lời",Ne="Post ID",qe="Tên người hỏi",xe="Người trả lời",Be="Bạn có thực sự muốn xoá câu trả lời này không?",Oe="ID người trả lời",Me="ID người hỏi",Re="Thông tin câu trả lời",Ve="Tìm theo ID",Fe="Best Answerr",Ue="FEED",Qe="Thuộc tính",We="Chỉnh sửa",ze="Thuộc tính người dùng",He="Top LIKE theo ngày",je="Top LIKE",Ke="Ngày",Xe="Không có dữ liệu lượt thích.",Ye="TOP 15",Ze="Lịch sử câu hỏi, trả lời",Ge="Feed của người dùng",Je="Feed của người dùng: {name} - ID: {id}",ts="Tải lại",es="STT",ss="Loại tối ưu",os="Đang tải dữ liệu...",ns="System Settings",rs="Hiển thị ở Q&A",is="Thứ tự",cs="Tỷ lệ",as="Hiển thị tất cả",us="Tải thêm",ms="Lượt thích",ls="Tương đồng",ps="Không có dữ liệu tương đồng.",hs="Top tương đồng",ds="Top tương đồng theo ngày",gs="Top 50",ys="Lịch sử xem",ws="Lịch sử xem bài viết",Ps="User ID",vs="Người xem",Ts="Xem lúc",$s="Người xem",_s="Tìm theo ID hoặc tên người xem",Ds="Ngày xem",As="Cập nhật thông tin cá nhân thành công.",Is="Danh sách trợ lý",fs="Trợ lý",Ss="Tìm kiếm trợ lý",Cs="AI Model",Es="Tên trợ lý",Ls="Lĩnh vực",ks="Chuyên môn",bs="Mô tả công việc",Ns="Ngôn ngữ",qs="Tiếng Việt",xs="Tiếng Nhật",Bs="Thêm mới",Os="Thêm mới trợ lý",Ms="Số lần report",Rs="Trạng thái report",Vs="Có",Fs="Không",Us="Tất cả",Qs="Ngày sinh",Ws="Tuổi",zs="Bạn có thực sự muốn bật câu hỏi này trong TOPICS không?",Hs="Bạn có thực sự muốn tắt câu hỏi này trong TOPICS không?",js="TOPICS",Ks="Tin tức",Xs="Danh sách tin tức",Ys="Tìm kiếm tin tức",Zs="Nội dung",Gs="Thời gian tạo",Js="Thêm mới tin tức",to="Cập nhật tin tức",eo="Tiêu đề tin tức",so="Bạn có thực sự muốn xóa tin tức này không?",oo="URL",no="View",ro="Chức danh",io="Công việc",co="Tên trường",ao="Điểm mạnh, điều quan tâm",uo="Lĩnh vực công việc phụ trách",mo="Các vấn đề có thể giải quyết, thế mạnh chuyên môn",lo="Dịch vụ đang phụ trách",po="Quest",ho="Danh sách Quest",go="Tìm kiếm Quest",yo="Thêm mới Quest",wo="Cập nhật Quest",Po="Tiêu đề",vo="Mô tả",To="Loại Quest",$o="Đơn vị tính",_o="Số điểm",Do="Hình ảnh",Ao="Thứ tự",Io="Chọn file...",fo="Chọn",So="File upload vượt quá kích thước tối đa cho phép (15MB).",Co="Bạn có thực sự muốn thực hiện không?",Eo="Cấu hình thời gian màn HOME",Lo="Nhóm",ko="Thời gian (giờ)",bo={label:"Premium Feature",list:"Danh sách Premium Feature",description:"Mô tả tính năng Premium",name:"Tên tính năng",price:"Giá (coin)",type:"Loại",create:"Thêm mới Premium Feature",update:"Cập nhật Premium Feature",search:"Tìm kiếm Premium Feature"},No="コイン",qo="種類",xo="1ページに",Bo="件表示。全{total}件中、{from}件目から{to}件目までを表示中です。",Oo="Community",Mo="Answerr_Q",Ro="Answerr_Topic",Vo="Các loại khác",Fo="Tạo mới câu hỏi",Uo="Cập nhật câu hỏi",Qo="Thêm mới community",Wo="Tên community",zo="Mô tả community",Ho="Camera Roll",jo="Unlock Posts",Ko="Unlock Time",Xo="Size",Yo="Small",Zo="Large",Go="Xác nhận",Jo="Lưu thay đổi",tn="Tiếp tục",en="Cảnh báo",sn="Admin Community",on="Tạo mới community",nn="Cập nhật community",rn={user:"Người dùng",quest:"Quest",post:"Câu hỏi",answer:"Câu trả lời",news:"News",history:"Lịch sử xem",setting:"Settings",adminCommunity:"Community"},cn={10:"10",20:"20",50:"50",100:"100",200:"200",appName:k,email:b,password:N,currentPassword:q,newPassword:x,confirmPassword:B,rememberMe:O,login:M,listSurvey:R,profile:V,updateProfile:F,profileName:U,changePassword:Q,logout:W,cancel:z,save:H,confirm:j,logoutConfirmationText:K,passwordUpdatedSuccessfully:X,addNew:Y,list:Z,surveyTitle:G,question:J,questionType:tt,questionPublic:et,questionContent:st,questionPoint:ot,addNewQuestion:nt,answerOptions:rt,answerChoice:it,addMoreAnswer:ct,checkbox:at,selectBox:ut,textBox:mt,textBoxWithStar:lt,publicNone:pt,publicPlatform:ht,publicApp:dt,publicPlatformApp:gt,surveySearch:yt,enterSurveySearch:wt,emptyData:Pt,emptyResult:vt,ID:Tt,surveyID:$t,questionID:_t,answerID:Dt,answerContent:At,surveySort:It,number:ft,selectSurvey:St,searchSurveyPlaceholder:Ct,listAttachedSurvey:Et,attachedSurveySearch:Lt,attachedSurveyTitle:kt,surveyTitleWasAttached:bt,delete:"Xóa",attachedSurveyDeleteConfirmation:Nt,survey:qt,surveyWasAttached:xt,answer:Bt,commonErrorMessage:Ot,selectQuestion:Mt,selectAnswer:Rt,questionWasSelected:Vt,surveyDeleteConfirmation:Ft,surveyIDWasAttached:Ut,userSearch:Qt,listUser:Wt,registerAt:zt,phone:Ht,username:jt,role:Kt,totalPoint:Xt,currentPoint:Yt,answerStartedAt:Zt,answerEndedAt:Gt,status:Jt,customer:te,administrator:ee,uncompleted:se,completed:oe,isAnswering:ne,searchByNameOrPhone:re,errorTitle:ie,errorMessage:ce,"error.503":"Xin lỗi, chúng tôi đang bảo trì. Vui lòng kiểm tra lại sau.","error.500":"Ồ, có lỗi xảy ra trên máy chủ của chúng tôi.","error.404":"Xin lỗi, trang bạn đang tìm kiếm không tìm thấy.","error.403":"Rất tiếc, bạn không được phép truy cập vào trang này.",userInfo:ae,generalInfo:ue,userID:me,postCount:le,answerCount:pe,commentCount:he,lastLoggedInTime:de,confirmDeleteUserMessage:ge,listPost:ye,search:we,postUserSearch:Pe,createdBy:ve,createdByID:Te,postContent:$e,postType:_e,postCreatedAt:De,postStatus:Ae,postInfo:Ie,confirmDeletePostMessage:fe,listPostAnswer:Se,post:Ce,answerSearch:Ee,postSearch:Le,postAnswerContent:ke,answerCreatedAt:be,postID:Ne,postCreatedBy:qe,answeredBy:xe,confirmDeletePostAnswerMessage:Be,userAnswerID:Oe,userPostID:Me,postAnswerInfo:Re,searchByID:Ve,bestAnswerCount:Fe,feed:Ue,attribute:Qe,edit:We,userAttribute:ze,topLikeByDay:He,topLike:je,date:Ke,emptyLikedData:Xe,top15:Ye,relatedData:Ze,userFeed:Ge,userFeedExtra:Je,refresh:ts,STT:es,qaType:ss,"postType.default":"Free","postType.smile":"ゆるい質問・相談","postType.not_smile":"真剣な質問・相談","postType.laugh":"オーギリ","postType.raise_hand":"体験者レビュー","postType.multiple":"どっち","qaType.friend":"Friend","qaType.follow":"Follow","qaType.similar":"Tương đồng","qaType.like":"Like","qaType.general":"Tổng hợp","qaType.latest":"Mới nhất",loading:os,systemSetting:ns,qaListConfig:rs,order:is,rate:cs,displayAll:as,loadMore:us,tabLike:ms,tabSimilar:ls,emptySimilarData:ps,topSimilar:hs,topSimilarByDay:ds,top50:gs,viewedData:ys,postViewHistory:ws,viewedByID:Ps,viewedBy:vs,viewedAt:Ts,searchByViewedUser:$s,searchByViewedUserPlaceholder:_s,viewedDate:Ds,updateProfileSuccessfully:As,assistantList:Is,assistant:fs,assistantSearch:Ss,assistantModel:Cs,assistantName:Es,assistantWork:Ls,assistantExpertise:ks,assistantDescription:bs,language:Ns,vietnamese:qs,japanese:xs,newAssistant:Bs,addNewAssistant:Os,reportCount:Ms,reportStatus:Rs,yes:Vs,no:Fs,all:Us,birthday:Qs,age:Ws,confirmEnableFeatureMessage:zs,confirmDisableFeatureMessage:Hs,TOPICS:js,news:Ks,listNews:Xs,newsSearch:Ys,newsContent:Zs,newsCreatedAt:Gs,createNews:Js,updateNews:to,newsTitle:eo,confirmDeleteNewsMessage:so,url:oo,view:no,position:ro,job:io,schoolName:co,brief:ao,work:uo,expert:mo,service_in_charge:lo,quest:po,questList:ho,questSearch:go,createQuest:yo,updateQuest:wo,title:Po,questDescription:vo,questType:To,unit:$o,questAmount:_o,image:Do,sort:Ao,chooseFile:Io,choose:fo,uploadMaxSize:So,confirmToggleQuestMessage:Co,"quest.unit.point":"ポイント","quest.unit.coin":"コイン",homeTimeConfig:Eo,group:Lo,time:ko,"feedType.general":"総合","feedType.recommended_answer":"評価の高い回答","feedType.laugh":"オーギリ","feedType.raise_hand":"体験者レビュー",premiumFeature:bo,coin:No,postAnswerType:qo,display:xo,itemPerPage:Bo,community:Oo,answerrQ:Mo,answerrTopic:Ro,other:Vo,createPost:Fo,updatePost:Uo,addNewCommunity:Qo,communityName:Wo,communityDescription:zo,cameraRoll:Ho,unlockPosts:jo,unlockTime:Ko,size:Xo,"Are you sure you want to remove this post?":"Bạn có chắc chắn muốn xoá bài viết này không?",Small:Yo,Large:Zo,"You can only add up to 10 posts.":"Chỉ có thể thêm tối đa 10 bài viết.","Invalid post ID.":"Invalid post ID.",deleteConfirmation:Go,"Are you sure you want to save your changes?":"Bạn có chắc chắn muốn lưu thay đổi không?",saveChanges:Jo,proceed:tn,warning:en,communityList:sn,"Are you sure you want to delete this community?":"Are you sure you want to delete this community?",createCommunity:on,updateCommunity:nn,nav:rn,"":""},an="みんなのHonNe　管理画面",un="メールアドレス",mn="パスワード",ln="現在のパスワード",pn="新しいパスワード",hn="パスワード再確認",dn="ログイン状態を保存する",gn="ログイン",yn="アンケート一覧",wn="プロフィール",Pn="プロフィール編集",vn="ユーザー名",Tn="パスワード変更",$n="ログアウト",_n="キャンセル",Dn="保存",An="確認",In="ログアウトします。よろしいですか？",fn="パスワードが変更されました。",Sn="新規を追加",Cn="一覧",En="アンケートタイトル",Ln="質問",kn="質問種別",bn="質問公開設定",Nn="質問文",qn="質問付与ポイント",xn="質問を追加",Bn="回答選択肢",On="選択{index}",Mn="+ 選択を追加",Rn="チェックボックス",Vn="プールダウン",Fn="記述式(星なし)",Un="記述式(星有り)",Qn="非公開",Wn="公開PF",zn="公開APP",Hn="公開PF,APP",jn="アンケート検索",Kn="アンケートタイトルで検索...",Xn="データがありません。",Yn="検索結果が見つかりませんでした。",Zn="ID",Gn="アンケートID",Jn="質問ID",tr="回答ID",er="回答内容",sr="アンケート順番",or="No.",nr="アンケートを選択...",rr="アンケートタイトルで検索...",ir="アンケート編集",cr="アンケートを追加",ar="アンケート差し込み追加",ur="アンケート差し込み編集",mr="アンケート差し込み一覧",lr="アンケート差し込み検索",pr="差し込みタイトル",hr="アンケートタイトル",dr="削除してもよろしいですか？",gr="アンケート",yr="差し込み対象アンケート",wr="回答",Pr="エラーが発生しました。再度試してください。",vr="質問を選択...",Tr="回答を選択...",$r="この質問がすでに選択されていました。",_r="このアンケートを削除してもよろしいですか？",Dr="差し込み対象アンケートID",Ar="ユーザー検索",Ir="ユーザー一覧",fr="登録日時",Sr="電話番号",Cr="ユーザー名",Er="ロール",Lr="総獲得ポイント",kr="現在ポイント",br="アンケート開始時間",Nr="最終回答日時",qr="ステータス",xr="ユーザー",Br="管理者",Or="回答中",Mr="回答完了",Rr="回答中",Vr="ユーザー名・電話番号で検索...",Fr="エラー",Ur="エラーが発生しました。しばらくしてから再度ご確認ください。",Qr="ユーザー情報",Wr="一般情報",zr="ユーザーID",Hr="質問数",jr="回答数",Kr="コメント数",Xr="最終ログイン日時",Yr="こちらのユーザーを削除しますか？",Zr="質問一覧",Gr="検索",Jr="ユーザーIDまたはユーザー名で検索",ti="ユーザー名",ei="ユーザーID",si="質問内容",oi="種別",ni="質問日時",ri="ステータス",ii="質問情報",ci="こちらの質問を削除しますか？",ai="回答一覧",ui="質問内容",mi="回答IDまたは内容で検索",li="質問IDまたは内容で検索",pi="回答内容",hi="回答日時",di="質問ID",gi="質問者名",yi="回答者名",wi="こちらの回答を削除しますか？",Pi="回答者ID",vi="質問者ID",Ti="回答情報",$i="ユーザーIDで検索",_i="Best Answerr",Di="FEED",Ai="属性",Ii="編集",fi="ユーザー情報 (属性) ",Si="いいね上位(DAY)",Ci="いいね上位",Ei="日付",Li="いいねのデータがありません。",ki="上位15",bi="質問、回答履歴",Ni="ユーザーFEED",qi="ユーザーFEED: {name} - ID: {id}",xi="更新",Bi="フィード番号",Oi="最適化種別",Mi="読込中...",Ri="設定",Vi="QAタブ",Fi="順番",Ui="割合",Qi="全て表示する",Wi="さらに読み込む",zi="いいね",Hi="被り値",ji="被り値のデータがありません。",Ki="被り値上位",Xi="被り値上位(DAY)",Yi="上位50",Zi="閲覧履歴",Gi="閲覧履歴",Ji="ユーザーID",tc="閲覧者",ec="日付",sc="閲覧者",oc="閲覧者のIDまたは名前で検索",nc="閲覧日付",rc="ユーザーの情報を更新しました。",ic="アシスタント一覧",cc="アシスタント",ac="アシスタント検索",uc="AI Model",mc="アシスタントの名前",lc="仕事の分野",pc="解決できる課題、担当する分野",hc="担当しているサービスについて",dc="言語",gc="Tiếng Việt",yc="日本語",wc="追加",Pc="アシスタント追加",vc="報告数",Tc="報告ステータス",$c="あり",_c="なし",Dc="全て",Ac="誕生日",Ic="年齢",fc="TOPICSフラグを付けてもよろしいでしょうか？",Sc="TOPICSフラグを外してもよろしいでしょうか？",Cc="TOPICS",Ec="ニュース",Lc="ニュース一覧",kc="ニュース検索",bc="内容",Nc="配信時間",qc="ニュースを新規追加",xc="ニュースを更新",Bc="タイトル",Oc="こちらのニュースを削除してもよろしいでしょうか？",Mc="URL",Rc="表示",Vc="肩書",Fc="仕事",Uc="学校名",Qc="得意なこと・興味があること",Wc="担当する仕事の分野の仕事",zc="解決できる課題、得意分野",Hc="担当しているサービスについて",jc="クエスト",Kc="クエスト一覧",Xc="クエスト検索",Yc="クエスト追加",Zc="クエスト編集",Gc="タイトル",Jc="ダイアログ",ta="クエストタイプ",ea="付与単位",sa="ポイント数",oa="画像",na="ソート",ra="ファイルを選択",ia="選択",ca="アップロードされたファイルは許可されている最大サイズ（15MB）を超えています。",aa="実行してもよろしいですか？",ua="ホーム画面の時間設定",ma="グループ",la="時間(h)",pa={label:"プレミアム機能",list:"プレミアム機能一覧",description:"プレミアム機能の説明",name:"機能名",price:"価格 (コイン)",type:"タイプ",create:"新規プレミアム機能",update:"プレミアム機能更新",search:"プレミアム機能検索"},ha="コイン",da="種類",ga="1ページに",ya="件表示。全{total}件中、{from}件目から{to}件目までを表示中です。",wa="コミュニティ",Pa="Answerr_Q",va="Answerr_Topic",Ta="その他",$a="質問を新規追加",_a="質問を更新",Da="コミュニティを追加",Aa="コミュニティ名",Ia="コミュニティの説明",fa="カメラロール",Sa="投稿をアンロック",Ca="アンロック時間",Ea="サイズ",La="Small",ka="Large",ba="確認",Na="変更を保存",qa="実行",xa="警告",Ba="コミュニティ一覧",Oa="コミュニティを追加",Ma="コミュニティを更新",Ra={user:"ユーザー",quest:"クエスト",post:"質問一覧",answer:"回答一覧",news:"ニュース",history:"閲覧履歴",setting:"設定",adminCommunity:"コミュニティ"},Va={10:"10",20:"20",50:"50",100:"100",200:"200",appName:an,email:un,password:mn,currentPassword:ln,newPassword:pn,confirmPassword:hn,rememberMe:dn,login:gn,listSurvey:yn,profile:wn,updateProfile:Pn,profileName:vn,changePassword:Tn,logout:$n,cancel:_n,save:Dn,confirm:An,logoutConfirmationText:In,passwordUpdatedSuccessfully:fn,addNew:Sn,list:Cn,surveyTitle:En,question:Ln,questionType:kn,questionPublic:bn,questionContent:Nn,questionPoint:qn,addNewQuestion:xn,answerOptions:Bn,answerChoice:On,addMoreAnswer:Mn,checkbox:Rn,selectBox:Vn,textBox:Fn,textBoxWithStar:Un,publicNone:Qn,publicPlatform:Wn,publicApp:zn,publicPlatformApp:Hn,surveySearch:jn,enterSurveySearch:Kn,emptyData:Xn,emptyResult:Yn,ID:Zn,surveyID:Gn,questionID:Jn,answerID:tr,answerContent:er,surveySort:sr,number:or,selectSurvey:nr,searchSurveyPlaceholder:rr,updateSurvey:ir,createNewSurvey:cr,createNewAttachedSurvey:ar,updateAttachedSurvey:ur,listAttachedSurvey:mr,attachedSurveySearch:lr,attachedSurveyTitle:pr,surveyTitleWasAttached:hr,delete:"削除",attachedSurveyDeleteConfirmation:dr,survey:gr,surveyWasAttached:yr,answer:wr,commonErrorMessage:Pr,selectQuestion:vr,selectAnswer:Tr,questionWasSelected:$r,surveyDeleteConfirmation:_r,surveyIDWasAttached:Dr,userSearch:Ar,listUser:Ir,registerAt:fr,phone:Sr,username:Cr,role:Er,totalPoint:Lr,currentPoint:kr,answerStartedAt:br,answerEndedAt:Nr,status:qr,customer:xr,administrator:Br,uncompleted:Or,completed:Mr,isAnswering:Rr,searchByNameOrPhone:Vr,errorTitle:Fr,errorMessage:Ur,"error.503":"申し訳ございません。ただいまメンテナンス中です。しばらくしてから再度ご確認ください。","error.500":"サーバーでエラーが発生しました。","error.404":"お探しのページが見つかりませんでした。","error.403":"このページへのアクセスは許可されていません。",userInfo:Qr,generalInfo:Wr,userID:zr,postCount:Hr,answerCount:jr,commentCount:Kr,lastLoggedInTime:Xr,confirmDeleteUserMessage:Yr,listPost:Zr,search:Gr,postUserSearch:Jr,createdBy:ti,createdByID:ei,postContent:si,postType:oi,postCreatedAt:ni,postStatus:ri,postInfo:ii,confirmDeletePostMessage:ci,listPostAnswer:ai,post:ui,answerSearch:mi,postSearch:li,postAnswerContent:pi,answerCreatedAt:hi,postID:di,postCreatedBy:gi,answeredBy:yi,confirmDeletePostAnswerMessage:wi,userAnswerID:Pi,userPostID:vi,postAnswerInfo:Ti,searchByID:$i,bestAnswerCount:_i,feed:Di,attribute:Ai,edit:Ii,userAttribute:fi,topLikeByDay:Si,topLike:Ci,date:Ei,emptyLikedData:Li,top15:ki,relatedData:bi,userFeed:Ni,userFeedExtra:qi,refresh:xi,STT:Bi,qaType:Oi,"postType.default":"Free","postType.smile":"ゆるい質問・相談","postType.not_smile":"真剣な質問・相談","postType.laugh":"オーギリ","postType.raise_hand":"体験者レビュー","postType.multiple":"どっち","qaType.friend":"友だち","qaType.follow":"フォロー","qaType.similar":"被り値","qaType.like":"いいね","qaType.general":"総合","qaType.latest":"新着",loading:Mi,systemSetting:Ri,qaListConfig:Vi,order:Fi,rate:Ui,displayAll:Qi,loadMore:Wi,tabLike:zi,tabSimilar:Hi,emptySimilarData:ji,topSimilar:Ki,topSimilarByDay:Xi,top50:Yi,viewedData:Zi,postViewHistory:Gi,viewedByID:Ji,viewedBy:tc,viewedAt:ec,searchByViewedUser:sc,searchByViewedUserPlaceholder:oc,viewedDate:nc,updateProfileSuccessfully:rc,assistantList:ic,assistant:cc,assistantSearch:ac,assistantModel:uc,assistantName:mc,assistantWork:lc,assistantExpertise:pc,assistantDescription:hc,language:dc,vietnamese:gc,japanese:yc,newAssistant:wc,addNewAssistant:Pc,reportCount:vc,reportStatus:Tc,yes:$c,no:_c,all:Dc,birthday:Ac,age:Ic,confirmEnableFeatureMessage:fc,confirmDisableFeatureMessage:Sc,TOPICS:Cc,news:Ec,listNews:Lc,newsSearch:kc,newsContent:bc,newsCreatedAt:Nc,createNews:qc,updateNews:xc,newsTitle:Bc,confirmDeleteNewsMessage:Oc,url:Mc,view:Rc,position:Vc,job:Fc,schoolName:Uc,brief:Qc,work:Wc,expert:zc,service_in_charge:Hc,quest:jc,questList:Kc,questSearch:Xc,createQuest:Yc,updateQuest:Zc,title:Gc,questDescription:Jc,questType:ta,unit:ea,questAmount:sa,image:oa,sort:na,chooseFile:ra,choose:ia,uploadMaxSize:ca,confirmToggleQuestMessage:aa,"quest.unit.point":"ポイント","quest.unit.coin":"コイン",homeTimeConfig:ua,group:ma,time:la,"feedType.general":"総合","feedType.recommended_answer":"評価の高い回答","feedType.laugh":"オーギリ","feedType.raise_hand":"体験者レビュー",premiumFeature:pa,coin:ha,postAnswerType:da,display:ga,itemPerPage:ya,community:wa,answerrQ:Pa,answerrTopic:va,other:Ta,createPost:$a,updatePost:_a,addNewCommunity:Da,communityName:Aa,communityDescription:Ia,cameraRoll:fa,unlockPosts:Sa,unlockTime:Ca,size:Ea,"Are you sure you want to remove this post?":"この投稿を削除してもよろしいですか？",Small:La,Large:ka,"You can only add up to 10 posts.":"最大10件まで追加できます。","Invalid post ID.":"無効な投稿IDです。",deleteConfirmation:ba,"Are you sure you want to save your changes?":"変更を保存しますか？",saveChanges:Na,proceed:qa,warning:xa,communityList:Ba,"Are you sure you want to delete this community?":"このコミュニティを削除しますか？",createCommunity:Oa,updateCommunity:Ma,nav:Ra,"":""},Fa=f({legacy:!1,locale:"vi",messages:{vi:cn,ja:Va}});class Ua{constructor(s){const e={zindex:99999,rtl:!1,transitionIn:"fadeInUp",transitionOut:"fadeOut",transitionInMobile:"fadeInUp",transitionOutMobile:"fadeOutDown",buttons:{},inputs:{},balloon:!1,close:!1,closeOnEscape:!1,position:"topRight",timeout:3e3,animateInside:!0,drag:!0,pauseOnHover:!0,resetOnHover:!1,progressBar:!1,layout:2,displayMode:2};this.options={...e,...s},this.izi=S,this.izi.settings(this.options)}getPayload(s,e="",r={}){return{...r,message:s,title:e}}success(s,e="",r={}){this.izi.success(this.getPayload(s,e,r))}warning(s,e="",r={}){this.izi.warning(this.getPayload(s,e,r))}error(s,e="",r={}){this.izi.error(this.getPayload(s,e,r))}question(s,e={}){this.izi.question(this.getPayload(s,e.title||"",e))}}const Qa={install:o=>{const s=new Ua;o.config.globalProperties.$toast=s;const e=window.toastMessage||null;e&&s.success(e),o.provide("$toast",o.config.globalProperties.$toast)}},Wa="Honne";v({title:o=>`${o} - ${Wa}`,resolve:o=>T(`./Pages/${o}.vue`,Object.assign({"./Pages/API/Index.vue":()=>t(()=>import("./Index-BxmaaYfS.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42])),"./Pages/API/Partials/ApiTokenManager.vue":()=>t(()=>import("./ApiTokenManager-DiSn4APj.js"),__vite__mapDeps([43,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,44,45,31,35,36,32,46,37,42,40,33,41])),"./Pages/Assistant/Index.vue":()=>t(()=>import("./Index-DTGpuJbl.js"),__vite__mapDeps([47,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48,49,50,51,52,30,31,32,33,34,40,53,54,55,56,38,39,57,29,41,58,59,42])),"./Pages/AttachedSurvey/Form.vue":()=>t(()=>import("./Form-BKk0RWcz.js"),__vite__mapDeps([60,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,1,2,29,30,31,32,33,34,61,38,39,40,41,42,62,63,64,65,66,56,57,58,55,67,49,50,51,52,68,69,70,71,72,73])),"./Pages/AttachedSurvey/Index.vue":()=>t(()=>import("./Index-BSNcjAa6.js"),__vite__mapDeps([74,64,65,39,66,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,48,29,30,1,2,31,32,33,34,40,41,54,55,56,38,57,58,67,49,63,50,51,52,68,69,70,71,72,73])),"./Pages/Auth/ConfirmPassword.vue":()=>t(()=>import("./ConfirmPassword-Bg3WfLWg.js"),__vite__mapDeps([75,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,76,31,34,42,40,33,41])),"./Pages/Auth/ForgotPassword.vue":()=>t(()=>import("./ForgotPassword-a4Z8wGOe.js"),__vite__mapDeps([77,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,76,31,34,42,40,33,41])),"./Pages/Auth/Login.vue":()=>t(()=>import("./Login-tVFDvVGW.js"),__vite__mapDeps([78,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,76,31,34,35,42,40,38,39,33,41])),"./Pages/Auth/Register.vue":()=>t(()=>import("./Register-COfWF3Kf.js"),__vite__mapDeps([79,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,76,31,34,35,42,40,33,41])),"./Pages/Auth/ResetPassword.vue":()=>t(()=>import("./ResetPassword-DNvf_p_e.js"),__vite__mapDeps([80,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,76,31,34,42,40,33,41])),"./Pages/Auth/TwoFactorChallenge.vue":()=>t(()=>import("./TwoFactorChallenge-Ny93PZzX.js"),__vite__mapDeps([81,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,76,31,34,42,40,33,41])),"./Pages/Auth/VerifyEmail.vue":()=>t(()=>import("./VerifyEmail-Bp3spFE7.js"),__vite__mapDeps([82,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,76,31,34,33])),"./Pages/Common/Setting.vue":()=>t(()=>import("./Setting-DO2tnMv5.js"),__vite__mapDeps([83,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,30,1,2,31,32,33,34,38,39,41,42])),"./Pages/Common/UnlockPost.vue":()=>t(()=>import("./UnlockPost-Bl0QjLHu.js"),__vite__mapDeps([84,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,1,2,49,65,85,38,39,31,66,30,32,33,34,41,55,56,57,29,58,67,63,50,51,52,68,69,70,71,72,73,86])),"./Pages/Community/CommunityFormContent.vue":()=>t(()=>import("./CommunityFormContent-D4B2O9XM.js"),__vite__mapDeps([87,88,1,2,3,89,42,59,41,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,49])),"./Pages/Community/Form.vue":()=>t(()=>import("./Form-D-q25kiI.js"),__vite__mapDeps([90,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,30,1,2,31,32,33,34,61,38,39,88,89,42,59,41,49,65,85,66,67,63,50,51,52,68,69,70,71,72,73])),"./Pages/Community/Index.vue":()=>t(()=>import("./Index-l_Mr0kHA.js"),__vite__mapDeps([91,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,50,51,52,30,1,2,31,32,33,34,49,92,38,39,65,85,66,67,63,68,69,70,71,72,73])),"./Pages/Error.vue":()=>t(()=>import("./Error-DzrmWfTG.js"),__vite__mapDeps([93,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28])),"./Pages/News/Detail.vue":()=>t(()=>import("./Detail-DTbaUjn6.js"),__vite__mapDeps([94,64,65,39,66,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,30,1,2,31,32,33,34,67,49,63,50,51,52,68,69,70,71,72,73])),"./Pages/News/Form.vue":()=>t(()=>import("./Form-CWx1Bw4l.js"),__vite__mapDeps([95,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,30,1,2,31,32,33,34,61,38,39,41,42,68,69,70,71,72])),"./Pages/News/Index.vue":()=>t(()=>import("./Index-BeoZa_-u.js"),__vite__mapDeps([96,64,65,39,66,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,50,51,52,30,1,2,31,32,33,34,40,53,54,55,56,38,57,29,41,58,61,49,92,67,63,68,69,70,71,72,73])),"./Pages/Post/CommunityFormModal.vue":()=>t(()=>import("./CommunityFormModal-Cm9J6GGm.js"),__vite__mapDeps([97,1,2,3,32,61,38,39,88,89,42,59,41,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,49])),"./Pages/Post/Detail.vue":()=>t(()=>import("./Detail-v2J2Cdf-.js"),__vite__mapDeps([98,64,65,39,66,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,30,1,2,31,32,33,34,67,49,63,50,51,52,68,69,70,71,72,73])),"./Pages/Post/Form.vue":()=>t(()=>import("./Form-Dtbk6W_1.js"),__vite__mapDeps([99,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,1,2,49,30,31,32,33,34,61,38,39,59,42,55,56,57,29,41,58,89,63,97,88,65,85,66,67,50,51,52,68,69,70,71,72,73,100])),"./Pages/Post/History.vue":()=>t(()=>import("./History-BO3_a1lb.js"),__vite__mapDeps([101,64,65,39,66,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,49,50,51,52,30,1,2,31,32,33,34,54,55,56,38,57,29,41,58,40,53,92,67,63,68,69,70,71,72,73])),"./Pages/Post/Index.vue":()=>t(()=>import("./Index-CTC7Rhsf.js"),__vite__mapDeps([102,1,2,3,64,65,39,66,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,50,51,52,30,31,32,33,34,40,53,54,55,56,38,57,29,41,58,61,49,92,67,63,68,69,70,71,72,73])),"./Pages/PostAnswer/Detail.vue":()=>t(()=>import("./Detail-BQsbFEKB.js"),__vite__mapDeps([103,1,2,3,64,65,39,66,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,30,31,32,33,34,67,49,63,50,51,52,68,69,70,71,72,73])),"./Pages/PostAnswer/Index.vue":()=>t(()=>import("./Index-DBEOG3wS.js"),__vite__mapDeps([104,1,2,3,64,65,39,66,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,50,51,52,30,31,32,33,34,40,53,54,55,56,38,57,29,41,58,61,92,49,67,63,68,69,70,71,72,73])),"./Pages/PremiumFeature/Form.vue":()=>t(()=>import("./Form-DevAWlwC.js"),__vite__mapDeps([105,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,30,1,2,31,32,33,34,61,38,39,41,59,42,55,56,57,29,58,89])),"./Pages/PremiumFeature/Index.vue":()=>t(()=>import("./Index-DhCjjOiw.js"),__vite__mapDeps([106,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,50,51,52,30,1,2,31,32,33,34,40,53,54,55,56,38,39,57,29,41,58,64,65,66,49,92,67,63,68,69,70,71,72,73])),"./Pages/Profile/ChangePassword.vue":()=>t(()=>import("./ChangePassword-hJv5iVnS.js"),__vite__mapDeps([107,30,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,1,2,31,32,33,34,108,42,40,38,39,41])),"./Pages/Profile/Partials/DeleteUserForm.vue":()=>t(()=>import("./DeleteUserForm-i7qyxAmA.js"),__vite__mapDeps([109,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,45,31,46,37,32,42,41])),"./Pages/Profile/Partials/LogoutOtherBrowserSessionsForm.vue":()=>t(()=>import("./LogoutOtherBrowserSessionsForm-B4Ipbiu7.js"),__vite__mapDeps([110,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44,45,31,37,32,42,33,41])),"./Pages/Profile/Partials/TwoFactorAuthenticationForm.vue":()=>t(()=>import("./TwoFactorAuthenticationForm-6SUwYxoT.js"),__vite__mapDeps([111,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,45,31,37,32,42,33,41,46,40])),"./Pages/Profile/Partials/UpdatePasswordForm.vue":()=>t(()=>import("./UpdatePasswordForm-C-XBt4ZQ.js"),__vite__mapDeps([108,3,1,2,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42,40,38,39,33,41])),"./Pages/Profile/Partials/UpdateProfileInformationForm.vue":()=>t(()=>import("./UpdateProfileInformationForm-DoqQHZAw.js"),__vite__mapDeps([112,3,1,2,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42,40,38,39,33,41])),"./Pages/Profile/Show.vue":()=>t(()=>import("./Show-BxB7ebM0.js"),__vite__mapDeps([113,30,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,1,2,31,32,33,34,112,42,40,38,39,41])),"./Pages/Quest/Form.vue":()=>t(()=>import("./Form-DDAzErHK.js"),__vite__mapDeps([114,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,30,1,2,31,32,33,34,61,38,39,41,59,42,89,55,56,57,29,58])),"./Pages/Quest/Index.vue":()=>t(()=>import("./Index-CUHwZHLR.js"),__vite__mapDeps([115,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,50,51,52,30,1,2,31,32,33,34,40,53,54,55,56,38,39,57,29,41,58,61,64,65,66,49,92,67,63,68,69,70,71,72,73])),"./Pages/Survey/Form.vue":()=>t(()=>import("./Form-i4svYnka.js"),__vite__mapDeps([116,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,1,2,29,30,31,32,33,34,61,38,39,40,41,42,55,56,57,58])),"./Pages/Survey/Index.vue":()=>t(()=>import("./Index-u_jjbVsY.js"),__vite__mapDeps([117,64,65,39,66,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,48,29,30,1,2,31,32,33,34,40,41,54,55,56,38,57,58,67,49,63,50,51,52,68,69,70,71,72,73])),"./Pages/Survey/Sort.vue":()=>t(()=>import("./Sort-Bsj7HNGi.js"),__vite__mapDeps([118,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,1,2,64,65,39,66,29,30,31,32,33,34,61,38,62,63,56,57,41,58,67,49,50,51,52,68,69,70,71,72,73])),"./Pages/User/Attribute.vue":()=>t(()=>import("./Attribute-UfrEzqz_.js"),__vite__mapDeps([119,30,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,1,2,31,32,33,34,50,51,52])),"./Pages/User/Detail.vue":()=>t(()=>import("./Detail-DOo2Euzb.js"),__vite__mapDeps([120,1,2,3,64,65,39,66,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,30,31,32,33,34,67,49,63,50,51,52,68,69,70,71,72,73])),"./Pages/User/Feed.vue":()=>t(()=>import("./Feed-DW4VPgsb.js"),__vite__mapDeps([121,64,65,39,66,30,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,1,2,31,32,33,34,38,67,49,63,50,51,52,68,69,70,71,72,73])),"./Pages/User/Index.vue":()=>t(()=>import("./Index-B3rlTfWi.js"),__vite__mapDeps([122,64,65,39,66,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,3,27,28,49,50,51,52,30,1,2,31,32,33,34,40,53,54,55,56,38,57,29,41,58,61,92,67,63,68,69,70,71,72,73]))})),setup({el:o,App:s,props:e,plugin:r}){return w({render:()=>P(s,e)}).use(_()).use(r).use(A,{theme:{preset:I,options:{darkModeSelector:"none"}}}).use($).use(Fa).use(Qa).directive("tooltip",D).mount(o)},progress:{color:"rgb(20 184 166)"}}).then(()=>console.log("App Initialized"));export{Fa as i};
