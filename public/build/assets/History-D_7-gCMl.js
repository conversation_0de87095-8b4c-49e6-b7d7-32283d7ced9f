import{f as D,t as k,c as U}from"./index-DHV2tfOS.js";import{T as I}from"./@inertiajs-BhKdJayA.js";import{s as x}from"./ziggy-js-RmARJSO4.js";import{b as E,s as H,a as n}from"./primevue-u0EmObz-.js";import{_ as N}from"./AppLayout-m_I9gnvX.js";import{_ as j}from"./Pagination-DDsmbrzN.js";import{_ as h}from"./InputLabel-BTXevqr4.js";import{_ as g}from"./SearchInput-CdoSYJL3.js";import{_ as O}from"./GridContainer-n7ZDMxOZ.js";import{v as L,b as T,k as v,o as f,S as d,a as c,l as a,K as w,a4 as s,c as z,O as b,P as p}from"./@vue-BnW70ngI.js";import"./moment-C5S46NFB.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./@primeuix-CNwdBq9K.js";import"./@primevue-Bw51iWDD.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BWHXZF7Q.js";import"./PrimaryButton-DE9sqoJj.js";import"./FixedSelectionBox-CwNS68U7.js";import"./SelectionBox-58uvzdoT.js";import"./LoadingIcon-CesYxFkK.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-ccBf1-WH.js";import"./lodash-DBgjQQU6.js";import"./TextInput-C52bsWxF.js";import"./@headlessui-gOb5_P77.js";const A=["textContent"],K={class:"p-6 sm:mx-2"},R={class:"flex items-stretch"},q={class:"mt-0 w-80 mr-8"},F={class:"mt-0 w-80 mr-8"},G={class:"mt-0 w-80 mr-8"},J={class:"relative mt-1"},Ve={__name:"History",props:{filters:Object,logs:Object},setup(o){const u=o,r=I({user:u.filters.user??"",postSearch:u.filters.post??"",date:u.filters.date??"",limit:u.filters.limit??10}),t=L({searching:!1,showUserSearchClearButton:(u.filters.user??"").trim().length>0,showPostSearchClearButton:(u.filters.post??"").trim().length>0,activePage:null,busy:T(()=>t.activePage!==null||t.searching||r.processing)}),m=()=>{if(r.processing)return!1;r.transform(e=>(e.post=e.postSearch,delete e.postSearch,e.date&&(e.date=k(e.date)),U(e))).get(x("postViewHistory"),{preserveScroll:!0,onSuccess:()=>{}})},S=()=>{t.showPostSearchClearButton=!0,m()},y=()=>{t.showUserSearchClearButton=!0,m()},$=()=>{r.user="",t.showSearchClearButton=!1,m()},C=()=>{r.postSearch="",t.showPostSearchClearButton=!1,m()},B=()=>{r.date="",m()},V=e=>{t.activePage=e,t.searching=!0},P=e=>{r.limit=e,r.user="",r.postSearch="",r.date="",m()};return(e,l)=>(f(),v(N,{title:e.$t("postViewHistory")},{header:d(()=>[c("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:p(e.$t("postViewHistory"))},null,8,A)]),default:d(()=>[c("div",K,[c("div",R,[c("div",q,[a(h,{for:"user-search",value:e.$t("searchByViewedUser")},null,8,["value"]),a(g,{id:"user-search",class:"mt-1 block w-full",modelValue:s(r).user,"onUpdate:modelValue":l[0]||(l[0]=i=>s(r).user=i),placeholder:e.$t("searchByViewedUserPlaceholder"),disabled:t.busy,"show-clear-button":t.showUserSearchClearButton,onInput:l[1]||(l[1]=i=>t.showUserSearchClearButton=!1),onClearSearch:$,onEnter:y},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),c("div",F,[a(h,{for:"user-search",value:e.$t("post")},null,8,["value"]),a(g,{id:"user-search",class:"mt-1 block w-full",modelValue:s(r).postSearch,"onUpdate:modelValue":l[2]||(l[2]=i=>s(r).postSearch=i),placeholder:e.$t("postSearch"),disabled:t.busy,"show-clear-button":t.showPostSearchClearButton,onInput:l[3]||(l[3]=i=>t.showPostSearchClearButton=!1),onClearSearch:C,onEnter:S},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),c("div",G,[a(h,{for:"user-search",value:e.$t("viewedDate")},null,8,["value"]),c("div",J,[a(s(E),{modelValue:s(r).date,"onUpdate:modelValue":l[4]||(l[4]=i=>s(r).date=i),size:"small",disabled:t.busy,placeholder:e.$t("viewedDate"),"hide-on-date-time-select":!0,"max-date":new Date,onDateSelect:m},null,8,["modelValue","disabled","placeholder","max-date"]),s(r).date?(f(),z("i",{key:0,class:"absolute pi pi-times input-clear-icon text-gray-400 hover:text-red-400 hover:cursor-pointer",onClick:B})):w("",!0)])])]),a(O,{loading:t.busy},{default:d(()=>[a(s(H),{value:o.logs.data},{empty:d(()=>[b(p(e.$t(o.filters.user&&o.filters.user!==""||o.filters.post&&o.filters.post!==""||o.filters.date&&o.filters.date!==""?"emptyResult":"emptyData")),1)]),default:d(()=>[a(s(n),{class:"number-column",field:"id",header:e.$t("ID")},null,8,["header"]),a(s(n),{class:"number-column",field:"post_id",header:e.$t("postID")},null,8,["header"]),a(s(n),{class:"title-flex-column",field:"content",header:e.$t("postContent")},null,8,["header"]),a(s(n),{class:"count-column",field:"answer_count",header:e.$t("answerCount")},null,8,["header"]),a(s(n),{class:"number-column",field:"user_id",header:e.$t("viewedByID")},null,8,["header"]),a(s(n),{class:"history-username-column",field:"username",header:e.$t("viewedBy")},null,8,["header"]),a(s(n),{class:"time-column",header:e.$t("viewedAt")},{body:d(({data:i})=>[b(p(s(D)(i.viewed_at)),1)]),_:1},8,["header"]),a(s(n),{class:"px-5"})]),_:1},8,["value"])]),_:1},8,["loading"]),o.logs.data.length>0?(f(),v(j,{key:0,class:"mt-5 flex items-center justify-center mx-auto",links:o.logs.links,active:t.activePage,disabled:t.busy,limit:o.logs.per_page,total:o.logs.total,from:o.logs.from,to:o.logs.to,onProgress:V,onChangeLimit:P},null,8,["links","active","disabled","limit","total","from","to"])):w("",!0)])]),_:1},8,["title"]))}};export{Ve as default};
