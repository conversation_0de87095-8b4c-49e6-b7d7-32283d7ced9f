import{T as _,i as w}from"./@inertiajs-Dt0-hqjZ.js";import{_ as h}from"./AppLayout-DKZEmXIb.js";import{_ as g}from"./RedButton-D21iPtqa.js";import{_ as b}from"./LoadingIcon-CesYxFkK.js";import{_ as C}from"./TextInput-C52bsWxF.js";import{_ as p}from"./InputError-gQdwtcoE.js";import{Q as v}from"./@vueup-DIjuzNyW.js";import{i as $,k as c,o as f,S as m,a as s,l as r,P as l,a4 as o,K as y}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";import"./PrimaryButton-DE9sqoJj.js";import"./quill-D-mw74c0.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";const k={class:"flex-1 flex items-center"},S=["textContent"],V=["textContent"],N={class:"max-w-7xl mx-auto py-6 px-6"},j={class:"bg-white rounded-md shadow flex flex-col overflow-auto"},B={class:"px-5 pt-3 pb-4 flex flex-col"},F=["textContent"],Q={class:"border-t px-5 pt-3 pb-4 flex flex-col news-editor-container"},T=["textContent"],U={class:"flex-1 flex flex-col"},Ct={__name:"Form",props:{news:Object,action:String},setup(d){const n=d,u=$("$toast"),t=_(n.news),x=()=>{if(t.processing)return!1;t.errors={},t.transform(e=>e).post(route("news.store"),{preserveScroll:!0,preserveState:!0,onSuccess:e=>{e.props.jetstream.flash.message&&u.success(e.props.jetstream.flash.message),n.action==="create"&&(window.location=route("news.list"))}})};return(e,i)=>(f(),c(h,{title:e.$t(n.action+"News")},{header:m(()=>[s("div",k,[s("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:l(e.$t(n.action+"News"))},null,8,S),r(o(w),{class:"primary-button text-sm mr-3",href:e.route("news.list"),textContent:l(e.$t("list"))},null,8,["href","textContent"]),r(g,{class:"normal-case",disabled:o(t).processing,onClick:x},{default:m(()=>[o(t).processing?(f(),c(b,{key:0,class:"mr-2"})):y("",!0),s("span",{class:"text-sm",textContent:l(e.$t("save"))},null,8,V)]),_:1},8,["disabled"])])]),default:m(()=>[s("div",N,[s("div",j,[s("div",B,[s("label",{textContent:l(e.$t("newsTitle")),class:"w-full"},null,8,F),r(C,{class:"block w-full mt-1",type:"text",modelValue:o(t).title,"onUpdate:modelValue":i[0]||(i[0]=a=>o(t).title=a),disabled:o(t).processing},null,8,["modelValue","disabled"]),r(p,{class:"w-full mt-1",message:o(t).errors.title},null,8,["message"])]),s("div",Q,[s("label",{textContent:l(e.$t("newsContent")),class:"w-full mb-1"},null,8,T),s("div",U,[r(o(v),{theme:"snow",class:"h-full flex-1 flex flex-col","content-type":"html",content:o(t).content,"v-model":o(t).content,"onUpdate:content":i[1]||(i[1]=a=>o(t).content=a)},null,8,["content","v-model"])]),r(p,{class:"w-full mt-1",message:o(t).errors.content},null,8,["message"])])])])]),_:1},8,["title"]))}};export{Ct as default};
