import{T as $}from"./@inertiajs-BhKdJayA.js";import{_ as v}from"./AppLayout-m_I9gnvX.js";import{_ as V}from"./PrimaryButton-DE9sqoJj.js";import{_ as k}from"./LoadingIcon-CesYxFkK.js";import{_ as u}from"./TextInput-C52bsWxF.js";import{_ as f}from"./InputError-gQdwtcoE.js";import{i as S,k as h,o as a,S as _,a as t,c,P as n,F as p,M as g,l as r,a4 as o,K as j}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BWHXZF7Q.js";const y={class:"flex-1 flex items-center"},U=["textContent"],B=["textContent"],F={class:"max-w-7xl mx-auto py-6 px-6"},N={class:"bg-white rounded-md shadow flex flex-col overflow-auto"},O={class:"w-full"},L={class:"text-left flex"},D=["textContent"],E={class:"text-left flex border-t even:bg-blue-50 odd:bg-white"},K=["textContent"],M=["textContent"],P=["textContent"],z=["textContent"],A={class:"border-l flex-1 flex flex-col"},G={class:"border-l flex-1 flex flex-col"},H={class:"bg-white rounded-md shadow flex flex-col overflow-auto mt-6"},I={class:"w-full"},J={class:"text-left flex"},Q=["textContent"],R={class:"text-left flex border-t even:bg-blue-50 odd:bg-white"},W=["textContent"],X=["textContent"],Y=["textContent"],Z={class:"border-l flex-1 flex flex-col"},xt={__name:"Setting",props:{settings:Object,timeSettings:Object},setup(b){const d=b,C=S("$toast"),e=$({...d.settings,time:d.timeSettings}),w=()=>{if(e.processing)return!1;e.errors={},e.transform(s=>s).post(route("setting.store"),{preserveScroll:!0,preserveState:!0,onSuccess:s=>{s.props.jetstream.flash.message&&C.success(s.props.jetstream.flash.message);for(const[x,m]of Object.entries(s.props.settings))e[x]=m}})};return(s,x)=>(a(),h(v,{title:s.$t("systemSetting")},{header:_(()=>[t("div",y,[t("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:n(s.$t("systemSetting"))},null,8,U),r(V,{class:"normal-case",disabled:o(e).processing,onClick:w},{default:_(()=>[o(e).processing?(a(),h(k,{key:0,class:"mr-2"})):j("",!0),t("span",{class:"text-sm",textContent:n(s.$t("save"))},null,8,B)]),_:1},8,["disabled"])])]),default:_(()=>[t("div",F,[t("div",N,[t("table",O,[t("tbody",null,[t("tr",L,[t("th",{colspan:"3",textContent:n(s.$t("qaListConfig"))},null,8,D)]),t("tr",E,[t("th",{class:"w-64",textContent:n(s.$t("qaType"))},null,8,K),t("th",{class:"border-l flex-1",textContent:n(s.$t("order"))},null,8,M),t("th",{class:"border-l flex-1",textContent:n(s.$t("rate"))},null,8,P)]),(a(!0),c(p,null,g(d.settings,(m,l)=>(a(),c("tr",{key:l,class:"text-left flex border-t even:bg-blue-50 odd:bg-white"},[t("td",{class:"w-64",textContent:n(s.$t("qaType."+l))},null,8,z),t("td",A,[r(u,{class:"block w-full",type:"text",modelValue:o(e)[l].index,"onUpdate:modelValue":i=>o(e)[l].index=i,disabled:o(e).processing},null,8,["modelValue","onUpdate:modelValue","disabled"]),r(f,{class:"w-full",message:o(e).errors[l+".index"]},null,8,["message"])]),t("td",G,[r(u,{class:"block w-full",type:"text",modelValue:o(e)[l].limit,"onUpdate:modelValue":i=>o(e)[l].limit=i,disabled:o(e).processing},null,8,["modelValue","onUpdate:modelValue","disabled"]),r(f,{class:"w-full",message:o(e).errors[l+".limit"]},null,8,["message"])])]))),128))])])]),t("div",H,[t("table",I,[t("tbody",null,[t("tr",J,[t("th",{colspan:"2",textContent:n(s.$t("homeTimeConfig"))},null,8,Q)]),t("tr",R,[t("th",{class:"w-64",textContent:n(s.$t("group"))},null,8,W),t("th",{class:"border-l flex-1",textContent:n(s.$t("time"))},null,8,X)]),(a(!0),c(p,null,g(d.timeSettings,(m,l)=>(a(),c("tr",{key:l,class:"text-left flex border-t even:bg-blue-50 odd:bg-white"},[t("td",{class:"w-64",textContent:n(s.$t("feedType."+l))},null,8,Y),t("td",Z,[r(u,{class:"block w-full",type:"text",modelValue:o(e).time[l],"onUpdate:modelValue":i=>o(e).time[l]=i,disabled:o(e).processing},null,8,["modelValue","onUpdate:modelValue","disabled"]),r(f,{class:"w-full",message:o(e).errors["time."+l]},null,8,["message"])])]))),128))])])])])]),_:1},8,["title"]))}};export{xt as default};
