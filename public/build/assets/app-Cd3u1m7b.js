const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Index-oGiYWJxO.js","assets/vue-i18n-DNS8h1FH.js","assets/@intlify-TnaUIxGf.js","assets/@vue-BnW70ngI.js","assets/@inertiajs-BhKdJayA.js","assets/axios-t--hEgTQ.js","assets/deepmerge-4BhuujTU.js","assets/qs-CbAGxgEG.js","assets/nprogress-R_QVVDqq.js","assets/lodash.clonedeep-DCKevjwB.js","assets/lodash.isequal-BCJU-oNO.js","assets/@element-plus-ccBf1-WH.js","assets/AppLayout-m_I9gnvX.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/SecondaryButton-BWHXZF7Q.js","assets/PrimaryButton-DE9sqoJj.js","assets/AuthenticationCardLogo-BID_CyH6.css","assets/Checkbox-BW6Lzxs4.js","assets/ConfirmationModal-koF4JoqQ.js","assets/DialogModal-CP7TKBlg.js","assets/LoadingIcon-CesYxFkK.js","assets/LoadingIcon-CiCW7nnq.css","assets/InputLabel-BTXevqr4.js","assets/TextInput-C52bsWxF.js","assets/InputError-gQdwtcoE.js","assets/ApiTokenManager-BUfkY4Yx.js","assets/ActionMessage-yNeSLSLA.js","assets/ActionSection-d716unDa.js","assets/DangerButton-C49GvHso.js","assets/Index-oLYr9KV6.js","assets/lodash-DBgjQQU6.js","assets/ziggy-js-RmARJSO4.js","assets/primevue-u0EmObz-.js","assets/@primeuix-CNwdBq9K.js","assets/@primevue-Bw51iWDD.js","assets/SearchInput-CdoSYJL3.js","assets/Pagination-DDsmbrzN.js","assets/FixedSelectionBox-CwNS68U7.js","assets/SelectionBox-58uvzdoT.js","assets/@heroicons-BLousAGu.js","assets/@headlessui-gOb5_P77.js","assets/TextAreaInput-y-SlU-FI.js","assets/Form-CT1mXjf4.js","assets/RedButton-D21iPtqa.js","assets/SurveySelectionBox-PZfoWyYA.js","assets/pinia-BB7AK9rL.js","assets/index-DHV2tfOS.js","assets/moment-C5S46NFB.js","assets/Index-LxTFsAN2.js","assets/ConfirmPassword-CvrkoK15.js","assets/AuthenticationCardLogo-C3QjX6Mv.js","assets/ForgotPassword-VFiC2krK.js","assets/Login-BZPCBtbY.js","assets/Register-ByQpjmbt.js","assets/ResetPassword-IdFspe6o.js","assets/TwoFactorChallenge-Ds97mZi8.js","assets/VerifyEmail-CdB4YxXk.js","assets/Setting-D6FwookN.js","assets/Error-DjGzLh61.js","assets/Detail-CRhKu-sR.js","assets/Form-NS6x2yD3.js","assets/@vueup-DxIbRV4Q.js","assets/quill-B8crIIz-.js","assets/parchment-DI0xVRB_.js","assets/eventemitter3-mWFy3unY.js","assets/lodash-es-BvFElN8u.js","assets/quill-delta-BfHWV4fh.js","assets/fast-diff-DNDSwfiB.js","assets/@vueup-CrSYVOAc.css","assets/Index-BHiOJvKd.js","assets/GridContainer-n7ZDMxOZ.js","assets/Detail-B6OELQqR.js","assets/History-D_7-gCMl.js","assets/Index-CwqoX6tW.js","assets/Detail-BA29rgPj.js","assets/Index-Dz9ngLt4.js","assets/Form-DBfrDle2.js","assets/ImageInput-DUQk7LRQ.js","assets/Index-ydQGed1L.js","assets/ChangePassword-D2tIZTaL.js","assets/UpdatePasswordForm-ucHuJcn3.js","assets/DeleteUserForm-C-0a0rRH.js","assets/LogoutOtherBrowserSessionsForm-BkAaIQkf.js","assets/TwoFactorAuthenticationForm-Db3EWR4P.js","assets/UpdateProfileInformationForm-_FQQkdu0.js","assets/Show-BMOukjUp.js","assets/Form-EiBJOnuP.js","assets/Index-CZS0q1Sn.js","assets/Form-CkWJlWd0.js","assets/Index-Co8gMbLo.js","assets/Sort-wPwfyOJe.js","assets/Attribute-BhQH4DMW.js","assets/Detail-Dpqjvdy_.js","assets/Feed-DURf_M10.js","assets/Index-C2Bsqr9w.js"])))=>i.map(i=>d[i]);
import{a as y}from"./axios-t--hEgTQ.js";import{a3 as w,h as P}from"./@vue-BnW70ngI.js";import{j as T}from"./@inertiajs-BhKdJayA.js";import{r as v}from"./laravel-vite-plugin-DEL3ZhID.js";import{o as $}from"./ziggy-js-RmARJSO4.js";import{c as _}from"./pinia-BB7AK9rL.js";import{T as D}from"./primevue-u0EmObz-.js";import{P as A,M as f}from"./@primevue-Bw51iWDD.js";import"./@vueup-DxIbRV4Q.js";import{c as I}from"./vue-i18n-DNS8h1FH.js";import{i as S}from"./izitoast-DiQh6Y8O.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./@primeuix-CNwdBq9K.js";import"./quill-B8crIIz-.js";import"./parchment-DI0xVRB_.js";import"./eventemitter3-mWFy3unY.js";import"./lodash-es-BvFElN8u.js";import"./quill-delta-BfHWV4fh.js";import"./fast-diff-DNDSwfiB.js";import"./@intlify-TnaUIxGf.js";const C="modulepreload",E=function(o){return"/build/"+o},p={},t=function(s,e,r){let l=Promise.resolve();if(e&&e.length>0){document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),i=(n==null?void 0:n.nonce)||(n==null?void 0:n.getAttribute("nonce"));l=Promise.allSettled(e.map(a=>{if(a=E(a),a in p)return;p[a]=!0;const u=a.endsWith(".css"),d=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${d}`))return;const c=document.createElement("link");if(c.rel=u?"stylesheet":C,u||(c.as="script"),c.crossOrigin="",c.href=a,i&&c.setAttribute("nonce",i),document.head.appendChild(c),u)return new Promise((m,g)=>{c.addEventListener("load",m),c.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${a}`)))})}))}function h(n){const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=n,window.dispatchEvent(i),!i.defaultPrevented)throw n}return l.then(n=>{for(const i of n||[])i.status==="rejected"&&h(i.reason);return s().catch(h)})},L=Intl.DateTimeFormat().resolvedOptions().timeZone;window.axios=y.create({baseURL:"/",withCredentials:!0,headers:{Accept:"application/json","X-TIMEZONE":L}});window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";const b="HonNe Console",k="Email",q="Mật khẩu",N="Mật khẩu hiện tại",x="Mật khẩu mới",B="Xác nhận mật khẩu",M="Nhớ mật khẩu",O="Đăng nhập",V="Danh sách khảo sát",F="Thông tin cá nhân",R="Cập nhật thông tin cá nhân",U="Tên người dùng",Q="Thay đổi mật khẩu",W="Đăng xuất",H="Hủy",j="Lưu",z="Xác nhận",K="Bạn có thực sự muốn đăng xuất không?",X="Thay đổi mật khẩu thành công.",Y="Thêm mới",Z="Danh sách",G="Tiêu đề khảo sát",J="Câu hỏi",tt="Loại câu hỏi",et="Chế độ công khai",st="Nội dung câu hỏi",ot="Điểm thưởng",nt="Thêm câu hỏi",rt="Lựa chọn trả lời",it="Tùy chọn {index}",at="+ Thêm tùy chọn",ct="Checkbox",ut="Pulldown",lt="Tự luận",ht="Tự luận có sao",pt="Không công khai",dt="Công khai trên nền tảng",mt="Công khai trên ứng dụng",gt="Công khai trên cả nền tảng và ứng dụng",yt="Tìm kiếm khảo sát",wt="Nhập tiêu đề khảo sát...",Pt="Không có dữ liệu.",Tt="Không tìm thấy kết quả theo yêu cầu.",vt="ID",$t="Survey ID",_t="Question ID",Dt="Answer ID",At="Lựa chọn trả lời",ft="Thứ tự khảo sát",It="No.",St="Chọn khảo sát...",Ct="Nhập để tìm khảo sát...",Et="Danh sách khảo sát chèn",Lt="Tìm kiếm khảo sát chèn",bt="Tiêu đề khảo sát chèn",kt="Tiêu đề khảo sát được chèn",qt="Bạn có thực sự muốn xóa dữ liệu này không?",Nt="Khảo sát",xt="Khảo sát được chèn",Bt="Câu trả lời",Mt="Có lỗi xảy ra, xin vui lòng thử lại!",Ot="Chọn câu hỏi...",Vt="Chọn câu trả lời...",Ft="Bạn đã chọn câu hỏi này rồi.",Rt="Bạn có thực sự muốn xóa khảo sát này không?",Ut="ID được chèn",Qt="Tìm kiếm người dùng",Wt="Danh sách người dùng",Ht="Thời gian đăng ký",jt="Số ĐT",zt="Tên người dùng",Kt="Vai trò",Xt="Tổng điểm tích lũy",Yt="Điểm hiện tại",Zt="Thời gian bắt đầu khảo sát",Gt="Ngày giờ trả lời cuối cùng",Jt="Trạng thái",te="Người dùng",ee="Quản trị viên",se="Chưa hoàn thành",oe="Hoàn tất trả lời",ne="Đang trả lời",re="Tìm theo tên hoặc số điện thoại...",ie="Có lỗi xảy ra",ae="Có lỗi xảy ra xin vui lòng thử lại sau.",ce="Thông tin người dùng",ue="Thông tin chung",le="ID người dùng",he="Số câu hỏi",pe="Số câu trả lời",de="Số comment",me="Thời gian đăng nhập lần cuối",ge="Bạn có thực sự muốn xoá người dùng này không?",ye="Danh sách câu hỏi",we="Tìm kiếm",Pe="Tìm kiếm theo ID hoặc tên",Te="Người tạo",ve="User ID",$e="Nội dung câu hỏi",_e="Phân loại",De="Thời gian hỏi",Ae="Trạng thái câu hỏi",fe="Thông tin câu hỏi",Ie="Bạn có thực sự muốn xoá câu hỏi này không?",Se="Danh sách câu trả lời",Ce="Câu hỏi",Ee="Tìm theo ID hoặc nội dung câu trả lời",Le="Tìm theo ID hoặc nội dung câu hỏi",be="Nội dung câu trả lời",ke="Thời gian trả lời",qe="Post ID",Ne="Tên người hỏi",xe="Người trả lời",Be="Bạn có thực sự muốn xoá câu trả lời này không?",Me="ID người trả lời",Oe="ID người hỏi",Ve="Thông tin câu trả lời",Fe="Tìm theo ID",Re="Best Answerr",Ue="FEED",Qe="Thuộc tính",We="Chỉnh sửa",He="Thuộc tính người dùng",je="Top LIKE theo ngày",ze="Top LIKE",Ke="Ngày",Xe="Không có dữ liệu lượt thích.",Ye="TOP 15",Ze="Lịch sử câu hỏi, trả lời",Ge="Feed của người dùng",Je="Feed của người dùng: {name} - ID: {id}",ts="Tải lại",es="STT",ss="Loại tối ưu",os="Đang tải dữ liệu...",ns="System Settings",rs="Hiển thị ở Q&A",is="Thứ tự",as="Tỷ lệ",cs="Hiển thị tất cả",us="Tải thêm",ls="Lượt thích",hs="Tương đồng",ps="Không có dữ liệu tương đồng.",ds="Top tương đồng",ms="Top tương đồng theo ngày",gs="Top 50",ys="Lịch sử xem",ws="Lịch sử xem bài viết",Ps="User ID",Ts="Người xem",vs="Xem lúc",$s="Người xem",_s="Tìm theo ID hoặc tên người xem",Ds="Ngày xem",As="Cập nhật thông tin cá nhân thành công.",fs="Danh sách trợ lý",Is="Trợ lý",Ss="Tìm kiếm trợ lý",Cs="AI Model",Es="Tên trợ lý",Ls="Lĩnh vực",bs="Chuyên môn",ks="Mô tả công việc",qs="Ngôn ngữ",Ns="Tiếng Việt",xs="Tiếng Nhật",Bs="Thêm mới",Ms="Thêm mới trợ lý",Os="Số lần report",Vs="Trạng thái report",Fs="Có",Rs="Không",Us="Tất cả",Qs="Ngày sinh",Ws="Tuổi",Hs="Bạn có thực sự muốn bật câu hỏi này trong TOPICS không?",js="Bạn có thực sự muốn tắt câu hỏi này trong TOPICS không?",zs="TOPICS",Ks="Tin tức",Xs="Danh sách tin tức",Ys="Tìm kiếm tin tức",Zs="Nội dung",Gs="Thời gian tạo",Js="Thêm mới tin tức",to="Cập nhật tin tức",eo="Tiêu đề tin tức",so="Bạn có thực sự muốn xóa tin tức này không?",oo="URL",no="View",ro="Chức danh",io="Công việc",ao="Tên trường",co="Điểm mạnh, điều quan tâm",uo="Lĩnh vực công việc phụ trách",lo="Các vấn đề có thể giải quyết, thế mạnh chuyên môn",ho="Dịch vụ đang phụ trách",po="Quest",mo="Danh sách Quest",go="Tìm kiếm Quest",yo="Thêm mới Quest",wo="Cập nhật Quest",Po="Tiêu đề",To="Mô tả",vo="Loại Quest",$o="Đơn vị tính",_o="Số điểm",Do="Hình ảnh",Ao="Thứ tự",fo="Chọn file...",Io="Chọn",So="File upload vượt quá kích thước tối đa cho phép (15MB).",Co="Bạn có thực sự muốn thực hiện không?",Eo="Cấu hình thời gian màn HOME",Lo="Nhóm",bo="Thời gian (giờ)",ko={label:"Premium Feature",list:"Danh sách Premium Feature",description:"Mô tả tính năng Premium",name:"Tên tính năng",price:"Giá (coin)",type:"Loại",create:"Thêm mới Premium Feature",update:"Cập nhật Premium Feature",search:"Tìm kiếm Premium Feature"},qo="コイン",No="種類",xo="1ページに",Bo="件表示。全{total}件中、{from}件目から{to}件目までを表示中です。",Mo={10:"10",20:"20",50:"50",100:"100",200:"200",appName:b,email:k,password:q,currentPassword:N,newPassword:x,confirmPassword:B,rememberMe:M,login:O,listSurvey:V,profile:F,updateProfile:R,profileName:U,changePassword:Q,logout:W,cancel:H,save:j,confirm:z,logoutConfirmationText:K,passwordUpdatedSuccessfully:X,addNew:Y,list:Z,surveyTitle:G,question:J,questionType:tt,questionPublic:et,questionContent:st,questionPoint:ot,addNewQuestion:nt,answerOptions:rt,answerChoice:it,addMoreAnswer:at,checkbox:ct,selectBox:ut,textBox:lt,textBoxWithStar:ht,publicNone:pt,publicPlatform:dt,publicApp:mt,publicPlatformApp:gt,surveySearch:yt,enterSurveySearch:wt,emptyData:Pt,emptyResult:Tt,ID:vt,surveyID:$t,questionID:_t,answerID:Dt,answerContent:At,surveySort:ft,number:It,selectSurvey:St,searchSurveyPlaceholder:Ct,listAttachedSurvey:Et,attachedSurveySearch:Lt,attachedSurveyTitle:bt,surveyTitleWasAttached:kt,delete:"Xóa",attachedSurveyDeleteConfirmation:qt,survey:Nt,surveyWasAttached:xt,answer:Bt,commonErrorMessage:Mt,selectQuestion:Ot,selectAnswer:Vt,questionWasSelected:Ft,surveyDeleteConfirmation:Rt,surveyIDWasAttached:Ut,userSearch:Qt,listUser:Wt,registerAt:Ht,phone:jt,username:zt,role:Kt,totalPoint:Xt,currentPoint:Yt,answerStartedAt:Zt,answerEndedAt:Gt,status:Jt,customer:te,administrator:ee,uncompleted:se,completed:oe,isAnswering:ne,searchByNameOrPhone:re,errorTitle:ie,errorMessage:ae,"error.503":"Xin lỗi, chúng tôi đang bảo trì. Vui lòng kiểm tra lại sau.","error.500":"Ồ, có lỗi xảy ra trên máy chủ của chúng tôi.","error.404":"Xin lỗi, trang bạn đang tìm kiếm không tìm thấy.","error.403":"Rất tiếc, bạn không được phép truy cập vào trang này.",userInfo:ce,generalInfo:ue,userID:le,postCount:he,answerCount:pe,commentCount:de,lastLoggedInTime:me,confirmDeleteUserMessage:ge,listPost:ye,search:we,postUserSearch:Pe,createdBy:Te,createdByID:ve,postContent:$e,postType:_e,postCreatedAt:De,postStatus:Ae,postInfo:fe,confirmDeletePostMessage:Ie,listPostAnswer:Se,post:Ce,answerSearch:Ee,postSearch:Le,postAnswerContent:be,answerCreatedAt:ke,postID:qe,postCreatedBy:Ne,answeredBy:xe,confirmDeletePostAnswerMessage:Be,userAnswerID:Me,userPostID:Oe,postAnswerInfo:Ve,searchByID:Fe,bestAnswerCount:Re,feed:Ue,attribute:Qe,edit:We,userAttribute:He,topLikeByDay:je,topLike:ze,date:Ke,emptyLikedData:Xe,top15:Ye,relatedData:Ze,userFeed:Ge,userFeedExtra:Je,refresh:ts,STT:es,qaType:ss,"postType.default":"Free","postType.smile":"ゆるい質問・相談","postType.not_smile":"真剣な質問・相談","postType.laugh":"オーギリ","postType.raise_hand":"体験者レビュー","postType.multiple":"どっち","qaType.friend":"Friend","qaType.follow":"Follow","qaType.similar":"Tương đồng","qaType.like":"Like","qaType.general":"Tổng hợp","qaType.latest":"Mới nhất",loading:os,systemSetting:ns,qaListConfig:rs,order:is,rate:as,displayAll:cs,loadMore:us,tabLike:ls,tabSimilar:hs,emptySimilarData:ps,topSimilar:ds,topSimilarByDay:ms,top50:gs,viewedData:ys,postViewHistory:ws,viewedByID:Ps,viewedBy:Ts,viewedAt:vs,searchByViewedUser:$s,searchByViewedUserPlaceholder:_s,viewedDate:Ds,updateProfileSuccessfully:As,assistantList:fs,assistant:Is,assistantSearch:Ss,assistantModel:Cs,assistantName:Es,assistantWork:Ls,assistantExpertise:bs,assistantDescription:ks,language:qs,vietnamese:Ns,japanese:xs,newAssistant:Bs,addNewAssistant:Ms,reportCount:Os,reportStatus:Vs,yes:Fs,no:Rs,all:Us,birthday:Qs,age:Ws,confirmEnableFeatureMessage:Hs,confirmDisableFeatureMessage:js,TOPICS:zs,news:Ks,listNews:Xs,newsSearch:Ys,newsContent:Zs,newsCreatedAt:Gs,createNews:Js,updateNews:to,newsTitle:eo,confirmDeleteNewsMessage:so,url:oo,view:no,position:ro,job:io,schoolName:ao,brief:co,work:uo,expert:lo,service_in_charge:ho,quest:po,questList:mo,questSearch:go,createQuest:yo,updateQuest:wo,title:Po,questDescription:To,questType:vo,unit:$o,questAmount:_o,image:Do,sort:Ao,chooseFile:fo,choose:Io,uploadMaxSize:So,confirmToggleQuestMessage:Co,"quest.unit.point":"ポイント","quest.unit.coin":"コイン",homeTimeConfig:Eo,group:Lo,time:bo,"feedType.general":"総合","feedType.recommended_answer":"評価の高い回答","feedType.laugh":"オーギリ","feedType.raise_hand":"体験者レビュー",premiumFeature:ko,coin:qo,postAnswerType:No,display:xo,itemPerPage:Bo,"":""},Oo="みんなのHonNe　管理画面",Vo="メールアドレス",Fo="パスワード",Ro="現在のパスワード",Uo="新しいパスワード",Qo="パスワード再確認",Wo="ログイン状態を保存する",Ho="ログイン",jo="アンケート一覧",zo="プロフィール",Ko="プロフィール編集",Xo="ユーザー名",Yo="パスワード変更",Zo="ログアウト",Go="キャンセル",Jo="保存",tn="確認",en="ログアウトします。よろしいですか？",sn="パスワードが変更されました。",on="新規を追加",nn="一覧",rn="アンケートタイトル",an="質問",cn="質問種別",un="質問公開設定",ln="質問文",hn="質問付与ポイント",pn="質問を追加",dn="回答選択肢",mn="選択{index}",gn="+ 選択を追加",yn="チェックボックス",wn="プールダウン",Pn="記述式(星なし)",Tn="記述式(星有り)",vn="非公開",$n="公開PF",_n="公開APP",Dn="公開PF,APP",An="アンケート検索",fn="アンケートタイトルで検索...",In="データがありません。",Sn="検索結果が見つかりませんでした。",Cn="ID",En="アンケートID",Ln="質問ID",bn="回答ID",kn="回答内容",qn="アンケート順番",Nn="No.",xn="アンケートを選択...",Bn="アンケートタイトルで検索...",Mn="アンケート編集",On="アンケートを追加",Vn="アンケート差し込み追加",Fn="アンケート差し込み編集",Rn="アンケート差し込み一覧",Un="アンケート差し込み検索",Qn="差し込みタイトル",Wn="アンケートタイトル",Hn="削除してもよろしいですか？",jn="アンケート",zn="差し込み対象アンケート",Kn="回答",Xn="エラーが発生しました。再度試してください。",Yn="質問を選択...",Zn="回答を選択...",Gn="この質問がすでに選択されていました。",Jn="このアンケートを削除してもよろしいですか？",tr="差し込み対象アンケートID",er="ユーザー検索",sr="ユーザー一覧",or="登録日時",nr="電話番号",rr="ユーザー名",ir="ロール",ar="総獲得ポイント",cr="現在ポイント",ur="アンケート開始時間",lr="最終回答日時",hr="ステータス",pr="ユーザー",dr="管理者",mr="回答中",gr="回答完了",yr="回答中",wr="ユーザー名・電話番号で検索...",Pr="エラー",Tr="エラーが発生しました。しばらくしてから再度ご確認ください。",vr="ユーザー情報",$r="一般情報",_r="ユーザーID",Dr="質問数",Ar="回答数",fr="コメント数",Ir="最終ログイン日時",Sr="こちらのユーザーを削除しますか？",Cr="質問一覧",Er="検索",Lr="ユーザーIDまたはユーザー名で検索",br="ユーザー名",kr="ユーザーID",qr="質問内容",Nr="種別",xr="質問日時",Br="ステータス",Mr="質問情報",Or="こちらの質問を削除しますか？",Vr="回答一覧",Fr="質問内容",Rr="回答IDまたは内容で検索",Ur="質問IDまたは内容で検索",Qr="回答内容",Wr="回答日時",Hr="質問ID",jr="質問者名",zr="回答者名",Kr="こちらの回答を削除しますか？",Xr="回答者ID",Yr="質問者ID",Zr="回答情報",Gr="ユーザーIDで検索",Jr="Best Answerr",ti="FEED",ei="属性",si="編集",oi="ユーザー情報 (属性) ",ni="いいね上位(DAY)",ri="いいね上位",ii="日付",ai="いいねのデータがありません。",ci="上位15",ui="質問、回答履歴",li="ユーザーFEED",hi="ユーザーFEED: {name} - ID: {id}",pi="更新",di="フィード番号",mi="最適化種別",gi="読込中...",yi="設定",wi="QAタブ",Pi="順番",Ti="割合",vi="全て表示する",$i="さらに読み込む",_i="いいね",Di="被り値",Ai="被り値のデータがありません。",fi="被り値上位",Ii="被り値上位(DAY)",Si="上位50",Ci="閲覧履歴",Ei="閲覧履歴",Li="ユーザーID",bi="閲覧者",ki="日付",qi="閲覧者",Ni="閲覧者のIDまたは名前で検索",xi="閲覧日付",Bi="ユーザーの情報を更新しました。",Mi="アシスタント一覧",Oi="アシスタント",Vi="アシスタント検索",Fi="AI Model",Ri="アシスタントの名前",Ui="仕事の分野",Qi="解決できる課題、担当する分野",Wi="担当しているサービスについて",Hi="言語",ji="Tiếng Việt",zi="日本語",Ki="追加",Xi="アシスタント追加",Yi="報告数",Zi="報告ステータス",Gi="あり",Ji="なし",ta="全て",ea="誕生日",sa="年齢",oa="TOPICSフラグを付けてもよろしいでしょうか？",na="TOPICSフラグを外してもよろしいでしょうか？",ra="TOPICS",ia="ニュース",aa="ニュース一覧",ca="ニュース検索",ua="内容",la="配信時間",ha="ニュースを新規追加",pa="ニュースを更新",da="タイトル",ma="こちらのニュースを削除してもよろしいでしょうか？",ga="URL",ya="表示",wa="肩書",Pa="仕事",Ta="学校名",va="得意なこと・興味があること",$a="担当する仕事の分野の仕事",_a="解決できる課題、得意分野",Da="担当しているサービスについて",Aa="クエスト",fa="クエスト一覧",Ia="クエスト検索",Sa="クエスト追加",Ca="クエスト編集",Ea="タイトル",La="ダイアログ",ba="クエストタイプ",ka="付与単位",qa="ポイント数",Na="画像",xa="ソート",Ba="ファイルを選択",Ma="選択",Oa="アップロードされたファイルは許可されている最大サイズ（15MB）を超えています。",Va="実行してもよろしいですか？",Fa="ホーム画面の時間設定",Ra="グループ",Ua="時間(h)",Qa={label:"プレミアム機能",list:"プレミアム機能一覧",description:"プレミアム機能の説明",name:"機能名",price:"価格 (コイン)",type:"タイプ",create:"新規プレミアム機能",update:"プレミアム機能更新",search:"プレミアム機能検索"},Wa="コイン",Ha="種類",ja="1ページに",za="件表示。全{total}件中、{from}件目から{to}件目までを表示中です。",Ka={10:"10",20:"20",50:"50",100:"100",200:"200",appName:Oo,email:Vo,password:Fo,currentPassword:Ro,newPassword:Uo,confirmPassword:Qo,rememberMe:Wo,login:Ho,listSurvey:jo,profile:zo,updateProfile:Ko,profileName:Xo,changePassword:Yo,logout:Zo,cancel:Go,save:Jo,confirm:tn,logoutConfirmationText:en,passwordUpdatedSuccessfully:sn,addNew:on,list:nn,surveyTitle:rn,question:an,questionType:cn,questionPublic:un,questionContent:ln,questionPoint:hn,addNewQuestion:pn,answerOptions:dn,answerChoice:mn,addMoreAnswer:gn,checkbox:yn,selectBox:wn,textBox:Pn,textBoxWithStar:Tn,publicNone:vn,publicPlatform:$n,publicApp:_n,publicPlatformApp:Dn,surveySearch:An,enterSurveySearch:fn,emptyData:In,emptyResult:Sn,ID:Cn,surveyID:En,questionID:Ln,answerID:bn,answerContent:kn,surveySort:qn,number:Nn,selectSurvey:xn,searchSurveyPlaceholder:Bn,updateSurvey:Mn,createNewSurvey:On,createNewAttachedSurvey:Vn,updateAttachedSurvey:Fn,listAttachedSurvey:Rn,attachedSurveySearch:Un,attachedSurveyTitle:Qn,surveyTitleWasAttached:Wn,delete:"削除",attachedSurveyDeleteConfirmation:Hn,survey:jn,surveyWasAttached:zn,answer:Kn,commonErrorMessage:Xn,selectQuestion:Yn,selectAnswer:Zn,questionWasSelected:Gn,surveyDeleteConfirmation:Jn,surveyIDWasAttached:tr,userSearch:er,listUser:sr,registerAt:or,phone:nr,username:rr,role:ir,totalPoint:ar,currentPoint:cr,answerStartedAt:ur,answerEndedAt:lr,status:hr,customer:pr,administrator:dr,uncompleted:mr,completed:gr,isAnswering:yr,searchByNameOrPhone:wr,errorTitle:Pr,errorMessage:Tr,"error.503":"申し訳ございません。ただいまメンテナンス中です。しばらくしてから再度ご確認ください。","error.500":"サーバーでエラーが発生しました。","error.404":"お探しのページが見つかりませんでした。","error.403":"このページへのアクセスは許可されていません。",userInfo:vr,generalInfo:$r,userID:_r,postCount:Dr,answerCount:Ar,commentCount:fr,lastLoggedInTime:Ir,confirmDeleteUserMessage:Sr,listPost:Cr,search:Er,postUserSearch:Lr,createdBy:br,createdByID:kr,postContent:qr,postType:Nr,postCreatedAt:xr,postStatus:Br,postInfo:Mr,confirmDeletePostMessage:Or,listPostAnswer:Vr,post:Fr,answerSearch:Rr,postSearch:Ur,postAnswerContent:Qr,answerCreatedAt:Wr,postID:Hr,postCreatedBy:jr,answeredBy:zr,confirmDeletePostAnswerMessage:Kr,userAnswerID:Xr,userPostID:Yr,postAnswerInfo:Zr,searchByID:Gr,bestAnswerCount:Jr,feed:ti,attribute:ei,edit:si,userAttribute:oi,topLikeByDay:ni,topLike:ri,date:ii,emptyLikedData:ai,top15:ci,relatedData:ui,userFeed:li,userFeedExtra:hi,refresh:pi,STT:di,qaType:mi,"postType.default":"Free","postType.smile":"ゆるい質問・相談","postType.not_smile":"真剣な質問・相談","postType.laugh":"オーギリ","postType.raise_hand":"体験者レビュー","postType.multiple":"どっち","qaType.friend":"友だち","qaType.follow":"フォロー","qaType.similar":"被り値","qaType.like":"いいね","qaType.general":"総合","qaType.latest":"新着",loading:gi,systemSetting:yi,qaListConfig:wi,order:Pi,rate:Ti,displayAll:vi,loadMore:$i,tabLike:_i,tabSimilar:Di,emptySimilarData:Ai,topSimilar:fi,topSimilarByDay:Ii,top50:Si,viewedData:Ci,postViewHistory:Ei,viewedByID:Li,viewedBy:bi,viewedAt:ki,searchByViewedUser:qi,searchByViewedUserPlaceholder:Ni,viewedDate:xi,updateProfileSuccessfully:Bi,assistantList:Mi,assistant:Oi,assistantSearch:Vi,assistantModel:Fi,assistantName:Ri,assistantWork:Ui,assistantExpertise:Qi,assistantDescription:Wi,language:Hi,vietnamese:ji,japanese:zi,newAssistant:Ki,addNewAssistant:Xi,reportCount:Yi,reportStatus:Zi,yes:Gi,no:Ji,all:ta,birthday:ea,age:sa,confirmEnableFeatureMessage:oa,confirmDisableFeatureMessage:na,TOPICS:ra,news:ia,listNews:aa,newsSearch:ca,newsContent:ua,newsCreatedAt:la,createNews:ha,updateNews:pa,newsTitle:da,confirmDeleteNewsMessage:ma,url:ga,view:ya,position:wa,job:Pa,schoolName:Ta,brief:va,work:$a,expert:_a,service_in_charge:Da,quest:Aa,questList:fa,questSearch:Ia,createQuest:Sa,updateQuest:Ca,title:Ea,questDescription:La,questType:ba,unit:ka,questAmount:qa,image:Na,sort:xa,chooseFile:Ba,choose:Ma,uploadMaxSize:Oa,confirmToggleQuestMessage:Va,"quest.unit.point":"ポイント","quest.unit.coin":"コイン",homeTimeConfig:Fa,group:Ra,time:Ua,"feedType.general":"総合","feedType.recommended_answer":"評価の高い回答","feedType.laugh":"オーギリ","feedType.raise_hand":"体験者レビュー",premiumFeature:Qa,coin:Wa,postAnswerType:Ha,display:ja,itemPerPage:za,"":""},Xa=I({legacy:!1,locale:"vi",messages:{vi:Mo,ja:Ka}});class Ya{constructor(s){const e={zindex:99999,rtl:!1,transitionIn:"fadeInUp",transitionOut:"fadeOut",transitionInMobile:"fadeInUp",transitionOutMobile:"fadeOutDown",buttons:{},inputs:{},balloon:!1,close:!1,closeOnEscape:!1,position:"topRight",timeout:3e3,animateInside:!0,drag:!0,pauseOnHover:!0,resetOnHover:!1,progressBar:!1,layout:2,displayMode:2};this.options={...e,...s},this.izi=S,this.izi.settings(this.options)}getPayload(s,e="",r={}){return{...r,message:s,title:e}}success(s,e="",r={}){this.izi.success(this.getPayload(s,e,r))}warning(s,e="",r={}){this.izi.warning(this.getPayload(s,e,r))}error(s,e="",r={}){this.izi.error(this.getPayload(s,e,r))}question(s,e={}){this.izi.question(this.getPayload(s,e.title||"",e))}}const Za={install:o=>{const s=new Ya;o.config.globalProperties.$toast=s;const e=window.toastMessage||null;e&&s.success(e),o.provide("$toast",o.config.globalProperties.$toast)}},Ga="Honne";T({title:o=>`${o} - ${Ga}`,resolve:o=>v(`./Pages/${o}.vue`,Object.assign({"./Pages/API/Index.vue":()=>t(()=>import("./Index-oGiYWJxO.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24])),"./Pages/API/Partials/ApiTokenManager.vue":()=>t(()=>import("./ApiTokenManager-BUfkY4Yx.js"),__vite__mapDeps([25,4,5,6,7,8,3,9,10,26,27,13,17,18,14,28,19,24,22,15,23])),"./Pages/Assistant/Index.vue":()=>t(()=>import("./Index-oLYr9KV6.js"),__vite__mapDeps([29,1,2,3,4,5,6,7,8,9,10,30,31,32,33,34,12,13,14,15,16,22,35,36,37,38,20,21,39,11,23,40,41,24])),"./Pages/AttachedSurvey/Form.vue":()=>t(()=>import("./Form-CT1mXjf4.js"),__vite__mapDeps([42,4,5,6,7,8,3,9,10,1,2,11,12,13,14,15,16,43,20,21,22,23,24,44,45,46,47,38,39,30,40,37])),"./Pages/AttachedSurvey/Index.vue":()=>t(()=>import("./Index-LxTFsAN2.js"),__vite__mapDeps([48,46,47,4,5,6,7,8,3,9,10,30,11,12,1,2,13,14,15,16,22,23,36,37,38,20,21,39,40])),"./Pages/Auth/ConfirmPassword.vue":()=>t(()=>import("./ConfirmPassword-CvrkoK15.js"),__vite__mapDeps([49,3,4,5,6,7,8,9,10,50,13,16,24,22,15,23])),"./Pages/Auth/ForgotPassword.vue":()=>t(()=>import("./ForgotPassword-VFiC2krK.js"),__vite__mapDeps([51,3,4,5,6,7,8,9,10,50,13,16,24,22,15,23])),"./Pages/Auth/Login.vue":()=>t(()=>import("./Login-BZPCBtbY.js"),__vite__mapDeps([52,3,4,5,6,7,8,9,10,50,13,16,17,24,22,20,21,15,23])),"./Pages/Auth/Register.vue":()=>t(()=>import("./Register-ByQpjmbt.js"),__vite__mapDeps([53,3,4,5,6,7,8,9,10,50,13,16,17,24,22,15,23])),"./Pages/Auth/ResetPassword.vue":()=>t(()=>import("./ResetPassword-IdFspe6o.js"),__vite__mapDeps([54,3,4,5,6,7,8,9,10,50,13,16,24,22,15,23])),"./Pages/Auth/TwoFactorChallenge.vue":()=>t(()=>import("./TwoFactorChallenge-Ds97mZi8.js"),__vite__mapDeps([55,3,4,5,6,7,8,9,10,50,13,16,24,22,15,23])),"./Pages/Auth/VerifyEmail.vue":()=>t(()=>import("./VerifyEmail-CdB4YxXk.js"),__vite__mapDeps([56,3,4,5,6,7,8,9,10,50,13,16,15])),"./Pages/Common/Setting.vue":()=>t(()=>import("./Setting-D6FwookN.js"),__vite__mapDeps([57,4,5,6,7,8,3,9,10,12,1,2,13,14,15,16,20,21,23,24])),"./Pages/Error.vue":()=>t(()=>import("./Error-DjGzLh61.js"),__vite__mapDeps([58,4,5,6,7,8,3,9,10])),"./Pages/News/Detail.vue":()=>t(()=>import("./Detail-CRhKu-sR.js"),__vite__mapDeps([59,46,47,4,5,6,7,8,3,9,10,12,1,2,13,14,15,16])),"./Pages/News/Form.vue":()=>t(()=>import("./Form-NS6x2yD3.js"),__vite__mapDeps([60,4,5,6,7,8,3,9,10,12,1,2,13,14,15,16,43,20,21,23,24,61,62,63,64,65,66,67,68])),"./Pages/News/Index.vue":()=>t(()=>import("./Index-BHiOJvKd.js"),__vite__mapDeps([69,46,47,4,5,6,7,8,3,9,10,32,33,34,12,1,2,13,14,15,16,22,35,36,37,38,20,21,39,11,30,23,40,43,31,70])),"./Pages/Post/Detail.vue":()=>t(()=>import("./Detail-B6OELQqR.js"),__vite__mapDeps([71,46,47,4,5,6,7,8,3,9,10,12,1,2,13,14,15,16])),"./Pages/Post/History.vue":()=>t(()=>import("./History-D_7-gCMl.js"),__vite__mapDeps([72,46,47,4,5,6,7,8,3,9,10,31,32,33,34,12,1,2,13,14,15,16,36,37,38,20,21,39,11,30,23,40,22,35,70])),"./Pages/Post/Index.vue":()=>t(()=>import("./Index-CwqoX6tW.js"),__vite__mapDeps([73,1,2,3,46,47,4,5,6,7,8,9,10,32,33,34,12,13,14,15,16,22,35,36,37,38,20,21,39,11,30,23,40,43,31,70])),"./Pages/PostAnswer/Detail.vue":()=>t(()=>import("./Detail-BA29rgPj.js"),__vite__mapDeps([74,1,2,3,46,47,4,5,6,7,8,9,10,12,13,14,15,16])),"./Pages/PostAnswer/Index.vue":()=>t(()=>import("./Index-Dz9ngLt4.js"),__vite__mapDeps([75,1,2,3,46,47,4,5,6,7,8,9,10,32,33,34,12,13,14,15,16,22,35,36,37,38,20,21,39,11,30,23,40,43,70,31])),"./Pages/PremiumFeature/Form.vue":()=>t(()=>import("./Form-DBfrDle2.js"),__vite__mapDeps([76,4,5,6,7,8,3,9,10,12,1,2,13,14,15,16,43,20,21,23,41,24,37,38,39,11,30,40,77])),"./Pages/PremiumFeature/Index.vue":()=>t(()=>import("./Index-ydQGed1L.js"),__vite__mapDeps([78,4,5,6,7,8,3,9,10,32,33,34,12,1,2,13,14,15,16,22,35,36,37,38,20,21,39,11,30,23,40,46,47,31,70])),"./Pages/Profile/ChangePassword.vue":()=>t(()=>import("./ChangePassword-D2tIZTaL.js"),__vite__mapDeps([79,12,4,5,6,7,8,3,9,10,1,2,13,14,15,16,80,24,22,20,21,23])),"./Pages/Profile/Partials/DeleteUserForm.vue":()=>t(()=>import("./DeleteUserForm-C-0a0rRH.js"),__vite__mapDeps([81,3,4,5,6,7,8,9,10,27,13,28,19,14,24,23])),"./Pages/Profile/Partials/LogoutOtherBrowserSessionsForm.vue":()=>t(()=>import("./LogoutOtherBrowserSessionsForm-BkAaIQkf.js"),__vite__mapDeps([82,3,4,5,6,7,8,9,10,26,27,13,19,14,24,15,23])),"./Pages/Profile/Partials/TwoFactorAuthenticationForm.vue":()=>t(()=>import("./TwoFactorAuthenticationForm-Db3EWR4P.js"),__vite__mapDeps([83,3,4,5,6,7,8,9,10,27,13,19,14,24,15,23,28,22])),"./Pages/Profile/Partials/UpdatePasswordForm.vue":()=>t(()=>import("./UpdatePasswordForm-ucHuJcn3.js"),__vite__mapDeps([80,3,1,2,4,5,6,7,8,9,10,24,22,20,21,15,23])),"./Pages/Profile/Partials/UpdateProfileInformationForm.vue":()=>t(()=>import("./UpdateProfileInformationForm-_FQQkdu0.js"),__vite__mapDeps([84,3,1,2,4,5,6,7,8,9,10,24,22,20,21,15,23])),"./Pages/Profile/Show.vue":()=>t(()=>import("./Show-BMOukjUp.js"),__vite__mapDeps([85,12,4,5,6,7,8,3,9,10,1,2,13,14,15,16,84,24,22,20,21,23])),"./Pages/Quest/Form.vue":()=>t(()=>import("./Form-EiBJOnuP.js"),__vite__mapDeps([86,4,5,6,7,8,3,9,10,12,1,2,13,14,15,16,43,20,21,23,41,24,77,37,38,39,11,30,40])),"./Pages/Quest/Index.vue":()=>t(()=>import("./Index-CZS0q1Sn.js"),__vite__mapDeps([87,4,5,6,7,8,3,9,10,32,33,34,12,1,2,13,14,15,16,22,35,36,37,38,20,21,39,11,30,23,40,43,46,47,31,70])),"./Pages/Survey/Form.vue":()=>t(()=>import("./Form-CkWJlWd0.js"),__vite__mapDeps([88,4,5,6,7,8,3,9,10,1,2,11,12,13,14,15,16,43,20,21,22,23,24,37,38,39,30,40])),"./Pages/Survey/Index.vue":()=>t(()=>import("./Index-Co8gMbLo.js"),__vite__mapDeps([89,46,47,4,5,6,7,8,3,9,10,30,11,12,1,2,13,14,15,16,22,23,36,37,38,20,21,39,40])),"./Pages/Survey/Sort.vue":()=>t(()=>import("./Sort-wPwfyOJe.js"),__vite__mapDeps([90,4,5,6,7,8,3,9,10,1,2,46,47,11,12,13,14,15,16,43,20,21,44,45,38,39,30,23,40])),"./Pages/User/Attribute.vue":()=>t(()=>import("./Attribute-BhQH4DMW.js"),__vite__mapDeps([91,12,4,5,6,7,8,3,9,10,1,2,13,14,15,16,32,33,34])),"./Pages/User/Detail.vue":()=>t(()=>import("./Detail-Dpqjvdy_.js"),__vite__mapDeps([92,1,2,3,46,47,4,5,6,7,8,9,10,12,13,14,15,16])),"./Pages/User/Feed.vue":()=>t(()=>import("./Feed-DURf_M10.js"),__vite__mapDeps([93,46,47,12,4,5,6,7,8,3,9,10,1,2,13,14,15,16,20,21])),"./Pages/User/Index.vue":()=>t(()=>import("./Index-C2Bsqr9w.js"),__vite__mapDeps([94,46,47,4,5,6,7,8,3,9,10,31,32,33,34,12,1,2,13,14,15,16,22,35,36,37,38,20,21,39,11,30,23,40,43,70]))})),setup({el:o,App:s,props:e,plugin:r}){return w({render:()=>P(s,e)}).use(_()).use(r).use(A,{theme:{preset:f,options:{darkModeSelector:"none"}}}).use($).use(Xa).use(Za).directive("tooltip",D).mount(o)},progress:{color:"rgb(20 184 166)"}}).then(()=>console.log("App Initialized"));
