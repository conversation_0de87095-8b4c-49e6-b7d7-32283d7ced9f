import{Q as P,T as U,i as M,N as T}from"./@inertiajs-Dt0-hqjZ.js";import{u as I}from"./vue-i18n-kWKo0idO.js";import{s as C}from"./ziggy-js-C7EU8ifa.js";import{_ as O}from"./AppLayout-CTb2MMqd.js";import{_ as R}from"./RedButton-D21iPtqa.js";import{_ as Q}from"./LoadingIcon-CLD0VpVl.js";import{_ as q}from"./TextAreaInput-DHjed6qD.js";import{_ as $}from"./InputError-gQdwtcoE.js";import{_ as A}from"./FixedSelectionBox-Bk5LSyGJ.js";import{_ as E}from"./ImageInput-BV1wAASf.js";import{d as K}from"./pinia-Ddsh4R0D.js";import{_ as G}from"./SelectionBox-D4JR3fGi.js";import{v as H,b as _,j as D,k as j,o as v,r as k,c as S,K as z,a as o,P as b,R as V,F,i as J,l as i,S as w,a4 as l}from"./@vue-BnW70ngI.js";import L from"./CommunityFormModal-Cm9J6GGm.js";import{_ as W}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{p as X}from"./@element-plus-CyTLADhX.js";import{_ as Y}from"./PrimaryButton-DE9sqoJj.js";import"./moment-C5S46NFB.js";import{c as Z}from"./confirmModal-DLZLapTY.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@intlify-xvnhHnag.js";import"./SecondaryButton-BoI1NwE9.js";/* empty css                                                    */import"./@heroicons-BLousAGu.js";import"./TextInput-DUNPEFms.js";import"./@headlessui-gOb5_P77.js";import"./CommunityFormContent.vue_vue_type_script_setup_true_lang-Dl2CgMFy.js";/* empty css                                                                     */import"./app-65VXU7yX.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./primevue-CrCPcMFN.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./izitoast-CYQMso0-.js";const N=K("community-stores",{state:()=>({communities:[]}),getters:{hasData:e=>e.communities.length>0,data:e=>e.communities},actions:{setData(e){this.communities=e},clear(){this.communities=[]},async loadData(){await window.axios.post("/community/list").then(e=>{this.setData(e.data)}).catch(e=>{console.log(e)})},async prepend(e){this.communities=[e,...this.communities]}}}),ee={__name:"CommunitySelectionBox",props:{label:{type:String,default:""},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:""},modelValue:{default:"",validator(e){return e===null||typeof e=="string"||typeof e=="number"}},clearData:{type:Boolean,default:!1},clearable:{type:Boolean,default:!1},showSelected:{type:Boolean,default:!0},enableSearch:{type:Boolean,default:!1},loadData:{type:Boolean,default:!0},searchPlaceholder:{type:String,default:""}},emits:["update:modelValue","update:loading","selected","dataCleared"],setup(e,{emit:g}){const s=g,d=e,u=N(),m=H({loading:!1,modelValue:d.modelValue??"",options:_(()=>u.data.map(r=>({label:r.name,value:r.community_id})))});D(()=>d.modelValue,r=>{m.modelValue=r??""},{immediate:!0}),D(()=>d.clearData,r=>{r===!0&&(m.modelValue="",s("dataCleared"))});const p=async()=>{s("update:loading",!0),m.loading=!0,await u.loadData().then(()=>{m.loading=!1,m.modelValue=d.modelValue,s("update:loading",!1)})};!u.hasData&&d.loadData&&p();const t=r=>{s("update:modelValue",r)},h=r=>{s("selected",r)};return(r,y)=>(v(),j(G,{label:e.label,disabled:e.disabled,loading:m.loading,placeholder:e.placeholder,options:m.options,"can-clear":e.clearable,"show-selected":e.showSelected,"enable-search":e.enableSearch,"search-placeholder":e.searchPlaceholder,modelValue:m.modelValue,"onUpdate:modelValue":[y[0]||(y[0]=x=>m.modelValue=x),t],onRefresh:p,onSelected:h},null,8,["label","disabled","loading","placeholder","options","can-clear","show-selected","enable-search","search-placeholder","modelValue"]))}},te=["textContent"],le=["disabled","aria-checked","aria-labelledby"],oe={__name:"Switcher",props:{modelValue:{type:[Number,Boolean],default:0,validator(e){return[0,1,!0,!1].includes(e)}},label:{type:String,default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"lg",validator(e){return["sm","md","lg"].includes(e)}},color:{type:String,default:"sky",validator(e){return["sky","blue","green","red","purple","indigo","pink"].includes(e)}}},emits:["update:modelValue","change"],setup(e,{emit:g}){const s=e,d=g,u=_(()=>s.modelValue===1||s.modelValue===!0),m=k(`switcher-${Math.random().toString(36).substr(2,9)}`),p={sm:{switch:"h-5 w-9",toggle:"h-4 w-4",translate:"translate-x-[18px]"},md:{switch:"h-6 w-11",toggle:"h-5 w-5",translate:"translate-x-[22px]"},lg:{switch:"h-7 w-14",toggle:"h-6 w-6",translate:"translate-x-[30px]"}},t={sky:"bg-sky-400",blue:"bg-blue-400",green:"bg-green-400",red:"bg-red-400",purple:"bg-purple-400",indigo:"bg-indigo-400",pink:"bg-pink-400"},h=_(()=>["mt-[6px] relative inline-flex flex-shrink-0 cursor-pointer rounded-full transition-colors duration-200 ease-in-out focus:outline-none",p[s.size].switch,s.disabled?"opacity-50 cursor-not-allowed":"",u.value?t[s.color]:"bg-gray-200"]),r=_(()=>["pointer-events-none absolute inset-0 rounded-full transition-colors duration-200 ease-in-out",u.value?t[s.color]:"bg-gray-200"]),y=_(()=>["pointer-events-none inline-block rounded-full bg-white shadow transform transition duration-200 ease-in-out flex items-center justify-center translate-y-0.5",p[s.size].toggle,u.value?p[s.size].translate:"translate-x-[2px]"]),x=()=>{if(s.disabled)return;const f=u.value?0:1;d("update:modelValue",f),d("change",f)};return(f,B)=>(v(),S(F,null,[e.label?(v(),S("label",{key:0,class:"w-full",textContent:b(e.label)},null,8,te)):z("",!0),o("button",{type:"button",class:V(h.value),disabled:e.disabled,onClick:x,role:"switch","aria-checked":u.value,"aria-labelledby":m.value},[o("span",{class:V(r.value),"aria-hidden":"true"},null,2),o("span",{class:V(y.value),"aria-hidden":"true"},null,2)],10,le)],64))}},ae=W(oe,[["__scopeId","data-v-a2d587f5"]]),se={class:"flex-1 flex items-center"},ne=["textContent"],re={class:"max-w-7xl mx-auto py-6 px-6"},ie={class:"bg-white rounded-md shadow px-5 py-4 space-y-4"},de={class:"flex flex-col"},me=["textContent"],ce={class:"flex space-x-6"},ue={class:"flex-1 flex flex-col"},pe=["textContent"],fe={class:"flex-1 flex flex-col"},ge={class:"flex space-x-6"},be={class:"flex-1 flex flex-col"},he={class:"flex items-end"},ye={class:"flex flex-col flex-1"},xe=["textContent"],_e={class:"flex flex-col ml-1.5"},ve=["disabled"],we={class:"flex-1 flex flex-col"},Ve={class:"mt-4 flex"},Ce=["textContent"],$e=["textContent"],Nt={__name:"Form",props:{post:Object,action:String},setup(e){const g=e,s=P(),{t:d}=I(),u=N(),m=[{value:"answerr_q",label:d("answerrQ")},{value:"answerr_topic",label:d("answerrTopic")}],p=J("$toast"),t=U(g.post),h=k(!1),r=async a=>{await u.prepend(a),t.community_id=a.community_id},y=()=>{if(g.action==="update")return!1;h.value=!0},x=()=>{if(t.processing)return!1;t.errors={},t.transform(a=>(typeof a.image=="string"&&delete a.image,a)).post(C("post.store"),{preserveScroll:!0,preserveState:!0,onSuccess:a=>{a.props.jetstream.flash.message&&p.success(a.props.jetstream.flash.message)}})},f=k(!1),B=async a=>{f.value||await Z(d("Are you sure you want to remove this post?"),async n=>{T.post(C("post.delete"),{id:a},{preserveScroll:!0,preserveState:!0,onBefore:()=>{f.value=!0},onSuccess:()=>{s.props.jetstream.flash.message&&p.success(s.props.jetstream.flash.message)},onFinish:()=>{f.value=!1,n(!0)}})})};return(a,n)=>(v(),S(F,null,[i(O,{title:a.$t(g.action+"Post")},{header:w(()=>[o("div",se,[o("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:b(a.$t(g.action+"Post"))},null,8,ne),i(l(M),{class:"primary-button text-sm",href:l(C)("post.list"),textContent:b(a.$t("list"))},null,8,["href","textContent"])])]),default:w(()=>[o("div",re,[o("div",ie,[o("div",de,[o("label",{textContent:b(a.$t("postContent")),class:"w-full"},null,8,me),i(q,{class:"block w-full mt-1",modelValue:l(t).content,"onUpdate:modelValue":n[0]||(n[0]=c=>l(t).content=c),disabled:l(t).processing,rows:"4"},null,8,["modelValue","disabled"]),i($,{class:"w-full mt-1",message:l(t).errors.content},null,8,["message"])]),o("div",ce,[o("div",ue,[o("label",{textContent:b(a.$t("postType")),class:"w-full mb-1"},null,8,pe),i(A,{modelValue:l(t).type,"onUpdate:modelValue":n[1]||(n[1]=c=>l(t).type=c),placeholder:a.$t("postType"),disabled:l(t).processing,options:m,clearable:!1},null,8,["modelValue","placeholder","disabled"]),i($,{class:"w-full mt-1",message:l(t).errors.type},null,8,["message"])]),o("div",fe,[i(E,{class:"flex-1",label:a.$t("image"),error:l(t).errors.image,disabled:l(t).processing,modelValue:l(t).image,"onUpdate:modelValue":[n[2]||(n[2]=c=>l(t).image=c),n[3]||(n[3]=c=>l(t).image=c)]},null,8,["label","error","disabled","modelValue"])])]),o("div",ge,[o("div",be,[o("div",he,[o("div",ye,[o("label",{textContent:b(a.$t("community")),class:"w-full"},null,8,xe),i(ee,{modelValue:l(t).community_id,"onUpdate:modelValue":n[4]||(n[4]=c=>l(t).community_id=c),placeholder:a.$t("community"),disabled:l(t).processing||e.action==="update",clearable:!1},null,8,["modelValue","placeholder","disabled"])]),o("div",_e,[o("button",{class:V(["rounded-md border transition-colors duration-200 border-sky-400 p-2.5 focus:outline-none text-white",{"bg-gray-200 cursor-default":l(t).processing,"bg-sky-400 disabled:hover:bg-sky-400 disabled:hover:border-sky-400 hover:bg-sky-500 hover:border-sky-500":!l(t).processing}]),disabled:l(t).processing||e.action==="update",onClick:y},[i(l(X),{class:"w-5 h-5","aria-hidden":"true"})],10,ve)])]),i($,{class:"w-full mt-1",message:l(t).errors.community_id},null,8,["message"])]),o("div",we,[i(ae,{modelValue:l(t).camera_roll,"onUpdate:modelValue":n[5]||(n[5]=c=>l(t).camera_roll=c),label:l(d)("cameraRoll")},null,8,["modelValue","label"])])])]),o("div",Ve,[i(R,{class:"normal-case ml-auto",disabled:l(t).processing||f.value,onClick:n[6]||(n[6]=c=>B(l(t).post_id))},{default:w(()=>[o("span",{class:"text-sm",textContent:b(a.$t("delete"))},null,8,Ce)]),_:1},8,["disabled"]),i(Y,{class:"normal-case ml-3",disabled:l(t).processing||f.value,onClick:x},{default:w(()=>[l(t).processing?(v(),j(Q,{key:0,class:"mr-2"})):z("",!0),o("span",{class:"text-sm",textContent:b(a.$t("save"))},null,8,$e)]),_:1},8,["disabled"])])])]),_:1},8,["title"]),i(L,{"show-modal":h.value,onCloseModal:n[7]||(n[7]=c=>h.value=!1),onCommunityCreated:r},null,8,["show-modal"])],64))}};export{Nt as default};
