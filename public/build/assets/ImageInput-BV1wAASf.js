import{u as _}from"./vue-i18n-kWKo0idO.js";import{_ as v}from"./InputError-gQdwtcoE.js";import{r as c,j as w,c as m,o as f,a,l as C,K as V,P as d,R as g}from"./@vue-BnW70ngI.js";const k={class:"flex flex-col"},F={class:"flex w-full"},S={class:"flex-1 flex flex-col mr-3"},j=["textContent"],B=["textContent"],I=["textContent"],$={class:"w-[70px] h-[70px] flex items-center justify-center"},z=["src"],E={__name:"ImageInput",props:{label:{type:String,default:""},error:{type:String,default:""},modelValue:{default:"",validator(e){return e===null||typeof e=="string"||typeof e=="number"||typeof e=="object"}},disabled:{type:Boolean,default:!1},defaultImage:{type:String,default:"/assets/images/no-image-img.svg"}},emits:["update:modelValue"],setup(e,{emit:p}){const{t:x}=_(),b=p,o=e,n=c(o.error);w(()=>o.error,t=>{n.value=t});const r=c(o.modelValue||"/assets/images/no-image-img.svg"),u=c(),l=document.createElement("input");l.type="file",l.accept="image/png,image/jpeg,image/webp",l.onchange=t=>{let s=t.target.files[0];if(n.value="",s.size>15*1024*1024){n.value=x("uploadMaxSize");return}u.value=s.name,l.value="";const i=new FileReader;i.onload=async()=>{const y=i.result;r.value=y},i.readAsDataURL(s),b("update:modelValue",s)};const h=()=>{o.disabled||l.click()};return(t,s)=>(f(),m("div",k,[a("div",F,[a("div",S,[e.label?(f(),m("label",{key:0,textContent:d(t.$t("image")),class:"w-full mb-1"},null,8,j)):V("",!0),a("div",{class:g(["flex flex-1 h-[42px] border border-gray-300 rounded-md shadow-sm px-3 flex items-center",{"bg-gray-200":e.disabled,"text-gray-400":!r.value}])},[a("div",{textContent:d(u.value??t.$t("chooseFile")),class:"flex-1 max-w-[380px] mr-3 truncate"},null,8,B),a("button",{type:"button",class:g(["button-select-file ml-auto",{"bg-gray-200":e.disabled}]),textContent:d(t.$t("choose")),onClick:h},null,10,I)],2)]),a("div",$,[a("img",{class:"max-w-full max-h-full",src:r.value},null,8,z)])]),C(v,{class:"w-full mt-1",message:n.value},null,8,["message"])]))}};export{E as _};
