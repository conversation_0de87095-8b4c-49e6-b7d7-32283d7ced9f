import{r as c,j as b,e as S,b as k,c as p,o as v,a as o,l as i,S as d,X as m,a0 as u,U as y,R as f,J as w,K as B}from"./@vue-BnW70ngI.js";const V={__name:"Modal",props:{show:{type:Boolean,default:!1},size:{type:String,default:"2xl"},paddingVertical:{type:String,default:"py-16"},center:{type:Boolean,default:!1}},emits:["close"],setup(e,{emit:n}){const t=e,h=n,l=c(),r=c(t.show);b(()=>t.show,()=>{var s;t.show?(document.body.style.overflow="hidden",r.value=!0,(s=l.value)==null||s.showModal()):(document.body.style.overflow=null,setTimeout(()=>{var a;(a=l.value)==null||a.close(),r.value=!1},200))}),S(()=>{l.value.addEventListener("cancel",()=>{h("close")})});const g=k(()=>({sm:"sm:max-w-sm",md:"sm:max-w-md",lg:"sm:max-w-lg",xl:"sm:max-w-xl","2xl":"sm:max-w-2xl"})[t.size]);return(s,a)=>(v(),p("dialog",{class:"z-50 m-0 min-h-full min-w-full overflow-y-auto bg-transparent backdrop:bg-transparent",ref_key:"dialog",ref:l},[o("div",{class:f(["flex h-screen overflow-y-auto px-4 sm:px-0 z-50",[e.center?"items-center":"items-start",e.paddingVertical]]),"scroll-region":""},[i(y,{"enter-active-class":"ease-out duration-300","enter-from-class":"opacity-0","enter-to-class":"opacity-100","leave-active-class":"ease-in duration-200","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:d(()=>[m(o("div",{class:"fixed inset-0 transform transition-all",onClick:a[0]||(a[0]=(...x)=>s.close&&s.close(...x))},a[1]||(a[1]=[o("div",{class:"absolute inset-0 bg-gray-500 opacity-75"},null,-1)]),512),[[u,e.show]])]),_:1}),i(y,{"enter-active-class":"ease-out duration-300","enter-from-class":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to-class":"opacity-100 translate-y-0 sm:scale-100","leave-active-class":"ease-in duration-200","leave-from-class":"opacity-100 translate-y-0 sm:scale-100","leave-to-class":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:d(()=>[m(o("div",{class:f(["bg-white rounded-lg overflow-hidden shadow-xl transform transition-all sm:w-full sm:mx-auto",g.value])},[r.value?w(s.$slots,"default",{key:0}):B("",!0)],2),[[u,e.show]])]),_:3})],2)],512))}},z=["type"],M={__name:"SecondaryButton",props:{type:{type:String,default:"button"}},setup(e){return(n,t)=>(v(),p("button",{class:"secondary-button",type:e.type},[w(n.$slots,"default")],8,z))}};export{M as _,V as a};
