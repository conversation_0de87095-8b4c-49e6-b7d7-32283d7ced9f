import{v as x,c as a,o as l,a as r,K as h,l as p,P as u,a4 as m,F as d,M as y,R as o,k as L,_ as M}from"./@vue-BnW70ngI.js";import{i as w}from"./@inertiajs-Dt0-hqjZ.js";import{_ as k}from"./FixedSelectionBox-CkXOgkaT.js";import{u as C}from"./vue-i18n-kWKo0idO.js";const H={class:"w-full flex items-center"},N={class:"mr-auto flex items-center text-sm"},T=["textContent"],V=["textContent"],$={key:0,class:"flex flex-wrap items-end -mb-1 ml-2"},B=["innerHTML"],P=["innerHTML"],F=["innerHTML"],E={__name:"Pagination",props:{links:Array,active:Number|null,disabled:Boolean,limit:Number|null,total:Number|0,from:Number|0,to:Number|0},emits:["progress","changeLimit"],setup(e,{emit:b}){const{t:i}=C(),n=x({limit:e.limit||10,limitOptions:[{value:10,label:i("10")},{value:20,label:i("20")},{value:50,label:i("50")},{value:100,label:i("100")},{value:200,label:i("200")}]}),g=b,f=()=>{g("changeLimit",n.limit)};return(v,c)=>(l(),a("div",H,[r("div",N,[r("span",{textContent:u(m(i)("display")),class:"mr-1.5"},null,8,T),p(k,{class:"w-[54px] pagination-limit-wrapper",modelValue:n.limit,"onUpdate:modelValue":c[0]||(c[0]=t=>n.limit=t),disabled:e.disabled,options:n.limitOptions,clearable:!1,onSelected:f},null,8,["modelValue","disabled","options"]),r("span",{textContent:u(m(i)("itemPerPage",{total:e.total,from:e.from,to:e.to})),class:"ml-1.5"},null,8,V)]),e.links.length>3?(l(),a("div",$,[(l(!0),a(d,null,y(e.links,(t,s)=>(l(),a(d,null,[t.url===null?(l(),a("div",{key:s,class:o(["mr-1 px-3.5 py-2.5 text-gray-400 text-sm leading-4 transition",{"border rounded":t.label!=="..."}]),innerHTML:t.label},null,10,B)):t.active?(l(),a("div",{key:`active-${s}`,class:o(["mr-1 px-3.5 py-2.5 text-sm leading-4 border focus:border-sky-500 rounded transition",{"bg-sky-500 text-white font-semibold":!e.active,"bg-gray-100/90":e.active}]),innerHTML:t.label},null,10,P)):(l(),a(d,{key:2},[e.disabled?(l(),a("div",{key:`pagination-disabled-${s}`,class:o(["mr-1 px-3.5 py-2.5 text-gray-400 text-sm leading-4 transition border rounded",{"bg-sky-500 text-white font-semibold":e.active===s,"bg-gray-100/90":e.active!==s}]),innerHTML:t.label},null,10,F)):(l(),L(m(w),{key:`link-${s}`,class:o(["mr-1 px-3.5 py-2.5 focus:text-sky-500 text-sm leading-4 hover:bg-sky-100 border focus:border-sky-500 rounded bg-white transition",{"bg-white":t.active}]),href:t.url,innerHTML:t.label,onClick:M(S=>v.$emit("progress",s),["prevent"])},null,8,["class","href","innerHTML","onClick"]))],64))],64))),256))])):h("",!0)]))}};export{E as _};
