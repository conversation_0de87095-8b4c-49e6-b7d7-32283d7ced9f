import{T as A}from"./@inertiajs-Dt0-hqjZ.js";import{_ as U}from"./ActionMessage-yNeSLSLA.js";import{S as h,_ as L}from"./ActionSection-d716unDa.js";import{_ as T}from"./Checkbox-BW6Lzxs4.js";import{_ as z}from"./ConfirmationModal-ClaGRyF5.js";import{_ as E}from"./DangerButton-C49GvHso.js";import{_ as F}from"./DialogModal-LfgJQ09a.js";import{b as J,a5 as K,c as l,o as a,l as o,a as i,S as s,J as $,_ as O,K as k,R as x,r as P,a4 as m,O as r,F as C,M as S,P as y}from"./@vue-BnW70ngI.js";import{_ as R}from"./InputError-gQdwtcoE.js";import{_ as I}from"./InputLabel-BTXevqr4.js";import{_ as V}from"./PrimaryButton-DE9sqoJj.js";import{_ as w}from"./SecondaryButton-BoI1NwE9.js";import{_ as Y}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{_ as q}from"./TextInput-DUNPEFms.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";const G={class:"md:grid md:grid-cols-3 md:gap-6"},H={class:"mt-5 md:mt-0 md:col-span-2"},Q={class:"grid grid-cols-6 gap-6"},W={key:0,class:"flex items-center justify-end px-4 py-3 bg-gray-50 text-end sm:px-6 shadow sm:rounded-bl-md sm:rounded-br-md"},X={__name:"FormSection",emits:["submitted"],setup(d){const c=J(()=>!!K().actions);return(n,u)=>(a(),l("div",G,[o(h,null,{title:s(()=>[$(n.$slots,"title")]),description:s(()=>[$(n.$slots,"description")]),_:3}),i("div",H,[i("form",{onSubmit:u[0]||(u[0]=O(_=>n.$emit("submitted"),["prevent"]))},[i("div",{class:x(["px-4 py-5 bg-white sm:p-6 shadow",c.value?"sm:rounded-tl-md sm:rounded-tr-md":"sm:rounded-md"])},[i("div",Q,[$(n.$slots,"form")])],2),c.value?(a(),l("div",W,[$(n.$slots,"actions")])):k("",!0)],32)])]))}},Z={},ee={class:"hidden sm:block"};function se(d,c){return a(),l("div",ee,c[0]||(c[0]=[i("div",{class:"py-8"},[i("div",{class:"border-t border-gray-200"})],-1)]))}const te=Y(Z,[["render",se]]),oe={class:"col-span-6 sm:col-span-4"},ie={key:0,class:"col-span-6"},ne={class:"mt-2 grid grid-cols-1 md:grid-cols-2 gap-4"},re={class:"flex items-center"},le={class:"ms-2 text-sm text-gray-600"},ae={key:0},me={class:"mt-10 sm:mt-0"},de={class:"space-y-6"},ue={class:"break-all"},pe={class:"flex items-center ms-2"},ce={key:0,class:"text-sm text-gray-400"},fe=["onClick"],ve=["onClick"],ke={key:0,class:"mt-4 bg-gray-100 px-4 py-2 rounded font-mono text-sm text-gray-500 break-all"},ge={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ye={class:"flex items-center"},_e={class:"ms-2 text-sm text-gray-600"},is={__name:"ApiTokenManager",props:{tokens:Array,availablePermissions:Array,defaultPermissions:Array},setup(d){const n=A({name:"",permissions:d.defaultPermissions}),u=A({permissions:[]}),_=A({}),b=P(!1),f=P(null),v=P(null),D=()=>{n.post(route("api-tokens.store"),{preserveScroll:!0,onSuccess:()=>{b.value=!0,n.reset()}})},B=p=>{u.permissions=p.abilities,f.value=p},N=()=>{u.put(route("api-tokens.update",f.value),{preserveScroll:!0,preserveState:!0,onSuccess:()=>f.value=null})},j=p=>{v.value=p},M=()=>{_.delete(route("api-tokens.destroy",v.value),{preserveScroll:!0,preserveState:!0,onSuccess:()=>v.value=null})};return(p,e)=>(a(),l("div",null,[o(X,{onSubmitted:D},{title:s(()=>e[9]||(e[9]=[r(" Create API Token ")])),description:s(()=>e[10]||(e[10]=[r(" API tokens allow third-party services to authenticate with our application on your behalf. ")])),form:s(()=>[i("div",oe,[o(I,{for:"name",value:"Name"}),o(q,{id:"name",modelValue:m(n).name,"onUpdate:modelValue":e[0]||(e[0]=t=>m(n).name=t),type:"text",class:"mt-1 block w-full",autofocus:""},null,8,["modelValue"]),o(R,{message:m(n).errors.name,class:"mt-2"},null,8,["message"])]),d.availablePermissions.length>0?(a(),l("div",ie,[o(I,{for:"permissions",value:"Permissions"}),i("div",ne,[(a(!0),l(C,null,S(d.availablePermissions,t=>(a(),l("div",{key:t},[i("label",re,[o(T,{checked:m(n).permissions,"onUpdate:checked":e[1]||(e[1]=g=>m(n).permissions=g),value:t},null,8,["checked","value"]),i("span",le,y(t),1)])]))),128))])])):k("",!0)]),actions:s(()=>[o(U,{on:m(n).recentlySuccessful,class:"me-3"},{default:s(()=>e[11]||(e[11]=[r(" Created. ")])),_:1},8,["on"]),o(V,{class:x({"opacity-25":m(n).processing}),disabled:m(n).processing},{default:s(()=>e[12]||(e[12]=[r(" Create ")])),_:1},8,["class","disabled"])]),_:1}),d.tokens.length>0?(a(),l("div",ae,[o(te),i("div",me,[o(L,null,{title:s(()=>e[13]||(e[13]=[r(" Manage API Tokens ")])),description:s(()=>e[14]||(e[14]=[r(" You may delete any of your existing tokens if they are no longer needed. ")])),content:s(()=>[i("div",de,[(a(!0),l(C,null,S(d.tokens,t=>(a(),l("div",{key:t.id,class:"flex items-center justify-between"},[i("div",ue,y(t.name),1),i("div",pe,[t.last_used_ago?(a(),l("div",ce," Last used "+y(t.last_used_ago),1)):k("",!0),d.availablePermissions.length>0?(a(),l("button",{key:1,class:"cursor-pointer ms-6 text-sm text-gray-400 underline",onClick:g=>B(t)}," Permissions ",8,fe)):k("",!0),i("button",{class:"cursor-pointer ms-6 text-sm text-red-500",onClick:g=>j(t)}," Delete ",8,ve)])]))),128))])]),_:1})])])):k("",!0),o(F,{show:b.value,onClose:e[3]||(e[3]=t=>b.value=!1)},{title:s(()=>e[15]||(e[15]=[r(" API Token ")])),content:s(()=>[e[16]||(e[16]=i("div",null," Please copy your new API token. For your security, it won't be shown again. ",-1)),p.$page.props.jetstream.flash.token?(a(),l("div",ke,y(p.$page.props.jetstream.flash.token),1)):k("",!0)]),footer:s(()=>[o(w,{onClick:e[2]||(e[2]=t=>b.value=!1)},{default:s(()=>e[17]||(e[17]=[r(" Close ")])),_:1})]),_:1},8,["show"]),o(F,{show:f.value!=null,onClose:e[6]||(e[6]=t=>f.value=null)},{title:s(()=>e[18]||(e[18]=[r(" API Token Permissions ")])),content:s(()=>[i("div",ge,[(a(!0),l(C,null,S(d.availablePermissions,t=>(a(),l("div",{key:t},[i("label",ye,[o(T,{checked:m(u).permissions,"onUpdate:checked":e[4]||(e[4]=g=>m(u).permissions=g),value:t},null,8,["checked","value"]),i("span",_e,y(t),1)])]))),128))])]),footer:s(()=>[o(w,{onClick:e[5]||(e[5]=t=>f.value=null)},{default:s(()=>e[19]||(e[19]=[r(" Cancel ")])),_:1}),o(V,{class:x(["ms-3",{"opacity-25":m(u).processing}]),disabled:m(u).processing,onClick:N},{default:s(()=>e[20]||(e[20]=[r(" Save ")])),_:1},8,["class","disabled"])]),_:1},8,["show"]),o(z,{show:v.value!=null,onClose:e[8]||(e[8]=t=>v.value=null)},{title:s(()=>e[21]||(e[21]=[r(" Delete API Token ")])),content:s(()=>e[22]||(e[22]=[r(" Are you sure you would like to delete this API token? ")])),footer:s(()=>[o(w,{onClick:e[7]||(e[7]=t=>v.value=null)},{default:s(()=>e[23]||(e[23]=[r(" Cancel ")])),_:1}),o(E,{class:x(["ms-3",{"opacity-25":m(_).processing}]),disabled:m(_).processing,onClick:M},{default:s(()=>e[24]||(e[24]=[r(" Delete ")])),_:1},8,["class","disabled"])]),_:1},8,["show"])]))}};export{is as default};
