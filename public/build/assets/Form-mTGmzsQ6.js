import{Q as O,T as D}from"./@inertiajs-Dt0-hqjZ.js";import{u as E}from"./vue-i18n-kWKo0idO.js";import{c as z,d as H,a as K}from"./@element-plus-CyTLADhX.js";import{_ as L}from"./AppLayout-DKZEmXIb.js";import{_ as R}from"./RedButton-D21iPtqa.js";import{_ as W}from"./LoadingIcon-CesYxFkK.js";import{_ as v}from"./InputLabel-BTXevqr4.js";import{_ as b}from"./TextInput-C52bsWxF.js";import{_ as d}from"./InputError-gQdwtcoE.js";import{_ as $}from"./FixedSelectionBox-CkXOgkaT.js";import{_ as G}from"./SecondaryButton-BoI1NwE9.js";import{i as J,v as X,k as w,o as c,S as g,a as i,c as p,l,a4 as t,F as T,M as Q,R as f,K as U,P as _}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@intlify-xvnhHnag.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./PrimaryButton-DE9sqoJj.js";import"./SelectionBox-CzAgH5wz.js";import"./@heroicons-BLousAGu.js";import"./@headlessui-gOb5_P77.js";const Y=["textContent"],Z=["textContent"],k={class:"max-w-7xl mx-auto py-6 px-6"},I={class:"bg-white rounded-md shadow px-6 py-5 flex flex-col mb-6"},ee={class:"flex-1 bg-white rounded-md shadow border border-gray-200 px-6 pt-4 pb-6 transition"},te={class:"mt-0 flex items-stretch"},se={class:"flex-1 mr-3"},oe={class:"flex-1 ml-3 mr-3"},le={class:"flex-1 ml-3"},ie={class:"mt-3"},ae={key:0,class:"mt-3 transition"},ne=["textContent"],re={class:"flex-1"},ue={class:"ml-6 flex items-center"},ce={class:"flex items-center mt-6"},me=["textContent"],et={__name:"Form",props:{title:String,survey:Object},setup(h){const{t:r}=E(),y=h,P=J("$toast"),x=O(),e=D(y.survey),m=X({clearQuestionType:!1,clearQuestionPublicTarget:!1}),S=[{value:"checkbox",label:r("checkbox")},{value:"select",label:r("selectBox")},{value:"text",label:r("textBox")},{value:"text_with_star",label:r("textBoxWithStar")}],B=[{value:"none",label:r("publicNone")},{value:"platform",label:r("publicPlatform")},{value:"app",label:r("publicApp")},{value:"platform_app",label:r("publicPlatformApp")}],N=()=>{if(e.processing)return!1;e.errors={},e.transform(o=>o).post(route("survey.store"),{onSuccess:()=>{e.survey_id||(e.reset(),m.clearQuestionType=!0,m.clearQuestionPublicTarget=!0),x.props.jetstream.flash.message&&P.success(x.props.jetstream.flash.message)}})},A=()=>{e.questions.push({question_id:"",type:"",content:"",point:"",public:"",choices:[{choice_id:"",content:""}]}),setTimeout(()=>{const o=document.getElementsByTagName("main")[0];o.scrollTo({left:0,top:o.scrollHeight,behavior:"smooth"})},0)},q=o=>{e.questions[o].choices.push({choice_id:"",content:""})},j=o=>{e.questions.length>1&&e.questions.splice(o,1)},F=(o,n)=>{e.questions[o].choices.length>1&&e.questions[o].choices.splice(n,1)},M=(o,n)=>{["checkbox","select"].includes(o.value)&&e.questions[n].choices.length===0&&q(n)};return(o,n)=>(c(),w(L,{title:h.title},{header:g(()=>[i("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:_(h.title)},null,8,Y),l(R,{class:f(["ml-auto",{"opacity-25":t(e).processing}]),disabled:t(e).processing,onClick:N},{default:g(()=>[t(e).processing?(c(),w(W,{key:0,class:"mr-2"})):U("",!0),i("span",{class:"text-sm",textContent:_(o.$t("save"))},null,8,Z)]),_:1},8,["class","disabled"])]),default:g(()=>[i("div",k,[i("div",I,[l(v,{for:"survey-title",value:o.$t("surveyTitle")},null,8,["value"]),l(b,{class:"mt-1 block w-full",id:"survey-title",modelValue:t(e).title,"onUpdate:modelValue":n[0]||(n[0]=C=>t(e).title=C),disabled:t(e).processing,type:"text"},null,8,["modelValue","disabled"]),l(d,{message:t(e).errors.title},null,8,["message"])]),(c(!0),p(T,null,Q(t(e).questions,(C,s)=>(c(),p("div",{class:f(["flex items-stretch",[s===0?"mt-2":"mt-6"]])},[i("div",ee,[i("div",te,[i("div",se,[l($,{modelValue:t(e).questions[s].type,"onUpdate:modelValue":a=>t(e).questions[s].type=a,label:o.$t("questionType"),disabled:t(e).processing,"clear-data":m.clearQuestionType,options:S,clearable:!1,onDataCleared:n[1]||(n[1]=a=>m.clearQuestionType=!1),onSelected:a=>M(a,s)},null,8,["modelValue","onUpdate:modelValue","label","disabled","clear-data","onSelected"]),l(d,{message:t(e).errors["questions."+s+".type"]},null,8,["message"])]),i("div",oe,[l($,{modelValue:t(e).questions[s].public,"onUpdate:modelValue":a=>t(e).questions[s].public=a,label:o.$t("questionPublic"),disabled:t(e).processing,"clear-data":m.clearQuestionPublicTarget,options:B,clearable:!1,onDataCleared:n[2]||(n[2]=a=>m.clearQuestionPublicTarget=!1)},null,8,["modelValue","onUpdate:modelValue","label","disabled","clear-data"]),l(d,{message:t(e).errors["questions."+s+".public"]},null,8,["message"])]),i("div",le,[l(v,{for:"question_point_"+s,value:o.$t("questionPoint")},null,8,["for","value"]),l(b,{class:"mt-1 block w-full",id:"question_point_"+s,modelValue:t(e).questions[s].point,"onUpdate:modelValue":a=>t(e).questions[s].point=a,disabled:t(e).processing,type:"text"},null,8,["id","modelValue","onUpdate:modelValue","disabled"]),l(d,{message:t(e).errors["questions."+s+".point"]},null,8,["message"])])]),i("div",ie,[l(v,{for:"question_"+s,value:o.$t("questionContent")},null,8,["for","value"]),l(b,{class:"mt-1 block w-full",id:"question_"+s,modelValue:t(e).questions[s].content,"onUpdate:modelValue":a=>t(e).questions[s].content=a,disabled:t(e).processing,type:"text"},null,8,["id","modelValue","onUpdate:modelValue","disabled"]),l(d,{message:t(e).errors["questions."+s+".content"]},null,8,["message"])]),["checkbox","select"].includes(t(e).questions[s].type)?(c(),p("div",ae,[i("div",{class:"mt-0",textContent:_(o.$t("answerOptions"))},null,8,ne),(c(!0),p(T,null,Q(t(e).questions[s].choices,(a,u)=>(c(),p("div",{class:f(["mt-1 flex items-start",{"mt-3 pt-2 border-t border-dashed":u>0}]),key:"choice_"+s+"_"+u},[i("div",re,[l(b,{class:"mt-1 block w-full",modelValue:t(e).questions[s].choices[u].content,"onUpdate:modelValue":V=>t(e).questions[s].choices[u].content=V,placeholder:o.$t("answerChoice",{index:u+1}),disabled:t(e).processing,type:"text"},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled"]),l(d,{message:t(e).errors["questions."+s+".choices."+u+".content"]},null,8,["message"])]),l(t(z),{class:f(["ml-6 w-5 text-gray-300 transition ease-in-out duration-150 mt-4",[t(e).questions[s].choices.length>1?"hover:cursor-pointer hover:text-red-500":""]]),onClick:V=>F(s,u)},null,8,["class","onClick"])],2))),128)),l(G,{class:"mt-3 text-sm h-[38px]",textContent:_(o.$t("addMoreAnswer")),onClick:a=>q(s)},null,8,["textContent","onClick"])])):U("",!0)]),i("div",ue,[l(t(H),{class:f(["w-6 text-gray-500 transition ease-in-out duration-150",[t(e).questions.length>1?"hover:cursor-pointer hover:text-red-500":""]]),onClick:a=>j(s)},null,8,["class","onClick"])])],2))),256)),i("div",ce,[i("div",{class:"ml-auto mr-2",textContent:_(o.$t("addNewQuestion"))},null,8,me),l(t(K),{class:"w-8 text-sky-500 transition ease-in-out duration-150 hover:cursor-pointer hover:text-sky-600",onClick:A})])])]),_:1},8,["title"]))}};export{et as default};
