import{T as z,i as U}from"./@inertiajs-Dt0-hqjZ.js";import{u as F}from"./vue-i18n-kWKo0idO.js";import{s as w}from"./ziggy-js-C7EU8ifa.js";import{_ as M}from"./AppLayout-DKZEmXIb.js";import{_ as P}from"./RedButton-D21iPtqa.js";import{_ as T}from"./LoadingIcon-CesYxFkK.js";import{_ as N}from"./TextAreaInput-y-SlU-FI.js";import{_ as v}from"./InputError-gQdwtcoE.js";import{_ as I}from"./FixedSelectionBox-CkXOgkaT.js";import{_ as O}from"./ImageInput-BV1wAASf.js";import{d as R}from"./pinia-Ddsh4R0D.js";import{_ as q}from"./SelectionBox-CzAgH5wz.js";import{v as E,b as y,j as k,k as $,o as x,r as S,c as C,K as B,a,P as g,R as _,F as D,i as K,l as c,S as V,a4 as l}from"./@vue-BnW70ngI.js";import Q from"./CommunityFormModal-Da4kliwT.js";import{_ as A}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{p as G}from"./@element-plus-CyTLADhX.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@intlify-xvnhHnag.js";import"./SecondaryButton-BoI1NwE9.js";import"./PrimaryButton-DE9sqoJj.js";import"./@heroicons-BLousAGu.js";import"./TextInput-C52bsWxF.js";import"./@headlessui-gOb5_P77.js";const j=R("community-stores",{state:()=>({communities:[]}),getters:{hasData:e=>e.communities.length>0,data:e=>e.communities},actions:{setData(e){this.communities=e},clear(){this.communities=[]},async loadData(){await window.axios.post("/community/list").then(e=>{this.setData(e.data)}).catch(e=>{console.log(e)})},async prepend(e){this.communities=[e,...this.communities]}}}),H={__name:"CommunitySelectionBox",props:{label:{type:String,default:""},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:""},modelValue:{default:"",validator(e){return e===null||typeof e=="string"||typeof e=="number"}},clearData:{type:Boolean,default:!1},clearable:{type:Boolean,default:!1},showSelected:{type:Boolean,default:!0},enableSearch:{type:Boolean,default:!1},loadData:{type:Boolean,default:!0},searchPlaceholder:{type:String,default:""}},emits:["update:modelValue","update:loading","selected","dataCleared"],setup(e,{emit:p}){const s=p,u=e,m=j(),r=E({loading:!1,modelValue:u.modelValue??"",options:y(()=>m.data.map(i=>({label:i.name,value:i.community_id})))});k(()=>u.modelValue,i=>{r.modelValue=i??""},{immediate:!0}),k(()=>u.clearData,i=>{i===!0&&(r.modelValue="",s("dataCleared"))});const t=async()=>{s("update:loading",!0),r.loading=!0,await m.loadData().then(()=>{r.loading=!1,r.modelValue=u.modelValue,s("update:loading",!1)})};!m.hasData&&u.loadData&&t();const f=i=>{s("update:modelValue",i)},h=i=>{s("selected",i)};return(i,b)=>(x(),$(q,{label:e.label,disabled:e.disabled,loading:r.loading,placeholder:e.placeholder,options:r.options,"can-clear":e.clearable,"show-selected":e.showSelected,"enable-search":e.enableSearch,"search-placeholder":e.searchPlaceholder,modelValue:r.modelValue,"onUpdate:modelValue":[b[0]||(b[0]=o=>r.modelValue=o),f],onRefresh:t,onSelected:h},null,8,["label","disabled","loading","placeholder","options","can-clear","show-selected","enable-search","search-placeholder","modelValue"]))}},J=["textContent"],L=["disabled","aria-checked","aria-labelledby"],W={__name:"Switcher",props:{modelValue:{type:[Number,Boolean],default:0,validator(e){return[0,1,!0,!1].includes(e)}},label:{type:String,default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"lg",validator(e){return["sm","md","lg"].includes(e)}},color:{type:String,default:"sky",validator(e){return["sky","blue","green","red","purple","indigo","pink"].includes(e)}}},emits:["update:modelValue","change"],setup(e,{emit:p}){const s=e,u=p,m=y(()=>s.modelValue===1||s.modelValue===!0),r=S(`switcher-${Math.random().toString(36).substr(2,9)}`),t={sm:{switch:"h-5 w-9",toggle:"h-4 w-4",translate:"translate-x-[18px]"},md:{switch:"h-6 w-11",toggle:"h-5 w-5",translate:"translate-x-[22px]"},lg:{switch:"h-7 w-14",toggle:"h-6 w-6",translate:"translate-x-[30px]"}},f={sky:"bg-sky-400",blue:"bg-blue-400",green:"bg-green-400",red:"bg-red-400",purple:"bg-purple-400",indigo:"bg-indigo-400",pink:"bg-pink-400"},h=y(()=>["mt-[6px] relative inline-flex flex-shrink-0 cursor-pointer rounded-full transition-colors duration-200 ease-in-out focus:outline-none",t[s.size].switch,s.disabled?"opacity-50 cursor-not-allowed":"",m.value?f[s.color]:"bg-gray-200"]),i=y(()=>["pointer-events-none absolute inset-0 rounded-full transition-colors duration-200 ease-in-out",m.value?f[s.color]:"bg-gray-200"]),b=y(()=>["pointer-events-none inline-block rounded-full bg-white shadow transform transition duration-200 ease-in-out flex items-center justify-center translate-y-0.5",t[s.size].toggle,m.value?t[s.size].translate:"translate-x-[2px]"]),o=()=>{if(s.disabled)return;const n=m.value?0:1;u("update:modelValue",n),u("change",n)};return(n,d)=>(x(),C(D,null,[e.label?(x(),C("label",{key:0,class:"w-full",textContent:g(e.label)},null,8,J)):B("",!0),a("button",{type:"button",class:_(h.value),disabled:e.disabled,onClick:o,role:"switch","aria-checked":m.value,"aria-labelledby":r.value},[a("span",{class:_(i.value),"aria-hidden":"true"},null,2),a("span",{class:_(b.value),"aria-hidden":"true"},null,2)],10,L)],64))}},X=A(W,[["__scopeId","data-v-a2d587f5"]]),Y={class:"flex-1 flex items-center"},Z=["textContent"],ee={class:"max-w-7xl mx-auto py-6 px-6"},te={class:"bg-white rounded-md shadow px-5 py-4 space-y-4"},le={class:"flex flex-col"},oe=["textContent"],ae={class:"flex space-x-6"},se={class:"flex-1 flex flex-col"},ne=["textContent"],ie={class:"flex-1 flex flex-col"},re={class:"flex space-x-6"},de={class:"flex-1 flex flex-col"},ce={class:"flex items-end"},me={class:"flex flex-col flex-1"},ue=["textContent"],pe={class:"flex flex-col ml-1.5"},fe=["disabled"],ge={class:"flex-1 flex flex-col"},be={class:"mt-4 flex"},he=["textContent"],ct={__name:"Form",props:{post:Object,action:String},setup(e){const p=e,{t:s}=F(),u=j(),m=[{value:"answerr_q",label:s("answerrQ")},{value:"answerr_topic",label:s("answerrTopic")}],r=K("$toast"),t=z(p.post),f=S(!1),h=async o=>{await u.prepend(o),t.community_id=o.community_id},i=()=>{if(p.action==="update")return!1;f.value=!0},b=()=>{if(t.processing)return!1;t.errors={},t.transform(o=>(typeof o.image=="string"&&delete o.image,o)).post(w("post.store"),{preserveScroll:!0,preserveState:!0,onSuccess:o=>{o.props.jetstream.flash.message&&r.success(o.props.jetstream.flash.message),p.action==="create"&&setTimeout(()=>{window.location=w("post.list")},1500)}})};return(o,n)=>(x(),C(D,null,[c(M,{title:o.$t(p.action+"Post")},{header:V(()=>[a("div",Y,[a("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:g(o.$t(p.action+"Post"))},null,8,Z),c(l(U),{class:"primary-button text-sm",href:l(w)("post.list"),textContent:g(o.$t("list"))},null,8,["href","textContent"])])]),default:V(()=>[a("div",ee,[a("div",te,[a("div",le,[a("label",{textContent:g(o.$t("postContent")),class:"w-full"},null,8,oe),c(N,{class:"block w-full mt-1",modelValue:l(t).content,"onUpdate:modelValue":n[0]||(n[0]=d=>l(t).content=d),disabled:l(t).processing,rows:"4"},null,8,["modelValue","disabled"]),c(v,{class:"w-full mt-1",message:l(t).errors.content},null,8,["message"])]),a("div",ae,[a("div",se,[a("label",{textContent:g(o.$t("postType")),class:"w-full mb-1"},null,8,ne),c(I,{modelValue:l(t).type,"onUpdate:modelValue":n[1]||(n[1]=d=>l(t).type=d),placeholder:o.$t("postType"),disabled:l(t).processing,options:m,clearable:!1},null,8,["modelValue","placeholder","disabled"]),c(v,{class:"w-full mt-1",message:l(t).errors.type},null,8,["message"])]),a("div",ie,[c(O,{class:"flex-1",label:o.$t("image"),error:l(t).errors.image,disabled:l(t).processing,modelValue:l(t).image,"onUpdate:modelValue":[n[2]||(n[2]=d=>l(t).image=d),n[3]||(n[3]=d=>l(t).image=d)]},null,8,["label","error","disabled","modelValue"])])]),a("div",re,[a("div",de,[a("div",ce,[a("div",me,[a("label",{textContent:g(o.$t("community")),class:"w-full"},null,8,ue),c(H,{modelValue:l(t).community_id,"onUpdate:modelValue":n[4]||(n[4]=d=>l(t).community_id=d),placeholder:o.$t("community"),disabled:l(t).processing||e.action==="update",clearable:!1},null,8,["modelValue","placeholder","disabled"])]),a("div",pe,[a("button",{class:_(["rounded-md border transition-colors duration-200 border-sky-400 p-2.5 focus:outline-none text-white",{"bg-gray-200 cursor-default":l(t).processing,"bg-sky-400 disabled:hover:bg-sky-400 disabled:hover:border-sky-400 hover:bg-sky-500 hover:border-sky-500":!l(t).processing}]),disabled:l(t).processing||e.action==="update",onClick:i},[c(l(G),{class:"w-5 h-5","aria-hidden":"true"})],10,fe)])]),c(v,{class:"w-full mt-1",message:l(t).errors.community_id},null,8,["message"])]),a("div",ge,[c(X,{modelValue:l(t).camera_roll,"onUpdate:modelValue":n[5]||(n[5]=d=>l(t).camera_roll=d),label:l(s)("cameraRoll")},null,8,["modelValue","label"])])])]),a("div",be,[c(P,{class:"normal-case ml-auto",disabled:l(t).processing,onClick:b},{default:V(()=>[l(t).processing?(x(),$(T,{key:0,class:"mr-2"})):B("",!0),a("span",{class:"text-sm",textContent:g(o.$t("save"))},null,8,he)]),_:1},8,["disabled"])])])]),_:1},8,["title"]),c(Q,{"show-modal":f.value,onCloseModal:n[6]||(n[6]=d=>f.value=!1),onCommunityCreated:h},null,8,["show-modal"])],64))}};export{ct as default};
