import{d as h}from"./pinia-BB7AK9rL.js";import{g as p}from"./index-DHV2tfOS.js";import{_ as m}from"./SelectionBox-58uvzdoT.js";import{v as y,b as r,j as g,k as b,o as S}from"./@vue-BnW70ngI.js";const V=h({id:"data-stores",state:()=>({surveys:[]}),getters:{hasData:e=>e.surveys.length>0,data:e=>e.surveys},actions:{setData(e){this.surveys=e},clear(){this.surveys=[]},async loadData(){await window.axios.post("/survey/pinia-store").then(e=>{this.setData(e.data)}).catch(e=>{console.log(e)})}}}),x={__name:"SurveySelectionBox",props:{label:{type:String,default:""},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:""},modelValue:{default:"",validator(e){return e===null||typeof e=="string"||typeof e=="number"}},clearData:{type:Boolean,default:!1},clearable:{type:Boolean,default:!1},showSelected:{type:Boolean,default:!0},enableSearch:{type:Boolean,default:!1},loadData:{type:Boolean,default:!0},searchPlaceholder:{type:String,default:""},exclude:{type:Array,default:[]}},emits:["update:modelValue","update:loading","selected","dataCleared"],setup(e,{emit:c}){const o=c,d=e,s=V(),a=y({loading:!1,modelValue:d.modelValue??"",exclude:r(()=>d.exclude),options:r(()=>{const l=[];return s.hasData&&s.data.forEach(t=>{a.exclude.includes(t.survey_id)||l.push({label:p(t.survey_id,"S")+" - "+t.title,title:t.title,value:t.survey_id})}),l})});g(()=>d.clearData,l=>{l===!0&&(a.modelValue="",o("dataCleared"))});const n=async()=>{o("update:loading",!0),a.loading=!0,await s.loadData().then(()=>{a.loading=!1,a.modelValue=d.modelValue,o("update:loading",!1)})};!s.hasData&&d.loadData&&n();const u=l=>{o("update:modelValue",l)},i=l=>{o("selected",l)};return(l,t)=>(S(),b(m,{label:e.label,disabled:e.disabled,loading:a.loading,placeholder:e.placeholder,options:a.options,"can-clear":e.clearable,"show-selected":e.showSelected,"enable-search":e.enableSearch,"search-placeholder":e.searchPlaceholder,modelValue:a.modelValue,"onUpdate:modelValue":[t[0]||(t[0]=f=>a.modelValue=f),u],onRefresh:n,onSelected:i},null,8,["label","disabled","loading","placeholder","options","can-clear","show-selected","enable-search","search-placeholder","modelValue"]))}};export{x as _};
