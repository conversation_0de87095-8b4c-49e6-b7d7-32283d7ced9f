import{Z as a}from"./@inertiajs-BhKdJayA.js";import{b as n,c as i,o as l,l as c,a as r,a4 as m,P as u,F as p}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";const d={class:"min-h-screen bg-gray-100 font-light flex items-center justify-center"},f=["textContent"],B={__name:"Error",props:{status:Number},setup(t){const o=t,s=n(()=>({503:"503: Service Unavailable",500:"500: Server Error",404:"404: Page Not Found",403:"403: Forbidden"})[o.status]);return(e,g)=>(l(),i(p,null,[c(m(a),{title:s.value??e.$t("errorTitle")},null,8,["title"]),r("div",d,[r("div",{class:"text-lg",textContent:u(e.$t(t.status?"error."+t.status:"errorMessage"))},null,8,f)])],64))}};export{B as default};
