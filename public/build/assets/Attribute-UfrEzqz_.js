import{_ as k}from"./AppLayout-_qQ0AdHn.js";import{_ as f}from"./PrimaryButton-DE9sqoJj.js";import{c as $,d as y,e as m,f as v,g as _}from"./primevue-CrCPcMFN.js";import{v as j,b as C,k as D,o as n,S as d,a as t,l as r,P as e,a4 as u,c as a,F as b,K as w,M as p,R as g}from"./@vue-BnW70ngI.js";import"./@inertiajs-Dt0-hqjZ.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";const I=["textContent"],T={class:"max-w-7xl mx-auto py-6 px-6"},O={class:"bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden"},S={class:"w-full"},L={class:"text-left flex"},B=["textContent"],A={class:"text-left flex border-t odd:bg-blue-50 even:bg-white"},N=["textContent"],V=["textContent"],F={class:"text-left flex border-t odd:bg-blue-50 even:bg-white"},z=["textContent"],E=["textContent"],K={class:"w-full"},M={class:"text-left flex border-b"},P=["textContent"],R={class:"text-left flex border-b odd:bg-blue-50 even:bg-white"},q=["textContent"],G=["textContent"],H=["textContent"],J=["textContent"],Q={key:0,class:"mb-6 w-full flex justify-center"},U=["textContent"],W={class:"w-full"},X={class:"text-left flex"},Y=["textContent"],Z={class:"text-left flex border-t odd:bg-blue-50 even:bg-white"},tt=["textContent"],et=["textContent"],st={class:"text-left flex border-t odd:bg-blue-50 even:bg-white"},ot=["textContent"],lt=["textContent"],nt=["textContent"],it={class:"w-full"},at={class:"text-left flex border-b"},dt=["textContent"],rt={class:"text-left flex border-b odd:bg-blue-50 even:bg-white"},ct=["textContent"],ut=["textContent"],xt=["textContent"],bt=["textContent"],ht={key:0,class:"mb-6 w-full flex justify-center"},ft=["textContent"],mt={class:"w-full"},_t={class:"text-left flex"},Ct=["textContent"],wt={class:"text-left flex border-t odd:bg-blue-50 even:bg-white"},pt=["textContent"],gt=["textContent"],kt={class:"text-left flex border-t odd:bg-blue-50 even:bg-white"},$t=["textContent"],yt=["textContent"],vt=["textContent"],ae={__name:"Attribute",props:{showToIndex:Number,attributes:Object,user:Object},setup(o){const h=o,c=j({showToIndex:h.showToIndex,showLikeDates:C(()=>{const s=[],i=Object.keys(h.attributes.like.data);for(let l=0;l<i.length;l++)l<c.showToIndex&&s.push(i[l]);return s}),showSimilarDates:C(()=>{const s=[],i=Object.keys(h.attributes.similar.data);for(let l=0;l<i.length;l++)l<c.showToIndex&&s.push(i[l]);return s})});return(s,i)=>(n(),D(k,{title:s.$t("userAttribute")},{header:d(()=>[t("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:e(s.$t("userAttribute"))},null,8,I)]),default:d(()=>[t("div",T,[t("div",O,[t("table",S,[t("thead",null,[t("tr",L,[t("th",{colspan:"2",textContent:e(s.$t("userInfo"))},null,8,B)])]),t("tbody",null,[t("tr",A,[t("td",{class:"w-1/3",textContent:e(s.$t("ID"))},null,8,N),t("td",{class:"border-l flex-1",textContent:e(o.user.user_id)},null,8,V)]),t("tr",F,[t("td",{class:"w-1/3",textContent:e(s.$t("username"))},null,8,z),t("td",{class:"border-l flex-1",textContent:e(o.user.name)},null,8,E)])])])]),r(u($),{value:"0",class:"bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden"},{default:d(()=>[r(u(y),null,{default:d(()=>[r(u(m),{value:"0",textContent:e(s.$t("tabSimilar"))},null,8,["textContent"]),r(u(m),{value:"1",textContent:e(s.$t("tabLike"))},null,8,["textContent"])]),_:1}),r(u(v),{class:"p-0"},{default:d(()=>[r(u(_),{value:"0"},{default:d(()=>[o.attributes.similar.ids.length>0?(n(),a(b,{key:0},[t("table",K,[t("thead",null,[t("tr",M,[t("th",{colspan:"2",textContent:e(s.$t("topSimilarByDay"))},null,8,P)])]),t("tbody",null,[t("tr",R,[t("td",{class:"w-1/3",textContent:e(s.$t("date"))},null,8,q),t("td",{class:"border-l flex-1",textContent:e(s.$t("ID"))},null,8,G)]),(n(!0),a(b,null,p(o.attributes.similar.data,(l,x)=>(n(),a("tr",{class:g(["text-left flex border-b odd:bg-blue-50 even:bg-white",c.showSimilarDates.includes(x)?"":"hidden"])},[t("td",{class:"w-1/3",textContent:e(x)},null,8,H),t("td",{class:"border-l flex-1",textContent:e(l.join(", "))},null,8,J)],2))),256))])]),Object.keys(o.attributes.similar.data).length>c.showToIndex?(n(),a("div",Q,[r(f,{class:"normal-case",onClick:i[0]||(i[0]=l=>c.showToIndex=Object.keys(o.attributes.similar.data).length)},{default:d(()=>[t("span",{class:"text-sm",textContent:e(s.$t("displayAll"))},null,8,U)]),_:1})])):w("",!0),t("table",W,[t("thead",null,[t("tr",X,[t("th",{colspan:"2",textContent:e(s.$t("topSimilar"))},null,8,Y)])]),t("tbody",null,[t("tr",Z,[t("td",{class:"w-1/3",textContent:e(s.$t("top50"))},null,8,tt),t("td",{class:"border-l flex-1",textContent:e(o.attributes.similar.ids.join(", "))},null,8,et)]),t("tr",st,[t("td",{class:"w-1/3",textContent:e(s.$t("viewedData"))},null,8,ot),t("td",{class:"border-l flex-1",textContent:e(o.attributes.similar.posts.join(", "))},null,8,lt)])])])],64)):(n(),a("div",{key:1,class:"p-3",textContent:e(s.$t("emptySimilarData"))},null,8,nt))]),_:1}),r(u(_),{value:"1"},{default:d(()=>[o.attributes.like.ids.length>0?(n(),a(b,{key:0},[t("table",it,[t("thead",null,[t("tr",at,[t("th",{colspan:"2",textContent:e(s.$t("topLikeByDay"))},null,8,dt)])]),t("tbody",null,[t("tr",rt,[t("td",{class:"w-1/3",textContent:e(s.$t("date"))},null,8,ct),t("td",{class:"border-l flex-1",textContent:e(s.$t("ID"))},null,8,ut)]),(n(!0),a(b,null,p(o.attributes.like.data,(l,x)=>(n(),a("tr",{class:g(["text-left flex border-b odd:bg-blue-50 even:bg-white",c.showLikeDates.includes(x)?"":"hidden"])},[t("td",{class:"w-1/3",textContent:e(x)},null,8,xt),t("td",{class:"border-l flex-1",textContent:e(l.join(", "))},null,8,bt)],2))),256))])]),Object.keys(o.attributes.like.data).length>c.showToIndex?(n(),a("div",ht,[r(f,{class:"normal-case",onClick:i[1]||(i[1]=l=>c.showToIndex=Object.keys(o.attributes.like.data).length)},{default:d(()=>[t("span",{class:"text-sm",textContent:e(s.$t("displayAll"))},null,8,ft)]),_:1})])):w("",!0),t("table",mt,[t("thead",null,[t("tr",_t,[t("th",{colspan:"2",textContent:e(s.$t("topLike"))},null,8,Ct)])]),t("tbody",null,[t("tr",wt,[t("td",{class:"w-1/3",textContent:e(s.$t("top15"))},null,8,pt),t("td",{class:"border-l flex-1",textContent:e(o.attributes.like.ids.join(", "))},null,8,gt)]),t("tr",kt,[t("td",{class:"w-1/3",textContent:e(s.$t("relatedData"))},null,8,$t),t("td",{class:"border-l flex-1",textContent:e(o.attributes.like.posts.join(", "))},null,8,yt)])])])],64)):(n(),a("div",{key:1,class:"p-3",textContent:e(s.$t("emptyLikedData"))},null,8,vt))]),_:1})]),_:1})]),_:1})])]),_:1},8,["title"]))}};export{ae as default};
