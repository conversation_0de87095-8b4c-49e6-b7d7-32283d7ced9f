import{i as h,w as b,c as w,o as p,l as t,a4 as s,S as m,a,_ as $,P as f,R as k,k as v,K as C,F as V}from"./@vue-BnW70ngI.js";import{Q as y,T as x,Z as B}from"./@inertiajs-BhKdJayA.js";import{A as E,a as j}from"./AuthenticationCardLogo-C3QjX6Mv.js";import{_ as z}from"./Checkbox-BW6Lzxs4.js";import{_ as d}from"./InputError-gQdwtcoE.js";import{_ as u}from"./InputLabel-BTXevqr4.js";import{_ as A}from"./LoadingIcon-CesYxFkK.js";import{_ as N}from"./PrimaryButton-DE9sqoJj.js";import{_}from"./TextInput-C52bsWxF.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const S={class:"mt-0"},U={class:"mt-4"},F={class:"block mt-4"},L={class:"flex items-center"},M=["textContent"],P={class:"flex items-center justify-end mt-4"},R=["textContent"],oe={__name:"Login",props:{canResetPassword:Boolean},setup(D){let n={};const i=h("$toast"),c=y();b(async()=>{c.props.jetstream.flash.throttle&&(e.setError(n),i.error(c.props.jetstream.flash.throttle))});const e=x({email:"",password:"",remember:!1}),g=()=>{if(e.processing)return!1;e.transform(o=>({...o,remember:e.remember?"on":""})).post(route("login"),{onBefore:()=>{n=e.errors,e.clearErrors()},onError:o=>{o.unauthorized&&i.error(o.unauthorized)}})};return(o,r)=>(p(),w(V,null,[t(s(B),{title:o.$t("login")},null,8,["title"]),t(j,null,{logo:m(()=>[t(E)]),default:m(()=>[a("form",{onSubmit:$(g,["prevent"]),autocomplete:"off"},[a("div",S,[t(u,{for:"email",value:o.$t("email")},null,8,["value"]),t(_,{id:"email",class:"mt-1",modelValue:s(e).email,"onUpdate:modelValue":r[0]||(r[0]=l=>s(e).email=l),autofocus:"",autocomplete:"off"},null,8,["modelValue"]),t(d,{class:"mt-2",message:s(e).errors.email},null,8,["message"])]),a("div",U,[t(u,{for:"password",value:o.$t("password")},null,8,["value"]),t(_,{id:"password",class:"mt-1",modelValue:s(e).password,"onUpdate:modelValue":r[1]||(r[1]=l=>s(e).password=l),type:"password",autocomplete:"off"},null,8,["modelValue"]),t(d,{class:"mt-2",message:s(e).errors.password},null,8,["message"])]),a("div",F,[a("label",L,[t(z,{checked:s(e).remember,"onUpdate:checked":r[2]||(r[2]=l=>s(e).remember=l),name:"remember"},null,8,["checked"]),a("span",{class:"ms-2 text-sm text-gray-600",textContent:f(o.$t("rememberMe"))},null,8,M)])]),a("div",P,[t(N,{class:k(["ms-4",{"opacity-25":s(e).processing}]),disabled:s(e).processing},{default:m(()=>[s(e).processing?(p(),v(A,{key:0,class:"mr-2",color:"#ffffff"})):C("",!0),a("span",{textContent:f(o.$t("login"))},null,8,R)]),_:1},8,["class","disabled"])])],32)]),_:1})],64))}};export{oe as default};
