import{Q as O,T as D}from"./@inertiajs-BhKdJayA.js";import{u as E}from"./vue-i18n-DNS8h1FH.js";import{c as z,d as H,a as K}from"./@element-plus-ccBf1-WH.js";import{_ as L}from"./AppLayout-m_I9gnvX.js";import{_ as R}from"./RedButton-D21iPtqa.js";import{_ as W}from"./LoadingIcon-CesYxFkK.js";import{_ as v}from"./InputLabel-BTXevqr4.js";import{_ as b}from"./TextInput-C52bsWxF.js";import{_ as m}from"./InputError-gQdwtcoE.js";import{_ as $}from"./FixedSelectionBox-CwNS68U7.js";import{_ as G}from"./SecondaryButton-BWHXZF7Q.js";import{i as J,v as X,k as w,o as c,S as g,a,c as p,l,a4 as t,F as T,M as Q,R as f,K as U,P as _}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./PrimaryButton-DE9sqoJj.js";import"./SelectionBox-58uvzdoT.js";import"./@heroicons-BLousAGu.js";import"./lodash-DBgjQQU6.js";import"./@headlessui-gOb5_P77.js";const Y=["textContent"],Z=["textContent"],k={class:"max-w-7xl mx-auto py-6 px-6"},I={class:"bg-white rounded-md shadow px-6 py-5 flex flex-col mb-6"},ee={class:"flex-1 bg-white rounded-md shadow border border-gray-200 px-6 pt-4 pb-6 transition"},te={class:"mt-0 flex items-stretch"},se={class:"flex-1 mr-3"},oe={class:"flex-1 ml-3 mr-3"},le={class:"flex-1 ml-3"},ae={class:"mt-3"},ne={key:0,class:"mt-3 transition"},ie=["textContent"],re={class:"flex-1"},ue={class:"ml-6 flex items-center"},ce={class:"flex items-center mt-6"},de=["textContent"],Me={__name:"Form",props:{title:String,survey:Object},setup(h){const{t:r}=E(),y=h,P=J("$toast"),x=O(),e=D(y.survey),d=X({clearQuestionType:!1,clearQuestionPublicTarget:!1}),S=[{value:"checkbox",label:r("checkbox")},{value:"select",label:r("selectBox")},{value:"text",label:r("textBox")},{value:"text_with_star",label:r("textBoxWithStar")}],B=[{value:"none",label:r("publicNone")},{value:"platform",label:r("publicPlatform")},{value:"app",label:r("publicApp")},{value:"platform_app",label:r("publicPlatformApp")}],N=()=>{if(e.processing)return!1;e.errors={},e.transform(o=>o).post(route("survey.store"),{onSuccess:()=>{e.survey_id||(e.reset(),d.clearQuestionType=!0,d.clearQuestionPublicTarget=!0),x.props.jetstream.flash.message&&P.success(x.props.jetstream.flash.message)}})},A=()=>{e.questions.push({question_id:"",type:"",content:"",point:"",public:"",choices:[{choice_id:"",content:""}]}),setTimeout(()=>{const o=document.getElementsByTagName("main")[0];o.scrollTo({left:0,top:o.scrollHeight,behavior:"smooth"})},0)},q=o=>{e.questions[o].choices.push({choice_id:"",content:""})},j=o=>{e.questions.length>1&&e.questions.splice(o,1)},F=(o,i)=>{e.questions[o].choices.length>1&&e.questions[o].choices.splice(i,1)},M=(o,i)=>{["checkbox","select"].includes(o.value)&&e.questions[i].choices.length===0&&q(i)};return(o,i)=>(c(),w(L,{title:h.title},{header:g(()=>[a("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:_(h.title)},null,8,Y),l(R,{class:f(["ml-auto",{"opacity-25":t(e).processing}]),disabled:t(e).processing,onClick:N},{default:g(()=>[t(e).processing?(c(),w(W,{key:0,class:"mr-2"})):U("",!0),a("span",{class:"text-sm",textContent:_(o.$t("save"))},null,8,Z)]),_:1},8,["class","disabled"])]),default:g(()=>[a("div",k,[a("div",I,[l(v,{for:"survey-title",value:o.$t("surveyTitle")},null,8,["value"]),l(b,{class:"mt-1 block w-full",id:"survey-title",modelValue:t(e).title,"onUpdate:modelValue":i[0]||(i[0]=C=>t(e).title=C),disabled:t(e).processing,type:"text"},null,8,["modelValue","disabled"]),l(m,{message:t(e).errors.title},null,8,["message"])]),(c(!0),p(T,null,Q(t(e).questions,(C,s)=>(c(),p("div",{class:f(["flex items-stretch",[s===0?"mt-2":"mt-6"]])},[a("div",ee,[a("div",te,[a("div",se,[l($,{modelValue:t(e).questions[s].type,"onUpdate:modelValue":n=>t(e).questions[s].type=n,label:o.$t("questionType"),disabled:t(e).processing,"clear-data":d.clearQuestionType,options:S,clearable:!1,onDataCleared:i[1]||(i[1]=n=>d.clearQuestionType=!1),onSelected:n=>M(n,s)},null,8,["modelValue","onUpdate:modelValue","label","disabled","clear-data","onSelected"]),l(m,{message:t(e).errors["questions."+s+".type"]},null,8,["message"])]),a("div",oe,[l($,{modelValue:t(e).questions[s].public,"onUpdate:modelValue":n=>t(e).questions[s].public=n,label:o.$t("questionPublic"),disabled:t(e).processing,"clear-data":d.clearQuestionPublicTarget,options:B,clearable:!1,onDataCleared:i[2]||(i[2]=n=>d.clearQuestionPublicTarget=!1)},null,8,["modelValue","onUpdate:modelValue","label","disabled","clear-data"]),l(m,{message:t(e).errors["questions."+s+".public"]},null,8,["message"])]),a("div",le,[l(v,{for:"question_point_"+s,value:o.$t("questionPoint")},null,8,["for","value"]),l(b,{class:"mt-1 block w-full",id:"question_point_"+s,modelValue:t(e).questions[s].point,"onUpdate:modelValue":n=>t(e).questions[s].point=n,disabled:t(e).processing,type:"text"},null,8,["id","modelValue","onUpdate:modelValue","disabled"]),l(m,{message:t(e).errors["questions."+s+".point"]},null,8,["message"])])]),a("div",ae,[l(v,{for:"question_"+s,value:o.$t("questionContent")},null,8,["for","value"]),l(b,{class:"mt-1 block w-full",id:"question_"+s,modelValue:t(e).questions[s].content,"onUpdate:modelValue":n=>t(e).questions[s].content=n,disabled:t(e).processing,type:"text"},null,8,["id","modelValue","onUpdate:modelValue","disabled"]),l(m,{message:t(e).errors["questions."+s+".content"]},null,8,["message"])]),["checkbox","select"].includes(t(e).questions[s].type)?(c(),p("div",ne,[a("div",{class:"mt-0",textContent:_(o.$t("answerOptions"))},null,8,ie),(c(!0),p(T,null,Q(t(e).questions[s].choices,(n,u)=>(c(),p("div",{class:f(["mt-1 flex items-start",{"mt-3 pt-2 border-t border-dashed":u>0}]),key:"choice_"+s+"_"+u},[a("div",re,[l(b,{class:"mt-1 block w-full",modelValue:t(e).questions[s].choices[u].content,"onUpdate:modelValue":V=>t(e).questions[s].choices[u].content=V,placeholder:o.$t("answerChoice",{index:u+1}),disabled:t(e).processing,type:"text"},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled"]),l(m,{message:t(e).errors["questions."+s+".choices."+u+".content"]},null,8,["message"])]),l(t(z),{class:f(["ml-6 w-5 text-gray-300 transition ease-in-out duration-150 mt-4",[t(e).questions[s].choices.length>1?"hover:cursor-pointer hover:text-red-500":""]]),onClick:V=>F(s,u)},null,8,["class","onClick"])],2))),128)),l(G,{class:"mt-3 text-sm h-[38px]",textContent:_(o.$t("addMoreAnswer")),onClick:n=>q(s)},null,8,["textContent","onClick"])])):U("",!0)]),a("div",ue,[l(t(H),{class:f(["w-6 text-gray-500 transition ease-in-out duration-150",[t(e).questions.length>1?"hover:cursor-pointer hover:text-red-500":""]]),onClick:n=>j(s)},null,8,["class","onClick"])])],2))),256)),a("div",ce,[a("div",{class:"ml-auto mr-2",textContent:_(o.$t("addNewQuestion"))},null,8,de),l(t(K),{class:"w-8 text-sky-500 transition ease-in-out duration-150 hover:cursor-pointer hover:text-sky-600",onClick:A})])])]),_:1},8,["title"]))}};export{Me as default};
