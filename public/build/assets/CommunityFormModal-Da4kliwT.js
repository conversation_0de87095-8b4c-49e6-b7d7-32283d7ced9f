import{T as y}from"./@inertiajs-Dt0-hqjZ.js";import{u as v}from"./vue-i18n-kWKo0idO.js";import{s as M}from"./ziggy-js-C7EU8ifa.js";import{_ as V,a as $}from"./SecondaryButton-BoI1NwE9.js";import{_ as k}from"./RedButton-D21iPtqa.js";import{_ as j}from"./LoadingIcon-CesYxFkK.js";import{_ as N}from"./TextAreaInput-y-SlU-FI.js";import{_ as S}from"./TextInput-C52bsWxF.js";import{_ as u}from"./InputError-gQdwtcoE.js";import{_ as B}from"./ImageInput-BV1wAASf.js";import{i as T,v as U,j as D,k as f,K as _,o as w,S as x,a as t,P as n,a4 as o,l}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@intlify-xvnhHnag.js";const F=["textContent"],I={class:"border-t border-b px-6 pt-4 pb-6 space-y-4"},K=["textContent"],P=["textContent"],q={class:"flex items-center justify-end px-3 py-3"},z=["textContent"],Mo={__name:"CommunityFormModal",props:{showModal:Boolean},emits:["closeModal","communityCreated"],setup(C,{emit:g}){const{t:r}=v(),d=g,p=C,b=T("$toast"),e=y({name:"",description:"",image:""}),a=U({showModal:!1,open:!1});D(()=>p.showModal,()=>{a.showModal=p.showModal,p.showModal&&setTimeout(()=>a.open=!0,150)});const c=()=>{a.open=!1,setTimeout(()=>{a.showModal=!1,d("closeModal")},150)},h=()=>{if(e.processing)return!1;e.errors={},e.post(M("community.store"),{preserveScroll:!0,preserveState:!0,onSuccess:i=>{i.props.jetstream.flash.message&&b.success(i.props.jetstream.flash.message),i.props.jetstream.flash.community&&d("communityCreated",i.props.jetstream.flash.community),c()}})};return(i,s)=>a.showModal?(w(),f($,{key:0,show:a.open,onClose:c,class:"community-form-modal"},{default:x(()=>[t("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:n(o(r)("addNewCommunity"))},null,8,F),t("div",I,[t("div",null,[t("label",{textContent:n(o(r)("communityName")),class:"w-full"},null,8,K),l(S,{class:"block w-full mt-1",modelValue:o(e).name,"onUpdate:modelValue":s[0]||(s[0]=m=>o(e).name=m),disabled:o(e).processing,rows:"4"},null,8,["modelValue","disabled"]),l(u,{class:"w-full mt-1",message:o(e).errors.name},null,8,["message"])]),t("div",null,[t("label",{textContent:n(o(r)("communityDescription")),class:"w-full"},null,8,P),l(N,{class:"block w-full mt-1",modelValue:o(e).description,"onUpdate:modelValue":s[1]||(s[1]=m=>o(e).description=m),disabled:o(e).processing,rows:"4"},null,8,["modelValue","disabled"]),l(u,{class:"w-full mt-1",message:o(e).errors.description},null,8,["message"])]),t("div",null,[l(B,{class:"flex-1",label:o(r)("image"),error:o(e).errors.image,disabled:o(e).processing,modelValue:o(e).image,"onUpdate:modelValue":[s[2]||(s[2]=m=>o(e).image=m),s[3]||(s[3]=m=>o(e).image=m)]},null,8,["label","error","disabled","modelValue"])])]),t("div",q,[l(V,{class:"mr-3 text-sm",textContent:n(o(r)("cancel")),onClick:c},null,8,["textContent"]),l(k,{class:"text-sm overflow-hidden h-[34px]",onClick:h},{default:x(()=>[o(e).processing?(w(),f(j,{key:0,class:"mr-1"})):_("",!0),t("span",{class:"text-sm text-white",textContent:n(o(r)("save"))},null,8,z)]),_:1})])]),_:1},8,["show"])):_("",!0)}};export{Mo as default};
