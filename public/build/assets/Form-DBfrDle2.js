import{T as g,i as b}from"./@inertiajs-BhKdJayA.js";import{_ as $}from"./AppLayout-m_I9gnvX.js";import{_ as w}from"./RedButton-D21iPtqa.js";import{_ as V}from"./LoadingIcon-CesYxFkK.js";import{_ as u}from"./TextInput-C52bsWxF.js";import{_ as C}from"./TextAreaInput-y-SlU-FI.js";import{_ as n}from"./InputError-gQdwtcoE.js";import{_ as h}from"./FixedSelectionBox-CwNS68U7.js";import{_ as y}from"./ImageInput-DUQk7LRQ.js";import{i as v,k as d,o as c,S as p,a as l,l as a,P as i,a4 as t,K as F}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BWHXZF7Q.js";import"./PrimaryButton-DE9sqoJj.js";import"./SelectionBox-58uvzdoT.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-ccBf1-WH.js";import"./lodash-DBgjQQU6.js";import"./@headlessui-gOb5_P77.js";const k={class:"flex-1 flex items-center"},S=["textContent"],j=["textContent"],U={class:"max-w-7xl mx-auto py-6 px-6"},B={class:"bg-white rounded-md shadow flex flex-col overflow-auto px-5 pt-3 pb-4"},N={class:"flex flex-col pb-3"},D=["textContent"],O={class:"flex pb-3"},z={class:"flex flex-col flex-1 mr-3"},K=["textContent"],P={class:"flex flex-col flex-1 mx-3"},T=["textContent"],q={class:"flex flex-col"},A=["textContent"],ce={__name:"Form",props:{feature:Object,types:Object,action:String},setup(f){const m=f,x=v("$toast"),e=g(m.feature),_=()=>{if(e.processing)return!1;e.errors={},e.transform(s=>(typeof s.image=="string"&&delete s.image,s)).post(route("premiumFeature.store"),{forceFormData:!0,preserveScroll:!0,preserveState:!0,onSuccess:s=>{s.props.jetstream.flash.message&&x.success(s.props.jetstream.flash.message),m.action==="create"&&(window.location=route("premiumFeature.list"))}})};return(s,o)=>(c(),d($,{title:s.$t("premiumFeature."+m.action)},{header:p(()=>[l("div",k,[l("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:i(s.$t("premiumFeature."+m.action))},null,8,S),a(t(b),{class:"primary-button text-sm mr-3",href:s.route("premiumFeature.list"),textContent:i(s.$t("list"))},null,8,["href","textContent"]),a(w,{class:"normal-case",disabled:t(e).processing,onClick:_},{default:p(()=>[t(e).processing?(c(),d(V,{key:0,class:"mr-2"})):F("",!0),l("span",{class:"text-sm",textContent:i(s.$t("save"))},null,8,j)]),_:1},8,["disabled"])])]),default:p(()=>[l("div",U,[l("div",B,[l("div",N,[l("label",{textContent:i(s.$t("premiumFeature.name")),class:"w-full"},null,8,D),a(u,{class:"block w-full",type:"text",modelValue:t(e).name,"onUpdate:modelValue":o[0]||(o[0]=r=>t(e).name=r),disabled:t(e).processing},null,8,["modelValue","disabled"]),a(n,{class:"w-full mt-1",message:t(e).errors.name},null,8,["message"])]),l("div",O,[l("div",z,[l("label",{textContent:i(s.$t("premiumFeature.type")),class:"w-full mb-1"},null,8,K),a(h,{modelValue:t(e).type,"onUpdate:modelValue":o[1]||(o[1]=r=>t(e).type=r),placeholder:s.$t("premiumFeature.type"),disabled:m.action==="update"||t(e).processing,options:m.types},null,8,["modelValue","placeholder","disabled","options"]),a(n,{class:"w-full mt-1",message:t(e).errors.type},null,8,["message"])]),l("div",P,[l("label",{textContent:i(s.$t("premiumFeature.price")),class:"w-full mb-1"},null,8,T),a(u,{class:"block w-full",type:"text",modelValue:t(e).price,"onUpdate:modelValue":o[2]||(o[2]=r=>t(e).price=r),disabled:t(e).processing},null,8,["modelValue","disabled"]),a(n,{class:"w-full mt-1",message:t(e).errors.price},null,8,["message"])]),a(y,{class:"flex-1 ml-3",label:s.$t("image"),error:t(e).errors.image,disabled:t(e).processing,modelValue:t(e).image,"onUpdate:modelValue":[o[3]||(o[3]=r=>t(e).image=r),o[4]||(o[4]=r=>t(e).image=r)]},null,8,["label","error","disabled","modelValue"])]),l("div",q,[l("label",{textContent:i(s.$t("premiumFeature.description")),class:"w-full mb-1"},null,8,A),a(C,{class:"block w-full resize-none h-[150px]",type:"text",modelValue:t(e).description,"onUpdate:modelValue":o[5]||(o[5]=r=>t(e).description=r),disabled:t(e).processing},null,8,["modelValue","disabled"]),a(n,{class:"w-full mt-1",message:t(e).errors.description},null,8,["message"])])])])]),_:1},8,["title"]))}};export{ce as default};
