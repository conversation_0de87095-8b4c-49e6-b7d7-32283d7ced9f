import{T as g,i as b}from"./@inertiajs-Dt0-hqjZ.js";import{_ as $}from"./AppLayout-CTb2MMqd.js";import{_ as w}from"./RedButton-D21iPtqa.js";import{_ as V}from"./LoadingIcon-CLD0VpVl.js";import{_ as u}from"./TextInput-DUNPEFms.js";import{_ as C}from"./TextAreaInput-DHjed6qD.js";import{_ as n}from"./InputError-gQdwtcoE.js";import{_ as h}from"./FixedSelectionBox-Bk5LSyGJ.js";import{_ as y}from"./ImageInput-BV1wAASf.js";import{i as v,k as d,o as c,S as p,a as o,l as i,P as a,a4 as t,K as F}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";import"./PrimaryButton-DE9sqoJj.js";/* empty css                                                    */import"./SelectionBox-D4JR3fGi.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-CyTLADhX.js";import"./@headlessui-gOb5_P77.js";const k={class:"flex-1 flex items-center"},S=["textContent"],j=["textContent"],U={class:"max-w-7xl mx-auto py-6 px-6"},B={class:"bg-white rounded-md shadow flex flex-col overflow-auto px-5 pt-3 pb-4"},N={class:"flex flex-col pb-3"},D=["textContent"],O={class:"flex pb-3"},z={class:"flex flex-col flex-1 mr-3"},K=["textContent"],P={class:"flex flex-col flex-1 mx-3"},T=["textContent"],q={class:"flex flex-col"},A=["textContent"],Be={__name:"Form",props:{feature:Object,types:Object,action:String},setup(f){const m=f,x=v("$toast"),e=g(m.feature),_=()=>{if(e.processing)return!1;e.errors={},e.transform(s=>(typeof s.image=="string"&&delete s.image,s)).post(route("premiumFeature.store"),{forceFormData:!0,preserveScroll:!0,preserveState:!0,onSuccess:s=>{s.props.jetstream.flash.message&&x.success(s.props.jetstream.flash.message),m.action==="create"&&(window.location=route("premiumFeature.list"))}})};return(s,l)=>(c(),d($,{title:s.$t("premiumFeature."+m.action)},{header:p(()=>[o("div",k,[o("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:a(s.$t("premiumFeature."+m.action))},null,8,S),i(t(b),{class:"primary-button text-sm mr-3",href:s.route("premiumFeature.list"),textContent:a(s.$t("list"))},null,8,["href","textContent"]),i(w,{class:"normal-case",disabled:t(e).processing,onClick:_},{default:p(()=>[t(e).processing?(c(),d(V,{key:0,class:"mr-2"})):F("",!0),o("span",{class:"text-sm",textContent:a(s.$t("save"))},null,8,j)]),_:1},8,["disabled"])])]),default:p(()=>[o("div",U,[o("div",B,[o("div",N,[o("label",{textContent:a(s.$t("premiumFeature.name")),class:"w-full"},null,8,D),i(u,{class:"block w-full",type:"text",modelValue:t(e).name,"onUpdate:modelValue":l[0]||(l[0]=r=>t(e).name=r),disabled:t(e).processing},null,8,["modelValue","disabled"]),i(n,{class:"w-full mt-1",message:t(e).errors.name},null,8,["message"])]),o("div",O,[o("div",z,[o("label",{textContent:a(s.$t("premiumFeature.type")),class:"w-full mb-1"},null,8,K),i(h,{modelValue:t(e).type,"onUpdate:modelValue":l[1]||(l[1]=r=>t(e).type=r),placeholder:s.$t("premiumFeature.type"),disabled:m.action==="update"||t(e).processing,options:m.types},null,8,["modelValue","placeholder","disabled","options"]),i(n,{class:"w-full mt-1",message:t(e).errors.type},null,8,["message"])]),o("div",P,[o("label",{textContent:a(s.$t("premiumFeature.price")),class:"w-full mb-1"},null,8,T),i(u,{class:"block w-full",type:"text",modelValue:t(e).price,"onUpdate:modelValue":l[2]||(l[2]=r=>t(e).price=r),disabled:t(e).processing},null,8,["modelValue","disabled"]),i(n,{class:"w-full mt-1",message:t(e).errors.price},null,8,["message"])]),i(y,{class:"flex-1 ml-3",label:s.$t("image"),error:t(e).errors.image,disabled:t(e).processing,modelValue:t(e).image,"onUpdate:modelValue":[l[3]||(l[3]=r=>t(e).image=r),l[4]||(l[4]=r=>t(e).image=r)]},null,8,["label","error","disabled","modelValue"])]),o("div",q,[o("label",{textContent:a(s.$t("premiumFeature.description")),class:"w-full mb-1"},null,8,A),i(C,{class:"block w-full resize-none h-[150px]",type:"text",modelValue:t(e).description,"onUpdate:modelValue":l[5]||(l[5]=r=>t(e).description=r),disabled:t(e).processing},null,8,["modelValue","disabled"]),i(n,{class:"w-full mt-1",message:t(e).errors.description},null,8,["message"])])])])]),_:1},8,["title"]))}};export{Be as default};
