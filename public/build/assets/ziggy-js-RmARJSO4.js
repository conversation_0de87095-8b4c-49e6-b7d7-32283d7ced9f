import{l as y}from"./qs-CbAGxgEG.js";function o(){return o=Object.assign?Object.assign.bind():function(a){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)({}).hasOwnProperty.call(t,r)&&(a[r]=t[r])}return a},o.apply(null,arguments)}class f{constructor(e,t,r){var i,s;this.name=e,this.definition=t,this.bindings=(i=t.bindings)!=null?i:{},this.wheres=(s=t.wheres)!=null?s:{},this.config=r}get template(){const e=`${this.origin}/${this.definition.uri}`.replace(/\/+$/,"");return e===""?"/":e}get origin(){return this.config.absolute?this.definition.domain?`${this.config.url.match(/^\w+:\/\//)[0]}${this.definition.domain}${this.config.port?`:${this.config.port}`:""}`:this.config.url:""}get parameterSegments(){var e,t;return(e=(t=this.template.match(/{[^}?]+\??}/g))==null?void 0:t.map(r=>({name:r.replace(/{|\??}/g,""),required:!/\?}$/.test(r)})))!=null?e:[]}matchesUrl(e){var t;if(!this.definition.methods.includes("GET"))return!1;const r=this.template.replace(/[.*+$()[\]]/g,"\\$&").replace(/(\/?){([^}?]*)(\??)}/g,(u,p,c,d)=>{var h;const l=`(?<${c}>${((h=this.wheres[c])==null?void 0:h.replace(/(^\^)|(\$$)/g,""))||"[^/?]+"})`;return d?`(${p}${l})?`:`${p}${l}`}).replace(/^\w+:\/\//,""),[i,s]=e.replace(/^\w+:\/\//,"").split("?"),n=(t=new RegExp(`^${r}/?$`).exec(i))!=null?t:new RegExp(`^${r}/?$`).exec(decodeURI(i));if(n){for(const u in n.groups)n.groups[u]=typeof n.groups[u]=="string"?decodeURIComponent(n.groups[u]):n.groups[u];return{params:n.groups,query:y.parse(s)}}return!1}compile(e){return this.parameterSegments.length?this.template.replace(/{([^}?]+)(\??)}/g,(t,r,i)=>{var s,n;if(!i&&[null,void 0].includes(e[r]))throw new Error(`Ziggy error: '${r}' parameter is required for route '${this.name}'.`);if(this.wheres[r]&&!new RegExp(`^${i?`(${this.wheres[r]})?`:this.wheres[r]}$`).test((n=e[r])!=null?n:""))throw new Error(`Ziggy error: '${r}' parameter '${e[r]}' does not match required format '${this.wheres[r]}' for route '${this.name}'.`);return encodeURI((s=e[r])!=null?s:"").replace(/%7C/g,"|").replace(/%25/g,"%").replace(/\$/g,"%24")}).replace(this.config.absolute?/(\.[^/]+?)(\/\/)/:/(^)(\/\/)/,"$1/").replace(/\/+$/,""):this.template}}class w extends String{constructor(e,t,r=!0,i){if(super(),this.t=i??(typeof Ziggy<"u"?Ziggy:globalThis==null?void 0:globalThis.Ziggy),this.t=o({},this.t,{absolute:r}),e){if(!this.t.routes[e])throw new Error(`Ziggy error: route '${e}' is not in the route list.`);this.i=new f(e,this.t.routes[e],this.t),this.o=this.u(t)}}toString(){const e=Object.keys(this.o).filter(t=>!this.i.parameterSegments.some(({name:r})=>r===t)).filter(t=>t!=="_query").reduce((t,r)=>o({},t,{[r]:this.o[r]}),{});return this.i.compile(this.o)+y.stringify(o({},e,this.o._query),{addQueryPrefix:!0,arrayFormat:"indices",encodeValuesOnly:!0,skipNulls:!0,encoder:(t,r)=>typeof t=="boolean"?Number(t):r(t)})}h(e){e?this.t.absolute&&e.startsWith("/")&&(e=this.l().host+e):e=this.m();let t={};const[r,i]=Object.entries(this.t.routes).find(([s,n])=>t=new f(s,n,this.t).matchesUrl(e))||[void 0,void 0];return o({name:r},t,{route:i})}m(){const{host:e,pathname:t,search:r}=this.l();return(this.t.absolute?e+t:t.replace(this.t.url.replace(/^\w*:\/\/[^/]+/,""),"").replace(/^\/+/,"/"))+r}current(e,t){const{name:r,params:i,query:s,route:n}=this.h();if(!e)return r;const u=new RegExp(`^${e.replace(/\./g,"\\.").replace(/\*/g,".*")}$`).test(r);if([null,void 0].includes(t)||!u)return u;const p=new f(r,n,this.t);t=this.u(t,p);const c=o({},i,s);if(Object.values(t).every(h=>!h)&&!Object.values(c).some(h=>h!==void 0))return!0;const d=(h,l)=>Object.entries(h).every(([g,m])=>Array.isArray(m)&&Array.isArray(l[g])?m.every($=>l[g].includes($)):typeof m=="object"&&typeof l[g]=="object"&&m!==null&&l[g]!==null?d(m,l[g]):l[g]==m);return d(t,c)}l(){var e,t,r,i,s,n;const{host:u="",pathname:p="",search:c=""}=typeof window<"u"?window.location:{};return{host:(e=(t=this.t.location)==null?void 0:t.host)!=null?e:u,pathname:(r=(i=this.t.location)==null?void 0:i.pathname)!=null?r:p,search:(s=(n=this.t.location)==null?void 0:n.search)!=null?s:c}}get params(){const{params:e,query:t}=this.h();return o({},e,t)}get routeParams(){return this.h().params}get queryParams(){return this.h().query}has(e){return this.t.routes.hasOwnProperty(e)}u(e={},t=this.i){e!=null||(e={}),e=["string","number"].includes(typeof e)?[e]:e;const r=t.parameterSegments.filter(({name:i})=>!this.t.defaults[i]);return Array.isArray(e)?e=e.reduce((i,s,n)=>o({},i,r[n]?{[r[n].name]:s}:typeof s=="object"?s:{[s]:""}),{}):r.length!==1||e[r[0].name]||!e.hasOwnProperty(Object.values(t.bindings)[0])&&!e.hasOwnProperty("id")||(e={[r[0].name]:e}),o({},this.$(t),this.p(e,t))}$(e){return e.parameterSegments.filter(({name:t})=>this.t.defaults[t]).reduce((t,{name:r},i)=>o({},t,{[r]:this.t.defaults[r]}),{})}p(e,{bindings:t,parameterSegments:r}){return Object.entries(e).reduce((i,[s,n])=>{if(!n||typeof n!="object"||Array.isArray(n)||!r.some(({name:u})=>u===s))return o({},i,{[s]:n});if(!n.hasOwnProperty(t[s])){if(!n.hasOwnProperty("id"))throw new Error(`Ziggy error: object passed as '${s}' parameter is missing route model binding key '${t[s]}'.`);t[s]="id"}return o({},i,{[s]:n[t[s]]})},{})}valueOf(){return this.toString()}}function b(a,e,t,r){const i=new w(a,e,t,r);return a?i.toString():i}const O={install(a,e){const t=(r,i,s,n=e)=>b(r,i,s,n);parseInt(a.version)>2?(a.config.globalProperties.route=t,a.provide("route",t)):a.mixin({methods:{route:t}})}};export{O as o,b as s};
