import{u as w}from"./vue-i18n-kWKo0idO.js";import{f as g}from"./index-BxmPUm2h.js";import{i as h}from"./@inertiajs-Dt0-hqjZ.js";import{_ as y}from"./AppLayout-_qQ0AdHn.js";import{k as m,o as s,S as x,a as t,c as a,K as v,P as e,F as u,M as b,a4 as c,O as k}from"./@vue-BnW70ngI.js";import"./@intlify-xvnhHnag.js";import"./moment-C5S46NFB.js";/* empty css                                                    *//* empty css                                                                     */import"./app-EptGTPPo.js";import"./axios-t--hEgTQ.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./ziggy-js-C7EU8ifa.js";import"./pinia-Ddsh4R0D.js";import"./primevue-CrCPcMFN.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./izitoast-CYQMso0-.js";import"./deepmerge-CxfS31y9.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";import"./PrimaryButton-DE9sqoJj.js";const $=["textContent"],I={class:"max-w-7xl mx-auto py-6 px-6"},B={class:"bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden"},D={class:"p-datatable-table w-full w-border"},N={class:"text-left flex"},V=["textContent"],A={class:"text-left flex border-t"},L=["textContent"],T={class:"border-l border-l-gray-200 flex-1"},j={class:"bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden"},F={class:"p-datatable-table w-full"},O={class:"text-left flex"},P=["textContent"],S={class:"border-t flex"},q=["textContent"],E=["textContent"],K=["textContent"],M=["textContent"],z=["textContent"],G=["textContent"],qt={__name:"Detail",props:{user:Object},setup(l){const d=l,{t:f}=w();let n=[{label:"registerAt",attribute:"created_at"},{label:"userID",attribute:"user_id"},{label:"username",attribute:"name"},{label:"phone",attribute:"phone"},{label:"birthday",attribute:"birthday"},{label:"age",attribute:"age"},{label:"position",attribute:"position"},{label:"job",attribute:"profile_label"}];d.user.profile_type==="student"?n.push({label:"schoolName",attribute:"school_name"}):d.user.profile_type==="employee"&&(n.push({label:"work",attribute:"work"}),n.push({label:"expert",attribute:"expert"}),n.push({label:"service_in_charge",attribute:"service_in_charge"})),d.user.profile_type&&n.push({label:"brief",attribute:"brief"}),n=[...n,{label:"postCount",attribute:"post_count"},{label:"answerCount",attribute:"answer_count"},{label:"commentCount",attribute:"comment_count"},{label:"role",attribute:"role"},{label:"status",attribute:"status_label"},{label:"lastLoggedInTime",attribute:"last_logged_in_at"}];const _=(o,i)=>{const r=o[i.attribute];return["registerAt","lastLoggedInTime"].includes(i.label)?g(r):["role"].includes(i.label)?f(r):r};return(o,i)=>(s(),m(y,{title:o.$t("userInfo")},{header:x(()=>[t("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:e(o.$t("userInfo"))},null,8,$)]),default:x(()=>[t("div",I,[t("div",B,[t("table",D,[t("thead",null,[t("tr",N,[t("th",{colspan:"2",textContent:e(o.$t("generalInfo"))},null,8,V)])]),t("tbody",null,[(s(!0),a(u,null,b(c(n),r=>(s(),a("tr",A,[t("td",{class:"w-1/3",textContent:e(o.$t(r.label))},null,8,L),t("td",T,[r.label==="postCount"&&l.user.post_count>0?(s(),m(c(h),{key:0,class:"hover:text-red-600 hover:underline",textContent:e(l.user.post_count),href:o.route("post.list",{user:l.user.user_id})},null,8,["textContent","href"])):r.label==="answerCount"&&l.user.answer_count>0?(s(),m(c(h),{key:1,class:"hover:text-red-600 hover:underline",textContent:e(l.user.answer_count),href:o.route("postAnswer.list",{user:l.user.user_id})},null,8,["textContent","href"])):(s(),a(u,{key:2},[k(e(_(l.user,r)),1)],64))])]))),256))])])]),l.user.surveys.length>0?(s(!0),a(u,{key:0},b(l.user.surveys,r=>(s(),a("div",j,[t("table",F,[t("thead",null,[t("tr",O,[t("th",{colspan:"3",textContent:e(r.title)},null,8,P)])]),t("tbody",null,[t("tr",S,[t("th",{class:"flex-1",textContent:e(o.$t("question"))},null,8,q),t("th",{class:"w-[550px]",textContent:e(o.$t("answer"))},null,8,E),t("th",{class:"w-[220px]",textContent:e(o.$t("questionPublic"))},null,8,K)]),(s(!0),a(u,null,b(r.questions,(p,C)=>(s(),a("tr",{class:"border-t flex",key:C},[t("td",{class:"flex-1",textContent:e(p.content)},null,8,M),t("td",{class:"w-[550px]",textContent:e(p.answer)},null,8,z),t("td",{class:"w-[220px]",textContent:e(o.$t(p.public))},null,8,G)]))),128))])])]))),256)):v("",!0)])]),_:1},8,["title"]))}};export{qt as default};
