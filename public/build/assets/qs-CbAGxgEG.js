var K=String.prototype.replace,G=/%20/g,P={RFC1738:"RFC1738",RFC3986:"RFC3986"},T={default:P.RFC3986,formatters:{RFC1738:function(i){return K.call(i,G,"+")},RFC3986:function(i){return String(i)}},RFC1738:P.RFC1738,RFC3986:P.RFC3986},W=T,C=Object.prototype.hasOwnProperty,O=Array.isArray,b=function(){for(var i=[],e=0;e<256;++e)i.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return i}(),X=function(e){for(;e.length>1;){var r=e.pop(),n=r.obj[r.prop];if(O(n)){for(var f=[],l=0;l<n.length;++l)typeof n[l]<"u"&&f.push(n[l]);r.obj[r.prop]=f}}},M=function(e,r){for(var n=r&&r.plainObjects?Object.create(null):{},f=0;f<e.length;++f)typeof e[f]<"u"&&(n[f]=e[f]);return n},Y=function i(e,r,n){if(!r)return e;if(typeof r!="object"){if(O(e))e.push(r);else if(e&&typeof e=="object")(n&&(n.plainObjects||n.allowPrototypes)||!C.call(Object.prototype,r))&&(e[r]=!0);else return[e,r];return e}if(!e||typeof e!="object")return[e].concat(r);var f=e;return O(e)&&!O(r)&&(f=M(e,n)),O(e)&&O(r)?(r.forEach(function(l,t){if(C.call(e,t)){var o=e[t];o&&typeof o=="object"&&l&&typeof l=="object"?e[t]=i(o,l,n):e.push(l)}else e[t]=l}),e):Object.keys(r).reduce(function(l,t){var o=r[t];return C.call(l,t)?l[t]=i(l[t],o,n):l[t]=o,l},f)},Z=function(e,r){return Object.keys(r).reduce(function(n,f){return n[f]=r[f],n},e)},q=function(i,e,r){var n=i.replace(/\+/g," ");if(r==="iso-8859-1")return n.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(n)}catch{return n}},J=function(e,r,n,f,l){if(e.length===0)return e;var t=e;if(typeof e=="symbol"?t=Symbol.prototype.toString.call(e):typeof e!="string"&&(t=String(e)),n==="iso-8859-1")return escape(t).replace(/%u[0-9a-f]{4}/gi,function(c){return"%26%23"+parseInt(c.slice(2),16)+"%3B"});for(var o="",u=0;u<t.length;++u){var a=t.charCodeAt(u);if(a===45||a===46||a===95||a===126||a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||l===W.RFC1738&&(a===40||a===41)){o+=t.charAt(u);continue}if(a<128){o=o+b[a];continue}if(a<2048){o=o+(b[192|a>>6]+b[128|a&63]);continue}if(a<55296||a>=57344){o=o+(b[224|a>>12]+b[128|a>>6&63]+b[128|a&63]);continue}u+=1,a=65536+((a&1023)<<10|t.charCodeAt(u)&1023),o+=b[240|a>>18]+b[128|a>>12&63]+b[128|a>>6&63]+b[128|a&63]}return o},ee=function(e){for(var r=[{obj:{o:e},prop:"o"}],n=[],f=0;f<r.length;++f)for(var l=r[f],t=l.obj[l.prop],o=Object.keys(t),u=0;u<o.length;++u){var a=o[u],c=t[a];typeof c=="object"&&c!==null&&n.indexOf(c)===-1&&(r.push({obj:t,prop:a}),n.push(c))}return X(r),e},re=function(e){return Object.prototype.toString.call(e)==="[object RegExp]"},ne=function(e){return!e||typeof e!="object"?!1:!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},te=function(e,r){return[].concat(e,r)},ae=function(e,r){if(O(e)){for(var n=[],f=0;f<e.length;f+=1)n.push(r(e[f]));return n}return r(e)},V={arrayToObject:M,assign:Z,combine:te,compact:ee,decode:q,encode:J,isBuffer:ne,isRegExp:re,maybeMap:ae,merge:Y},A=V,S=T,ie=Object.prototype.hasOwnProperty,B={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,r){return e+"["+r+"]"},repeat:function(e){return e}},x=Array.isArray,fe=String.prototype.split,le=Array.prototype.push,_=function(i,e){le.apply(i,x(e)?e:[e])},ue=Date.prototype.toISOString,I=S.default,v={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:A.encode,encodeValuesOnly:!1,format:I,formatter:S.formatters[I],indices:!1,serializeDate:function(e){return ue.call(e)},skipNulls:!1,strictNullHandling:!1},oe=function(e){return typeof e=="string"||typeof e=="number"||typeof e=="boolean"||typeof e=="symbol"||typeof e=="bigint"},ce=function i(e,r,n,f,l,t,o,u,a,c,m,p,h,d){var s=e;if(typeof o=="function"?s=o(r,s):s instanceof Date?s=c(s):n==="comma"&&x(s)&&(s=A.maybeMap(s,function(D){return D instanceof Date?c(D):D})),s===null){if(f)return t&&!h?t(r,v.encoder,d,"key",m):r;s=""}if(oe(s)||A.isBuffer(s)){if(t){var $=h?r:t(r,v.encoder,d,"key",m);if(n==="comma"&&h){for(var L=fe.call(String(s),","),R="",N=0;N<L.length;++N)R+=(N===0?"":",")+p(t(L[N],v.encoder,d,"value",m));return[p($)+"="+R]}return[p($)+"="+p(t(s,v.encoder,d,"value",m))]}return[p(r)+"="+p(String(s))]}var F=[];if(typeof s>"u")return F;var w;if(n==="comma"&&x(s))w=[{value:s.length>0?s.join(",")||null:void 0}];else if(x(o))w=o;else{var H=Object.keys(s);w=u?H.sort(u):H}for(var E=0;E<w.length;++E){var g=w[E],z=typeof g=="object"&&typeof g.value<"u"?g.value:s[g];if(!(l&&z===null)){var k=x(s)?typeof n=="function"?n(r,g):r:r+(a?"."+g:"["+g+"]");_(F,i(z,k,n,f,l,t,o,u,a,c,m,p,h,d))}}return F},se=function(e){if(!e)return v;if(e.encoder!==null&&typeof e.encoder<"u"&&typeof e.encoder!="function")throw new TypeError("Encoder has to be a function.");var r=e.charset||v.charset;if(typeof e.charset<"u"&&e.charset!=="utf-8"&&e.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var n=S.default;if(typeof e.format<"u"){if(!ie.call(S.formatters,e.format))throw new TypeError("Unknown format option provided.");n=e.format}var f=S.formatters[n],l=v.filter;return(typeof e.filter=="function"||x(e.filter))&&(l=e.filter),{addQueryPrefix:typeof e.addQueryPrefix=="boolean"?e.addQueryPrefix:v.addQueryPrefix,allowDots:typeof e.allowDots>"u"?v.allowDots:!!e.allowDots,charset:r,charsetSentinel:typeof e.charsetSentinel=="boolean"?e.charsetSentinel:v.charsetSentinel,delimiter:typeof e.delimiter>"u"?v.delimiter:e.delimiter,encode:typeof e.encode=="boolean"?e.encode:v.encode,encoder:typeof e.encoder=="function"?e.encoder:v.encoder,encodeValuesOnly:typeof e.encodeValuesOnly=="boolean"?e.encodeValuesOnly:v.encodeValuesOnly,filter:l,format:n,formatter:f,serializeDate:typeof e.serializeDate=="function"?e.serializeDate:v.serializeDate,skipNulls:typeof e.skipNulls=="boolean"?e.skipNulls:v.skipNulls,sort:typeof e.sort=="function"?e.sort:null,strictNullHandling:typeof e.strictNullHandling=="boolean"?e.strictNullHandling:v.strictNullHandling}},de=function(i,e){var r=i,n=se(e),f,l;typeof n.filter=="function"?(l=n.filter,r=l("",r)):x(n.filter)&&(l=n.filter,f=l);var t=[];if(typeof r!="object"||r===null)return"";var o;e&&e.arrayFormat in B?o=e.arrayFormat:e&&"indices"in e?o=e.indices?"indices":"repeat":o="indices";var u=B[o];f||(f=Object.keys(r)),n.sort&&f.sort(n.sort);for(var a=0;a<f.length;++a){var c=f[a];n.skipNulls&&r[c]===null||_(t,ce(r[c],c,u,n.strictNullHandling,n.skipNulls,n.encode?n.encoder:null,n.filter,n.sort,n.allowDots,n.serializeDate,n.format,n.formatter,n.encodeValuesOnly,n.charset))}var m=t.join(n.delimiter),p=n.addQueryPrefix===!0?"?":"";return n.charsetSentinel&&(n.charset==="iso-8859-1"?p+="utf8=%26%2310003%3B&":p+="utf8=%E2%9C%93&"),m.length>0?p+m:""},j=V,Q=Object.prototype.hasOwnProperty,ye=Array.isArray,y={allowDots:!1,allowPrototypes:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:j.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},ve=function(i){return i.replace(/&#(\d+);/g,function(e,r){return String.fromCharCode(parseInt(r,10))})},U=function(i,e){return i&&typeof i=="string"&&e.comma&&i.indexOf(",")>-1?i.split(","):i},pe="utf8=%26%2310003%3B",me="utf8=%E2%9C%93",he=function(e,r){var n={},f=r.ignoreQueryPrefix?e.replace(/^\?/,""):e,l=r.parameterLimit===1/0?void 0:r.parameterLimit,t=f.split(r.delimiter,l),o=-1,u,a=r.charset;if(r.charsetSentinel)for(u=0;u<t.length;++u)t[u].indexOf("utf8=")===0&&(t[u]===me?a="utf-8":t[u]===pe&&(a="iso-8859-1"),o=u,u=t.length);for(u=0;u<t.length;++u)if(u!==o){var c=t[u],m=c.indexOf("]="),p=m===-1?c.indexOf("="):m+1,h,d;p===-1?(h=r.decoder(c,y.decoder,a,"key"),d=r.strictNullHandling?null:""):(h=r.decoder(c.slice(0,p),y.decoder,a,"key"),d=j.maybeMap(U(c.slice(p+1),r),function(s){return r.decoder(s,y.decoder,a,"value")})),d&&r.interpretNumericEntities&&a==="iso-8859-1"&&(d=ve(d)),c.indexOf("[]=")>-1&&(d=ye(d)?[d]:d),Q.call(n,h)?n[h]=j.combine(n[h],d):n[h]=d}return n},be=function(i,e,r,n){for(var f=n?e:U(e,r),l=i.length-1;l>=0;--l){var t,o=i[l];if(o==="[]"&&r.parseArrays)t=[].concat(f);else{t=r.plainObjects?Object.create(null):{};var u=o.charAt(0)==="["&&o.charAt(o.length-1)==="]"?o.slice(1,-1):o,a=parseInt(u,10);!r.parseArrays&&u===""?t={0:f}:!isNaN(a)&&o!==u&&String(a)===u&&a>=0&&r.parseArrays&&a<=r.arrayLimit?(t=[],t[a]=f):u!=="__proto__"&&(t[u]=f)}f=t}return f},ge=function(e,r,n,f){if(e){var l=n.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,t=/(\[[^[\]]*])/,o=/(\[[^[\]]*])/g,u=n.depth>0&&t.exec(l),a=u?l.slice(0,u.index):l,c=[];if(a){if(!n.plainObjects&&Q.call(Object.prototype,a)&&!n.allowPrototypes)return;c.push(a)}for(var m=0;n.depth>0&&(u=o.exec(l))!==null&&m<n.depth;){if(m+=1,!n.plainObjects&&Q.call(Object.prototype,u[1].slice(1,-1))&&!n.allowPrototypes)return;c.push(u[1])}return u&&c.push("["+l.slice(u.index)+"]"),be(c,r,n,f)}},Oe=function(e){if(!e)return y;if(e.decoder!==null&&e.decoder!==void 0&&typeof e.decoder!="function")throw new TypeError("Decoder has to be a function.");if(typeof e.charset<"u"&&e.charset!=="utf-8"&&e.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=typeof e.charset>"u"?y.charset:e.charset;return{allowDots:typeof e.allowDots>"u"?y.allowDots:!!e.allowDots,allowPrototypes:typeof e.allowPrototypes=="boolean"?e.allowPrototypes:y.allowPrototypes,arrayLimit:typeof e.arrayLimit=="number"?e.arrayLimit:y.arrayLimit,charset:r,charsetSentinel:typeof e.charsetSentinel=="boolean"?e.charsetSentinel:y.charsetSentinel,comma:typeof e.comma=="boolean"?e.comma:y.comma,decoder:typeof e.decoder=="function"?e.decoder:y.decoder,delimiter:typeof e.delimiter=="string"||j.isRegExp(e.delimiter)?e.delimiter:y.delimiter,depth:typeof e.depth=="number"||e.depth===!1?+e.depth:y.depth,ignoreQueryPrefix:e.ignoreQueryPrefix===!0,interpretNumericEntities:typeof e.interpretNumericEntities=="boolean"?e.interpretNumericEntities:y.interpretNumericEntities,parameterLimit:typeof e.parameterLimit=="number"?e.parameterLimit:y.parameterLimit,parseArrays:e.parseArrays!==!1,plainObjects:typeof e.plainObjects=="boolean"?e.plainObjects:y.plainObjects,strictNullHandling:typeof e.strictNullHandling=="boolean"?e.strictNullHandling:y.strictNullHandling}},xe=function(i,e){var r=Oe(e);if(i===""||i===null||typeof i>"u")return r.plainObjects?Object.create(null):{};for(var n=typeof i=="string"?he(i,r):i,f=r.plainObjects?Object.create(null):{},l=Object.keys(n),t=0;t<l.length;++t){var o=l[t],u=ge(o,n[o],r,typeof i=="string");f=j.merge(f,u,r)}return j.compact(f)},je=de,we=xe,Se=T,Ne={formats:Se,parse:we,stringify:je};export{Ne as l};
