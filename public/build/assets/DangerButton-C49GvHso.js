import{c as r,o as n,J as o}from"./@vue-BnW70ngI.js";const s=["type"],c={__name:"DangerButton",props:{type:{type:String,default:"button"}},setup(e){return(t,i)=>(n(),r("button",{type:e.type,class:"inline-flex items-center justify-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150"},[o(t.$slots,"default")],8,s))}};export{c as _};
