import{b as t,c as n,o as s,Y as r}from"./@vue-BnW70ngI.js";const a=16,i="#ffffff",p={__name:"LoadingIcon",props:{size:{type:Number,default:null},color:{type:String,default:null}},setup(c){const o=c,l=t(()=>{const e={};return o.size&&o.size!==a&&(e["--icon-size"]=`${o.size}px`),o.color&&o.color!==i&&(e["--background-color"]=`${o.color}`),e});return(e,u)=>(s(),n("i",{class:"loading-icon",style:r(l.value)},null,4))}};export{p as _};
