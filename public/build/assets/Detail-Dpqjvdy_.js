import{u as w}from"./vue-i18n-DNS8h1FH.js";import{f as g}from"./index-DHV2tfOS.js";import{i as f}from"./@inertiajs-BhKdJayA.js";import{_ as y}from"./AppLayout-m_I9gnvX.js";import{k as c,o as n,S as _,a as t,c as a,K as v,P as e,F as u,M as h,a4 as x,O as k}from"./@vue-BnW70ngI.js";import"./@intlify-TnaUIxGf.js";import"./moment-C5S46NFB.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BWHXZF7Q.js";import"./PrimaryButton-DE9sqoJj.js";const $=["textContent"],I={class:"max-w-7xl mx-auto py-6 px-6"},B={class:"bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden"},D={class:"p-datatable-table w-full w-border"},N={class:"text-left flex"},V=["textContent"],A={class:"text-left flex border-t"},L=["textContent"],T={class:"border-l border-l-gray-200 flex-1"},j={class:"bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden"},F={class:"p-datatable-table w-full"},O={class:"text-left flex"},P=["textContent"],S={class:"border-t flex"},q=["textContent"],E=["textContent"],K=["textContent"],M=["textContent"],z=["textContent"],G=["textContent"],at={__name:"Detail",props:{user:Object},setup(o){const d=o,{t:p}=w();let r=[{label:"registerAt",attribute:"created_at"},{label:"userID",attribute:"user_id"},{label:"username",attribute:"name"},{label:"phone",attribute:"phone"},{label:"birthday",attribute:"birthday"},{label:"age",attribute:"age"},{label:"position",attribute:"position"},{label:"job",attribute:"profile_label"}];d.user.profile_type==="student"?r.push({label:"schoolName",attribute:"school_name"}):d.user.profile_type==="employee"&&(r.push({label:"work",attribute:"work"}),r.push({label:"expert",attribute:"expert"}),r.push({label:"service_in_charge",attribute:"service_in_charge"})),d.user.profile_type&&r.push({label:"brief",attribute:"brief"}),r=[...r,{label:"postCount",attribute:"post_count"},{label:"answerCount",attribute:"answer_count"},{label:"commentCount",attribute:"comment_count"},{label:"role",attribute:"role"},{label:"status",attribute:"status_label"},{label:"lastLoggedInTime",attribute:"last_logged_in_at"}];const m=(l,i)=>{const s=l[i.attribute];return["registerAt","lastLoggedInTime"].includes(i.label)?g(s):["role"].includes(i.label)?p(s):s};return(l,i)=>(n(),c(y,{title:l.$t("userInfo")},{header:_(()=>[t("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:e(l.$t("userInfo"))},null,8,$)]),default:_(()=>[t("div",I,[t("div",B,[t("table",D,[t("thead",null,[t("tr",N,[t("th",{colspan:"2",textContent:e(l.$t("generalInfo"))},null,8,V)])]),t("tbody",null,[(n(!0),a(u,null,h(x(r),s=>(n(),a("tr",A,[t("td",{class:"w-1/3",textContent:e(l.$t(s.label))},null,8,L),t("td",T,[s.label==="postCount"&&o.user.post_count>0?(n(),c(x(f),{key:0,class:"hover:text-red-600 hover:underline",textContent:e(o.user.post_count),href:l.route("post.list",{user:o.user.user_id})},null,8,["textContent","href"])):s.label==="answerCount"&&o.user.answer_count>0?(n(),c(x(f),{key:1,class:"hover:text-red-600 hover:underline",textContent:e(o.user.answer_count),href:l.route("postAnswer.list",{user:o.user.user_id})},null,8,["textContent","href"])):(n(),a(u,{key:2},[k(e(m(o.user,s)),1)],64))])]))),256))])])]),o.user.surveys.length>0?(n(!0),a(u,{key:0},h(o.user.surveys,s=>(n(),a("div",j,[t("table",F,[t("thead",null,[t("tr",O,[t("th",{colspan:"3",textContent:e(s.title)},null,8,P)])]),t("tbody",null,[t("tr",S,[t("th",{class:"flex-1",textContent:e(l.$t("question"))},null,8,q),t("th",{class:"w-[550px]",textContent:e(l.$t("answer"))},null,8,E),t("th",{class:"w-[220px]",textContent:e(l.$t("questionPublic"))},null,8,K)]),(n(!0),a(u,null,h(s.questions,(b,C)=>(n(),a("tr",{class:"border-t flex",key:C},[t("td",{class:"flex-1",textContent:e(b.content)},null,8,M),t("td",{class:"w-[550px]",textContent:e(b.answer)},null,8,z),t("td",{class:"w-[220px]",textContent:e(l.$t(b.public))},null,8,G)]))),128))])])]))),256)):v("",!0)])]),_:1},8,["title"]))}};export{at as default};
