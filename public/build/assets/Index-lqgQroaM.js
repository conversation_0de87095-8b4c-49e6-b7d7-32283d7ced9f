import{u as z}from"./vue-i18n-kWKo0idO.js";import{f as K,c as G}from"./index-k9QJQake.js";import{Q as J,T as W,i as y,N as B}from"./@inertiajs-Dt0-hqjZ.js";import{s as X,a as m}from"./primevue-CrCPcMFN.js";import{_ as Y}from"./AppLayout-CTb2MMqd.js";import{_ as w}from"./InputLabel-BTXevqr4.js";import{_ as V}from"./SearchInput-CdoSYJL3.js";import{_ as Z}from"./Pagination-D56Hn3as.js";import{_ as I}from"./LoadingIcon-CLD0VpVl.js";import{_ as M,a as P}from"./SecondaryButton-BoI1NwE9.js";import{_ as U}from"./RedButton-D21iPtqa.js";import{_ as k}from"./FixedSelectionBox-Bk5LSyGJ.js";import{s as h}from"./ziggy-js-C7EU8ifa.js";import{_ as ee}from"./GridContainer-BC3u-41x.js";import{i as te,v as se,b as oe,c as g,o as d,l as r,k as v,K as b,S as i,a as n,a4 as o,P as u,O as S,F as T,R as re}from"./@vue-BnW70ngI.js";import"./@intlify-xvnhHnag.js";import"./moment-C5S46NFB.js";/* empty css                                                    *//* empty css                                                                     */import"./app-65VXU7yX.js";import"./axios-t--hEgTQ.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./pinia-Ddsh4R0D.js";import"./@primevue-BllOwQ3c.js";import"./@primeuix-CKSY3gPt.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./izitoast-CYQMso0-.js";import"./deepmerge-CxfS31y9.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./PrimaryButton-DE9sqoJj.js";import"./SelectionBox-D4JR3fGi.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-CyTLADhX.js";import"./TextInput-DUNPEFms.js";import"./@headlessui-gOb5_P77.js";const le={class:"flex-1 flex items-center"},ae=["textContent"],ne={class:"p-6 sm:mx-2"},ie={class:"flex items-stretch"},ue={class:"mt-0 w-80 mr-8"},ce={class:"mt-0 w-80 mr-8"},de={class:"mt-0 w-64 mr-8"},pe={class:"mt-0 w-64 mr-8"},me={class:"mt-0 w-64 mr-8"},he=["innerHTML"],fe=["onClick"],ye=["onClick"],ve={key:3,class:"w-4"},be=["textContent"],Ce=["textContent"],we={class:"flex items-center justify-end px-3 py-3"},ge=["textContent"],$e=["textContent"],xe=["textContent"],_e={class:"flex items-center justify-end px-3 py-3"},ke=["textContent"],Pt={__name:"Index",props:{filters:Object,posts:Object},setup(c){const F=te("$toast"),$=J(),{t:C}=z(),f=c,l=W({search:f.filters.search??"",user:f.filters.user??"",reported:f.filters.reported??"",type:f.filters.type??"",topics:f.filters.topics??"",limit:f.filters.limit??10}),e=se({showSearchClearButton:(f.filters.search??"").trim().length>0,showUserSearchClearButton:(f.filters.user??"").trim().length>0,searching:!1,showModal:!1,open:!1,deleteId:null,deleting:!1,selectOptions:[{value:"yes",label:C("yes")},{value:"no",label:C("no")}],typeOptions:[{value:"answerr_q",label:C("answerrQ")},{value:"answerr_topic",label:C("answerrTopic")},{value:"other",label:C("other")}],showFeatureModal:!1,openFeature:!1,featureId:null,processing:!1,currentFeatureState:0,activePage:null,busy:oe(()=>e.activePage!==null||e.searching||l.processing)}),p=()=>{if(l.processing)return!1;l.transform(t=>G(t)).get(h("post.list"),{preserveScroll:!0,onSuccess:()=>{}})},j=()=>{e.showSearchClearButton=!0,p()},D=()=>{e.showUserSearchClearButton=!0,p()},O=()=>{l.search="",e.showSearchClearButton=!1,p()},N=()=>{l.user="",e.showUserSearchClearButton=!1,p()},E=t=>{e.deleteId=t,e.showModal=!0,setTimeout(()=>e.open=!0,150)},x=async()=>{e.open=!1,setTimeout(()=>e.showModal=!1,150)},_=async()=>{e.openFeature=!1,setTimeout(()=>e.showFeatureModal=!1,150)},L=()=>{e.deleting||B.post(h("post.delete"),{id:e.deleteId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{e.deleting=!0},onSuccess:()=>{e.deleteId=null,$.props.jetstream.flash.message&&F.success($.props.jetstream.flash.message),x()},onFinish:()=>{e.deleting=!1}})},q=(t,a)=>{e.featureId=t,e.showFeatureModal=!0,e.currentFeatureState=a,setTimeout(()=>e.openFeature=!0,150)},A=()=>{e.processing||B.post(h("post.toggleFeature"),{id:e.featureId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{e.processing=!0},onSuccess:()=>{e.featureId=null,$.props.jetstream.flash.message&&F.success($.props.jetstream.flash.message),_()},onFinish:()=>{e.processing=!1}})},H=t=>{e.activePage=t,e.searching=!0},Q=t=>{l.limit=t,l.search="",l.user="",l.reported="",l.type="",l.topics="",p()};return(t,a)=>(d(),g(T,null,[r(Y,{title:t.$t("listPost")},{header:i(()=>[n("div",le,[n("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:u(t.$t("listPost"))},null,8,ae),r(o(y),{class:"primary-button text-sm",href:o(h)("post.form"),textContent:u(t.$t("addNew"))},null,8,["href","textContent"])])]),default:i(()=>[n("div",ne,[n("div",ie,[n("div",ue,[r(w,{for:"search",value:t.$t("search")},null,8,["value"]),r(V,{id:"search",class:"mt-1 block w-full",modelValue:o(l).search,"onUpdate:modelValue":a[0]||(a[0]=s=>o(l).search=s),placeholder:t.$t("postSearch"),disabled:e.busy,"show-clear-button":e.showSearchClearButton,onInput:a[1]||(a[1]=s=>e.showSearchClearButton=!1),onClearSearch:O,onEnter:j},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),n("div",ce,[r(w,{for:"search-user",value:t.$t("createdBy")},null,8,["value"]),r(V,{id:"search-user",class:"mt-1 block w-full",modelValue:o(l).user,"onUpdate:modelValue":a[2]||(a[2]=s=>o(l).user=s),placeholder:t.$t("postUserSearch"),disabled:e.busy,"show-clear-button":e.showUserSearchClearButton,onInput:a[3]||(a[3]=s=>e.showUserSearchClearButton=!1),onClearSearch:N,onEnter:D},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),n("div",de,[r(w,{for:"type-search",value:t.$t("postType"),class:"mb-1"},null,8,["value"]),r(k,{modelValue:o(l).type,"onUpdate:modelValue":a[4]||(a[4]=s=>o(l).type=s),placeholder:t.$t("all"),disabled:e.busy,options:e.typeOptions,clearable:!0,onCleared:p,onSelected:p},null,8,["modelValue","placeholder","disabled","options"])]),n("div",pe,[r(w,{for:"report-search",value:t.$t("reportStatus"),class:"mb-1"},null,8,["value"]),r(k,{modelValue:o(l).reported,"onUpdate:modelValue":a[5]||(a[5]=s=>o(l).reported=s),placeholder:t.$t("all"),disabled:e.busy,options:e.selectOptions,clearable:!0,onCleared:p,onSelected:p},null,8,["modelValue","placeholder","disabled","options"])]),n("div",me,[r(w,{for:"topics-search",value:t.$t("TOPICS"),class:"mb-1"},null,8,["value"]),r(k,{modelValue:o(l).topics,"onUpdate:modelValue":a[6]||(a[6]=s=>o(l).topics=s),placeholder:t.$t("all"),disabled:e.busy,options:e.selectOptions,clearable:!0,onCleared:p,onSelected:p},null,8,["modelValue","placeholder","disabled","options"])])]),r(ee,{loading:e.busy},{default:i(()=>[r(o(X),{value:c.posts.data},{empty:i(()=>[S(u(t.$t(c.filters.search&&c.filters.search!==""||c.filters.user&&c.filters.user!==""?"emptyResult":"emptyData")),1)]),default:i(()=>[r(o(m),{class:"number-column",header:t.$t("ID")},{body:i(({data:s})=>[r(o(y),{class:"hover:text-sky-600 hover:underline",textContent:u(s.post_id),href:o(h)("post.detail",{post:s.post_id})},null,8,["textContent","href"])]),_:1},8,["header"]),r(o(m),{class:"post-username-column",header:t.$t("createdBy")},{body:i(({data:s})=>[r(o(y),{class:"hover:text-sky-600 hover:underline",textContent:u(s.username),href:o(h)("post.list",{user:s.user_id})},null,8,["textContent","href"])]),_:1},8,["header"]),r(o(m),{class:"number-column extra",header:t.$t("createdByID")},{body:i(({data:s})=>[r(o(y),{class:"hover:text-sky-600 hover:underline",textContent:u(s.user_id),href:o(h)("post.list",{user:s.user_id})},null,8,["textContent","href"])]),_:1},8,["header"]),r(o(m),{class:"title-flex-column",header:t.$t("postContent")},{body:i(({data:s})=>[n("div",{innerHTML:s.content},null,8,he)]),_:1},8,["header"]),r(o(m),{class:"post-type-column",field:"tag",header:t.$t("postType")},null,8,["header"]),r(o(m),{class:"w-56",field:"community",header:t.$t("community")},null,8,["header"]),r(o(m),{class:"time-column",header:t.$t("postCreatedAt")},{body:i(({data:s})=>[S(u(o(K)(s.created_at)),1)]),_:1},8,["header"]),r(o(m),{class:"count-column",header:t.$t("answerCount")},{body:i(({data:s})=>[s.answer_count>0?(d(),v(o(y),{key:0,class:"hover:text-red-600 hover:underline",textContent:u(s.answer_count),href:o(h)("postAnswer.list",{post:s.post_id})},null,8,["textContent","href"])):(d(),g(T,{key:1},[S(u(s.answer_count),1)],64))]),_:1},8,["header"]),r(o(m),{class:"count-column",field:"report_count",header:t.$t("reportCount")},null,8,["header"]),r(o(m),{class:"status-column",field:"status_label",header:t.$t("status")},null,8,["header"]),r(o(m),{class:"action-column extra"},{body:i(({data:s})=>[r(o(y),{href:o(h)("post.detail",{post:s.post_id})},{default:i(()=>a[7]||(a[7]=[n("i",{class:"pi pi-info-circle text-gray-500 hover:text-sky-600"},null,-1)])),_:2},1032,["href"]),parseInt(s.status)!==0?(d(),g("i",{key:0,class:"pi pi-trash text-red-400 hover:text-red-600 hover:cursor-pointer",onClick:R=>E(s.post_id)},null,8,fe)):b("",!0),s.status===1?(d(),g("i",{key:1,class:re(["pi pi-flag hover:text-lime-700 hover:cursor-pointer",[s.featured===1?"text-lime-700":"text-gray-400"]]),onClick:R=>q(s.post_id,s.featured)},null,10,ye)):b("",!0),s.status===1&&(s.type==="answerr_topic"||s.type==="answerr_q")?(d(),v(o(y),{key:2,href:o(h)("post.form",{post:s.post_id})},{default:i(()=>a[8]||(a[8]=[n("i",{class:"pi pi-pen-to-square text-sky-400 hover:text-sky-600 hover:cursor-pointer"},null,-1)])),_:2},1032,["href"])):(d(),g("i",ve))]),_:1})]),_:1},8,["value"])]),_:1},8,["loading"]),c.posts.data.length>0?(d(),v(Z,{key:0,class:"mt-5 flex items-center justify-center mx-auto",links:c.posts.links,active:e.activePage,disabled:e.busy,limit:c.posts.per_page,total:c.posts.total,from:c.posts.from,to:c.posts.to,onProgress:H,onChangeLimit:Q},null,8,["links","active","disabled","limit","total","from","to"])):b("",!0)])]),_:1},8,["title"]),e.showModal?(d(),v(P,{key:0,show:e.open,onClose:x},{default:i(()=>[n("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:u(t.$t("confirm"))},null,8,be),n("div",{class:"border-t border-b p-4",textContent:u(t.$t("confirmDeletePostMessage"))},null,8,Ce),n("div",we,[r(M,{class:"mr-3 text-sm",textContent:u(t.$t("cancel")),onClick:x},null,8,["textContent"]),r(U,{class:"text-sm overflow-hidden h-[34px]",onClick:L},{default:i(()=>[e.deleting?(d(),v(I,{key:0,class:"mr-1"})):b("",!0),n("span",{class:"text-sm text-white",textContent:u(t.$t("delete"))},null,8,ge)]),_:1})])]),_:1},8,["show"])):b("",!0),e.showFeatureModal?(d(),v(P,{key:1,show:e.openFeature,onClose:_},{default:i(()=>[n("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:u(t.$t("confirm"))},null,8,$e),n("div",{class:"border-t border-b p-4",textContent:u(t.$t("confirm"+(e.currentFeatureState===0?"EnableFeature":"DisableFeature")+"Message"))},null,8,xe),n("div",_e,[r(M,{class:"mr-3 text-sm",textContent:u(t.$t("cancel")),onClick:_},null,8,["textContent"]),r(U,{class:"text-sm overflow-hidden h-[34px]",onClick:A},{default:i(()=>[e.processing?(d(),v(I,{key:0,class:"mr-1"})):b("",!0),n("span",{class:"text-sm text-white",textContent:u(t.$t("confirm"))},null,8,ke)]),_:1})])]),_:1},8,["show"])):b("",!0)],64))}};export{Pt as default};
