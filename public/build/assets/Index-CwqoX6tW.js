import{u as Q}from"./vue-i18n-DNS8h1FH.js";import{f as q,c as G}from"./index-DHV2tfOS.js";import{Q as J,T as W,i as b,N as F}from"./@inertiajs-BhKdJayA.js";import{s as X,a as d}from"./primevue-u0EmObz-.js";import{_ as Y}from"./AppLayout-m_I9gnvX.js";import{_ as y}from"./InputLabel-BTXevqr4.js";import{_ as B}from"./SearchInput-CdoSYJL3.js";import{_ as Z}from"./Pagination-DDsmbrzN.js";import{_ as I}from"./LoadingIcon-CesYxFkK.js";import{_ as V,a as M}from"./SecondaryButton-BWHXZF7Q.js";import{_ as P}from"./RedButton-D21iPtqa.js";import{_ as U}from"./FixedSelectionBox-CwNS68U7.js";import{s as h}from"./ziggy-js-RmARJSO4.js";import{_ as ee}from"./GridContainer-n7ZDMxOZ.js";import{i as te,v as se,b as oe,c as $,o as m,l as r,k as v,K as C,S as a,a as n,a4 as o,P as u,O as _,F as j,R as re}from"./@vue-BnW70ngI.js";import"./@intlify-TnaUIxGf.js";import"./moment-C5S46NFB.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./@primeuix-CNwdBq9K.js";import"./@primevue-Bw51iWDD.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./PrimaryButton-DE9sqoJj.js";import"./SelectionBox-58uvzdoT.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-ccBf1-WH.js";import"./lodash-DBgjQQU6.js";import"./TextInput-C52bsWxF.js";import"./@headlessui-gOb5_P77.js";const le=["textContent"],ae={class:"p-6 sm:mx-2"},ne={class:"flex items-stretch"},ie={class:"mt-0 w-80 mr-8"},ue={class:"mt-0 w-96 mr-8"},ce={class:"mt-0 w-64 mr-8"},de={class:"mt-0 w-64 mr-8"},me=["innerHTML"],pe=["onClick"],he=["onClick"],fe=["textContent"],Ce=["textContent"],ve={class:"flex items-center justify-end px-3 py-3"},be=["textContent"],ge=["textContent"],ye=["textContent"],$e={class:"flex items-center justify-end px-3 py-3"},we=["textContent"],st={__name:"Index",props:{filters:Object,posts:Object},setup(c){const S=te("$toast"),g=J(),{t:k}=Q(),f=c,l=W({search:f.filters.search??"",user:f.filters.user??"",reported:f.filters.reported??"",topics:f.filters.topics??"",limit:f.filters.limit??10}),e=se({showSearchClearButton:(f.filters.search??"").trim().length>0,showUserSearchClearButton:(f.filters.user??"").trim().length>0,searching:!1,showModal:!1,open:!1,deleteId:null,deleting:!1,selectOptions:[{value:"yes",label:k("yes")},{value:"no",label:k("no")}],showFeatureModal:!1,openFeature:!1,featureId:null,processing:!1,currentFeatureState:0,activePage:null,busy:oe(()=>e.activePage!==null||e.searching||l.processing)}),p=()=>{if(l.processing)return!1;l.transform(t=>G(t)).get(h("post.list"),{preserveScroll:!0,onSuccess:()=>{}})},T=()=>{e.showSearchClearButton=!0,p()},D=()=>{e.showUserSearchClearButton=!0,p()},O=()=>{l.search="",e.showSearchClearButton=!1,p()},N=()=>{l.user="",e.showUserSearchClearButton=!1,p()},E=t=>{e.deleteId=t,e.showModal=!0,setTimeout(()=>e.open=!0,150)},w=async()=>{e.open=!1,setTimeout(()=>e.showModal=!1,150)},x=async()=>{e.openFeature=!1,setTimeout(()=>e.showFeatureModal=!1,150)},L=()=>{e.deleting||F.post(h("post.delete"),{id:e.deleteId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{e.deleting=!0},onSuccess:()=>{e.deleteId=null,g.props.jetstream.flash.message&&S.success(g.props.jetstream.flash.message),w()},onFinish:()=>{e.deleting=!1}})},A=(t,i)=>{e.featureId=t,e.showFeatureModal=!0,e.currentFeatureState=i,setTimeout(()=>e.openFeature=!0,150)},H=()=>{e.processing||F.post(h("post.toggleFeature"),{id:e.featureId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{e.processing=!0},onSuccess:()=>{e.featureId=null,g.props.jetstream.flash.message&&S.success(g.props.jetstream.flash.message),x()},onFinish:()=>{e.processing=!1}})},R=t=>{e.activePage=t,e.searching=!0},z=t=>{l.limit=t,l.search="",l.user="",l.reported="",l.topics="",p()};return(t,i)=>(m(),$(j,null,[r(Y,{title:t.$t("listPost")},{header:a(()=>[n("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:u(t.$t("listPost"))},null,8,le)]),default:a(()=>[n("div",ae,[n("div",ne,[n("div",ie,[r(y,{for:"search",value:t.$t("search")},null,8,["value"]),r(B,{id:"search",class:"mt-1 block w-full",modelValue:o(l).search,"onUpdate:modelValue":i[0]||(i[0]=s=>o(l).search=s),placeholder:t.$t("postSearch"),disabled:e.busy,"show-clear-button":e.showSearchClearButton,onInput:i[1]||(i[1]=s=>e.showSearchClearButton=!1),onClearSearch:O,onEnter:T},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),n("div",ue,[r(y,{for:"search-user",value:t.$t("createdBy")},null,8,["value"]),r(B,{id:"search-user",class:"mt-1 block w-full",modelValue:o(l).user,"onUpdate:modelValue":i[2]||(i[2]=s=>o(l).user=s),placeholder:t.$t("postUserSearch"),disabled:e.busy,"show-clear-button":e.showUserSearchClearButton,onInput:i[3]||(i[3]=s=>e.showUserSearchClearButton=!1),onClearSearch:N,onEnter:D},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),n("div",ce,[r(y,{for:"report-search",value:t.$t("reportStatus"),class:"mb-1"},null,8,["value"]),r(U,{modelValue:o(l).reported,"onUpdate:modelValue":i[4]||(i[4]=s=>o(l).reported=s),placeholder:t.$t("all"),disabled:e.busy,options:e.selectOptions,clearable:!0,onCleared:p,onSelected:p},null,8,["modelValue","placeholder","disabled","options"])]),n("div",de,[r(y,{for:"topics-search",value:t.$t("TOPICS"),class:"mb-1"},null,8,["value"]),r(U,{modelValue:o(l).topics,"onUpdate:modelValue":i[5]||(i[5]=s=>o(l).topics=s),placeholder:t.$t("all"),disabled:e.busy,options:e.selectOptions,clearable:!0,onCleared:p,onSelected:p},null,8,["modelValue","placeholder","disabled","options"])])]),r(ee,{loading:e.busy},{default:a(()=>[r(o(X),{value:c.posts.data},{empty:a(()=>[_(u(t.$t(c.filters.search&&c.filters.search!==""||c.filters.user&&c.filters.user!==""?"emptyResult":"emptyData")),1)]),default:a(()=>[r(o(d),{class:"number-column",header:t.$t("ID")},{body:a(({data:s})=>[r(o(b),{class:"hover:text-sky-600 hover:underline",textContent:u(s.post_id),href:o(h)("post.detail",{post:s.post_id})},null,8,["textContent","href"])]),_:1},8,["header"]),r(o(d),{class:"post-username-column",header:t.$t("createdBy")},{body:a(({data:s})=>[r(o(b),{class:"hover:text-sky-600 hover:underline",textContent:u(s.username),href:o(h)("post.list",{user:s.user_id})},null,8,["textContent","href"])]),_:1},8,["header"]),r(o(d),{class:"number-column extra",header:t.$t("createdByID")},{body:a(({data:s})=>[r(o(b),{class:"hover:text-sky-600 hover:underline",textContent:u(s.user_id),href:o(h)("post.list",{user:s.user_id})},null,8,["textContent","href"])]),_:1},8,["header"]),r(o(d),{class:"title-flex-column",header:t.$t("postContent")},{body:a(({data:s})=>[n("div",{innerHTML:s.content},null,8,me)]),_:1},8,["header"]),r(o(d),{class:"post-type-column",field:"tag",header:t.$t("postType")},null,8,["header"]),r(o(d),{class:"time-column",header:t.$t("postCreatedAt")},{body:a(({data:s})=>[_(u(o(q)(s.created_at)),1)]),_:1},8,["header"]),r(o(d),{class:"count-column",header:t.$t("answerCount")},{body:a(({data:s})=>[s.answer_count>0?(m(),v(o(b),{key:0,class:"hover:text-red-600 hover:underline",textContent:u(s.answer_count),href:o(h)("postAnswer.list",{post:s.post_id})},null,8,["textContent","href"])):(m(),$(j,{key:1},[_(u(s.answer_count),1)],64))]),_:1},8,["header"]),r(o(d),{class:"count-column",field:"report_count",header:t.$t("reportCount")},null,8,["header"]),r(o(d),{class:"status-column",field:"status_label",header:t.$t("status")},null,8,["header"]),r(o(d),{class:"action-column"},{body:a(({data:s})=>[r(o(b),{href:o(h)("post.detail",{post:s.post_id})},{default:a(()=>i[6]||(i[6]=[n("i",{class:"pi pi-info-circle text-gray-500 hover:text-sky-600"},null,-1)])),_:2},1032,["href"]),parseInt(s.status)!==0?(m(),$("i",{key:0,class:"pi pi-trash text-red-400 hover:text-red-600 hover:cursor-pointer",onClick:K=>E(s.post_id)},null,8,pe)):C("",!0),s.status===1?(m(),$("i",{key:1,class:re(["pi pi-flag hover:text-lime-700 hover:cursor-pointer",[s.featured===1?"text-lime-700":"text-gray-400"]]),onClick:K=>A(s.post_id,s.featured)},null,10,he)):C("",!0)]),_:1})]),_:1},8,["value"])]),_:1},8,["loading"]),c.posts.data.length>0?(m(),v(Z,{key:0,class:"mt-5 flex items-center justify-center mx-auto",links:c.posts.links,active:e.activePage,disabled:e.busy,limit:c.posts.per_page,total:c.posts.total,from:c.posts.from,to:c.posts.to,onProgress:R,onChangeLimit:z},null,8,["links","active","disabled","limit","total","from","to"])):C("",!0)])]),_:1},8,["title"]),e.showModal?(m(),v(M,{key:0,show:e.open,onClose:w},{default:a(()=>[n("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:u(t.$t("confirm"))},null,8,fe),n("div",{class:"border-t border-b p-4",textContent:u(t.$t("confirmDeletePostMessage"))},null,8,Ce),n("div",ve,[r(V,{class:"mr-3 text-sm",textContent:u(t.$t("cancel")),onClick:w},null,8,["textContent"]),r(P,{class:"text-sm overflow-hidden h-[34px]",onClick:L},{default:a(()=>[e.deleting?(m(),v(I,{key:0,class:"mr-1"})):C("",!0),n("span",{class:"text-sm text-white",textContent:u(t.$t("delete"))},null,8,be)]),_:1})])]),_:1},8,["show"])):C("",!0),e.showFeatureModal?(m(),v(M,{key:1,show:e.openFeature,onClose:x},{default:a(()=>[n("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:u(t.$t("confirm"))},null,8,ge),n("div",{class:"border-t border-b p-4",textContent:u(t.$t("confirm"+(e.currentFeatureState===0?"EnableFeature":"DisableFeature")+"Message"))},null,8,ye),n("div",$e,[r(V,{class:"mr-3 text-sm",textContent:u(t.$t("cancel")),onClick:x},null,8,["textContent"]),r(P,{class:"text-sm overflow-hidden h-[34px]",onClick:H},{default:a(()=>[e.processing?(m(),v(I,{key:0,class:"mr-1"})):C("",!0),n("span",{class:"text-sm text-white",textContent:u(t.$t("confirm"))},null,8,we)]),_:1})])]),_:1},8,["show"])):C("",!0)],64))}};export{st as default};
