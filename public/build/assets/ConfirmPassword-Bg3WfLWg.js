import{r as n,c as l,o as d,l as r,a4 as t,S as e,a,_ as u,R as f,O as c,F as _}from"./@vue-BnW70ngI.js";import{T as w,Z as g}from"./@inertiajs-Dt0-hqjZ.js";import{A as b,a as x}from"./AuthenticationCardLogo-B-NI73cE.js";import{_ as v}from"./InputError-gQdwtcoE.js";import{_ as y}from"./InputLabel-BTXevqr4.js";import{_ as C}from"./PrimaryButton-DE9sqoJj.js";import{_ as V}from"./TextInput-DUNPEFms.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const k={class:"flex justify-end mt-4"},eo={__name:"ConfirmPassword",setup(A){const o=w({password:""}),i=n(null),m=()=>{o.post(route("password.confirm"),{onFinish:()=>{o.reset(),i.value.focus()}})};return($,s)=>(d(),l(_,null,[r(t(g),{title:"Secure Area"}),r(x,null,{logo:e(()=>[r(b)]),default:e(()=>[s[2]||(s[2]=a("div",{class:"mb-4 text-sm text-gray-600"}," This is a secure area of the application. Please confirm your password before continuing. ",-1)),a("form",{onSubmit:u(m,["prevent"])},[a("div",null,[r(y,{for:"password",value:"Password"}),r(V,{id:"password",ref_key:"passwordInput",ref:i,modelValue:t(o).password,"onUpdate:modelValue":s[0]||(s[0]=p=>t(o).password=p),type:"password",class:"mt-1 block w-full",required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),r(v,{class:"mt-2",message:t(o).errors.password},null,8,["message"])]),a("div",k,[r(C,{class:f(["ms-4",{"opacity-25":t(o).processing}]),disabled:t(o).processing},{default:e(()=>s[1]||(s[1]=[c(" Confirm ")])),_:1},8,["class","disabled"])])],32)]),_:1})],64))}};export{eo as default};
