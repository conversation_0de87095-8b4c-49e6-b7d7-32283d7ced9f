import{g as p}from"./get-intrinsic-BFhK1_aj.js";import{c as i}from"./call-bound-C_1-0vVo.js";import{o as s}from"./object-inspect-Cfg_CA0t.js";import{t as u}from"./es-errors-CFxpeikN.js";var f=p,r=i,c=s,v=u,o=f("%Map%",!0),m=r("Map.prototype.get",!0),l=r("Map.prototype.set",!0),d=r("Map.prototype.has",!0),M=r("Map.prototype.delete",!0),$=r("Map.prototype.size",!0),I=!!o&&function(){var t,n={assert:function(e){if(!n.has(e))throw new v("Side channel does not contain "+c(e))},delete:function(e){if(t){var a=M(t,e);return $(t)===0&&(t=void 0),a}return!1},get:function(e){if(t)return m(t,e)},has:function(e){return t?d(t,e):!1},set:function(e,a){t||(t=new o),l(t,e,a)}};return n};export{I as s};
