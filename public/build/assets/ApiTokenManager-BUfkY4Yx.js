import{T as A}from"./@inertiajs-BhKdJayA.js";import{_ as U}from"./ActionMessage-yNeSLSLA.js";import{S as h,_ as L}from"./ActionSection-d716unDa.js";import{_ as T}from"./Checkbox-BW6Lzxs4.js";import{_ as z}from"./ConfirmationModal-koF4JoqQ.js";import{_ as E}from"./DangerButton-C49GvHso.js";import{_ as F}from"./DialogModal-CP7TKBlg.js";import{b as J,a5 as K,c as r,o as a,l as o,a as n,S as s,J as $,_ as O,K as k,R as x,r as P,a4 as d,O as l,F as C,M as S,P as y}from"./@vue-BnW70ngI.js";import{_ as R}from"./InputError-gQdwtcoE.js";import{_ as I}from"./InputLabel-BTXevqr4.js";import{_ as V}from"./PrimaryButton-DE9sqoJj.js";import{_ as w}from"./SecondaryButton-BWHXZF7Q.js";import{_ as Y}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{_ as q}from"./TextInput-C52bsWxF.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";const G={class:"md:grid md:grid-cols-3 md:gap-6"},H={class:"mt-5 md:mt-0 md:col-span-2"},Q={class:"grid grid-cols-6 gap-6"},W={key:0,class:"flex items-center justify-end px-4 py-3 bg-gray-50 text-end sm:px-6 shadow sm:rounded-bl-md sm:rounded-br-md"},X={__name:"FormSection",emits:["submitted"],setup(m){const c=J(()=>!!K().actions);return(i,u)=>(a(),r("div",G,[o(h,null,{title:s(()=>[$(i.$slots,"title")]),description:s(()=>[$(i.$slots,"description")]),_:3}),n("div",H,[n("form",{onSubmit:u[0]||(u[0]=O(_=>i.$emit("submitted"),["prevent"]))},[n("div",{class:x(["px-4 py-5 bg-white sm:p-6 shadow",c.value?"sm:rounded-tl-md sm:rounded-tr-md":"sm:rounded-md"])},[n("div",Q,[$(i.$slots,"form")])],2),c.value?(a(),r("div",W,[$(i.$slots,"actions")])):k("",!0)],32)])]))}},Z={},ee={class:"hidden sm:block"};function se(m,c){return a(),r("div",ee,c[0]||(c[0]=[n("div",{class:"py-8"},[n("div",{class:"border-t border-gray-200"})],-1)]))}const te=Y(Z,[["render",se]]),oe={class:"col-span-6 sm:col-span-4"},ne={key:0,class:"col-span-6"},ie={class:"mt-2 grid grid-cols-1 md:grid-cols-2 gap-4"},le={class:"flex items-center"},re={class:"ms-2 text-sm text-gray-600"},ae={key:0},de={class:"mt-10 sm:mt-0"},me={class:"space-y-6"},ue={class:"break-all"},pe={class:"flex items-center ms-2"},ce={key:0,class:"text-sm text-gray-400"},fe=["onClick"],ve=["onClick"],ke={key:0,class:"mt-4 bg-gray-100 px-4 py-2 rounded font-mono text-sm text-gray-500 break-all"},ge={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ye={class:"flex items-center"},_e={class:"ms-2 text-sm text-gray-600"},ze={__name:"ApiTokenManager",props:{tokens:Array,availablePermissions:Array,defaultPermissions:Array},setup(m){const i=A({name:"",permissions:m.defaultPermissions}),u=A({permissions:[]}),_=A({}),b=P(!1),f=P(null),v=P(null),D=()=>{i.post(route("api-tokens.store"),{preserveScroll:!0,onSuccess:()=>{b.value=!0,i.reset()}})},B=p=>{u.permissions=p.abilities,f.value=p},N=()=>{u.put(route("api-tokens.update",f.value),{preserveScroll:!0,preserveState:!0,onSuccess:()=>f.value=null})},j=p=>{v.value=p},M=()=>{_.delete(route("api-tokens.destroy",v.value),{preserveScroll:!0,preserveState:!0,onSuccess:()=>v.value=null})};return(p,e)=>(a(),r("div",null,[o(X,{onSubmitted:D},{title:s(()=>e[9]||(e[9]=[l(" Create API Token ")])),description:s(()=>e[10]||(e[10]=[l(" API tokens allow third-party services to authenticate with our application on your behalf. ")])),form:s(()=>[n("div",oe,[o(I,{for:"name",value:"Name"}),o(q,{id:"name",modelValue:d(i).name,"onUpdate:modelValue":e[0]||(e[0]=t=>d(i).name=t),type:"text",class:"mt-1 block w-full",autofocus:""},null,8,["modelValue"]),o(R,{message:d(i).errors.name,class:"mt-2"},null,8,["message"])]),m.availablePermissions.length>0?(a(),r("div",ne,[o(I,{for:"permissions",value:"Permissions"}),n("div",ie,[(a(!0),r(C,null,S(m.availablePermissions,t=>(a(),r("div",{key:t},[n("label",le,[o(T,{checked:d(i).permissions,"onUpdate:checked":e[1]||(e[1]=g=>d(i).permissions=g),value:t},null,8,["checked","value"]),n("span",re,y(t),1)])]))),128))])])):k("",!0)]),actions:s(()=>[o(U,{on:d(i).recentlySuccessful,class:"me-3"},{default:s(()=>e[11]||(e[11]=[l(" Created. ")])),_:1},8,["on"]),o(V,{class:x({"opacity-25":d(i).processing}),disabled:d(i).processing},{default:s(()=>e[12]||(e[12]=[l(" Create ")])),_:1},8,["class","disabled"])]),_:1}),m.tokens.length>0?(a(),r("div",ae,[o(te),n("div",de,[o(L,null,{title:s(()=>e[13]||(e[13]=[l(" Manage API Tokens ")])),description:s(()=>e[14]||(e[14]=[l(" You may delete any of your existing tokens if they are no longer needed. ")])),content:s(()=>[n("div",me,[(a(!0),r(C,null,S(m.tokens,t=>(a(),r("div",{key:t.id,class:"flex items-center justify-between"},[n("div",ue,y(t.name),1),n("div",pe,[t.last_used_ago?(a(),r("div",ce," Last used "+y(t.last_used_ago),1)):k("",!0),m.availablePermissions.length>0?(a(),r("button",{key:1,class:"cursor-pointer ms-6 text-sm text-gray-400 underline",onClick:g=>B(t)}," Permissions ",8,fe)):k("",!0),n("button",{class:"cursor-pointer ms-6 text-sm text-red-500",onClick:g=>j(t)}," Delete ",8,ve)])]))),128))])]),_:1})])])):k("",!0),o(F,{show:b.value,onClose:e[3]||(e[3]=t=>b.value=!1)},{title:s(()=>e[15]||(e[15]=[l(" API Token ")])),content:s(()=>[e[16]||(e[16]=n("div",null," Please copy your new API token. For your security, it won't be shown again. ",-1)),p.$page.props.jetstream.flash.token?(a(),r("div",ke,y(p.$page.props.jetstream.flash.token),1)):k("",!0)]),footer:s(()=>[o(w,{onClick:e[2]||(e[2]=t=>b.value=!1)},{default:s(()=>e[17]||(e[17]=[l(" Close ")])),_:1})]),_:1},8,["show"]),o(F,{show:f.value!=null,onClose:e[6]||(e[6]=t=>f.value=null)},{title:s(()=>e[18]||(e[18]=[l(" API Token Permissions ")])),content:s(()=>[n("div",ge,[(a(!0),r(C,null,S(m.availablePermissions,t=>(a(),r("div",{key:t},[n("label",ye,[o(T,{checked:d(u).permissions,"onUpdate:checked":e[4]||(e[4]=g=>d(u).permissions=g),value:t},null,8,["checked","value"]),n("span",_e,y(t),1)])]))),128))])]),footer:s(()=>[o(w,{onClick:e[5]||(e[5]=t=>f.value=null)},{default:s(()=>e[19]||(e[19]=[l(" Cancel ")])),_:1}),o(V,{class:x(["ms-3",{"opacity-25":d(u).processing}]),disabled:d(u).processing,onClick:N},{default:s(()=>e[20]||(e[20]=[l(" Save ")])),_:1},8,["class","disabled"])]),_:1},8,["show"]),o(z,{show:v.value!=null,onClose:e[8]||(e[8]=t=>v.value=null)},{title:s(()=>e[21]||(e[21]=[l(" Delete API Token ")])),content:s(()=>e[22]||(e[22]=[l(" Are you sure you would like to delete this API token? ")])),footer:s(()=>[o(w,{onClick:e[7]||(e[7]=t=>v.value=null)},{default:s(()=>e[23]||(e[23]=[l(" Cancel ")])),_:1}),o(E,{class:x(["ms-3",{"opacity-25":d(_).processing}]),disabled:d(_).processing,onClick:M},{default:s(()=>e[24]||(e[24]=[l(" Delete ")])),_:1},8,["class","disabled"])]),_:1},8,["show"])]))}};export{ze as default};
