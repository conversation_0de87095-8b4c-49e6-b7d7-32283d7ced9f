import{T as H}from"./@inertiajs-Dt0-hqjZ.js";import{u as J}from"./vue-i18n-kWKo0idO.js";import{s as x}from"./ziggy-js-C7EU8ifa.js";import"./moment-C5S46NFB.js";import{c as K,a as R}from"./confirmModal-DLZLapTY.js";import{_ as Y}from"./AppLayout-CTb2MMqd.js";import{_ as q}from"./PrimaryButton-DE9sqoJj.js";import{_ as w}from"./LoadingIcon-CLD0VpVl.js";import{_ as G}from"./TextInput-DUNPEFms.js";import{_ as y}from"./FixedSelectionBox-Bk5LSyGJ.js";import{p as Q}from"./@element-plus-CyTLADhX.js";import{_ as W}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{i as X,v as Z,r as D,e as tt,k as C,o as p,S as v,a as o,c as h,K as b,P as d,l as g,a4 as f,F as et,M as ot,R as k}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@intlify-xvnhHnag.js";/* empty css                                                                     */import"./app-65VXU7yX.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./pinia-Ddsh4R0D.js";import"./primevue-CrCPcMFN.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./izitoast-CYQMso0-.js";import"./SecondaryButton-BoI1NwE9.js";/* empty css                                                    */import"./SelectionBox-D4JR3fGi.js";import"./@heroicons-BLousAGu.js";import"./@headlessui-gOb5_P77.js";const st={class:"flex-1 flex items-center"},rt=["textContent"],nt={class:"flex items-center space-x-3"},at=["textContent"],lt={class:"max-w-7xl mx-auto py-6 px-6 select-none"},dt={class:"bg-white rounded-md shadow flex flex-col"},it={class:"w-full rounded-md"},ct={class:"text-left flex border-t even:bg-blue-50 odd:bg-white rounded-t-md"},ut=["textContent"],pt=["textContent"],mt=["textContent"],gt=["textContent"],ft=["textContent"],ht=["textContent"],_t={class:"border-l w-[49px]"},vt=["draggable","onDragstart","onDragover","onDrop"],bt={class:"border-l w-20 p-3 flex items-center"},It={class:"flex items-center space-x-2"},xt={class:"font-medium text-gray-700"},wt={class:"border-l w-36 p-3"},yt={class:"relative"},Dt={key:0,class:"absolute w-4 h-4 right-2 top-1/2 transform -translate-y-1/2 -mt-0.5"},Ct={class:"border-l flex-1 p-3"},kt={class:"max-w-md"},$t={class:"text-sm text-gray-800 line-clamp-2"},Ft={class:"border-l w-32 p-3"},zt={class:"border-l w-32 p-3"},Vt={class:"border-l w-32 p-3"},Tt={class:"border-l w-[49px]"},Pt=["onClick"],Mt={key:0,class:"w-full border-t"},St={colspan:"7",class:"p-4"},Bt=["textContent"],Ut={__name:"UnlockPost",props:{posts:Object},setup($){const{t:m}=J(),c=$,_=X("$toast"),i=H(c.posts),s=Z({draggedIndex:null,draggedItem:null,isDragging:!1,dropIndicatorIndex:null,isInputFocused:!1}),l=D([]),u=D(!1),F=()=>{l.value=Object.keys(c.posts).map((t,r)=>({id:t,order:r+1,postId:c.posts[t].post_id||t,content:c.posts[t].content||"",type:c.posts[t].type||"",unlockTime:c.posts[t].unlock_time||"",size:c.posts[t].size||"small",originalData:c.posts[t]}))};tt(()=>{F()});const z=()=>{s.isInputFocused=!0},V=t=>{s.isInputFocused=!1,A(t)},T=(t,r)=>{if(s.isDragging||i.processing||u.value||s.isInputFocused)return t.preventDefault(),!1;s.draggedIndex=r,s.draggedItem=l.value[r],s.isDragging=!0,t.dataTransfer.effectAllowed="move",t.dataTransfer.setData("text/html",t.target),t.target.classList.add("dragging");const e=t.target.cloneNode(!0);e.style.transform="none",e.style.opacity="0.8",document.body.appendChild(e),t.dataTransfer.setDragImage(e,0,0),setTimeout(()=>{document.body.removeChild(e)},0)},P=t=>{s.isDragging=!1,s.dropIndicatorIndex=null,s.draggedIndex=null,s.draggedItem=null,t.target.classList.remove("dragging")},M=(t,r)=>{t.preventDefault(),t.dataTransfer.dropEffect="move",s.draggedIndex!==r&&(s.dropIndicatorIndex=r)},S=()=>{s.dropIndicatorIndex=null},B=(t,r)=>{t.preventDefault();const e=s.draggedIndex;if(e===null||e===r)return;const a=l.value[e];l.value.splice(e,1),l.value.splice(r,0,a),l.value.forEach((n,N)=>{n.order=N+1}),s.draggedIndex=null,s.draggedItem=null,s.dropIndicatorIndex=null},U=async t=>{await K(m("Are you sure you want to remove this post?"))&&(l.value.splice(t,1),l.value.forEach((e,a)=>{e.order=a+1}))},L=()=>{if(s.isDragging||i.processing||u.value)return;if(l.value.length===10){_.warning(m("You can only add up to 10 posts."));return}const t={id:`new_${Date.now()}`,order:l.value.length+1,postId:"",content:"",type:"",unlockTime:1,size:"small",originalData:{}};l.value.push(t)},j=async()=>{if(i.processing||u.value)return!1;await R(m("Are you sure you want to save your changes?"),async t=>{i.errors={},i.transform(()=>{const r=[];return l.value.forEach(e=>{e.postId&&r.push({post_id:e.postId,unlock_time:e.unlockTime,size:e.size})}),{data:r}}).post(x("setting.storeUnlockPosts"),{preserveScroll:!0,preserveState:!0,onSuccess:r=>{r.props.jetstream.flash.message&&_.success(r.props.jetstream.flash.message);for(const[e,a]of Object.entries(r.props.settings||{}))i[e]=a},onFinish:()=>{t(!0)}})})},E=[{value:"small",label:m("Small")},{value:"large",label:m("Large")}],O=[{value:1,label:"1"},{value:2,label:"2"}],A=async t=>{if(!t.postId||t.postId.toString().trim()===""){t.content="",t.type="";return}if(!(u.value||t.content&&t.lastFetchedId===t.postId)){u.value=!0;try{const e=(await window.axios.get(x("post.detailJson",{post:t.postId}))).data.post;t.content=e.content,t.type=e.type,t.lastFetchedId=t.postId}catch{I(t),t.postId="",_.error(m("Invalid post ID."))}finally{u.value=!1}}},I=t=>{t.content="",t.type="",t.lastFetchedId=null};return(t,r)=>(p(),C(Y,{title:t.$t("unlockPosts")},{header:v(()=>[o("div",st,[o("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:d(t.$t("unlockPosts"))},null,8,rt),o("div",nt,[g(q,{class:"normal-case",disabled:f(i).processing,onClick:j},{default:v(()=>[f(i).processing?(p(),C(w,{key:0,class:"mr-2"})):b("",!0),o("span",{class:"text-sm",textContent:d(t.$t("save"))},null,8,at)]),_:1},8,["disabled"])])])]),default:v(()=>[o("div",lt,[o("div",dt,[o("table",it,[o("tbody",null,[o("tr",ct,[o("th",{class:"border-l w-20",textContent:d(t.$t("order"))},null,8,ut),o("th",{class:"border-l w-36",textContent:d(t.$t("postID"))},null,8,pt),o("th",{class:"border-l flex-1",textContent:d(t.$t("postContent"))},null,8,mt),o("th",{class:"border-l w-32",textContent:d(t.$t("postType"))},null,8,gt),o("th",{class:"border-l w-32",textContent:d(t.$t("unlockTime"))},null,8,ft),o("th",{class:"border-l w-32",textContent:d(t.$t("size"))},null,8,ht),o("th",_t,[g(f(Q),{class:"w-4 h-4 cursor-pointer text-sky-500 hover:text-sky-600",onClick:L})])]),(p(!0),h(et,null,ot(l.value,(e,a)=>(p(),h("tr",{key:e.id,class:k(["text-left flex border-t transition-all duration-200",{"cursor-move":!s.isInputFocused,"cursor-default":s.isInputFocused,"even:bg-blue-50 odd:bg-white hover:bg-blue-100":!s.isDragging||s.draggedIndex!==a,"bg-blue-50 hover:bg-blue-100 shadow-lg border radius-sm border-blue-300":s.draggedIndex===a&&s.isDragging,"border-t-4 border-blue-500":s.dropIndicatorIndex===a,"hover:shadow-md":s.draggedIndex!==a&&!s.isInputFocused}]),draggable:!s.isInputFocused,onDragstart:n=>T(n,a),onDragend:P,onDragover:n=>M(n,a),onDragleave:S,onDrop:n=>B(n,a)},[o("td",bt,[o("div",It,[(p(),h("svg",{class:k(["w-4 h-4 transition-colors duration-200",{"text-gray-400 cursor-move":!s.isInputFocused,"text-gray-300 cursor-not-allowed":s.isInputFocused}]),fill:"currentColor",viewBox:"0 0 20 20"},r[0]||(r[0]=[o("path",{d:"M7 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM7 8a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM7 14a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 8a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 14a2 2 0 1 0 0 4 2 2 0 0 0 0-4z"},null,-1)]),2)),o("span",xt,d(e.order),1)])]),o("td",wt,[o("div",yt,[g(G,{modelValue:e.postId,"onUpdate:modelValue":n=>e.postId=n,type:"number",min:"1",placeholder:t.$t("postID"),disabled:f(i).processing||u.value,tabindex:a+1,onFocus:z,onBlur:n=>V(e),onInput:n=>I(e)},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","tabindex","onBlur","onInput"]),u.value&&e.postId?(p(),h("div",Dt,[g(w,{class:"w-4 h-4",color:"#9ca3af"})])):b("",!0)])]),o("td",Ct,[o("div",kt,[o("p",$t,d(e.content),1)])]),o("td",Ft,d(e.type),1),o("td",zt,[g(y,{class:"w-full",modelValue:e.unlockTime,"onUpdate:modelValue":n=>e.unlockTime=n,placeholder:t.$t("unlockTime"),options:O,disabled:f(i).processing,clearable:!1},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled"])]),o("td",Vt,[g(y,{class:"w-full",modelValue:e.size,"onUpdate:modelValue":n=>e.size=n,placeholder:t.$t("size"),options:E,disabled:f(i).processing,clearable:!1},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled"])]),o("td",Tt,[o("button",{onClick:n=>U(a),class:"text-red-400 hover:text-red-600 transition-colors duration-200 focus:outline-none"},r[1]||(r[1]=[o("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Pt)])],42,vt))),128)),l.value.length===0?(p(),h("tr",Mt,[o("td",St,[o("div",{class:"w-full text-center",textContent:d(t.$t("emptyData"))},null,8,Bt)])])):b("",!0)])])])])]),_:1},8,["title"]))}},je=W(Ut,[["__scopeId","data-v-05a27e34"]]);export{je as default};
