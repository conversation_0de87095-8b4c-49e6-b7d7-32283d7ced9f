import{c as p,o as f,l as e,a4 as o,S as i,a as t,_ as g,K as _,O as d,R as w,F as v}from"./@vue-BnW70ngI.js";import{T as y,Z as V,i as h}from"./@inertiajs-Dt0-hqjZ.js";import{A as k,a as x}from"./AuthenticationCardLogo-B-NI73cE.js";import{_ as b}from"./Checkbox-BW6Lzxs4.js";import{_ as m}from"./InputError-gQdwtcoE.js";import{_ as l}from"./InputLabel-BTXevqr4.js";import{_ as C}from"./PrimaryButton-DE9sqoJj.js";import{_ as u}from"./TextInput-DUNPEFms.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const P={class:"mt-4"},$={class:"mt-4"},q={class:"mt-4"},A={key:0,class:"mt-4"},N={class:"flex items-center"},U={class:"ms-2"},F=["href"],R=["href"],T={class:"flex items-center justify-end mt-4"},ge={__name:"Register",setup(B){const s=y({name:"",email:"",password:"",password_confirmation:"",terms:!1}),c=()=>{s.post(route("register"),{onFinish:()=>s.reset("password","password_confirmation")})};return(n,r)=>(f(),p(v,null,[e(o(V),{title:"Register"}),e(x,null,{logo:i(()=>[e(k)]),default:i(()=>[t("form",{onSubmit:g(c,["prevent"])},[t("div",null,[e(l,{for:"name",value:"Name"}),e(u,{id:"name",modelValue:o(s).name,"onUpdate:modelValue":r[0]||(r[0]=a=>o(s).name=a),type:"text",class:"mt-1 block w-full",required:"",autofocus:"",autocomplete:"name"},null,8,["modelValue"]),e(m,{class:"mt-2",message:o(s).errors.name},null,8,["message"])]),t("div",P,[e(l,{for:"email",value:"Email"}),e(u,{id:"email",modelValue:o(s).email,"onUpdate:modelValue":r[1]||(r[1]=a=>o(s).email=a),type:"email",class:"mt-1 block w-full",required:"",autocomplete:"username"},null,8,["modelValue"]),e(m,{class:"mt-2",message:o(s).errors.email},null,8,["message"])]),t("div",$,[e(l,{for:"password",value:"Password"}),e(u,{id:"password",modelValue:o(s).password,"onUpdate:modelValue":r[2]||(r[2]=a=>o(s).password=a),type:"password",class:"mt-1 block w-full",required:"",autocomplete:"new-password"},null,8,["modelValue"]),e(m,{class:"mt-2",message:o(s).errors.password},null,8,["message"])]),t("div",q,[e(l,{for:"password_confirmation",value:"Confirm Password"}),e(u,{id:"password_confirmation",modelValue:o(s).password_confirmation,"onUpdate:modelValue":r[3]||(r[3]=a=>o(s).password_confirmation=a),type:"password",class:"mt-1 block w-full",required:"",autocomplete:"new-password"},null,8,["modelValue"]),e(m,{class:"mt-2",message:o(s).errors.password_confirmation},null,8,["message"])]),n.$page.props.jetstream.hasTermsAndPrivacyPolicyFeature?(f(),p("div",A,[e(l,{for:"terms"},{default:i(()=>[t("div",N,[e(b,{id:"terms",checked:o(s).terms,"onUpdate:checked":r[4]||(r[4]=a=>o(s).terms=a),name:"terms",required:""},null,8,["checked"]),t("div",U,[r[5]||(r[5]=d(" I agree to the ")),t("a",{target:"_blank",href:n.route("terms.show"),class:"underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},"Terms of Service",8,F),r[6]||(r[6]=d(" and ")),t("a",{target:"_blank",href:n.route("policy.show"),class:"underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},"Privacy Policy",8,R)])]),e(m,{class:"mt-2",message:o(s).errors.terms},null,8,["message"])]),_:1})])):_("",!0),t("div",T,[e(o(h),{href:n.route("login"),class:"underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},{default:i(()=>r[7]||(r[7]=[d(" Already registered? ")])),_:1},8,["href"]),e(C,{class:w(["ms-4",{"opacity-25":o(s).processing}]),disabled:o(s).processing},{default:i(()=>r[8]||(r[8]=[d(" Register ")])),_:1},8,["class","disabled"])])],32)]),_:1})],64))}};export{ge as default};
