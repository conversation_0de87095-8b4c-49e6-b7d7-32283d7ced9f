import{r as x,b as O,u as be,i as W,w as B,e as M,f as me,h as U,F as te,d as I,p as ne,g as he,j as le,n as A,t as $}from"./@vue-BnW70ngI.js";function ge(e,n,l){let a=x(l==null?void 0:l.value),o=O(()=>e.value!==void 0);return[O(()=>o.value?e.value:a.value),function(r){return o.value||(a.value=r),n==null?void 0:n(r)}]}var G;let ye=Symbol("headlessui.useid"),Oe=0;const V=(G=be)!=null?G:function(){return W(ye,()=>`${++Oe}`)()};function g(e){var n;if(e==null||e.value==null)return null;let l=(n=e.value.$el)!=null?n:e.value;return l instanceof Node?l:null}function D(e,n,...l){if(e in n){let o=n[e];return typeof o=="function"?o(...l):o}let a=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(o=>`"${o}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(a,D),a}var we=Object.defineProperty,xe=(e,n,l)=>n in e?we(e,n,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[n]=l,Q=(e,n,l)=>(xe(e,typeof n!="symbol"?n+"":n,l),l);let Se=class{constructor(){Q(this,"current",this.detect()),Q(this,"currentId",0)}set(n){this.current!==n&&(this.currentId=0,this.current=n)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}},z=new Se;function Le(e){if(z.isServer)return null;if(e instanceof Node)return e.ownerDocument;if(e!=null&&e.hasOwnProperty("value")){let n=g(e);if(n)return n.ownerDocument}return document}let Y=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var Pe=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(Pe||{}),Ee=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(Ee||{}),$e=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))($e||{}),_=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(_||{});function re(e,n=0){var l;return e===((l=Le(e))==null?void 0:l.body)?!1:D(n,{0(){return e.matches(Y)},1(){let a=e;for(;a!==null;){if(a.matches(Y))return!0;a=a.parentElement}return!1}})}var De=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(De||{});typeof window<"u"&&typeof document<"u"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function Re(e,n=l=>l){return e.slice().sort((l,a)=>{let o=n(l),r=n(a);if(o===null||r===null)return 0;let t=o.compareDocumentPosition(r);return t&Node.DOCUMENT_POSITION_FOLLOWING?-1:t&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function Te(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function je(){return/Android/gi.test(window.navigator.userAgent)}function Ae(){return Te()||je()}function C(e,n,l){z.isServer||B(a=>{document.addEventListener(e,n,l),a(()=>document.removeEventListener(e,n,l))})}function Ie(e,n,l){z.isServer||B(a=>{window.addEventListener(e,n,l),a(()=>window.removeEventListener(e,n,l))})}function ke(e,n,l=O(()=>!0)){function a(r,t){if(!l.value||r.defaultPrevented)return;let s=t(r);if(s===null||!s.getRootNode().contains(s))return;let f=function c(p){return typeof p=="function"?c(p()):Array.isArray(p)||p instanceof Set?p:[p]}(e);for(let c of f){if(c===null)continue;let p=c instanceof HTMLElement?c:g(c);if(p!=null&&p.contains(s)||r.composed&&r.composedPath().includes(p))return}return!re(s,_.Loose)&&s.tabIndex!==-1&&r.preventDefault(),n(r,s)}let o=x(null);C("pointerdown",r=>{var t,s;l.value&&(o.value=((s=(t=r.composedPath)==null?void 0:t.call(r))==null?void 0:s[0])||r.target)},!0),C("mousedown",r=>{var t,s;l.value&&(o.value=((s=(t=r.composedPath)==null?void 0:t.call(r))==null?void 0:s[0])||r.target)},!0),C("click",r=>{Ae()||o.value&&(a(r,()=>o.value),o.value=null)},!0),C("touchend",r=>a(r,()=>r.target instanceof HTMLElement?r.target:null),!0),Ie("blur",r=>a(r,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}function X(e,n){if(e)return e;let l=n??"button";if(typeof l=="string"&&l.toLowerCase()==="button")return"button"}function Fe(e,n){let l=x(X(e.value.type,e.value.as));return M(()=>{l.value=X(e.value.type,e.value.as)}),B(()=>{var a;l.value||g(n)&&g(n)instanceof HTMLButtonElement&&!((a=g(n))!=null&&a.hasAttribute("type"))&&(l.value="button")}),l}function J(e){return[e.screenX,e.screenY]}function Ne(){let e=x([-1,-1]);return{wasMoved(n){let l=J(n);return e.value[0]===l[0]&&e.value[1]===l[1]?!1:(e.value=l,!0)},update(n){e.value=J(n)}}}var K=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(K||{}),Ce=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(Ce||{});function k({visible:e=!0,features:n=0,ourProps:l,theirProps:a,...o}){var r;let t=ae(a,l),s=Object.assign(o,{props:t});if(e||n&2&&t.static)return H(s);if(n&1){let f=(r=t.unmount)==null||r?0:1;return D(f,{0(){return null},1(){return H({...o,props:{...t,hidden:!0,style:{display:"none"}}})}})}return H(s)}function H({props:e,attrs:n,slots:l,slot:a,name:o}){var r,t;let{as:s,...f}=ue(e,["unmount","static"]),c=(r=l.default)==null?void 0:r.call(l,a),p={};if(a){let i=!1,v=[];for(let[S,w]of Object.entries(a))typeof w=="boolean"&&(i=!0),w===!0&&v.push(S);i&&(p["data-headlessui-state"]=v.join(" "))}if(s==="template"){if(c=oe(c??[]),Object.keys(f).length>0||Object.keys(n).length>0){let[i,...v]=c??[];if(!Be(i)||v.length>0)throw new Error(['Passing props on "template"!',"",`The current component <${o} /> is rendering a "template".`,"However we need to passthrough the following props:",Object.keys(f).concat(Object.keys(n)).map(m=>m.trim()).filter((m,E,R)=>R.indexOf(m)===E).sort((m,E)=>m.localeCompare(E)).map(m=>`  - ${m}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "template".',"Render a single element as the child so that we can forward the props onto that element."].map(m=>`  - ${m}`).join(`
`)].join(`
`));let S=ae((t=i.props)!=null?t:{},f,p),w=me(i,S,!0);for(let m in S)m.startsWith("on")&&(w.props||(w.props={}),w.props[m]=S[m]);return w}return Array.isArray(c)&&c.length===1?c[0]:c}return U(s,Object.assign({},f,p),{default:()=>c})}function oe(e){return e.flatMap(n=>n.type===te?oe(n.children):[n])}function ae(...e){if(e.length===0)return{};if(e.length===1)return e[0];let n={},l={};for(let a of e)for(let o in a)o.startsWith("on")&&typeof a[o]=="function"?(l[o]!=null||(l[o]=[]),l[o].push(a[o])):n[o]=a[o];if(n.disabled||n["aria-disabled"])return Object.assign(n,Object.fromEntries(Object.keys(l).map(a=>[a,void 0])));for(let a in l)Object.assign(n,{[a](o,...r){let t=l[a];for(let s of t){if(o instanceof Event&&o.defaultPrevented)return;s(o,...r)}}});return n}function Me(e){let n=Object.assign({},e);for(let l in n)n[l]===void 0&&delete n[l];return n}function ue(e,n=[]){let l=Object.assign({},e);for(let a of n)a in l&&delete l[a];return l}function Be(e){return e==null?!1:typeof e.type=="string"||typeof e.type=="object"||typeof e.type=="function"}var ie=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(ie||{});let Ve=I({name:"Hidden",props:{as:{type:[Object,String],default:"div"},features:{type:Number,default:1}},setup(e,{slots:n,attrs:l}){return()=>{var a;let{features:o,...r}=e,t={"aria-hidden":(o&2)===2?!0:(a=r["aria-hidden"])!=null?a:void 0,hidden:(o&4)===4?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(o&4)===4&&(o&2)!==2&&{display:"none"}}};return k({ourProps:t,theirProps:r,slot:{},attrs:l,slots:n,name:"Hidden"})}}}),se=Symbol("Context");var F=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(F||{});function He(){return W(se,null)}function Ue(e){ne(se,e)}var y=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(y||{});function Ke(e){throw new Error("Unexpected object: "+e)}var L=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(L||{});function We(e,n){let l=n.resolveItems();if(l.length<=0)return null;let a=n.resolveActiveIndex(),o=a??-1;switch(e.focus){case 0:{for(let r=0;r<l.length;++r)if(!n.resolveDisabled(l[r],r,l))return r;return a}case 1:{o===-1&&(o=l.length);for(let r=o-1;r>=0;--r)if(!n.resolveDisabled(l[r],r,l))return r;return a}case 2:{for(let r=o+1;r<l.length;++r)if(!n.resolveDisabled(l[r],r,l))return r;return a}case 3:{for(let r=l.length-1;r>=0;--r)if(!n.resolveDisabled(l[r],r,l))return r;return a}case 4:{for(let r=0;r<l.length;++r)if(n.resolveId(l[r],r,l)===e.id)return r;return a}case 5:return null;default:Ke(e)}}function de(e={},n=null,l=[]){for(let[a,o]of Object.entries(e))ve(l,ce(n,a),o);return l}function ce(e,n){return e?e+"["+n+"]":n}function ve(e,n,l){if(Array.isArray(l))for(let[a,o]of l.entries())ve(e,ce(n,a.toString()),o);else l instanceof Date?e.push([n,l.toISOString()]):typeof l=="boolean"?e.push([n,l?"1":"0"]):typeof l=="string"?e.push([n,l]):typeof l=="number"?e.push([n,`${l}`]):l==null?e.push([n,""]):de(l,n,e)}let Z=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function ee(e){var n,l;let a=(n=e.innerText)!=null?n:"",o=e.cloneNode(!0);if(!(o instanceof HTMLElement))return a;let r=!1;for(let s of o.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))s.remove(),r=!0;let t=r?(l=o.innerText)!=null?l:"":a;return Z.test(t)&&(t=t.replace(Z,"")),t}function ze(e){let n=e.getAttribute("aria-label");if(typeof n=="string")return n.trim();let l=e.getAttribute("aria-labelledby");if(l){let a=l.split(" ").map(o=>{let r=document.getElementById(o);if(r){let t=r.getAttribute("aria-label");return typeof t=="string"?t.trim():ee(r).trim()}return null}).filter(Boolean);if(a.length>0)return a.join(", ")}return ee(e).trim()}function _e(e){let n=x(""),l=x("");return()=>{let a=g(e);if(!a)return"";let o=a.innerText;if(n.value===o)return l.value;let r=ze(a).trim().toLowerCase();return n.value=o,l.value=r,r}}function qe(e,n){return e===n}var Ge=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Ge||{}),Qe=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(Qe||{}),Ye=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(Ye||{});function Xe(e){requestAnimationFrame(()=>requestAnimationFrame(e))}let pe=Symbol("ListboxContext");function N(e){let n=W(pe,null);if(n===null){let l=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,N),l}return n}let et=I({name:"Listbox",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"template"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>qe},horizontal:{type:[Boolean],default:!1},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},multiple:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(e,{slots:n,attrs:l,emit:a}){let o=x(1),r=x(null),t=x(null),s=x(null),f=x([]),c=x(""),p=x(null),i=x(1);function v(u=d=>d){let d=p.value!==null?f.value[p.value]:null,b=Re(u(f.value.slice()),P=>g(P.dataRef.domRef)),h=d?b.indexOf(d):null;return h===-1&&(h=null),{options:b,activeOptionIndex:h}}let S=O(()=>e.multiple?1:0),[w,m]=ge(O(()=>e.modelValue),u=>a("update:modelValue",u),O(()=>e.defaultValue)),E=O(()=>w.value===void 0?D(S.value,{1:[],0:void 0}):w.value),R={listboxState:o,value:E,mode:S,compare(u,d){if(typeof e.by=="string"){let b=e.by;return(u==null?void 0:u[b])===(d==null?void 0:d[b])}return e.by(u,d)},orientation:O(()=>e.horizontal?"horizontal":"vertical"),labelRef:r,buttonRef:t,optionsRef:s,disabled:O(()=>e.disabled),options:f,searchQuery:c,activeOptionIndex:p,activationTrigger:i,closeListbox(){e.disabled||o.value!==1&&(o.value=1,p.value=null)},openListbox(){e.disabled||o.value!==0&&(o.value=0)},goToOption(u,d,b){if(e.disabled||o.value===1)return;let h=v(),P=We(u===L.Specific?{focus:L.Specific,id:d}:{focus:u},{resolveItems:()=>h.options,resolveActiveIndex:()=>h.activeOptionIndex,resolveId:j=>j.id,resolveDisabled:j=>j.dataRef.disabled});c.value="",p.value=P,i.value=b??1,f.value=h.options},search(u){if(e.disabled||o.value===1)return;let d=c.value!==""?0:1;c.value+=u.toLowerCase();let b=(p.value!==null?f.value.slice(p.value+d).concat(f.value.slice(0,p.value+d)):f.value).find(P=>P.dataRef.textValue.startsWith(c.value)&&!P.dataRef.disabled),h=b?f.value.indexOf(b):-1;h===-1||h===p.value||(p.value=h,i.value=1)},clearSearch(){e.disabled||o.value!==1&&c.value!==""&&(c.value="")},registerOption(u,d){let b=v(h=>[...h,{id:u,dataRef:d}]);f.value=b.options,p.value=b.activeOptionIndex},unregisterOption(u){let d=v(b=>{let h=b.findIndex(P=>P.id===u);return h!==-1&&b.splice(h,1),b});f.value=d.options,p.value=d.activeOptionIndex,i.value=1},theirOnChange(u){e.disabled||m(u)},select(u){e.disabled||m(D(S.value,{0:()=>u,1:()=>{let d=$(R.value.value).slice(),b=$(u),h=d.findIndex(P=>R.compare(b,$(P)));return h===-1?d.push(b):d.splice(h,1),d}}))}};ke([t,s],(u,d)=>{var b;R.closeListbox(),re(d,_.Loose)||(u.preventDefault(),(b=g(t))==null||b.focus())},O(()=>o.value===0)),ne(pe,R),Ue(O(()=>D(o.value,{0:F.Open,1:F.Closed})));let T=O(()=>{var u;return(u=g(t))==null?void 0:u.closest("form")});return M(()=>{le([T],()=>{if(!T.value||e.defaultValue===void 0)return;function u(){R.theirOnChange(e.defaultValue)}return T.value.addEventListener("reset",u),()=>{var d;(d=T.value)==null||d.removeEventListener("reset",u)}},{immediate:!0})}),()=>{let{name:u,modelValue:d,disabled:b,form:h,...P}=e,j={open:o.value===0,disabled:b,value:E.value};return U(te,[...u!=null&&E.value!=null?de({[u]:E.value}).map(([q,fe])=>U(Ve,Me({features:ie.Hidden,key:q,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:h,disabled:b,name:q,value:fe}))):[],k({ourProps:{},theirProps:{...l,...ue(P,["defaultValue","onUpdate:modelValue","horizontal","multiple","by"])},slot:j,slots:n,attrs:l,name:"Listbox"})])}}}),tt=I({name:"ListboxLabel",props:{as:{type:[Object,String],default:"label"},id:{type:String,default:null}},setup(e,{attrs:n,slots:l}){var a;let o=(a=e.id)!=null?a:`headlessui-listbox-label-${V()}`,r=N("ListboxLabel");function t(){var s;(s=g(r.buttonRef))==null||s.focus({preventScroll:!0})}return()=>{let s={open:r.listboxState.value===0,disabled:r.disabled.value},{...f}=e,c={id:o,ref:r.labelRef,onClick:t};return k({ourProps:c,theirProps:f,slot:s,attrs:n,slots:l,name:"ListboxLabel"})}}}),nt=I({name:"ListboxButton",props:{as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(e,{attrs:n,slots:l,expose:a}){var o;let r=(o=e.id)!=null?o:`headlessui-listbox-button-${V()}`,t=N("ListboxButton");a({el:t.buttonRef,$el:t.buttonRef});function s(i){switch(i.key){case y.Space:case y.Enter:case y.ArrowDown:i.preventDefault(),t.openListbox(),A(()=>{var v;(v=g(t.optionsRef))==null||v.focus({preventScroll:!0}),t.value.value||t.goToOption(L.First)});break;case y.ArrowUp:i.preventDefault(),t.openListbox(),A(()=>{var v;(v=g(t.optionsRef))==null||v.focus({preventScroll:!0}),t.value.value||t.goToOption(L.Last)});break}}function f(i){switch(i.key){case y.Space:i.preventDefault();break}}function c(i){t.disabled.value||(t.listboxState.value===0?(t.closeListbox(),A(()=>{var v;return(v=g(t.buttonRef))==null?void 0:v.focus({preventScroll:!0})})):(i.preventDefault(),t.openListbox(),Xe(()=>{var v;return(v=g(t.optionsRef))==null?void 0:v.focus({preventScroll:!0})})))}let p=Fe(O(()=>({as:e.as,type:n.type})),t.buttonRef);return()=>{var i,v;let S={open:t.listboxState.value===0,disabled:t.disabled.value,value:t.value.value},{...w}=e,m={ref:t.buttonRef,id:r,type:p.value,"aria-haspopup":"listbox","aria-controls":(i=g(t.optionsRef))==null?void 0:i.id,"aria-expanded":t.listboxState.value===0,"aria-labelledby":t.labelRef.value?[(v=g(t.labelRef))==null?void 0:v.id,r].join(" "):void 0,disabled:t.disabled.value===!0?!0:void 0,onKeydown:s,onKeyup:f,onClick:c};return k({ourProps:m,theirProps:w,slot:S,attrs:n,slots:l,name:"ListboxButton"})}}}),lt=I({name:"ListboxOptions",props:{as:{type:[Object,String],default:"ul"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(e,{attrs:n,slots:l,expose:a}){var o;let r=(o=e.id)!=null?o:`headlessui-listbox-options-${V()}`,t=N("ListboxOptions"),s=x(null);a({el:t.optionsRef,$el:t.optionsRef});function f(i){switch(s.value&&clearTimeout(s.value),i.key){case y.Space:if(t.searchQuery.value!=="")return i.preventDefault(),i.stopPropagation(),t.search(i.key);case y.Enter:if(i.preventDefault(),i.stopPropagation(),t.activeOptionIndex.value!==null){let v=t.options.value[t.activeOptionIndex.value];t.select(v.dataRef.value)}t.mode.value===0&&(t.closeListbox(),A(()=>{var v;return(v=g(t.buttonRef))==null?void 0:v.focus({preventScroll:!0})}));break;case D(t.orientation.value,{vertical:y.ArrowDown,horizontal:y.ArrowRight}):return i.preventDefault(),i.stopPropagation(),t.goToOption(L.Next);case D(t.orientation.value,{vertical:y.ArrowUp,horizontal:y.ArrowLeft}):return i.preventDefault(),i.stopPropagation(),t.goToOption(L.Previous);case y.Home:case y.PageUp:return i.preventDefault(),i.stopPropagation(),t.goToOption(L.First);case y.End:case y.PageDown:return i.preventDefault(),i.stopPropagation(),t.goToOption(L.Last);case y.Escape:i.preventDefault(),i.stopPropagation(),t.closeListbox(),A(()=>{var v;return(v=g(t.buttonRef))==null?void 0:v.focus({preventScroll:!0})});break;case y.Tab:i.preventDefault(),i.stopPropagation();break;default:i.key.length===1&&(t.search(i.key),s.value=setTimeout(()=>t.clearSearch(),350));break}}let c=He(),p=O(()=>c!==null?(c.value&F.Open)===F.Open:t.listboxState.value===0);return()=>{var i,v;let S={open:t.listboxState.value===0},{...w}=e,m={"aria-activedescendant":t.activeOptionIndex.value===null||(i=t.options.value[t.activeOptionIndex.value])==null?void 0:i.id,"aria-multiselectable":t.mode.value===1?!0:void 0,"aria-labelledby":(v=g(t.buttonRef))==null?void 0:v.id,"aria-orientation":t.orientation.value,id:r,onKeydown:f,role:"listbox",tabIndex:0,ref:t.optionsRef};return k({ourProps:m,theirProps:w,slot:S,attrs:n,slots:l,features:K.RenderStrategy|K.Static,visible:p.value,name:"ListboxOptions"})}}}),rt=I({name:"ListboxOption",props:{as:{type:[Object,String],default:"li"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(e,{slots:n,attrs:l,expose:a}){var o;let r=(o=e.id)!=null?o:`headlessui-listbox-option-${V()}`,t=N("ListboxOption"),s=x(null);a({el:s,$el:s});let f=O(()=>t.activeOptionIndex.value!==null?t.options.value[t.activeOptionIndex.value].id===r:!1),c=O(()=>D(t.mode.value,{0:()=>t.compare($(t.value.value),$(e.value)),1:()=>$(t.value.value).some(u=>t.compare($(u),$(e.value)))})),p=O(()=>D(t.mode.value,{1:()=>{var u;let d=$(t.value.value);return((u=t.options.value.find(b=>d.some(h=>t.compare($(h),$(b.dataRef.value)))))==null?void 0:u.id)===r},0:()=>c.value})),i=_e(s),v=O(()=>({disabled:e.disabled,value:e.value,get textValue(){return i()},domRef:s}));M(()=>t.registerOption(r,v)),he(()=>t.unregisterOption(r)),M(()=>{le([t.listboxState,c],()=>{t.listboxState.value===0&&c.value&&D(t.mode.value,{1:()=>{p.value&&t.goToOption(L.Specific,r)},0:()=>{t.goToOption(L.Specific,r)}})},{immediate:!0})}),B(()=>{t.listboxState.value===0&&f.value&&t.activationTrigger.value!==0&&A(()=>{var u,d;return(d=(u=g(s))==null?void 0:u.scrollIntoView)==null?void 0:d.call(u,{block:"nearest"})})});function S(u){if(e.disabled)return u.preventDefault();t.select(e.value),t.mode.value===0&&(t.closeListbox(),A(()=>{var d;return(d=g(t.buttonRef))==null?void 0:d.focus({preventScroll:!0})}))}function w(){if(e.disabled)return t.goToOption(L.Nothing);t.goToOption(L.Specific,r)}let m=Ne();function E(u){m.update(u)}function R(u){m.wasMoved(u)&&(e.disabled||f.value||t.goToOption(L.Specific,r,0))}function T(u){m.wasMoved(u)&&(e.disabled||f.value&&t.goToOption(L.Nothing))}return()=>{let{disabled:u}=e,d={active:f.value,selected:c.value,disabled:u},{value:b,disabled:h,...P}=e,j={id:r,ref:s,role:"option",tabIndex:u===!0?void 0:-1,"aria-disabled":u===!0?!0:void 0,"aria-selected":c.value,disabled:void 0,onClick:S,onFocus:w,onPointerenter:E,onMouseenter:E,onPointermove:R,onMousemove:R,onPointerleave:T,onMouseleave:T};return k({ourProps:j,theirProps:P,slot:d,attrs:l,slots:n,name:"ListboxOption"})}}});export{lt as A,tt as E,rt as F,et as I,nt as j};
