import{T as _,i as w}from"./@inertiajs-BhKdJayA.js";import{_ as h}from"./AppLayout-m_I9gnvX.js";import{_ as g}from"./RedButton-D21iPtqa.js";import{_ as b}from"./LoadingIcon-CesYxFkK.js";import{_ as C}from"./TextInput-C52bsWxF.js";import{_ as c}from"./InputError-gQdwtcoE.js";import{Q as v}from"./@vueup-DxIbRV4Q.js";import{i as $,k as p,o as f,S as m,a as o,l,P as n,a4 as s,K as y}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BWHXZF7Q.js";import"./PrimaryButton-DE9sqoJj.js";import"./quill-B8crIIz-.js";import"./parchment-DI0xVRB_.js";import"./eventemitter3-mWFy3unY.js";import"./lodash-es-BvFElN8u.js";import"./quill-delta-BfHWV4fh.js";import"./fast-diff-DNDSwfiB.js";const k={class:"flex-1 flex items-center"},S=["textContent"],V=["textContent"],N={class:"max-w-7xl mx-auto py-6 px-6"},j={class:"bg-white rounded-md shadow flex flex-col overflow-auto"},B={class:"px-5 pt-3 pb-4 flex flex-col"},F=["textContent"],Q={class:"border-t px-5 pt-3 pb-4 flex flex-col news-editor-container"},T=["textContent"],U={class:"flex-1 flex flex-col"},rt={__name:"Form",props:{news:Object,action:String},setup(d){const r=d,u=$("$toast"),t=_(r.news),x=()=>{if(t.processing)return!1;t.errors={},t.transform(e=>e).post(route("news.store"),{preserveScroll:!0,preserveState:!0,onSuccess:e=>{e.props.jetstream.flash.message&&u.success(e.props.jetstream.flash.message),r.action==="create"&&(window.location=route("news.list"))}})};return(e,a)=>(f(),p(h,{title:e.$t(r.action+"News")},{header:m(()=>[o("div",k,[o("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:n(e.$t(r.action+"News"))},null,8,S),l(s(w),{class:"primary-button text-sm mr-3",href:e.route("news.list"),textContent:n(e.$t("list"))},null,8,["href","textContent"]),l(g,{class:"normal-case",disabled:s(t).processing,onClick:x},{default:m(()=>[s(t).processing?(f(),p(b,{key:0,class:"mr-2"})):y("",!0),o("span",{class:"text-sm",textContent:n(e.$t("save"))},null,8,V)]),_:1},8,["disabled"])])]),default:m(()=>[o("div",N,[o("div",j,[o("div",B,[o("label",{textContent:n(e.$t("newsTitle")),class:"w-full"},null,8,F),l(C,{class:"block w-full mt-1",type:"text",modelValue:s(t).title,"onUpdate:modelValue":a[0]||(a[0]=i=>s(t).title=i),disabled:s(t).processing},null,8,["modelValue","disabled"]),l(c,{class:"w-full mt-1",message:s(t).errors.title},null,8,["message"])]),o("div",Q,[o("label",{textContent:n(e.$t("newsContent")),class:"w-full mb-1"},null,8,T),o("div",U,[l(s(v),{theme:"snow",class:"h-full flex-1 flex flex-col","content-type":"html",content:s(t).content,"v-model":s(t).content,"onUpdate:content":a[1]||(a[1]=i=>s(t).content=i)},null,8,["content","v-model"])]),l(c,{class:"w-full mt-1",message:s(t).errors.content},null,8,["message"])])])])]),_:1},8,["title"]))}};export{rt as default};
