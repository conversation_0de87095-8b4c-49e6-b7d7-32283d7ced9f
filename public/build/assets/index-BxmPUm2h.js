import{h as e}from"./moment-C5S46NFB.js";/* empty css                                                    *//* empty css                                                                     */import"./app-EptGTPPo.js";const i=(t,r="")=>r+t.toString().padStart(6,"0"),n=(t,r)=>t?e.utc(t).local().format(r):"",f=t=>n(t,"YYYY/MM/DD HH:mm:ss"),u=t=>e.utc(t).local().format("YYYY-MM-DD"),D=t=>Object.fromEntries(Object.entries(t).filter(([r,o])=>o!==null&&o!==""&&o!==void 0));export{D as c,f,i as g,u as t};
