import{c as m,o as l,l as s,a4 as o,S as r,a,K as d,P as p,_ as c,R as f,O as _,F as w}from"./@vue-BnW70ngI.js";import{T as g,Z as y}from"./@inertiajs-BhKdJayA.js";import{A as x,a as b}from"./AuthenticationCardLogo-C3QjX6Mv.js";import{_ as k}from"./InputError-gQdwtcoE.js";import{_ as V}from"./InputLabel-BTXevqr4.js";import{_ as v}from"./PrimaryButton-DE9sqoJj.js";import{_ as C}from"./TextInput-C52bsWxF.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const F={key:0,class:"mb-4 font-medium text-sm text-green-600"},N={class:"flex items-center justify-end mt-4"},K={__name:"ForgotPassword",props:{status:String},setup(i){const t=g({email:""}),n=()=>{t.post(route("password.email"))};return(P,e)=>(l(),m(w,null,[s(o(y),{title:"Forgot Password"}),s(b,null,{logo:r(()=>[s(x)]),default:r(()=>[e[2]||(e[2]=a("div",{class:"mb-4 text-sm text-gray-600"}," Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one. ",-1)),i.status?(l(),m("div",F,p(i.status),1)):d("",!0),a("form",{onSubmit:c(n,["prevent"])},[a("div",null,[s(V,{for:"email",value:"Email"}),s(C,{id:"email",modelValue:o(t).email,"onUpdate:modelValue":e[0]||(e[0]=u=>o(t).email=u),type:"email",class:"mt-1 block w-full",required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),s(k,{class:"mt-2",message:o(t).errors.email},null,8,["message"])]),a("div",N,[s(v,{class:f({"opacity-25":o(t).processing}),disabled:o(t).processing},{default:r(()=>e[1]||(e[1]=[_(" Email Password Reset Link ")])),_:1},8,["class","disabled"])])],32)]),_:1})],64))}};export{K as default};
