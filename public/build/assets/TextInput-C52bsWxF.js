import{r as u,e as r,c as d,o as p,$ as i}from"./@vue-BnW70ngI.js";const f=["value","disabled","placeholder"],m={__name:"TextInput",props:{modelValue:{default:"",validator(e){return e===null||typeof e=="string"||typeof e=="number"||typeof e=="object"}},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:""}},emits:["update:modelValue","enter"],setup(e,{expose:n}){const l=u(null);r(()=>{l.value.hasAttribute("autofocus")&&setTimeout(()=>l.value.focus(),50)}),n({focus:()=>l.value.focus()});const s=t=>{["ArrowUp","ArrowDown","Enter"," ","Home","End","Escape"].includes(t.key)&&t.stopPropagation()};return(t,o)=>(p(),d("input",{class:"input-text",ref_key:"input",ref:l,value:e.modelValue,disabled:e.disabled,placeholder:e.placeholder,onInput:o[0]||(o[0]=a=>t.$emit("update:modelValue",a.target.value)),onKeyup:o[1]||(o[1]=i(a=>t.$emit("enter"),["enter"])),onKeydown:s,autocomplete:"off"},null,40,f))}};export{m as _};
