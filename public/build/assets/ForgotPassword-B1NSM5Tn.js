import{c as m,o as l,l as t,a4 as e,S as r,a,K as u,P as d,_ as c,R as f,O as _,F as w}from"./@vue-BnW70ngI.js";import{T as g,Z as y}from"./@inertiajs-Dt0-hqjZ.js";import{A as x,a as b}from"./AuthenticationCardLogo-BnhG9BN9.js";import{_ as k}from"./InputError-gQdwtcoE.js";import{_ as V}from"./InputLabel-BTXevqr4.js";import{_ as v}from"./PrimaryButton-DE9sqoJj.js";import{_ as C}from"./TextInput-C52bsWxF.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const F={key:0,class:"mb-4 font-medium text-sm text-green-600"},N={class:"flex items-center justify-end mt-4"},it={__name:"ForgotPassword",props:{status:String},setup(i){const o=g({email:""}),p=()=>{o.post(route("password.email"))};return(P,s)=>(l(),m(w,null,[t(e(y),{title:"Forgot Password"}),t(b,null,{logo:r(()=>[t(x)]),default:r(()=>[s[2]||(s[2]=a("div",{class:"mb-4 text-sm text-gray-600"}," Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one. ",-1)),i.status?(l(),m("div",F,d(i.status),1)):u("",!0),a("form",{onSubmit:c(p,["prevent"])},[a("div",null,[t(V,{for:"email",value:"Email"}),t(C,{id:"email",modelValue:e(o).email,"onUpdate:modelValue":s[0]||(s[0]=n=>e(o).email=n),type:"email",class:"mt-1 block w-full",required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),t(k,{class:"mt-2",message:e(o).errors.email},null,8,["message"])]),a("div",N,[t(v,{class:f({"opacity-25":e(o).processing}),disabled:e(o).processing},{default:r(()=>s[1]||(s[1]=[_(" Email Password Reset Link ")])),_:1},8,["class","disabled"])])],32)]),_:1})],64))}};export{it as default};
