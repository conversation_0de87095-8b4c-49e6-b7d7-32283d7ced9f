import{T as C,i as b}from"./@inertiajs-Dt0-hqjZ.js";import{s as S,a as u}from"./primevue-CrCPcMFN.js";import{_ as k}from"./AppLayout-CTb2MMqd.js";import{_ as B}from"./InputLabel-BTXevqr4.js";import{_ as F}from"./SearchInput-CdoSYJL3.js";import{_ as P}from"./Pagination-D56Hn3as.js";import{c as V}from"./index-k9QJQake.js";import{s as f}from"./ziggy-js-C7EU8ifa.js";import{_}from"./GridContainer-BC3u-41x.js";import{v as N,b as D,k as d,o as h,S as o,a as l,l as s,K as v,a4 as t,O as L,P as g}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";import"./PrimaryButton-DE9sqoJj.js";import"./FixedSelectionBox-Bk5LSyGJ.js";import"./SelectionBox-D4JR3fGi.js";import"./LoadingIcon-CLD0VpVl.js";/* empty css                                                    */import"./@heroicons-BLousAGu.js";import"./@element-plus-CyTLADhX.js";import"./TextInput-DUNPEFms.js";import"./@headlessui-gOb5_P77.js";import"./moment-C5S46NFB.js";/* empty css                                                                     */import"./app-65VXU7yX.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./pinia-Ddsh4R0D.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./izitoast-CYQMso0-.js";const T={class:"flex-1 flex items-center"},j=["textContent"],I={class:"p-6 sm:mx-2"},O={class:"flex items-stretch"},E={class:"mt-0 w-80 mr-8"},H=["innerHTML"],M=["src"],Ae={__name:"Index",props:{filters:Object,features:Object},setup(r){const c=r,m=C({search:c.filters.search??"",limit:c.filters.limit??10}),a=N({showSearchClearButton:(c.filters.search??"").trim().length>0,searching:!1,activePage:null,busy:D(()=>a.activePage!==null||a.searching||m.processing)}),p=()=>{if(m.processing)return!1;m.transform(e=>V(e)).get(f("premiumFeature.list"),{preserveScroll:!0,onSuccess:()=>{}})},$=()=>{a.showSearchClearButton=!0,p()},y=()=>{m.search="",a.showSearchClearButton=!1,p()},w=e=>{a.activePage=e,a.searching=!0},x=e=>{m.limit=e,m.search="",p()};return(e,n)=>(h(),d(k,{title:e.$t("premiumFeature.list")},{header:o(()=>[l("div",T,[l("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:g(e.$t("premiumFeature.list"))},null,8,j),s(t(b),{class:"primary-button text-sm",href:t(f)("premiumFeature.form",{feature:""}),textContent:g(e.$t("addNew"))},null,8,["href","textContent"])])]),default:o(()=>[l("div",I,[l("div",O,[l("div",E,[s(B,{for:"search",value:e.$t("search")},null,8,["value"]),s(F,{id:"search",class:"block w-full",modelValue:t(m).search,"onUpdate:modelValue":n[0]||(n[0]=i=>t(m).search=i),placeholder:e.$t("premiumFeature.search"),disabled:a.busy,"show-clear-button":a.showSearchClearButton,onInput:n[1]||(n[1]=i=>a.showSearchClearButton=!1),onClearSearch:y,onEnter:$},null,8,["modelValue","placeholder","disabled","show-clear-button"])])]),s(_,{loading:a.busy},{default:o(()=>[s(t(S),{value:r.features.data},{empty:o(()=>[L(g(e.$t(r.filters.search&&r.filters.search!==""||r.filters.status&&r.filters.status!==""?"emptyResult":"emptyData")),1)]),default:o(()=>[s(t(u),{class:"number-column small",field:"premium_id",header:e.$t("ID")},null,8,["header"]),s(t(u),{class:"w-96",field:"name",header:e.$t("premiumFeature.name")},null,8,["header"]),s(t(u),{class:"flex-1",header:e.$t("premiumFeature.description")},{body:o(({data:i})=>[l("div",{innerHTML:i.description.replace(/(\r\n|\n|\r)/g,"<br />")},null,8,H)]),_:1},8,["header"]),s(t(u),{class:"status-column",field:"price",header:e.$t("coin")},null,8,["header"]),s(t(u),{class:"status-column",header:e.$t("image")},{body:o(({data:i})=>[l("img",{class:"max-w-full max-h-full",src:i.image},null,8,M)]),_:1},8,["header"]),s(t(u),{class:"action-column small"},{body:o(({data:i})=>[i.status!==0?(h(),d(t(b),{key:0,href:t(f)("premiumFeature.form",{feature:i.premium_id})},{default:o(()=>n[2]||(n[2]=[l("i",{class:"pi pi-pen-to-square text-blue-400 hover:text-blue-600"},null,-1)])),_:2},1032,["href"])):v("",!0)]),_:1})]),_:1},8,["value"])]),_:1},8,["loading"]),r.features.data.length>0?(h(),d(P,{key:0,class:"mt-5 flex items-center justify-center mx-auto",links:r.features.links,active:a.activePage,disabled:a.busy,limit:r.features.per_page,total:r.features.total,from:r.features.from,to:r.features.to,onProgress:w,onChangeLimit:x},null,8,["links","active","disabled","limit","total","from","to"])):v("",!0)])]),_:1},8,["title"]))}};export{Ae as default};
