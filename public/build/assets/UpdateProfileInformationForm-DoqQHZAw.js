import{i as d,c as _,o as i,a as r,l as e,a4 as t,S as $,k as g,K as v,P as V,R as b,_ as h}from"./@vue-BnW70ngI.js";import{u as x}from"./vue-i18n-kWKo0idO.js";import{Q as y,T as k}from"./@inertiajs-Dt0-hqjZ.js";import{_ as w}from"./InputError-gQdwtcoE.js";import{_ as l}from"./InputLabel-BTXevqr4.js";import{_ as P}from"./LoadingIcon-CLD0VpVl.js";import{_ as S}from"./PrimaryButton-DE9sqoJj.js";import{_ as p}from"./TextInput-DUNPEFms.js";import"./@intlify-xvnhHnag.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";/* empty css                                                    */const B={class:"mt-0"},C={class:"mt-4"},I={class:"flex items-center justify-end text-end mt-5"},N=["textContent"],fo={__name:"UpdateProfileInformationForm",props:{user:Object},setup(n){const u=d("$toast"),{t:c}=x();y();const o=k({_method:"PUT",name:n.user.name}),f=()=>{o.post(route("user-profile-information.update"),{errorBag:"updateProfileInformation",preserveScroll:!0,onSuccess:()=>{u.success(c("updateProfileSuccessfully"))}})};return(s,a)=>(i(),_("form",{class:"px-6 py-5 bg-white shadow rounded-md mx-auto sm:max-w-xl",onSubmit:h(f,["prevent"]),autocomplete:"off"},[r("div",B,[e(l,{for:"name",value:s.$t("profileName")},null,8,["value"]),e(p,{id:"name",modelValue:t(o).name,"onUpdate:modelValue":a[0]||(a[0]=m=>t(o).name=m),type:"text",class:"mt-1 block w-full",autocomplete:"off"},null,8,["modelValue"]),e(w,{class:"mt-2",message:t(o).errors.name},null,8,["message"])]),r("div",C,[e(l,{for:"email",value:s.$t("email")},null,8,["value"]),e(p,{id:"email",modelValue:s.$page.props.auth.user.email,"onUpdate:modelValue":a[1]||(a[1]=m=>s.$page.props.auth.user.email=m),type:"text",class:"mt-1 block w-full",autocomplete:"off",disabled:""},null,8,["modelValue"])]),r("div",I,[e(S,{class:b({"opacity-25":t(o).processing}),disabled:t(o).processing},{default:$(()=>[t(o).processing?(i(),g(P,{key:0,class:"mr-2"})):v("",!0),r("span",{textContent:V(s.$t("save"))},null,8,N)]),_:1},8,["class","disabled"])])],32))}};export{fo as default};
