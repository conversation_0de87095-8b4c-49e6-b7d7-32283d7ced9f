import{a as n}from"./SecondaryButton-BWHXZF7Q.js";import{k as d,o,S as m,c as a,K as l,a as f,J as s}from"./@vue-BnW70ngI.js";const h={key:0,class:"text-lg font-semibold text-gray-900 border-b py-4 px-6"},p={class:"px-6 py-4 text-gray-600"},x={key:1,class:"flex flex-row justify-end px-6 py-4 border-t text-end"},w={__name:"DialogModal",props:{show:{type:Boolean,default:!1},maxWidth:{type:String,default:"2xl"},closeable:{type:Boolean,default:!0}},emits:["close"],setup(t,{emit:r}){const c=r,i=()=>{c("close")};return(e,u)=>(o(),d(n,{show:t.show,"max-width":t.maxWidth,closeable:t.closeable,onClose:i},{default:m(()=>[e.$slots.title?(o(),a("div",h,[s(e.$slots,"title")])):l("",!0),f("div",p,[s(e.$slots,"content")]),e.$slots.footer?(o(),a("div",x,[s(e.$slots,"footer")])):l("",!0)]),_:3},8,["show","max-width","closeable"]))}};export{w as _};
