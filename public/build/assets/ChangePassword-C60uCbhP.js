import{_ as i}from"./AppLayout-DKZEmXIb.js";import p from"./UpdatePasswordForm-DSN2rNTu.js";import{k as m,o as e,S as o,a as r,l as a,P as s}from"./@vue-BnW70ngI.js";import"./@inertiajs-Dt0-hqjZ.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";import"./PrimaryButton-DE9sqoJj.js";import"./InputError-gQdwtcoE.js";import"./InputLabel-BTXevqr4.js";import"./LoadingIcon-CesYxFkK.js";import"./TextInput-C52bsWxF.js";const n=["textContent"],l={class:"max-w-7xl mx-auto py-10 px-6"},W={__name:"ChangePassword",setup(c){return(t,_)=>(e(),m(i,{title:t.$t("changePassword")},{header:o(()=>[r("h2",{class:"text-xl text-gray-800 leading-tight",textContent:s(t.$t("changePassword"))},null,8,n)]),default:o(()=>[r("div",l,[a(p,{user:t.$page.props.auth.user},null,8,["user"])])]),_:1},8,["title"]))}};export{W as default};
