import{i as v,r as u,c as g,o as c,a,l as r,a4 as o,S as V,k as y,K as $,P as k,R as P,_ as b}from"./@vue-BnW70ngI.js";import{u as x}from"./vue-i18n-kWKo0idO.js";import{T as S}from"./@inertiajs-Dt0-hqjZ.js";import{_ as p}from"./InputError-gQdwtcoE.js";import{_ as l}from"./InputLabel-BTXevqr4.js";import{_ as B}from"./LoadingIcon-CesYxFkK.js";import{_ as C}from"./PrimaryButton-DE9sqoJj.js";import{_ as n}from"./TextInput-C52bsWxF.js";import"./@intlify-xvnhHnag.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";const I={class:"mt-0"},U={class:"mt-4"},h={class:"mt-4"},N={class:"flex items-center justify-end text-end mt-5"},j=["textContent"],fs={__name:"UpdatePasswordForm",setup(E){const f=v("$toast"),{t:w}=x(),i=u(null),d=u(null),s=S({current_password:"",password:"",password_confirmation:""}),_=()=>{s.put(route("user-password.update"),{errorBag:"updatePassword",preserveScroll:!0,onSuccess:()=>{s.reset(),f.success(w("passwordUpdatedSuccessfully"))},onError:()=>{s.errors.password&&(s.reset("password","password_confirmation"),i.value.focus()),s.errors.current_password&&(s.reset("current_password"),d.value.focus())}})};return(m,t)=>(c(),g("form",{class:"px-6 py-5 bg-white shadow rounded-md mx-auto sm:max-w-xl",onSubmit:b(_,["prevent"]),autocomplete:"off"},[a("div",I,[r(l,{for:"current_password",value:m.$t("currentPassword")},null,8,["value"]),r(n,{id:"current_password",class:"mt-1 block w-full",ref_key:"currentPasswordInput",ref:d,modelValue:o(s).current_password,"onUpdate:modelValue":t[0]||(t[0]=e=>o(s).current_password=e),type:"password",autocomplete:"off"},null,8,["modelValue"]),r(p,{class:"mt-2",message:o(s).errors.current_password},null,8,["message"])]),a("div",U,[r(l,{for:"password",value:m.$t("newPassword")},null,8,["value"]),r(n,{id:"password",ref_key:"passwordInput",ref:i,modelValue:o(s).password,"onUpdate:modelValue":t[1]||(t[1]=e=>o(s).password=e),type:"password",class:"mt-1 block w-full",autocomplete:"off"},null,8,["modelValue"]),r(p,{class:"mt-2",message:o(s).errors.password},null,8,["message"])]),a("div",h,[r(l,{for:"password_confirmation",value:m.$t("confirmPassword")},null,8,["value"]),r(n,{id:"password_confirmation",modelValue:o(s).password_confirmation,"onUpdate:modelValue":t[2]||(t[2]=e=>o(s).password_confirmation=e),type:"password",class:"mt-1 block w-full",autocomplete:"off"},null,8,["modelValue"]),r(p,{class:"mt-2",message:o(s).errors.password_confirmation},null,8,["message"])]),a("div",N,[r(C,{class:P({"opacity-25":o(s).processing}),disabled:o(s).processing},{default:V(()=>[o(s).processing?(c(),y(B,{key:0,class:"mr-2"})):$("",!0),a("span",{textContent:k(m.$t("save"))},null,8,j)]),_:1},8,["class","disabled"])])],32))}};export{fs as default};
