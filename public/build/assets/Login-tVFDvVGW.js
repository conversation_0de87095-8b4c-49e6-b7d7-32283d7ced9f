import{i as h,w as b,c as w,o as c,l as o,a4 as t,S as i,a as r,_ as $,P as f,R as k,k as v,K as C,F as V}from"./@vue-BnW70ngI.js";import{Q as y,T as x,Z as B}from"./@inertiajs-Dt0-hqjZ.js";import{A as E,a as j}from"./AuthenticationCardLogo-B-NI73cE.js";import{_ as z}from"./Checkbox-BW6Lzxs4.js";import{_ as d}from"./InputError-gQdwtcoE.js";import{_ as u}from"./InputLabel-BTXevqr4.js";import{_ as A}from"./LoadingIcon-CLD0VpVl.js";import{_ as N}from"./PrimaryButton-DE9sqoJj.js";import{_}from"./TextInput-DUNPEFms.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";/* empty css                                                    */const S={class:"mt-0"},U={class:"mt-4"},F={class:"block mt-4"},L={class:"flex items-center"},M=["textContent"],P={class:"flex items-center justify-end mt-4"},R=["textContent"],ve={__name:"Login",props:{canResetPassword:Boolean},setup(D){let l={};const n=h("$toast"),p=y();b(async()=>{p.props.jetstream.flash.throttle&&(e.setError(l),n.error(p.props.jetstream.flash.throttle))});const e=x({email:"",password:"",remember:!1}),g=()=>{if(e.processing)return!1;e.transform(s=>({...s,remember:e.remember?"on":""})).post(route("login"),{onBefore:()=>{l=e.errors,e.clearErrors()},onError:s=>{s.unauthorized&&n.error(s.unauthorized)}})};return(s,a)=>(c(),w(V,null,[o(t(B),{title:s.$t("login")},null,8,["title"]),o(j,null,{logo:i(()=>[o(E)]),default:i(()=>[r("form",{onSubmit:$(g,["prevent"]),autocomplete:"off"},[r("div",S,[o(u,{for:"email",value:s.$t("email")},null,8,["value"]),o(_,{id:"email",class:"mt-1",modelValue:t(e).email,"onUpdate:modelValue":a[0]||(a[0]=m=>t(e).email=m),autofocus:"",autocomplete:"off"},null,8,["modelValue"]),o(d,{class:"mt-2",message:t(e).errors.email},null,8,["message"])]),r("div",U,[o(u,{for:"password",value:s.$t("password")},null,8,["value"]),o(_,{id:"password",class:"mt-1",modelValue:t(e).password,"onUpdate:modelValue":a[1]||(a[1]=m=>t(e).password=m),type:"password",autocomplete:"off"},null,8,["modelValue"]),o(d,{class:"mt-2",message:t(e).errors.password},null,8,["message"])]),r("div",F,[r("label",L,[o(z,{checked:t(e).remember,"onUpdate:checked":a[2]||(a[2]=m=>t(e).remember=m),name:"remember"},null,8,["checked"]),r("span",{class:"ms-2 text-sm text-gray-600",textContent:f(s.$t("rememberMe"))},null,8,M)])]),r("div",P,[o(N,{class:k(["ms-4",{"opacity-25":t(e).processing}]),disabled:t(e).processing},{default:i(()=>[t(e).processing?(c(),v(A,{key:0,class:"mr-2",color:"#ffffff"})):C("",!0),r("span",{textContent:f(s.$t("login"))},null,8,R)]),_:1},8,["class","disabled"])])],32)]),_:1})],64))}};export{ve as default};
