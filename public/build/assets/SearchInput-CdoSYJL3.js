import{r as p,e as m,c as n,o as s,a as f,K as y,$ as h,R as w}from"./@vue-BnW70ngI.js";const v={class:"relative"},b=["value","disabled","placeholder"],V={__name:"SearchInput",props:{modelValue:{default:"",validator(e){return e===null||typeof e=="string"||typeof e=="number"||typeof e=="object"}},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:""},showClearButton:{type:Boolean,default:!1}},emits:["update:modelValue","enter","clearSearch","input"],setup(e,{expose:r,emit:u}){const l=u,a=p(null),i=t=>{l("update:modelValue",t.target.value),l("input")};m(()=>{a.value.hasAttribute("autofocus")&&setTimeout(()=>a.value.focus(),50)}),r({focus:()=>a.value.focus()});const d=t=>{["ArrowUp","ArrowDown","Enter"," ","Home","End","Escape"].includes(t.key)&&t.stopPropagation()};return(t,o)=>(s(),n("div",v,[f("input",{class:w(["input-text",e.showClearButton&&e.modelValue?"pr-[36px]":""]),ref_key:"input",ref:a,value:e.modelValue,disabled:e.disabled,placeholder:e.placeholder,onInput:i,onKeyup:o[0]||(o[0]=h(c=>t.$emit("enter"),["enter"])),onKeydown:d,autocomplete:"off"},null,42,b),e.showClearButton&&e.modelValue?(s(),n("i",{key:0,class:"absolute pi pi-times input-clear-icon text-gray-400 hover:text-red-400 hover:cursor-pointer",onClick:o[1]||(o[1]=c=>t.$emit("clearSearch"))})):y("",!0)]))}};export{V as _};
