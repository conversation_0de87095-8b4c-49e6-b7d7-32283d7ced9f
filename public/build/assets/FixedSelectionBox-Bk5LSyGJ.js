import{_ as i}from"./SelectionBox-D4JR3fGi.js";import{v as m,j as p,k as f,o as V}from"./@vue-BnW70ngI.js";const B={__name:"FixedSelectionBox",props:{label:{type:String,default:""},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:""},modelValue:{default:"",validator(e){return e===null||typeof e=="string"||typeof e=="number"}},options:{type:Array,default:[]},clearData:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0}},emits:["update:modelValue","selected","dataCleared","cleared"],setup(e,{emit:n}){const a=n,o=e,t=m({modelValue:o.modelValue??""});p(()=>o.clearData,l=>{l===!0&&(t.modelValue="",a("dataCleared"))});const r=l=>{a("update:modelValue",l)},c=l=>{a("selected",l)},s=()=>{a("cleared")};return(l,d)=>(V(),f(i,{label:e.label,disabled:e.disabled,placeholder:e.placeholder,options:e.options,"can-refresh":!1,"can-clear":e.clearable,"value-type":"string",modelValue:t.modelValue,"onUpdate:modelValue":[d[0]||(d[0]=u=>t.modelValue=u),r],onSelected:c,onCleared:s},null,8,["label","disabled","placeholder","options","can-clear","modelValue"]))}};export{B as _};
