import{Q as S,i as k,N as w}from"./@inertiajs-Dt0-hqjZ.js";import{_ as N}from"./AppLayout-_qQ0AdHn.js";import{_ as j}from"./RedButton-D21iPtqa.js";import{_ as B}from"./LoadingIcon-CLD0VpVl.js";import{_ as P}from"./CommunityFormContent.vue_vue_type_script_setup_true_lang-Dl2CgMFy.js";import{_ as V}from"./PrimaryButton-DE9sqoJj.js";import"./moment-C5S46NFB.js";import{c as D}from"./confirmModal-DS4sumdl.js";import{s as _}from"./ziggy-js-C7EU8ifa.js";import{u as F}from"./vue-i18n-kWKo0idO.js";import{i as A,r as l,k as c,o as p,S as r,a as e,l as u,K as y,P as a,a4 as v}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";/* empty css                                                    */import"./ImageInput-BV1wAASf.js";import"./InputError-gQdwtcoE.js";import"./TextAreaInput-DHjed6qD.js";import"./TextInput-DUNPEFms.js";/* empty css                                                                     */import"./app-EptGTPPo.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./pinia-Ddsh4R0D.js";import"./primevue-CrCPcMFN.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./izitoast-CYQMso0-.js";import"./@intlify-xvnhHnag.js";const I={class:"flex-1 flex items-center"},K=["textContent"],O={class:"max-w-7xl mx-auto py-6 px-6"},Q={class:"bg-white rounded-md shadow flex flex-col overflow-auto community-form-container"},q={class:"flex mt-4"},z={class:"ml-auto flex items-center"},E=["textContent"],G=["textContent"],Lt={__name:"Form",props:{community:Object,action:String},setup(i){const{t:x}=F(),h=A("$toast"),f=S(),d=i,n=l(!1),o=l(!1),s=l(!1),C=()=>{o.value||(n.value=!0)},g=()=>{o.value=!1},$=t=>{o.value=t,t||(n.value=!1)},b=async t=>{s.value||await D(x("Are you sure you want to delete this community?"),async m=>{w.post(_("community.delete"),{community_id:t,redirect:!0},{preserveScroll:!0,preserveState:!0,onBefore:()=>{s.value=!0},onSuccess:()=>{f.props.jetstream.flash.message&&h.success(f.props.jetstream.flash.message)},onFinish:()=>{s.value=!1,m(!0)}})})};return(t,m)=>(p(),c(N,{title:t.$t(d.action+"Community")},{header:r(()=>[e("div",I,[e("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:a(t.$t(d.action+"Community"))},null,8,K),u(v(k),{class:"primary-button text-sm",href:v(_)("community.list"),textContent:a(t.$t("list"))},null,8,["href","textContent"])])]),default:r(()=>[e("div",O,[e("div",Q,[u(P,{community:i.community,submit:n.value,action:"create",onCommunitySaved:g,onProcessing:$},null,8,["community","submit"])]),e("div",q,[e("div",z,[i.action==="update"?(p(),c(j,{key:0,class:"normal-case ml-auto",disabled:s.value,onClick:m[0]||(m[0]=H=>b(i.community.community_id))},{default:r(()=>[e("span",{class:"text-sm",textContent:a(t.$t("delete"))},null,8,E)]),_:1},8,["disabled"])):y("",!0),u(V,{class:"normal-case ml-3",disabled:o.value,onClick:C},{default:r(()=>[o.value?(p(),c(B,{key:0,class:"mr-2"})):y("",!0),e("span",{class:"text-sm",textContent:a(t.$t("save"))},null,8,G)]),_:1},8,["disabled"])])])])]),_:1},8,["title"]))}};export{Lt as default};
