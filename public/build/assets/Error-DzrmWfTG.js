import{Z as s}from"./@inertiajs-Dt0-hqjZ.js";import{b as m,c as a,o as p,l as n,a as e,a4 as l,P as c,F as u}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";const d={class:"min-h-screen bg-gray-100 font-light flex items-center justify-center"},f=["textContent"],G={__name:"Error",props:{status:Number},setup(t){const o=t,i=m(()=>({503:"503: Service Unavailable",500:"500: Server Error",404:"404: Page Not Found",403:"403: Forbidden"})[o.status]);return(r,g)=>(p(),a(u,null,[n(l(s),{title:i.value??r.$t("errorTitle")},null,8,["title"]),e("div",d,[e("div",{class:"text-lg",textContent:c(r.$t(t.status?"error."+t.status:"errorMessage"))},null,8,f)])],64))}};export{G as default};
