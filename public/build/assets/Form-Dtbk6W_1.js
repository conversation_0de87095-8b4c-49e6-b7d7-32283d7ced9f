import{Q as P,T as U,i as M,N as T}from"./@inertiajs-Dt0-hqjZ.js";import{u as I}from"./vue-i18n-kWKo0idO.js";import{s as C}from"./ziggy-js-C7EU8ifa.js";import{_ as O}from"./AppLayout-_qQ0AdHn.js";import{_ as R}from"./RedButton-D21iPtqa.js";import{_ as Q}from"./LoadingIcon-CLD0VpVl.js";import{_ as q}from"./TextAreaInput-DHjed6qD.js";import{_ as k}from"./InputError-gQdwtcoE.js";import{_ as A}from"./FixedSelectionBox-Bk5LSyGJ.js";import{_ as E}from"./ImageInput-BV1wAASf.js";import{d as K}from"./pinia-Ddsh4R0D.js";import{_ as G}from"./SelectionBox-D4JR3fGi.js";import{v as H,b as v,j as z,k as $,o as x,r as S,c as B,K as D,a as o,P as h,R as V,F,i as J,l as c,S as w,a4 as l}from"./@vue-BnW70ngI.js";import L from"./CommunityFormModal-Cm9J6GGm.js";import{_ as W}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{p as X}from"./@element-plus-CyTLADhX.js";import{_ as Y}from"./PrimaryButton-DE9sqoJj.js";import"./moment-C5S46NFB.js";import{c as Z}from"./confirmModal-DS4sumdl.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@intlify-xvnhHnag.js";import"./SecondaryButton-BoI1NwE9.js";/* empty css                                                    */import"./@heroicons-BLousAGu.js";import"./TextInput-DUNPEFms.js";import"./@headlessui-gOb5_P77.js";import"./CommunityFormContent.vue_vue_type_script_setup_true_lang-Dl2CgMFy.js";/* empty css                                                                     */import"./app-EptGTPPo.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./primevue-CrCPcMFN.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./izitoast-CYQMso0-.js";const N=K("community-stores",{state:()=>({communities:[]}),getters:{hasData:e=>e.communities.length>0,data:e=>e.communities},actions:{setData(e){this.communities=e},clear(){this.communities=[]},async loadData(){await window.axios.post("/community/list").then(e=>{this.setData(e.data)}).catch(e=>{console.log(e)})},async prepend(e){this.communities=[e,...this.communities]}}}),ee={__name:"CommunitySelectionBox",props:{label:{type:String,default:""},disabled:{type:Boolean,default:!1},placeholder:{type:String,default:""},modelValue:{default:"",validator(e){return e===null||typeof e=="string"||typeof e=="number"}},clearData:{type:Boolean,default:!1},clearable:{type:Boolean,default:!1},showSelected:{type:Boolean,default:!0},enableSearch:{type:Boolean,default:!1},loadData:{type:Boolean,default:!0},searchPlaceholder:{type:String,default:""}},emits:["update:modelValue","update:loading","selected","dataCleared"],setup(e,{emit:g}){const a=g,i=e,u=N(),d=H({loading:!1,modelValue:i.modelValue??"",options:v(()=>u.data.map(r=>({label:r.name,value:r.community_id})))});z(()=>i.modelValue,r=>{d.modelValue=r??""},{immediate:!0}),z(()=>i.clearData,r=>{r===!0&&(d.modelValue="",a("dataCleared"))});const p=async()=>{a("update:loading",!0),d.loading=!0,await u.loadData().then(()=>{d.loading=!1,d.modelValue=i.modelValue,a("update:loading",!1)})};!u.hasData&&i.loadData&&p();const t=r=>{a("update:modelValue",r)},b=r=>{a("selected",r)};return(r,y)=>(x(),$(G,{label:e.label,disabled:e.disabled,loading:d.loading,placeholder:e.placeholder,options:d.options,"can-clear":e.clearable,"show-selected":e.showSelected,"enable-search":e.enableSearch,"search-placeholder":e.searchPlaceholder,modelValue:d.modelValue,"onUpdate:modelValue":[y[0]||(y[0]=_=>d.modelValue=_),t],onRefresh:p,onSelected:b},null,8,["label","disabled","loading","placeholder","options","can-clear","show-selected","enable-search","search-placeholder","modelValue"]))}},te=["textContent"],le=["disabled","aria-checked","aria-labelledby"],oe={__name:"Switcher",props:{modelValue:{type:[Number,Boolean],default:0,validator(e){return[0,1,!0,!1].includes(e)}},label:{type:String,default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"lg",validator(e){return["sm","md","lg"].includes(e)}},color:{type:String,default:"sky",validator(e){return["sky","blue","green","red","purple","indigo","pink"].includes(e)}}},emits:["update:modelValue","change"],setup(e,{emit:g}){const a=e,i=g,u=v(()=>a.modelValue===1||a.modelValue===!0),d=S(`switcher-${Math.random().toString(36).substr(2,9)}`),p={sm:{switch:"h-5 w-9",toggle:"h-4 w-4",translate:"translate-x-[18px]"},md:{switch:"h-6 w-11",toggle:"h-5 w-5",translate:"translate-x-[22px]"},lg:{switch:"h-7 w-14",toggle:"h-6 w-6",translate:"translate-x-[30px]"}},t={sky:"bg-sky-400",blue:"bg-blue-400",green:"bg-green-400",red:"bg-red-400",purple:"bg-purple-400",indigo:"bg-indigo-400",pink:"bg-pink-400"},b=v(()=>["mt-[6px] relative inline-flex flex-shrink-0 cursor-pointer rounded-full transition-colors duration-200 ease-in-out focus:outline-none",p[a.size].switch,a.disabled?"opacity-50 cursor-not-allowed":"",u.value?t[a.color]:"bg-gray-200"]),r=v(()=>["pointer-events-none absolute inset-0 rounded-full transition-colors duration-200 ease-in-out",u.value?t[a.color]:"bg-gray-200"]),y=v(()=>["pointer-events-none inline-block rounded-full bg-white shadow transform transition duration-200 ease-in-out flex items-center justify-center translate-y-0.5",p[a.size].toggle,u.value?p[a.size].translate:"translate-x-[2px]"]),_=()=>{if(a.disabled)return;const f=u.value?0:1;i("update:modelValue",f),i("change",f)};return(f,j)=>(x(),B(F,null,[e.label?(x(),B("label",{key:0,class:"w-full",textContent:h(e.label)},null,8,te)):D("",!0),o("button",{type:"button",class:V(b.value),disabled:e.disabled,onClick:_,role:"switch","aria-checked":u.value,"aria-labelledby":d.value},[o("span",{class:V(r.value),"aria-hidden":"true"},null,2),o("span",{class:V(y.value),"aria-hidden":"true"},null,2)],10,le)],64))}},se=W(oe,[["__scopeId","data-v-a2d587f5"]]),ae={class:"flex-1 flex items-center"},ne=["textContent"],re={class:"max-w-7xl mx-auto py-6 px-6"},ie={class:"bg-white rounded-md shadow px-5 py-4 space-y-4"},de={class:"flex flex-col"},me=["textContent"],ce={class:"flex space-x-6"},ue={class:"flex-1 flex flex-col"},pe=["textContent"],fe={class:"flex-1 flex flex-col"},ge={class:"flex space-x-6"},he={class:"flex-1 flex flex-col"},be={class:"flex items-end"},ye={class:"flex flex-col flex-1"},xe=["textContent"],_e={class:"flex flex-col ml-1.5"},ve=["disabled"],we={class:"flex-1 flex flex-col"},Ve={class:"mt-4 flex"},Ce={class:"ml-auto flex items-center"},ke=["textContent"],$e=["textContent"],Pt={__name:"Form",props:{post:Object,action:String},setup(e){const g=e,a=P(),{t:i}=I(),u=N(),d=[{value:"answerr_q",label:i("answerrQ")},{value:"answerr_topic",label:i("answerrTopic")}],p=J("$toast"),t=U(g.post),b=S(!1),r=async s=>{await u.prepend(s),t.community_id=s.community_id},y=()=>{if(g.action==="update")return!1;b.value=!0},_=()=>{if(t.processing)return!1;t.errors={},t.transform(s=>(typeof s.image=="string"&&delete s.image,s)).post(C("post.store"),{preserveScroll:!0,preserveState:!0,onSuccess:s=>{s.props.jetstream.flash.message&&p.success(s.props.jetstream.flash.message)}})},f=S(!1),j=async s=>{f.value||await Z(i("Are you sure you want to remove this post?"),async n=>{T.post(C("post.delete"),{id:s},{preserveScroll:!0,preserveState:!0,onBefore:()=>{f.value=!0},onSuccess:()=>{a.props.jetstream.flash.message&&p.success(a.props.jetstream.flash.message)},onFinish:()=>{f.value=!1,n(!0)}})})};return(s,n)=>(x(),B(F,null,[c(O,{title:s.$t(g.action+"Post")},{header:w(()=>[o("div",ae,[o("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:h(s.$t(g.action+"Post"))},null,8,ne),c(l(M),{class:"primary-button text-sm",href:l(C)("post.list"),textContent:h(s.$t("list"))},null,8,["href","textContent"])])]),default:w(()=>[o("div",re,[o("div",ie,[o("div",de,[o("label",{textContent:h(s.$t("postContent")),class:"w-full"},null,8,me),c(q,{class:"block w-full mt-1",modelValue:l(t).content,"onUpdate:modelValue":n[0]||(n[0]=m=>l(t).content=m),disabled:l(t).processing,rows:"4"},null,8,["modelValue","disabled"]),c(k,{class:"w-full mt-1",message:l(t).errors.content},null,8,["message"])]),o("div",ce,[o("div",ue,[o("label",{textContent:h(s.$t("postType")),class:"w-full mb-1"},null,8,pe),c(A,{modelValue:l(t).type,"onUpdate:modelValue":n[1]||(n[1]=m=>l(t).type=m),placeholder:s.$t("postType"),disabled:l(t).processing,options:d,clearable:!1},null,8,["modelValue","placeholder","disabled"]),c(k,{class:"w-full mt-1",message:l(t).errors.type},null,8,["message"])]),o("div",fe,[c(E,{class:"flex-1",label:s.$t("image"),error:l(t).errors.image,disabled:l(t).processing,modelValue:l(t).image,"onUpdate:modelValue":[n[2]||(n[2]=m=>l(t).image=m),n[3]||(n[3]=m=>l(t).image=m)]},null,8,["label","error","disabled","modelValue"])])]),o("div",ge,[o("div",he,[o("div",be,[o("div",ye,[o("label",{textContent:h(s.$t("community")),class:"w-full"},null,8,xe),c(ee,{modelValue:l(t).community_id,"onUpdate:modelValue":n[4]||(n[4]=m=>l(t).community_id=m),placeholder:s.$t("community"),disabled:l(t).processing||e.action==="update",clearable:!1},null,8,["modelValue","placeholder","disabled"])]),o("div",_e,[o("button",{class:V(["rounded-md border transition-colors duration-200 border-sky-400 p-2.5 focus:outline-none text-white",{"bg-gray-200 cursor-default":l(t).processing,"bg-sky-400 disabled:hover:bg-sky-400 disabled:hover:border-sky-400 hover:bg-sky-500 hover:border-sky-500":!l(t).processing}]),disabled:l(t).processing||e.action==="update",onClick:y},[c(l(X),{class:"w-5 h-5","aria-hidden":"true"})],10,ve)])]),c(k,{class:"w-full mt-1",message:l(t).errors.community_id},null,8,["message"])]),o("div",we,[c(se,{modelValue:l(t).camera_roll,"onUpdate:modelValue":n[5]||(n[5]=m=>l(t).camera_roll=m),label:l(i)("cameraRoll")},null,8,["modelValue","label"])])])]),o("div",Ve,[o("div",Ce,[e.action==="update"?(x(),$(R,{key:0,class:"normal-case",disabled:l(t).processing||f.value,onClick:n[6]||(n[6]=m=>j(l(t).post_id))},{default:w(()=>[o("span",{class:"text-sm",textContent:h(s.$t("delete"))},null,8,ke)]),_:1},8,["disabled"])):D("",!0),c(Y,{class:"normal-case ml-3",disabled:l(t).processing||f.value,onClick:_},{default:w(()=>[l(t).processing?(x(),$(Q,{key:0,class:"mr-2"})):D("",!0),o("span",{class:"text-sm",textContent:h(s.$t("save"))},null,8,$e)]),_:1},8,["disabled"])])])])]),_:1},8,["title"]),c(L,{"show-modal":b.value,onCloseModal:n[7]||(n[7]=m=>b.value=!1),onCommunityCreated:r},null,8,["show-modal"])],64))}};export{Pt as default};
