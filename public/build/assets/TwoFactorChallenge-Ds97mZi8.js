import{r as u,c as r,o as a,l as o,a4 as t,S as d,a as i,F as n,O as l,_ as y,R as b,n as C}from"./@vue-BnW70ngI.js";import{T as h,Z as V}from"./@inertiajs-BhKdJayA.js";import{A as w,a as T}from"./AuthenticationCardLogo-C3QjX6Mv.js";import{_}from"./InputError-gQdwtcoE.js";import{_ as v}from"./InputLabel-BTXevqr4.js";import{_ as I}from"./PrimaryButton-DE9sqoJj.js";import{_ as g}from"./TextInput-C52bsWxF.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const U={class:"mb-4 text-sm text-gray-600"},$={key:0},A={key:1},B={class:"flex items-center justify-end mt-4"},J={__name:"TwoFactorChallenge",setup(F){const c=u(!1),e=h({code:"",recovery_code:""}),f=u(null),p=u(null),k=async()=>{c.value^=!0,await C(),c.value?(f.value.focus(),e.code=""):(p.value.focus(),e.recovery_code="")},x=()=>{e.post(route("two-factor.login"))};return(N,s)=>(a(),r(n,null,[o(t(V),{title:"Two-factor Confirmation"}),o(T,null,{logo:d(()=>[o(w)]),default:d(()=>[i("div",U,[c.value?(a(),r(n,{key:1},[l(" Please confirm access to your account by entering one of your emergency recovery codes. ")],64)):(a(),r(n,{key:0},[l(" Please confirm access to your account by entering the authentication code provided by your authenticator application. ")],64))]),i("form",{onSubmit:y(x,["prevent"])},[c.value?(a(),r("div",A,[o(v,{for:"recovery_code",value:"Recovery Code"}),o(g,{id:"recovery_code",ref_key:"recoveryCodeInput",ref:f,modelValue:t(e).recovery_code,"onUpdate:modelValue":s[1]||(s[1]=m=>t(e).recovery_code=m),type:"text",class:"mt-1 block w-full",autocomplete:"one-time-code"},null,8,["modelValue"]),o(_,{class:"mt-2",message:t(e).errors.recovery_code},null,8,["message"])])):(a(),r("div",$,[o(v,{for:"code",value:"Code"}),o(g,{id:"code",ref_key:"codeInput",ref:p,modelValue:t(e).code,"onUpdate:modelValue":s[0]||(s[0]=m=>t(e).code=m),type:"text",inputmode:"numeric",class:"mt-1 block w-full",autofocus:"",autocomplete:"one-time-code"},null,8,["modelValue"]),o(_,{class:"mt-2",message:t(e).errors.code},null,8,["message"])])),i("div",B,[i("button",{type:"button",class:"text-sm text-gray-600 hover:text-gray-900 underline cursor-pointer",onClick:y(k,["prevent"])},[c.value?(a(),r(n,{key:1},[l(" Use an authentication code ")],64)):(a(),r(n,{key:0},[l(" Use a recovery code ")],64))]),o(I,{class:b(["ms-4",{"opacity-25":t(e).processing}]),disabled:t(e).processing},{default:d(()=>s[2]||(s[2]=[l(" Log in ")])),_:1},8,["class","disabled"])])],32)]),_:1})],64))}};export{J as default};
