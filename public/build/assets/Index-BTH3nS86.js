import{u as D}from"./vue-i18n-kWKo0idO.js";import{N as E,T as k}from"./@inertiajs-Dt0-hqjZ.js";import{t as I}from"./lodash-Bx_YDCCc.js";import{s as V}from"./ziggy-js-C7EU8ifa.js";import{s as T,a as p}from"./primevue-CrCPcMFN.js";import{_ as z}from"./AppLayout-DKZEmXIb.js";import{_ as m}from"./InputLabel-BTXevqr4.js";import{_ as F}from"./SearchInput-CdoSYJL3.js";import{_ as O}from"./Pagination-Dmt48FUb.js";import{_ as x}from"./LoadingIcon-CesYxFkK.js";import{_ as A,a as R}from"./SecondaryButton-BoI1NwE9.js";import{_ as C}from"./PrimaryButton-DE9sqoJj.js";import{_ as S}from"./FixedSelectionBox-CkXOgkaT.js";import{_}from"./TextInput-C52bsWxF.js";import{_ as W}from"./TextAreaInput-y-SlU-FI.js";import{_ as w}from"./InputError-gQdwtcoE.js";import{i as K,v as P,c as q,o as u,l as s,k as f,K as g,S as d,a as o,R as G,a4 as l,O as H,P as c,F as J}from"./@vue-BnW70ngI.js";import"./@intlify-xvnhHnag.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SelectionBox-CzAgH5wz.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-CyTLADhX.js";import"./@headlessui-gOb5_P77.js";const Q={class:"flex-1 flex items-center mr-auto"},X=["textContent"],Y=["textContent"],Z={class:"p-6 sm:mx-2"},ee={class:"flex items-stretch"},se={class:"mt-0 w-80 mr-8"},te={class:"mt-0 w-80 mr-8"},ae={class:"mt-0 w-80 mr-8"},le=["textContent"],oe={class:"border-t border-b p-4"},re={class:"flex items-stretch"},ne={class:"flex-1 mr-3"},ie={class:"flex-1 ml-3"},me={class:"mt-3"},de={class:"mt-3"},ue={class:"flex items-center justify-end px-3 py-3"},ce=["textContent"],is={__name:"Index",props:{filters:Object,assistants:Object},setup(i){const{t:b}=D(),y=K("$toast"),h=i,e=P({searched:h.filters.search??"",model:h.filters.model??"multilingual-e5-large-instruct",showSearchClearButton:(h.filters.search??"").trim().length>0,searchedLanguage:h.filters.language??"vi",searching:!1,showModal:!1,open:!1,languages:[{value:"vi",label:b("vietnamese")},{value:"ja",label:b("japanese")}],models:[{value:"multilingual-e5-large-instruct",label:"multilingual-e5-large-instruct"}]}),v=I(()=>E.get(V("assistant"),{search:e.searched,model:e.model?e.model:"",language:e.searchedLanguage},{preserveState:!0,onBefore:()=>{e.searching=!0},onFinish:()=>{e.searching=!1}}),1e3),B=()=>{e.showSearchClearButton=!0,v()},L=()=>{e.searched="",e.showSearchClearButton=!1,v()},j=()=>{v()},N=()=>{v()};let a=k({name:"",work:"",expertise:"",description:"",language:"vi"});const $=()=>{a.language=e.searchedLanguage,e.open=!1,setTimeout(()=>{e.showModal=!1,a.clearErrors()},150)},M=()=>{e.showModal=!0,setTimeout(()=>e.open=!0,150)},U=()=>{if(a.processing)return!1;a.errors={},a.transform(t=>(t.language=e.searchedLanguage??t.language,t)).post(V("assistant.store"),{preserveScroll:!0,preserveState:!0,onSuccess:t=>{t.props.jetstream.flash.message&&y.success(t.props.jetstream.flash.message),$(),a=k({name:"",work:"",expertise:"",description:"",language:e.searchedLanguage})}})};return(t,r)=>(u(),q(J,null,[s(z,{title:t.$t("assistantList")},{header:d(()=>[o("div",Q,[o("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:c(t.$t("assistantList"))},null,8,X),s(C,{class:"normal-case ml-2",disabled:l(a).processing,onClick:M},{default:d(()=>[l(a).processing?(u(),f(x,{key:0,class:"mr-2"})):g("",!0),o("span",{class:"text-sm",textContent:c(t.$t("newAssistant"))},null,8,Y)]),_:1},8,["disabled"])])]),default:d(()=>[o("div",Z,[o("div",ee,[o("div",se,[s(m,{for:"user-search",value:t.$t("assistantSearch")},null,8,["value"]),s(F,{id:"user-search",class:"mt-1 block w-full",modelValue:e.searched,"onUpdate:modelValue":r[0]||(r[0]=n=>e.searched=n),placeholder:"...",disabled:e.searching,"show-clear-button":e.showSearchClearButton,onInput:r[1]||(r[1]=n=>e.showSearchClearButton=!1),onClearSearch:L,onEnter:B},null,8,["modelValue","disabled","show-clear-button"])]),o("div",te,[s(m,{for:"user-search",value:t.$t("assistantModel")},null,8,["value"]),s(S,{modelValue:e.model,"onUpdate:modelValue":r[2]||(r[2]=n=>e.model=n),disabled:e.searching,options:e.models,clearable:!1,onSelected:j},null,8,["modelValue","disabled","options"])]),o("div",ae,[s(m,{for:"language-search",value:t.$t("language")},null,8,["value"]),s(S,{modelValue:e.searchedLanguage,"onUpdate:modelValue":r[3]||(r[3]=n=>e.searchedLanguage=n),disabled:e.searching,options:e.languages,clearable:!1,onSelected:N},null,8,["modelValue","disabled","options"])])]),o("div",{class:G(["bg-white rounded-md shadow overflow-auto mt-5 flex flex-col",{"grid-loading":e.searching}])},[e.searching?(u(),f(x,{key:0,class:"grid-loading-icon z-10",size:36,color:"rgb(55 65 81)"})):g("",!0),s(l(T),{value:i.assistants.data},{empty:d(()=>[H(c(t.$t(i.filters.search&&i.filters.search!==""||i.filters.language&&i.filters.language!==""?"emptyResult":"emptyData")),1)]),default:d(()=>[s(l(p),{class:"number-column",field:"assistant_id",header:t.$t("ID")},null,8,["header"]),s(l(p),{class:"post-username-column",field:"name",header:t.$t("assistantName")},null,8,["header"]),s(l(p),{class:"w-[250px]",field:"work",header:t.$t("assistantWork")},null,8,["header"]),s(l(p),{class:"w-[320px]",field:"expertise",header:t.$t("assistantExpertise")},null,8,["header"]),s(l(p),{class:"title-flex-column",field:"description",header:t.$t("assistantDescription")},null,8,["header"])]),_:1},8,["value"])],2),i.assistants.data.length>0?(u(),f(O,{key:0,class:"mt-5 flex items-center justify-center mx-auto",links:i.assistants.links},null,8,["links"])):g("",!0)])]),_:1},8,["title"]),e.showModal?(u(),f(R,{key:0,show:e.open,onClose:$},{default:d(()=>[o("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:c(t.$t("addNewAssistant"))},null,8,le),o("div",oe,[o("div",re,[o("div",ne,[s(m,{for:"input-name",value:t.$t("assistantName")},null,8,["value"]),s(_,{id:"input-name",class:"mt-1",modelValue:l(a).name,"onUpdate:modelValue":r[4]||(r[4]=n=>l(a).name=n)},null,8,["modelValue"]),s(w,{message:l(a).errors.name},null,8,["message"])]),o("div",ie,[s(m,{for:"input-work",value:t.$t("assistantWork")},null,8,["value"]),s(_,{id:"input-work",class:"mt-1",modelValue:l(a).work,"onUpdate:modelValue":r[5]||(r[5]=n=>l(a).work=n)},null,8,["modelValue"]),s(w,{message:l(a).errors.work},null,8,["message"])])]),o("div",me,[s(m,{for:"input-expertise",value:t.$t("assistantExpertise")},null,8,["value"]),s(_,{id:"input-expertise",class:"mt-1",modelValue:l(a).expertise,"onUpdate:modelValue":r[6]||(r[6]=n=>l(a).expertise=n)},null,8,["modelValue"]),s(w,{message:l(a).errors.expertise},null,8,["message"])]),o("div",de,[s(m,{for:"input-description",value:t.$t("assistantDescription")},null,8,["value"]),s(W,{id:"input-expertise",class:"mt-1 w-full",modelValue:l(a).description,"onUpdate:modelValue":r[7]||(r[7]=n=>l(a).description=n)},null,8,["modelValue"]),s(w,{message:l(a).errors.description},null,8,["message"])])]),o("div",ue,[s(A,{class:"mr-3 text-sm",textContent:c(t.$t("cancel")),onClick:$},null,8,["textContent"]),s(C,{class:"text-sm overflow-hidden h-[34px]",onClick:U},{default:d(()=>[l(a).processing?(u(),f(x,{key:0,class:"mr-1"})):g("",!0),o("span",{class:"text-sm text-white",textContent:c(t.$t("save"))},null,8,ce)]),_:1})])]),_:1},8,["show"])):g("",!0)],64))}};export{is as default};
