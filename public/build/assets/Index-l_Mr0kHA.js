import{Q as _,i as u,N as x}from"./@inertiajs-Dt0-hqjZ.js";import{s as v,a as s}from"./primevue-CrCPcMFN.js";import{_ as $}from"./AppLayout-_qQ0AdHn.js";import{s as l}from"./ziggy-js-C7EU8ifa.js";import{_ as g}from"./GridContainer-BC3u-41x.js";import"./moment-C5S46NFB.js";import{c as b}from"./confirmModal-DS4sumdl.js";import{u as C}from"./vue-i18n-kWKo0idO.js";import{i as N,r as w,k,o as D,S as o,a as m,l as r,a4 as e,O as S,P as p}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";import"./PrimaryButton-DE9sqoJj.js";import"./LoadingIcon-CLD0VpVl.js";/* empty css                                                    *//* empty css                                                                     */import"./app-EptGTPPo.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./pinia-Ddsh4R0D.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./izitoast-CYQMso0-.js";import"./@intlify-xvnhHnag.js";const j={class:"flex-1 flex items-center"},B=["textContent"],L={class:"p-6 sm:mx-2"},I=["innerHTML"],T=["src","alt"],V=["onClick"],It={__name:"Index",props:{communities:Object},setup(d){const{t:f}=C(),h=N("$toast"),c=_(),n=w(!1),y=async t=>{n.value||await b(f("Are you sure you want to delete this community?"),async a=>{x.post(l("community.delete"),{community_id:t},{preserveScroll:!0,preserveState:!0,onBefore:()=>{n.value=!0},onSuccess:()=>{c.props.jetstream.flash.message&&h.success(c.props.jetstream.flash.message)},onFinish:()=>{n.value=!1,a(!0)}})})};return(t,a)=>(D(),k($,{title:t.$t("communityList")},{header:o(()=>[m("div",j,[m("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:p(t.$t("communityList"))},null,8,B),r(e(u),{class:"primary-button text-sm",href:e(l)("community.form",{community:""}),textContent:p(t.$t("addNew"))},null,8,["href","textContent"])])]),default:o(()=>[m("div",L,[r(g,{style:{"margin-top":"0"}},{default:o(()=>[r(e(v),{value:d.communities.data},{empty:o(()=>[S(p(t.$t("emptyData")),1)]),default:o(()=>[r(e(s),{class:"number-column small",field:"community_id",header:t.$t("ID")},null,8,["header"]),r(e(s),{class:"w-[400px]",field:"name",header:t.$t("communityName")},null,8,["header"]),r(e(s),{class:"title-flex-column",header:t.$t("communityDescription")},{body:o(({data:i})=>[m("div",{innerHTML:i.description.replace(/(\r\n|\n|\r)/g,"<br />")},null,8,I)]),_:1},8,["header"]),r(e(s),{class:"status-column",header:t.$t("image")},{body:o(({data:i})=>[m("img",{class:"max-w-full max-h-full",src:i.image,alt:i.name},null,8,T)]),_:1},8,["header"]),r(e(s),{class:"action-column small"},{body:o(({data:i})=>[r(e(u),{href:e(l)("community.form",{community:i.community_id})},{default:o(()=>a[0]||(a[0]=[m("i",{class:"pi pi-pen-to-square text-blue-400 hover:text-blue-600"},null,-1)])),_:2},1032,["href"]),m("i",{class:"pi pi-trash text-red-400 hover:text-red-600 hover:cursor-pointer",onClick:H=>y(i.community_id)},null,8,V)]),_:1})]),_:1},8,["value"])]),_:1})])]),_:1},8,["title"]))}};export{It as default};
