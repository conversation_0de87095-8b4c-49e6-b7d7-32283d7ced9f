import{T as b,i as g}from"./@inertiajs-BhKdJayA.js";import{_ as w}from"./AppLayout-m_I9gnvX.js";import{_ as $}from"./RedButton-D21iPtqa.js";import{_ as C}from"./LoadingIcon-CesYxFkK.js";import{_ as d}from"./TextInput-C52bsWxF.js";import{_ as V}from"./TextAreaInput-y-SlU-FI.js";import{_ as m}from"./InputError-gQdwtcoE.js";import{_ as h}from"./ImageInput-DUQk7LRQ.js";import{_ as v}from"./FixedSelectionBox-CwNS68U7.js";import{i as y,k as p,o as c,S as u,a as l,l as n,P as i,a4 as t,K as q}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BWHXZF7Q.js";import"./PrimaryButton-DE9sqoJj.js";import"./SelectionBox-58uvzdoT.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-ccBf1-WH.js";import"./lodash-DBgjQQU6.js";import"./@headlessui-gOb5_P77.js";const k={class:"flex-1 flex items-center"},S=["textContent"],U=["textContent"],j={class:"max-w-7xl mx-auto py-6 px-6"},B={class:"bg-white rounded-md shadow flex flex-col overflow-auto px-5 pt-3 pb-4"},D={class:"flex flex-col pb-2"},F=["textContent"],N={class:"flex pb-2"},T={class:"flex flex-col flex-1 mr-3"},O=["textContent"],Q={class:"flex pb-2"},z={class:"flex flex-col flex-1 mr-3"},A=["textContent"],K={class:"flex items-center"},P=["textContent"],E={class:"flex flex-col flex-1 ml-3"},G=["textContent"],H={class:"flex flex-col"},I=["textContent"],ge={__name:"Form",props:{quest:Object,types:Object,action:String},setup(f){const r=f,x=y("$toast"),e=b(r.quest),_=()=>{if(e.processing)return!1;e.errors={},e.transform(s=>(typeof s.image=="string"&&delete s.image,s)).post(route("quest.store"),{forceFormData:!0,preserveScroll:!0,preserveState:!0,onSuccess:s=>{s.props.jetstream.flash.message&&x.success(s.props.jetstream.flash.message),r.action==="create"&&(window.location=route("quest.list"))}})};return(s,o)=>(c(),p(w,{title:s.$t(r.action+"Quest")},{header:u(()=>[l("div",k,[l("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:i(s.$t(r.action+"Quest"))},null,8,S),n(t(g),{class:"primary-button text-sm mr-3",href:s.route("quest.list"),textContent:i(s.$t("list"))},null,8,["href","textContent"]),n($,{class:"normal-case",disabled:t(e).processing,onClick:_},{default:u(()=>[t(e).processing?(c(),p(C,{key:0,class:"mr-2"})):q("",!0),l("span",{class:"text-sm",textContent:i(s.$t("save"))},null,8,U)]),_:1},8,["disabled"])])]),default:u(()=>[l("div",j,[l("div",B,[l("div",D,[l("label",{textContent:i(s.$t("title")),class:"w-full"},null,8,F),n(d,{class:"block w-full",type:"text",modelValue:t(e).title,"onUpdate:modelValue":o[0]||(o[0]=a=>t(e).title=a),disabled:t(e).processing},null,8,["modelValue","disabled"]),n(m,{class:"w-full mt-1",message:t(e).errors.title},null,8,["message"])]),l("div",N,[l("div",T,[l("label",{textContent:i(s.$t("questType")),class:"w-full mb-1"},null,8,O),n(v,{modelValue:t(e).type,"onUpdate:modelValue":o[1]||(o[1]=a=>t(e).type=a),placeholder:s.$t("questType"),disabled:r.action==="update"||t(e).processing,options:r.types},null,8,["modelValue","placeholder","disabled","options"]),n(m,{class:"w-full mt-1",message:t(e).errors.type},null,8,["message"])]),n(h,{class:"flex-1 ml-3",label:s.$t("image"),error:t(e).errors.image,disabled:t(e).processing,modelValue:t(e).image,"onUpdate:modelValue":[o[2]||(o[2]=a=>t(e).image=a),o[3]||(o[3]=a=>t(e).image=a)]},null,8,["label","error","disabled","modelValue"])]),l("div",Q,[l("div",z,[l("label",{textContent:i(s.$t("questAmount")),class:"w-full mb-1"},null,8,A),l("div",K,[n(d,{class:"block w-full",type:"text",modelValue:t(e).amount,"onUpdate:modelValue":o[4]||(o[4]=a=>t(e).amount=a),disabled:t(e).processing},null,8,["modelValue","disabled"]),l("div",{class:"ml-2 w-[100px]",textContent:i(s.$t("quest.unit.coin"))},null,8,P)]),n(m,{class:"w-full mt-1",message:t(e).errors.amount},null,8,["message"])]),l("div",E,[l("label",{textContent:i(s.$t("sort")),class:"w-full mb-1"},null,8,G),n(d,{class:"block w-full",type:"text",modelValue:t(e).sort,"onUpdate:modelValue":o[5]||(o[5]=a=>t(e).sort=a),disabled:t(e).processing},null,8,["modelValue","disabled"]),n(m,{class:"w-full mt-1",message:t(e).errors.sort},null,8,["message"])])]),l("div",H,[l("label",{textContent:i(s.$t("questDescription")),class:"w-full mb-1"},null,8,I),n(V,{class:"block w-full resize-none h-[150px]",type:"text",modelValue:t(e).description,"onUpdate:modelValue":o[6]||(o[6]=a=>t(e).description=a),disabled:t(e).processing},null,8,["modelValue","disabled"]),n(m,{class:"w-full mt-1",message:t(e).errors.description},null,8,["message"])])])])]),_:1},8,["title"]))}};export{ge as default};
