import{i as S,Z as L,N}from"./@inertiajs-Dt0-hqjZ.js";import{u as P}from"./vue-i18n-kWKo0idO.js";import{_ as V}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{c as g,o as l,r as A,e as B,g as j,b,a as s,X as x,l as n,J as d,D as $,a4 as c,a0 as _,S as f,R as m,U as D,k as v,v as E,K as y,P as o,O as F,F as T}from"./@vue-BnW70ngI.js";import{_ as U,a as z}from"./SecondaryButton-BoI1NwE9.js";import{_ as M}from"./PrimaryButton-DE9sqoJj.js";const q={},H=["alt"];function I(r,i){return l(),g("img",{src:"/assets/images/honne_logo_black.png",class:"block h-8",alt:r.$t("appName")},null,8,H)}const O=V(q,[["render",I]]),R={class:"relative"},J={__name:"Dropdown",props:{align:{type:String,default:"right"},width:{type:String,default:"48"},contentClasses:{type:Array,default:()=>["py-1","bg-white"]}},setup(r){const i=r;let e=A(!1);const p=t=>{e.value&&t.key==="Escape"&&(e.value=!1)};B(()=>document.addEventListener("keydown",p)),j(()=>document.removeEventListener("keydown",p));const h=b(()=>({48:"w-48"})[i.width.toString()]),C=b(()=>i.align==="left"?"ltr:origin-top-left rtl:origin-top-right start-0":i.align==="right"?"ltr:origin-top-right rtl:origin-top-left end-0":"origin-top");return(t,a)=>(l(),g("div",R,[s("div",{onClick:a[0]||(a[0]=k=>$(e)?e.value=!c(e):e=!c(e))},[d(t.$slots,"trigger")]),x(s("div",{class:"fixed inset-0 z-40",onClick:a[1]||(a[1]=k=>$(e)?e.value=!1:e=!1)},null,512),[[_,c(e)]]),n(D,{"enter-active-class":"transition ease-out duration-200","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:f(()=>[x(s("div",{class:m(["absolute z-50 mt-2 rounded-md shadow-lg",[h.value,C.value]]),style:{display:"none"},onClick:a[2]||(a[2]=k=>$(e)?e.value=!1:e=!1)},[s("div",{class:m(["rounded-md ring-1 ring-black ring-opacity-5",r.contentClasses])},[d(t.$slots,"content")],2)],2),[[_,c(e)]])]),_:3})]))}},K={key:0,type:"submit",class:"block w-full px-4 py-2 text-start leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out"},X=["href"],w={__name:"DropdownLink",props:{href:String,type:String},setup(r){return(i,e)=>r.type==="button"?(l(),g("button",K,[d(i.$slots,"default")])):r.type==="a"?(l(),g("a",{key:1,href:r.href,class:"block px-4 py-2 leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out"},[d(i.$slots,"default")],8,X)):(l(),v(c(S),{key:2,href:r.href,class:"block px-4 py-2 leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out"},{default:f(()=>[d(i.$slots,"default")]),_:3},8,["href"]))}},u={__name:"NavLink",props:{href:String,active:Boolean},setup(r){const i=r,e=b(()=>i.active?"link-active":"link-normal");return(p,h)=>(l(),v(c(S),{class:m(e.value),href:r.href},{default:f(()=>[d(p.$slots,"default")]),_:3},8,["class","href"]))}},Z={class:"mx-auto px-4 sm:mx-2"},G={class:"flex justify-between h-16"},Q={class:"flex"},W={class:"shrink-0 flex items-center"},Y={class:"flex space-x-8 -my-px ms-10"},tt={class:"flex items-center ms-6"},et={class:"ms-3 relative"},st={class:"inline-flex.rounded-md"},ot={type:"button",class:"inline-flex items-center px-3 py-2 border border-transparent leading-4 rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none focus:bg-gray-50 active:bg-gray-50 transition ease-in-out duration-150"},nt={key:0,class:"bg-white shadow"},rt={class:"mx-auto px-6 flex items-center font-semibold h-[70px] sm:mx-2"},at=["textContent"],it=["textContent"],lt={class:"flex items-center justify-end p-3"},ut=["textContent"],ht={__name:"AppLayout",props:{title:String},setup(r){const i=P(),e=E({confirmLogout:!1,open:!1}),p=()=>{e.confirmLogout=!0,setTimeout(()=>e.open=!0,150)},h=()=>{e.open=!1,setTimeout(()=>e.confirmLogout=!1,150)},C=()=>{N.post(route("logout"))};return(t,a)=>(l(),g(T,null,[n(c(L),{title:r.title},null,8,["title"]),s("div",{class:m(["min-h-screen bg-gray-100 font-light text-gray-700","locale-"+c(i).locale.value])},[s("nav",{class:m(["bg-white",[t.$slots.header?"border-b border-gray-100":"shadow"]])},[s("div",Z,[s("div",G,[s("div",Q,[s("div",W,[n(O)]),s("div",Y,[n(u,{href:t.route("user.list"),active:t.route().current("user.*"),textContent:o(t.$t("listUser"))},null,8,["href","active","textContent"]),n(u,{href:t.route("post.list"),active:t.route().current("post.*"),textContent:o(t.$t("listPost"))},null,8,["href","active","textContent"]),n(u,{href:t.route("postAnswer.list"),active:t.route().current("postAnswer.*"),textContent:o(t.$t("listPostAnswer"))},null,8,["href","active","textContent"]),n(u,{href:t.route("postViewHistory"),active:t.route().current("postViewHistory"),textContent:o(t.$t("postViewHistory"))},null,8,["href","active","textContent"]),n(u,{href:t.route("news.list"),active:t.route().current("news.*"),textContent:o(t.$t("news"))},null,8,["href","active","textContent"]),n(u,{href:t.route("quest.list"),active:t.route().current("quest.*"),textContent:o(t.$t("quest"))},null,8,["href","active","textContent"]),n(u,{href:t.route("premiumFeature.list"),active:t.route().current("premiumFeature.*"),textContent:o(t.$t("premiumFeature.label"))},null,8,["href","active","textContent"]),n(u,{href:t.route("system.settings"),active:t.route().current("system.settings"),textContent:o(t.$t("systemSetting"))},null,8,["href","active","textContent"])])]),s("div",tt,[s("div",et,[n(J,{align:"right",width:"48"},{trigger:f(()=>[s("span",st,[s("button",ot,[F(o(t.$page.props.auth.user.name)+" ",1),a[0]||(a[0]=s("svg",{class:"ms-2 -me-0.5 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 8.25l-7.5 7.5-7.5-7.5"})],-1))])])]),content:f(()=>[t.$page.props.jetstream.canUpdateProfileInformation?(l(),v(w,{key:0,href:t.route("profile.detail"),textContent:o(t.$t("profile"))},null,8,["href","textContent"])):y("",!0),t.$page.props.jetstream.canUpdatePassword?(l(),v(w,{key:1,href:t.route("profile.changePassword"),textContent:o(t.$t("changePassword"))},null,8,["href","textContent"])):y("",!0),a[1]||(a[1]=s("div",{class:"border-t border-gray-200"},null,-1)),n(w,{type:"button",textContent:o(t.$t("logout")),onClick:p},null,8,["textContent"])]),_:1})])])])])],2),t.$slots.header?(l(),g("header",nt,[s("div",rt,[d(t.$slots,"header")])])):y("",!0),s("main",{class:m(["mt-[2px]",[t.$slots.header?"":"without-header"]])},[d(t.$slots,"default")],2)],2),e.confirmLogout?(l(),v(z,{key:0,show:e.open,closeable:!0,size:"lg","padding-vertical":"py-20",onClose:h},{default:f(()=>[s("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:o(t.$t("confirm"))},null,8,at),s("div",{class:"border-t px-3 py-4 font-light",textContent:o(t.$t("logoutConfirmationText"))},null,8,it),a[2]||(a[2]=s("div",{class:"border-t"},null,-1)),s("div",lt,[n(U,{class:"mr-3 text-sm h-[38px]",textContent:o(t.$t("cancel")),onClick:h},null,8,["textContent"]),n(M,{class:"text-sm",onClick:C},{default:f(()=>[s("span",{class:"text-sm",textContent:o(t.$t("logout"))},null,8,ut)]),_:1})])]),_:1},8,["show"])):y("",!0)],64))}};export{ht as _};
