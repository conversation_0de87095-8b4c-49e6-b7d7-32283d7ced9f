import{_ as r}from"./LoadingIcon-CesYxFkK.js";import{c as l,o as e,k as s,K as n,J as t,R as i}from"./@vue-BnW70ngI.js";const g={__name:"GridContainer",props:{loading:<PERSON>olean},setup(o){return(a,c)=>(e(),l("div",{class:i(["bg-white rounded-md shadow overflow-auto mt-5 flex flex-col",{"grid-loading":o.loading}])},[o.loading?(e(),s(r,{key:0,class:"grid-loading-icon z-10",size:36,color:"rgb(2 132 199)"})):n("",!0),t(a.$slots,"default")],2))}};export{g as _};
