import{f as k,c as O}from"./index-DHV2tfOS.js";import{Q as A,T as F,i as p,N as L}from"./@inertiajs-BhKdJayA.js";import{s as h}from"./ziggy-js-RmARJSO4.js";import{s as q,a as d}from"./primevue-u0EmObz-.js";import{_ as K}from"./AppLayout-m_I9gnvX.js";import{_ as S}from"./InputLabel-BTXevqr4.js";import{_ as I}from"./SearchInput-CdoSYJL3.js";import{_ as Q}from"./Pagination-DDsmbrzN.js";import{_ as R}from"./LoadingIcon-CesYxFkK.js";import{_ as X,a as z}from"./SecondaryButton-BWHXZF7Q.js";import{_ as G}from"./RedButton-D21iPtqa.js";import{_ as H}from"./GridContainer-n7ZDMxOZ.js";import{i as J,v as W,b as Y,N as Z,c as f,o as m,l as o,k as y,K as v,S as l,a,a4 as s,P as i,O as $,F as B,X as C}from"./@vue-BnW70ngI.js";import"./moment-C5S46NFB.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./@primeuix-CNwdBq9K.js";import"./@primevue-Bw51iWDD.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./PrimaryButton-DE9sqoJj.js";import"./FixedSelectionBox-CwNS68U7.js";import"./SelectionBox-58uvzdoT.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-ccBf1-WH.js";import"./lodash-DBgjQQU6.js";import"./TextInput-C52bsWxF.js";import"./@headlessui-gOb5_P77.js";const ee=["textContent"],te={class:"p-6 sm:mx-2"},se={class:"flex items-stretch"},re={class:"mt-0 w-80 mr-8"},oe={class:"mt-0 w-80 mr-8"},le=["textContent"],ae=["textContent"],ne={class:"pi pi-info-circle text-gray-500"},ie={class:"pi pi-pen-to-square text-gray-300"},ue={class:"pi pi-chart-bar text-sky-400"},ce={class:"pi pi-database text-amber-400"},de={class:"pi pi-trash text-red-400"},me={key:0,class:"pi pi-pen-to-square text-gray-300"},he=["onClick"],pe=["textContent"],fe=["textContent"],ve={class:"flex items-center justify-end px-3 py-3"},_e=["textContent"],Ye={__name:"Index",props:{filters:Object,users:Object},setup(u){const D=J("$toast"),x=A(),_=u,c=F({id:_.filters.id??"",search:_.filters.search??"",limit:_.filters.limit??10}),r=W({showSearchClearButton:(_.filters.search??"").trim().length>0,showSearchIDClearButton:(_.filters.id??"").trim().length>0,searching:!1,showModal:!1,open:!1,deleteId:null,deleting:!1,activePage:null,busy:Y(()=>r.activePage!==null||r.searching||c.processing)}),g=()=>{if(c.processing)return!1;c.transform(e=>O(e)).get(h("user.list"),{preserveScroll:!0,onSuccess:()=>{}})},V=()=>{r.showSearchClearButton=!0,g()},N=()=>{c.search="",r.showSearchClearButton=!1,g()},P=()=>{r.showSearchIDClearButton=!0,g()},j=()=>{c.id="",r.showSearchIDClearButton=!1,g()},U=e=>{r.deleteId=e,r.showModal=!0,setTimeout(()=>r.open=!0,150)},w=()=>{r.open=!1,setTimeout(()=>r.showModal=!1,150)},M=()=>{r.deleting||L.post(h("user.delete"),{id:r.deleteId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{r.deleting=!0},onSuccess:()=>{r.deleteId=null,x.props.jetstream.flash.message&&D.success(x.props.jetstream.flash.message),w()},onFinish:()=>{r.deleting=!1}})},T=e=>{r.activePage=e,r.searching=!0},E=e=>{c.limit=e,c.id="",c.search="",g()};return(e,n)=>{const b=Z("tooltip");return m(),f(B,null,[o(K,{title:e.$t("listUser")},{header:l(()=>[a("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:i(e.$t("listUser"))},null,8,ee)]),default:l(()=>[a("div",te,[a("div",se,[a("div",re,[o(S,{for:"user-search",value:e.$t("userSearch")},null,8,["value"]),o(I,{id:"user-search",class:"mt-1 block w-full",modelValue:s(c).search,"onUpdate:modelValue":n[0]||(n[0]=t=>s(c).search=t),placeholder:e.$t("searchByNameOrPhone"),disabled:r.busy,"show-clear-button":r.showSearchClearButton,onInput:n[1]||(n[1]=t=>r.showSearchClearButton=!1),onClearSearch:N,onEnter:V},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),a("div",oe,[o(S,{for:"id-search",value:e.$t("ID")},null,8,["value"]),o(I,{id:"id-search",class:"mt-1 block w-full",modelValue:s(c).id,"onUpdate:modelValue":n[2]||(n[2]=t=>s(c).id=t),placeholder:e.$t("searchByID"),disabled:r.busy,"show-clear-button":r.showSearchIDClearButton,onInput:n[3]||(n[3]=t=>r.showSearchIDClearButton=!1),onClearSearch:j,onEnter:P},null,8,["modelValue","placeholder","disabled","show-clear-button"])])]),o(H,{loading:r.busy},{default:l(()=>[o(s(q),{value:u.users.data},{empty:l(()=>[$(i(e.$t(u.filters.search&&u.filters.search!==""||u.filters.id&&u.filters.id!==""?"emptyResult":"emptyData")),1)]),default:l(()=>[o(s(d),{class:"number-column",header:e.$t("ID")},{body:l(({data:t})=>[o(s(p),{class:"hover:text-sky-600 hover:underline",textContent:i(t.user_id),href:s(h)("user.detail",{user:t.user_id})},null,8,["textContent","href"])]),_:1},8,["header"]),o(s(d),{class:"time-column",header:e.$t("registerAt")},{body:l(({data:t})=>[$(i(s(k)(t.created_at)),1)]),_:1},8,["header"]),o(s(d),{class:"phone-column",field:"phone",header:e.$t("phone")},null,8,["header"]),o(s(d),{class:"username-column",field:"name",header:e.$t("username")},null,8,["header"]),o(s(d),{class:"role-column",header:e.$t("role")},{body:l(({data:t})=>[$(i(e.$t(t.role)),1)]),_:1},8,["header"]),o(s(d),{class:"count-column",header:e.$t("postCount")},{body:l(({data:t})=>[t.post_count?(m(),y(s(p),{key:0,class:"hover:text-red-600 hover:underline",textContent:i(t.post_count),href:s(h)("post.list",{user:t.user_id})},null,8,["textContent","href"])):(m(),f("span",{key:1,textContent:i(t.post_count)},null,8,le))]),_:1},8,["header"]),o(s(d),{class:"count-column",header:e.$t("answerCount")},{body:l(({data:t})=>[t.answer_count?(m(),y(s(p),{key:0,class:"hover:text-red-600 hover:underline",textContent:i(t.answer_count),href:s(h)("postAnswer.list",{user:t.user_id})},null,8,["textContent","href"])):(m(),f("span",{key:1,textContent:i(t.answer_count)},null,8,ae))]),_:1},8,["header"]),o(s(d),{class:"count-column",field:"comment_count",header:e.$t("commentCount")},null,8,["header"]),o(s(d),{class:"count-column",field:"best_answer_count",header:e.$t("bestAnswerCount")},null,8,["header"]),o(s(d),{class:"status-column",field:"status_label",header:e.$t("status")},null,8,["header"]),o(s(d),{class:"last-logged-time-column",header:e.$t("lastLoggedInTime")},{body:l(({data:t})=>[$(i(s(k)(t.last_logged_in_at)),1)]),_:1},8,["header"]),o(s(d),{class:"action-column extra"},{header:l(()=>[C(a("i",ne,null,512),[[b,e.$t("userInfo"),void 0,{top:!0}]]),C(a("i",ie,null,512),[[b,e.$t("edit"),void 0,{top:!0}]]),C(a("i",ue,null,512),[[b,e.$t("attribute"),void 0,{top:!0}]]),C(a("i",ce,null,512),[[b,e.$t("feed"),void 0,{top:!0}]]),C(a("i",de,null,512),[[b,e.$t("delete"),void 0,{top:!0}]])]),body:l(({data:t})=>[parseInt(t.status)!==0?(m(),f(B,{key:0},[o(s(p),{href:s(h)("user.detail",{user:t.user_id})},{default:l(()=>n[4]||(n[4]=[a("i",{class:"pi pi-info-circle text-gray-500 hover:text-sky-600"},null,-1)])),_:2},1032,["href"]),t.is_super_admin?v("",!0):(m(),f("i",me)),o(s(p),{href:s(h)("user.attribute",{user:t.user_id})},{default:l(()=>n[5]||(n[5]=[a("i",{class:"pi pi-chart-bar text-sky-400 hover:text-sky-600 hover:cursor-pointer"},null,-1)])),_:2},1032,["href"]),o(s(p),{href:s(h)("user.feed",{user:t.user_id})},{default:l(()=>n[6]||(n[6]=[a("i",{class:"pi pi-database text-amber-400 hover:text-amber-600 hover:cursor-pointer"},null,-1)])),_:2},1032,["href"]),t.is_super_admin?v("",!0):(m(),f("i",{key:1,class:"pi pi-trash text-red-400 hover:text-red-600 hover:cursor-pointer",onClick:ge=>U(t.user_id)},null,8,he))],64)):v("",!0)]),_:1})]),_:1},8,["value"])]),_:1},8,["loading"]),u.users.data.length>0?(m(),y(Q,{key:0,class:"mt-5 flex items-center justify-center mx-auto",links:u.users.links,active:r.activePage,disabled:r.busy,limit:u.users.per_page,total:u.users.total,from:u.users.from,to:u.users.to,onProgress:T,onChangeLimit:E},null,8,["links","active","disabled","limit","total","from","to"])):v("",!0)])]),_:1},8,["title"]),r.showModal?(m(),y(z,{key:0,show:r.open,onClose:w},{default:l(()=>[a("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:i(e.$t("confirm"))},null,8,pe),a("div",{class:"border-t border-b p-4",textContent:i(e.$t("confirmDeleteUserMessage"))},null,8,fe),a("div",ve,[o(X,{class:"mr-3 text-sm",textContent:i(e.$t("cancel")),onClick:w},null,8,["textContent"]),o(G,{class:"text-sm overflow-hidden h-[34px]",onClick:M},{default:l(()=>[r.deleting?(m(),y(R,{key:0,class:"mr-1"})):v("",!0),a("span",{class:"text-sm text-white",textContent:i(e.$t("delete"))},null,8,_e)]),_:1})])]),_:1},8,["show"])):v("",!0)],64)}}};export{Ye as default};
