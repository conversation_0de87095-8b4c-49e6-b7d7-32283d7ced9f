import{u as k}from"./vue-i18n-kWKo0idO.js";import{_ as g,a as b}from"./SecondaryButton-BoI1NwE9.js";import{_ as $}from"./RedButton-D21iPtqa.js";import{_ as B}from"./LoadingIcon-CLD0VpVl.js";import{_ as N}from"./CommunityFormContent.vue_vue_type_script_setup_true_lang-Dl2CgMFy.js";import{r as u,v as S,j as P,k as d,K as f,o as _,S as h,a as r,l as n,P as l,a4 as p}from"./@vue-BnW70ngI.js";import"./@intlify-xvnhHnag.js";/* empty css                                                    */import"./ImageInput-BV1wAASf.js";import"./InputError-gQdwtcoE.js";import"./TextAreaInput-DHjed6qD.js";import"./TextInput-DUNPEFms.js";import"./@inertiajs-Dt0-hqjZ.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./ziggy-js-C7EU8ifa.js";const V=["textContent"],j={class:"flex items-center justify-end px-3 py-3"},T=["textContent"],vt={__name:"CommunityFormModal",props:{showModal:Boolean},emits:["closeModal","communityCreated"],setup(x,{emit:C}){const{t:s}=k(),c=C,m=x,a=u(!1),o=u(!1),v={name:"",description:"",image:""},t=S({showModal:!1,open:!1});P(()=>m.showModal,()=>{t.showModal=m.showModal,m.showModal&&setTimeout(()=>t.open=!0,150)});const i=()=>{o.value||(t.open=!1,setTimeout(()=>{t.showModal=!1,c("closeModal")},150))},w=()=>{o.value||(a.value=!0)},y=e=>{c("communityCreated",e),o.value=!1,i()},M=e=>{o.value=e,e||(a.value=!1)};return(e,D)=>t.showModal?(_(),d(b,{key:0,show:t.open,onClose:i,class:"community-form-modal"},{default:h(()=>[r("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:l(p(s)("addNewCommunity"))},null,8,V),n(N,{community:v,submit:a.value,action:"create",onCommunitySaved:y,onProcessing:M},null,8,["submit"]),r("div",j,[n(g,{class:"mr-3 text-sm",textContent:l(p(s)("cancel")),onClick:i},null,8,["textContent"]),n($,{class:"text-sm overflow-hidden h-[34px]",onClick:w},{default:h(()=>[o.value?(_(),d(B,{key:0,class:"mr-1"})):f("",!0),r("span",{class:"text-sm text-white",textContent:l(p(s)("save"))},null,8,T)]),_:1})])]),_:1},8,["show"])):f("",!0)}};export{vt as default};
