var K=String.prototype.replace,W=/%20/g,A={RFC1738:"RFC1738",RFC3986:"RFC3986"},Q={default:A.RFC3986,formatters:{RFC1738:function(l){return K.call(l,W,"+")},RFC3986:function(l){return String(l)}},RFC1738:A.RFC1738,RFC3986:A.RFC3986},X=Q,C=Object.prototype.hasOwnProperty,O=Array.isArray,b=function(){for(var l=[],e=0;e<256;++e)l.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return l}(),Y=function(e){for(;e.length>1;){var r=e.pop(),t=r.obj[r.prop];if(O(t)){for(var i=[],a=0;a<t.length;++a)typeof t[a]<"u"&&i.push(t[a]);r.obj[r.prop]=i}}},Z=function(e,r){for(var t=r&&r.plainObjects?Object.create(null):{},i=0;i<e.length;++i)typeof e[i]<"u"&&(t[i]=e[i]);return t},J=function l(e,r,t){if(!r)return e;if(typeof r!="object"){if(O(e))e.push(r);else if(e&&typeof e=="object")(t&&(t.plainObjects||t.allowPrototypes)||!C.call(Object.prototype,r))&&(e[r]=!0);else return[e,r];return e}if(!e||typeof e!="object")return[e].concat(r);var i=e;return O(e)&&!O(r)&&(i=Z(e,t)),O(e)&&O(r)?(r.forEach(function(a,n){if(C.call(e,n)){var s=e[n];s&&typeof s=="object"&&a&&typeof a=="object"?e[n]=l(s,a,t):e.push(a)}else e[n]=a}),e):Object.keys(r).reduce(function(a,n){var s=r[n];return C.call(a,n)?a[n]=l(a[n],s,t):a[n]=s,a},i)},ee=function(e,r){return Object.keys(r).reduce(function(t,i){return t[i]=r[i],t},e)},re=function(l,e,r){var t=l.replace(/\+/g," ");if(r==="iso-8859-1")return t.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(t)}catch{return t}},te=function(e,r,t,i,a){if(e.length===0)return e;var n=e;if(typeof e=="symbol"?n=Symbol.prototype.toString.call(e):typeof e!="string"&&(n=String(e)),t==="iso-8859-1")return escape(n).replace(/%u[0-9a-f]{4}/gi,function(f){return"%26%23"+parseInt(f.slice(2),16)+"%3B"});for(var s="",u=0;u<n.length;++u){var o=n.charCodeAt(u);if(o===45||o===46||o===95||o===126||o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||a===X.RFC1738&&(o===40||o===41)){s+=n.charAt(u);continue}if(o<128){s=s+b[o];continue}if(o<2048){s=s+(b[192|o>>6]+b[128|o&63]);continue}if(o<55296||o>=57344){s=s+(b[224|o>>12]+b[128|o>>6&63]+b[128|o&63]);continue}u+=1,o=65536+((o&1023)<<10|n.charCodeAt(u)&1023),s+=b[240|o>>18]+b[128|o>>12&63]+b[128|o>>6&63]+b[128|o&63]}return s},ne=function(e){for(var r=[{obj:{o:e},prop:"o"}],t=[],i=0;i<r.length;++i)for(var a=r[i],n=a.obj[a.prop],s=Object.keys(n),u=0;u<s.length;++u){var o=s[u],f=n[o];typeof f=="object"&&f!==null&&t.indexOf(f)===-1&&(r.push({obj:n,prop:o}),t.push(f))}return Y(r),e},ie=function(e){return Object.prototype.toString.call(e)==="[object RegExp]"},ae=function(e){return!e||typeof e!="object"?!1:!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},le=function(e,r){return[].concat(e,r)},oe=function(e,r){if(O(e)){for(var t=[],i=0;i<e.length;i+=1)t.push(r(e[i]));return t}return r(e)},_={arrayToObject:Z,assign:ee,combine:le,compact:ne,decode:re,encode:te,isBuffer:ae,isRegExp:ie,maybeMap:oe,merge:J},R=_,S=Q,se=Object.prototype.hasOwnProperty,B={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,r){return e+"["+r+"]"},repeat:function(e){return e}},x=Array.isArray,ue=String.prototype.split,fe=Array.prototype.push,V=function(l,e){fe.apply(l,x(e)?e:[e])},ce=Date.prototype.toISOString,U=S.default,v={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:R.encode,encodeValuesOnly:!1,format:U,formatter:S.formatters[U],indices:!1,serializeDate:function(e){return ce.call(e)},skipNulls:!1,strictNullHandling:!1},de=function(e){return typeof e=="string"||typeof e=="number"||typeof e=="boolean"||typeof e=="symbol"||typeof e=="bigint"},he=function l(e,r,t,i,a,n,s,u,o,f,p,d,y,h){var c=e;if(typeof s=="function"?c=s(r,c):c instanceof Date?c=f(c):t==="comma"&&x(c)&&(c=R.maybeMap(c,function(F){return F instanceof Date?f(F):F})),c===null){if(i)return n&&!y?n(r,v.encoder,h,"key",p):r;c=""}if(de(c)||R.isBuffer(c)){if(n){var L=y?r:n(r,v.encoder,h,"key",p);if(t==="comma"&&y){for(var H=ue.call(String(c),","),I="",E=0;E<H.length;++E)I+=(E===0?"":",")+d(n(H[E],v.encoder,h,"value",p));return[d(L)+"="+I]}return[d(L)+"="+d(n(c,v.encoder,h,"value",p))]}return[d(r)+"="+d(String(c))]}var N=[];if(typeof c>"u")return N;var j;if(t==="comma"&&x(c))j=[{value:c.length>0?c.join(",")||null:void 0}];else if(x(s))j=s;else{var q=Object.keys(c);j=u?q.sort(u):q}for(var P=0;P<j.length;++P){var w=j[P],z=typeof w=="object"&&typeof w.value<"u"?w.value:c[w];if(!(a&&z===null)){var G=x(c)?typeof t=="function"?t(r,w):r:r+(o?"."+w:"["+w+"]");V(N,l(z,G,t,i,a,n,s,u,o,f,p,d,y,h))}}return N},pe=function(e){if(!e)return v;if(e.encoder!==null&&typeof e.encoder<"u"&&typeof e.encoder!="function")throw new TypeError("Encoder has to be a function.");var r=e.charset||v.charset;if(typeof e.charset<"u"&&e.charset!=="utf-8"&&e.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=S.default;if(typeof e.format<"u"){if(!se.call(S.formatters,e.format))throw new TypeError("Unknown format option provided.");t=e.format}var i=S.formatters[t],a=v.filter;return(typeof e.filter=="function"||x(e.filter))&&(a=e.filter),{addQueryPrefix:typeof e.addQueryPrefix=="boolean"?e.addQueryPrefix:v.addQueryPrefix,allowDots:typeof e.allowDots>"u"?v.allowDots:!!e.allowDots,charset:r,charsetSentinel:typeof e.charsetSentinel=="boolean"?e.charsetSentinel:v.charsetSentinel,delimiter:typeof e.delimiter>"u"?v.delimiter:e.delimiter,encode:typeof e.encode=="boolean"?e.encode:v.encode,encoder:typeof e.encoder=="function"?e.encoder:v.encoder,encodeValuesOnly:typeof e.encodeValuesOnly=="boolean"?e.encodeValuesOnly:v.encodeValuesOnly,filter:a,format:t,formatter:i,serializeDate:typeof e.serializeDate=="function"?e.serializeDate:v.serializeDate,skipNulls:typeof e.skipNulls=="boolean"?e.skipNulls:v.skipNulls,sort:typeof e.sort=="function"?e.sort:null,strictNullHandling:typeof e.strictNullHandling=="boolean"?e.strictNullHandling:v.strictNullHandling}},ye=function(l,e){var r=l,t=pe(e),i,a;typeof t.filter=="function"?(a=t.filter,r=a("",r)):x(t.filter)&&(a=t.filter,i=a);var n=[];if(typeof r!="object"||r===null)return"";var s;e&&e.arrayFormat in B?s=e.arrayFormat:e&&"indices"in e?s=e.indices?"indices":"repeat":s="indices";var u=B[s];i||(i=Object.keys(r)),t.sort&&i.sort(t.sort);for(var o=0;o<i.length;++o){var f=i[o];t.skipNulls&&r[f]===null||V(n,he(r[f],f,u,t.strictNullHandling,t.skipNulls,t.encode?t.encoder:null,t.filter,t.sort,t.allowDots,t.serializeDate,t.format,t.formatter,t.encodeValuesOnly,t.charset))}var p=n.join(t.delimiter),d=t.addQueryPrefix===!0?"?":"";return t.charsetSentinel&&(t.charset==="iso-8859-1"?d+="utf8=%26%2310003%3B&":d+="utf8=%E2%9C%93&"),p.length>0?d+p:""},$=_,T=Object.prototype.hasOwnProperty,me=Array.isArray,m={allowDots:!1,allowPrototypes:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:$.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},ve=function(l){return l.replace(/&#(\d+);/g,function(e,r){return String.fromCharCode(parseInt(r,10))})},k=function(l,e){return l&&typeof l=="string"&&e.comma&&l.indexOf(",")>-1?l.split(","):l},ge="utf8=%26%2310003%3B",be="utf8=%E2%9C%93",we=function(e,r){var t={},i=r.ignoreQueryPrefix?e.replace(/^\?/,""):e,a=r.parameterLimit===1/0?void 0:r.parameterLimit,n=i.split(r.delimiter,a),s=-1,u,o=r.charset;if(r.charsetSentinel)for(u=0;u<n.length;++u)n[u].indexOf("utf8=")===0&&(n[u]===be?o="utf-8":n[u]===ge&&(o="iso-8859-1"),s=u,u=n.length);for(u=0;u<n.length;++u)if(u!==s){var f=n[u],p=f.indexOf("]="),d=p===-1?f.indexOf("="):p+1,y,h;d===-1?(y=r.decoder(f,m.decoder,o,"key"),h=r.strictNullHandling?null:""):(y=r.decoder(f.slice(0,d),m.decoder,o,"key"),h=$.maybeMap(k(f.slice(d+1),r),function(c){return r.decoder(c,m.decoder,o,"value")})),h&&r.interpretNumericEntities&&o==="iso-8859-1"&&(h=ve(h)),f.indexOf("[]=")>-1&&(h=me(h)?[h]:h),T.call(t,y)?t[y]=$.combine(t[y],h):t[y]=h}return t},Oe=function(l,e,r,t){for(var i=t?e:k(e,r),a=l.length-1;a>=0;--a){var n,s=l[a];if(s==="[]"&&r.parseArrays)n=[].concat(i);else{n=r.plainObjects?Object.create(null):{};var u=s.charAt(0)==="["&&s.charAt(s.length-1)==="]"?s.slice(1,-1):s,o=parseInt(u,10);!r.parseArrays&&u===""?n={0:i}:!isNaN(o)&&s!==u&&String(o)===u&&o>=0&&r.parseArrays&&o<=r.arrayLimit?(n=[],n[o]=i):u!=="__proto__"&&(n[u]=i)}i=n}return i},xe=function(e,r,t,i){if(e){var a=t.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,n=/(\[[^[\]]*])/,s=/(\[[^[\]]*])/g,u=t.depth>0&&n.exec(a),o=u?a.slice(0,u.index):a,f=[];if(o){if(!t.plainObjects&&T.call(Object.prototype,o)&&!t.allowPrototypes)return;f.push(o)}for(var p=0;t.depth>0&&(u=s.exec(a))!==null&&p<t.depth;){if(p+=1,!t.plainObjects&&T.call(Object.prototype,u[1].slice(1,-1))&&!t.allowPrototypes)return;f.push(u[1])}return u&&f.push("["+a.slice(u.index)+"]"),Oe(f,r,t,i)}},$e=function(e){if(!e)return m;if(e.decoder!==null&&e.decoder!==void 0&&typeof e.decoder!="function")throw new TypeError("Decoder has to be a function.");if(typeof e.charset<"u"&&e.charset!=="utf-8"&&e.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=typeof e.charset>"u"?m.charset:e.charset;return{allowDots:typeof e.allowDots>"u"?m.allowDots:!!e.allowDots,allowPrototypes:typeof e.allowPrototypes=="boolean"?e.allowPrototypes:m.allowPrototypes,arrayLimit:typeof e.arrayLimit=="number"?e.arrayLimit:m.arrayLimit,charset:r,charsetSentinel:typeof e.charsetSentinel=="boolean"?e.charsetSentinel:m.charsetSentinel,comma:typeof e.comma=="boolean"?e.comma:m.comma,decoder:typeof e.decoder=="function"?e.decoder:m.decoder,delimiter:typeof e.delimiter=="string"||$.isRegExp(e.delimiter)?e.delimiter:m.delimiter,depth:typeof e.depth=="number"||e.depth===!1?+e.depth:m.depth,ignoreQueryPrefix:e.ignoreQueryPrefix===!0,interpretNumericEntities:typeof e.interpretNumericEntities=="boolean"?e.interpretNumericEntities:m.interpretNumericEntities,parameterLimit:typeof e.parameterLimit=="number"?e.parameterLimit:m.parameterLimit,parseArrays:e.parseArrays!==!1,plainObjects:typeof e.plainObjects=="boolean"?e.plainObjects:m.plainObjects,strictNullHandling:typeof e.strictNullHandling=="boolean"?e.strictNullHandling:m.strictNullHandling}},je=function(l,e){var r=$e(e);if(l===""||l===null||typeof l>"u")return r.plainObjects?Object.create(null):{};for(var t=typeof l=="string"?we(l,r):l,i=r.plainObjects?Object.create(null):{},a=Object.keys(t),n=0;n<a.length;++n){var s=a[n],u=xe(s,t[s],r,typeof l=="string");i=$.merge(i,u,r)}return $.compact(i)},Se=ye,Ee=je,Ne=Q,M={formats:Ne,parse:Ee,stringify:Se};function g(){return g=Object.assign?Object.assign.bind():function(l){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var t in r)({}).hasOwnProperty.call(r,t)&&(l[t]=r[t])}return l},g.apply(null,arguments)}class D{constructor(e,r,t){var i,a;this.name=e,this.definition=r,this.bindings=(i=r.bindings)!=null?i:{},this.wheres=(a=r.wheres)!=null?a:{},this.config=t}get template(){const e=`${this.origin}/${this.definition.uri}`.replace(/\/+$/,"");return e===""?"/":e}get origin(){return this.config.absolute?this.definition.domain?`${this.config.url.match(/^\w+:\/\//)[0]}${this.definition.domain}${this.config.port?`:${this.config.port}`:""}`:this.config.url:""}get parameterSegments(){var e,r;return(e=(r=this.template.match(/{[^}?]+\??}/g))==null?void 0:r.map(t=>({name:t.replace(/{|\??}/g,""),required:!/\?}$/.test(t)})))!=null?e:[]}matchesUrl(e){var r;if(!this.definition.methods.includes("GET"))return!1;const t=this.template.replace(/[.*+$()[\]]/g,"\\$&").replace(/(\/?){([^}?]*)(\??)}/g,(s,u,o,f)=>{var p;const d=`(?<${o}>${((p=this.wheres[o])==null?void 0:p.replace(/(^\^)|(\$$)/g,""))||"[^/?]+"})`;return f?`(${u}${d})?`:`${u}${d}`}).replace(/^\w+:\/\//,""),[i,a]=e.replace(/^\w+:\/\//,"").split("?"),n=(r=new RegExp(`^${t}/?$`).exec(i))!=null?r:new RegExp(`^${t}/?$`).exec(decodeURI(i));if(n){for(const s in n.groups)n.groups[s]=typeof n.groups[s]=="string"?decodeURIComponent(n.groups[s]):n.groups[s];return{params:n.groups,query:M.parse(a)}}return!1}compile(e){return this.parameterSegments.length?this.template.replace(/{([^}?]+)(\??)}/g,(r,t,i)=>{var a,n;if(!i&&[null,void 0].includes(e[t]))throw new Error(`Ziggy error: '${t}' parameter is required for route '${this.name}'.`);if(this.wheres[t]&&!new RegExp(`^${i?`(${this.wheres[t]})?`:this.wheres[t]}$`).test((n=e[t])!=null?n:""))throw new Error(`Ziggy error: '${t}' parameter '${e[t]}' does not match required format '${this.wheres[t]}' for route '${this.name}'.`);return encodeURI((a=e[t])!=null?a:"").replace(/%7C/g,"|").replace(/%25/g,"%").replace(/\$/g,"%24")}).replace(this.config.absolute?/(\.[^/]+?)(\/\/)/:/(^)(\/\/)/,"$1/").replace(/\/+$/,""):this.template}}class Pe extends String{constructor(e,r,t=!0,i){if(super(),this.t=i??(typeof Ziggy<"u"?Ziggy:globalThis==null?void 0:globalThis.Ziggy),this.t=g({},this.t,{absolute:t}),e){if(!this.t.routes[e])throw new Error(`Ziggy error: route '${e}' is not in the route list.`);this.i=new D(e,this.t.routes[e],this.t),this.o=this.u(r)}}toString(){const e=Object.keys(this.o).filter(r=>!this.i.parameterSegments.some(({name:t})=>t===r)).filter(r=>r!=="_query").reduce((r,t)=>g({},r,{[t]:this.o[t]}),{});return this.i.compile(this.o)+M.stringify(g({},e,this.o._query),{addQueryPrefix:!0,arrayFormat:"indices",encodeValuesOnly:!0,skipNulls:!0,encoder:(r,t)=>typeof r=="boolean"?Number(r):t(r)})}h(e){e?this.t.absolute&&e.startsWith("/")&&(e=this.l().host+e):e=this.m();let r={};const[t,i]=Object.entries(this.t.routes).find(([a,n])=>r=new D(a,n,this.t).matchesUrl(e))||[void 0,void 0];return g({name:t},r,{route:i})}m(){const{host:e,pathname:r,search:t}=this.l();return(this.t.absolute?e+r:r.replace(this.t.url.replace(/^\w*:\/\/[^/]+/,""),"").replace(/^\/+/,"/"))+t}current(e,r){const{name:t,params:i,query:a,route:n}=this.h();if(!e)return t;const s=new RegExp(`^${e.replace(/\./g,"\\.").replace(/\*/g,".*")}$`).test(t);if([null,void 0].includes(r)||!s)return s;const u=new D(t,n,this.t);r=this.u(r,u);const o=g({},i,a);if(Object.values(r).every(p=>!p)&&!Object.values(o).some(p=>p!==void 0))return!0;const f=(p,d)=>Object.entries(p).every(([y,h])=>Array.isArray(h)&&Array.isArray(d[y])?h.every(c=>d[y].includes(c)):typeof h=="object"&&typeof d[y]=="object"&&h!==null&&d[y]!==null?f(h,d[y]):d[y]==h);return f(r,o)}l(){var e,r,t,i,a,n;const{host:s="",pathname:u="",search:o=""}=typeof window<"u"?window.location:{};return{host:(e=(r=this.t.location)==null?void 0:r.host)!=null?e:s,pathname:(t=(i=this.t.location)==null?void 0:i.pathname)!=null?t:u,search:(a=(n=this.t.location)==null?void 0:n.search)!=null?a:o}}get params(){const{params:e,query:r}=this.h();return g({},e,r)}get routeParams(){return this.h().params}get queryParams(){return this.h().query}has(e){return this.t.routes.hasOwnProperty(e)}u(e={},r=this.i){e!=null||(e={}),e=["string","number"].includes(typeof e)?[e]:e;const t=r.parameterSegments.filter(({name:i})=>!this.t.defaults[i]);return Array.isArray(e)?e=e.reduce((i,a,n)=>g({},i,t[n]?{[t[n].name]:a}:typeof a=="object"?a:{[a]:""}),{}):t.length!==1||e[t[0].name]||!e.hasOwnProperty(Object.values(r.bindings)[0])&&!e.hasOwnProperty("id")||(e={[t[0].name]:e}),g({},this.$(r),this.p(e,r))}$(e){return e.parameterSegments.filter(({name:r})=>this.t.defaults[r]).reduce((r,{name:t},i)=>g({},r,{[t]:this.t.defaults[t]}),{})}p(e,{bindings:r,parameterSegments:t}){return Object.entries(e).reduce((i,[a,n])=>{if(!n||typeof n!="object"||Array.isArray(n)||!t.some(({name:s})=>s===a))return g({},i,{[a]:n});if(!n.hasOwnProperty(r[a])){if(!n.hasOwnProperty("id"))throw new Error(`Ziggy error: object passed as '${a}' parameter is missing route model binding key '${r[a]}'.`);r[a]="id"}return g({},i,{[a]:n[r[a]]})},{})}valueOf(){return this.toString()}}function Fe(l,e,r,t){const i=new Pe(l,e,r,t);return l?i.toString():i}const Ae={install(l,e){const r=(t,i,a,n=e)=>Fe(t,i,a,n);parseInt(l.version)>2?(l.config.globalProperties.route=r,l.provide("route",r)):l.mixin({methods:{route:r}})}};export{Ae as o,Fe as s};
