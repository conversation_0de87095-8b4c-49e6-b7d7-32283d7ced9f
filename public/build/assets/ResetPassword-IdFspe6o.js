import{c,o as f,l as o,a4 as e,S as l,a as t,_ as w,R as _,O as g,F as V}from"./@vue-BnW70ngI.js";import{T as k,Z as v}from"./@inertiajs-BhKdJayA.js";import{A as b,a as x}from"./AuthenticationCardLogo-C3QjX6Mv.js";import{_ as m}from"./InputError-gQdwtcoE.js";import{_ as i}from"./InputLabel-BTXevqr4.js";import{_ as y}from"./PrimaryButton-DE9sqoJj.js";import{_ as n}from"./TextInput-C52bsWxF.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const C={class:"mt-4"},P={class:"mt-4"},R={class:"flex items-center justify-end mt-4"},O={__name:"ResetPassword",props:{email:String,token:String},setup(p){const d=p,s=k({token:d.token,email:d.email,password:"",password_confirmation:""}),u=()=>{s.post(route("password.update"),{onFinish:()=>s.reset("password","password_confirmation")})};return(S,a)=>(f(),c(V,null,[o(e(v),{title:"Reset Password"}),o(x,null,{logo:l(()=>[o(b)]),default:l(()=>[t("form",{onSubmit:w(u,["prevent"])},[t("div",null,[o(i,{for:"email",value:"Email"}),o(n,{id:"email",modelValue:e(s).email,"onUpdate:modelValue":a[0]||(a[0]=r=>e(s).email=r),type:"email",class:"mt-1 block w-full",required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),o(m,{class:"mt-2",message:e(s).errors.email},null,8,["message"])]),t("div",C,[o(i,{for:"password",value:"Password"}),o(n,{id:"password",modelValue:e(s).password,"onUpdate:modelValue":a[1]||(a[1]=r=>e(s).password=r),type:"password",class:"mt-1 block w-full",required:"",autocomplete:"new-password"},null,8,["modelValue"]),o(m,{class:"mt-2",message:e(s).errors.password},null,8,["message"])]),t("div",P,[o(i,{for:"password_confirmation",value:"Confirm Password"}),o(n,{id:"password_confirmation",modelValue:e(s).password_confirmation,"onUpdate:modelValue":a[2]||(a[2]=r=>e(s).password_confirmation=r),type:"password",class:"mt-1 block w-full",required:"",autocomplete:"new-password"},null,8,["modelValue"]),o(m,{class:"mt-2",message:e(s).errors.password_confirmation},null,8,["message"])]),t("div",R,[o(y,{class:_({"opacity-25":e(s).processing}),disabled:e(s).processing},{default:l(()=>a[3]||(a[3]=[g(" Reset Password ")])),_:1},8,["class","disabled"])])],32)]),_:1})],64))}};export{O as default};
