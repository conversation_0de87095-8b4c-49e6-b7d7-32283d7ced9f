import{i as d,c as _,o as l,a as r,l as o,a4 as s,S as $,k as g,K as v,P as V,R as b,_ as h}from"./@vue-BnW70ngI.js";import{u as x}from"./vue-i18n-DNS8h1FH.js";import{Q as y,T as k}from"./@inertiajs-BhKdJayA.js";import{_ as w}from"./InputError-gQdwtcoE.js";import{_ as i}from"./InputLabel-BTXevqr4.js";import{_ as P}from"./LoadingIcon-CesYxFkK.js";import{_ as S}from"./PrimaryButton-DE9sqoJj.js";import{_ as n}from"./TextInput-C52bsWxF.js";import"./@intlify-TnaUIxGf.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";const B={class:"mt-0"},C={class:"mt-4"},I={class:"flex items-center justify-end text-end mt-5"},N=["textContent"],J={__name:"UpdateProfileInformationForm",props:{user:Object},setup(p){const u=d("$toast"),{t:c}=x();y();const e=k({_method:"PUT",name:p.user.name}),f=()=>{e.post(route("user-profile-information.update"),{errorBag:"updateProfileInformation",preserveScroll:!0,onSuccess:()=>{u.success(c("updateProfileSuccessfully"))}})};return(t,a)=>(l(),_("form",{class:"px-6 py-5 bg-white shadow rounded-md mx-auto sm:max-w-xl",onSubmit:h(f,["prevent"]),autocomplete:"off"},[r("div",B,[o(i,{for:"name",value:t.$t("profileName")},null,8,["value"]),o(n,{id:"name",modelValue:s(e).name,"onUpdate:modelValue":a[0]||(a[0]=m=>s(e).name=m),type:"text",class:"mt-1 block w-full",autocomplete:"off"},null,8,["modelValue"]),o(w,{class:"mt-2",message:s(e).errors.name},null,8,["message"])]),r("div",C,[o(i,{for:"email",value:t.$t("email")},null,8,["value"]),o(n,{id:"email",modelValue:t.$page.props.auth.user.email,"onUpdate:modelValue":a[1]||(a[1]=m=>t.$page.props.auth.user.email=m),type:"text",class:"mt-1 block w-full",autocomplete:"off",disabled:""},null,8,["modelValue"])]),r("div",I,[o(S,{class:b({"opacity-25":s(e).processing}),disabled:s(e).processing},{default:$(()=>[s(e).processing?(l(),g(P,{key:0,class:"mr-2"})):v("",!0),r("span",{textContent:V(t.$t("save"))},null,8,N)]),_:1},8,["class","disabled"])])],32))}};export{J as default};
