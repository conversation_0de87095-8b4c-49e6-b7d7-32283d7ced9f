import{Q as c}from"./quill-D-mw74c0.js";import{D}from"./quill-delta-D18WSM5Q.js";import{d as R,h as F,e as U,A as V,r as k,j as f,n as M}from"./@vue-BnW70ngI.js";/*!
 * VueQuill @vueup/vue-quill v1.2.0
 * https://vueup.github.io/vue-quill/
 * 
 * Includes quill v1.3.7
 * https://quilljs.com/
 * 
 * Copyright (c) 2023 Ahmad <PERSON>
 * Released under the MIT license
 * Date: 2023-05-12T08:44:03.742Z
 */const A={essential:[[{header:[1,2,3,4,5,6,!1]}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"},{align:[]}],["blockquote","code-block","link"],[{color:[]},"clean"]],minimal:[[{header:1},{header:2}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"},{align:[]}]],full:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}],["link","video","image"],["clean"]]},K=R({name:"QuillEditor",inheritAttrs:!1,props:{content:{type:[String,Object]},contentType:{type:String,default:"delta",validator:o=>["delta","html","text"].includes(o)},enable:{type:Boolean,default:!0},readOnly:{type:Boolean,default:!1},placeholder:{type:String,required:!1},theme:{type:String,default:"snow",validator:o=>["snow","bubble",""].includes(o)},toolbar:{type:[String,Array,Object],required:!1,validator:o=>typeof o=="string"&&o!==""?o.charAt(0)==="#"?!0:Object.keys(A).indexOf(o)!==-1:!0},modules:{type:Object,required:!1},options:{type:Object,required:!1},globalOptions:{type:Object,required:!1}},emits:["textChange","selectionChange","editorChange","update:content","focus","blur","ready"],setup:(o,i)=>{U(()=>{b()}),V(()=>{t=null});let t,m;const r=k(),b=()=>{var e;if(r.value){if(m=_(),o.modules)if(Array.isArray(o.modules))for(const n of o.modules)c.register(`modules/${n.name}`,n.module);else c.register(`modules/${o.modules.name}`,o.modules.module);t=new c(r.value,m),d(o.content),t.on("text-change",L),t.on("selection-change",S),t.on("editor-change",E),o.theme!=="bubble"&&r.value.classList.remove("ql-bubble"),o.theme!=="snow"&&r.value.classList.remove("ql-snow"),(e=t.getModule("toolbar"))===null||e===void 0||e.container.addEventListener("mousedown",n=>{n.preventDefault()}),i.emit("ready",t)}},_=()=>{const e={};if(o.theme!==""&&(e.theme=o.theme),o.readOnly&&(e.readOnly=o.readOnly),o.placeholder&&(e.placeholder=o.placeholder),o.toolbar&&o.toolbar!==""&&(e.modules={toolbar:(()=>{if(typeof o.toolbar=="object")return o.toolbar;if(typeof o.toolbar=="string")return o.toolbar.charAt(0)==="#"?o.toolbar:A[o.toolbar]})()}),o.modules){const n=(()=>{var l,s;const u={};if(Array.isArray(o.modules))for(const q of o.modules)u[q.name]=(l=q.options)!==null&&l!==void 0?l:{};else u[o.modules.name]=(s=o.modules.options)!==null&&s!==void 0?s:{};return u})();e.modules=Object.assign({},e.modules,n)}return Object.assign({},o.globalOptions,o.options,e)},v=e=>typeof e=="object"&&e?e.slice():e,w=e=>Object.values(e.ops).some(n=>!n.retain||Object.keys(n).length!==1);let a;const h=e=>{if(typeof a==typeof e){if(e===a)return!0;if(typeof e=="object"&&e&&typeof a=="object"&&a)return!w(a.diff(e))}return!1},L=(e,n,l)=>{a=v(g()),h(o.content)||i.emit("update:content",a),i.emit("textChange",{delta:e,oldContents:n,source:l})},y=k(),S=(e,n,l)=>{y.value=!!(t!=null&&t.hasFocus()),i.emit("selectionChange",{range:e,oldRange:n,source:l})};f(y,e=>{e?i.emit("focus",r):i.emit("blur",r)});const E=(...e)=>{e[0]==="text-change"&&i.emit("editorChange",{name:e[0],delta:e[1],oldContents:e[2],source:e[3]}),e[0]==="selection-change"&&i.emit("editorChange",{name:e[0],range:e[1],oldRange:e[2],source:e[3]})},x=()=>r.value,H=()=>{var e;return(e=t==null?void 0:t.getModule("toolbar"))===null||e===void 0?void 0:e.container},Q=()=>{if(t)return t;throw`The quill editor hasn't been instantiated yet,
                  make sure to call this method when the editor ready
                  or use v-on:ready="onReady(quill)" event instead.`},g=(e,n)=>o.contentType==="html"?C():o.contentType==="text"?O(e,n):t==null?void 0:t.getContents(e,n),d=(e,n="api")=>{const l=e||(o.contentType==="delta"?new D:"");o.contentType==="html"?j(l):o.contentType==="text"?T(l,n):t==null||t.setContents(l,n),a=v(l)},O=(e,n)=>{var l;return(l=t==null?void 0:t.getText(e,n))!==null&&l!==void 0?l:""},T=(e,n="api")=>{t==null||t.setText(e,n)},C=()=>{var e;return(e=t==null?void 0:t.root.innerHTML)!==null&&e!==void 0?e:""},j=e=>{t&&(t.root.innerHTML=e)},$=(e,n="api")=>{const l=t==null?void 0:t.clipboard.convert(e);l&&(t==null||t.setContents(l,n))},z=()=>{t==null||t.focus()},B=()=>{M(()=>{var e;!i.slots.toolbar&&t&&((e=t.getModule("toolbar"))===null||e===void 0||e.container.remove()),b()})};return f(()=>o.content,e=>{if(!t||!e||h(e))return;const n=t.getSelection();n&&M(()=>t==null?void 0:t.setSelection(n)),d(e)},{deep:!0}),f(()=>o.enable,e=>{t&&t.enable(e)}),{editor:r,getEditor:x,getToolbar:H,getQuill:Q,getContents:g,setContents:d,getHTML:C,setHTML:j,pasteHTML:$,focus:z,getText:O,setText:T,reinit:B}},render(){var o,i;return[(i=(o=this.$slots).toolbar)===null||i===void 0?void 0:i.call(o),F("div",{ref:"editor",...this.$attrs})]}});export{K as Q};
