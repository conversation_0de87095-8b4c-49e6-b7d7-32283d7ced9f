import{c as s,o as t,l as o,S as c,X as n,a as i,J as r,a0 as l,U as d}from"./@vue-BnW70ngI.js";const _={class:"text-sm text-gray-600"},m={__name:"ActionMessage",props:{on:Boolean},setup(e){return(a,p)=>(t(),s("div",null,[o(d,{"leave-active-class":"transition ease-in duration-1000","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:c(()=>[n(i("div",_,[r(a.$slots,"default")],512),[[l,e.on]])]),_:3})]))}};export{m as _};
