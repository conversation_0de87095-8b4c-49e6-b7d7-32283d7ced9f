var Uo=Object.defineProperty,uo=Object.getOwnPropertySymbols,Jo=Object.prototype.hasOwnProperty,Qo=Object.prototype.propertyIsEnumerable,bo=(o,r,n)=>r in o?Uo(o,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):o[r]=n,or=(o,r)=>{for(var n in r||(r={}))Jo.call(r,n)&&bo(o,n,r[n]);if(uo)for(var n of uo(r))Qo.call(r,n)&&bo(o,n,r[n]);return o};function F(o){return o==null||o===""||Array.isArray(o)&&o.length===0||!(o instanceof Date)&&typeof o=="object"&&Object.keys(o).length===0}function rr(o,r,n,e=1){let t=-1;const i=F(o),a=F(r);return i&&a?t=0:i?t=e:a?t=-e:typeof o=="string"&&typeof r=="string"?t=n(o,r):t=o<r?-1:o>r?1:0,t}function no(o,r,n=new WeakSet){if(o===r)return!0;if(!o||!r||typeof o!="object"||typeof r!="object"||n.has(o)||n.has(r))return!1;n.add(o).add(r);const e=Array.isArray(o),t=Array.isArray(r);let i,a,d;if(e&&t){if(a=o.length,a!=r.length)return!1;for(i=a;i--!==0;)if(!no(o[i],r[i],n))return!1;return!0}if(e!=t)return!1;const c=o instanceof Date,l=r instanceof Date;if(c!=l)return!1;if(c&&l)return o.getTime()==r.getTime();const s=o instanceof RegExp,p=r instanceof RegExp;if(s!=p)return!1;if(s&&p)return o.toString()==r.toString();const u=Object.keys(o);if(a=u.length,a!==Object.keys(r).length)return!1;for(i=a;i--!==0;)if(!Object.prototype.hasOwnProperty.call(r,u[i]))return!1;for(i=a;i--!==0;)if(d=u[i],!no(o[d],r[d],n))return!1;return!0}function nr(o,r){return no(o,r)}function $o(o){return typeof o=="function"&&"call"in o&&"apply"in o}function g(o){return!F(o)}function po(o,r){if(!o||!r)return null;try{const n=o[r];if(g(n))return n}catch{}if(Object.keys(o).length){if($o(r))return r(o);if(r.indexOf(".")===-1)return o[r];{const n=r.split(".");let e=o;for(let t=0,i=n.length;t<i;++t){if(e==null)return null;e=e[n[t]]}return e}}return null}function er(o,r,n){return n?po(o,n)===po(r,n):nr(o,r)}function Ol(o,r){if(o!=null&&r&&r.length){for(const n of r)if(er(o,n))return!0}return!1}function B(o,r=!0){return o instanceof Object&&o.constructor===Object&&(r||Object.keys(o).length!==0)}function xo(o={},r={}){const n=or({},o);return Object.keys(r).forEach(e=>{const t=e;B(r[t])&&t in o&&B(o[t])?n[t]=xo(o[t],r[t]):n[t]=r[t]}),n}function tr(...o){return o.reduce((r,n,e)=>e===0?n:xo(r,n),{})}function Il(o,r){let n=-1;if(r){for(let e=0;e<r.length;e++)if(r[e]===o){n=e;break}}return n}function Ll(o,r){let n=-1;if(g(o))try{n=o.findLastIndex(r)}catch{n=o.lastIndexOf([...o].reverse().find(r))}return n}function D(o,...r){return $o(o)?o(...r):o}function z(o,r=!0){return typeof o=="string"&&(r||o!=="")}function go(o){return z(o)?o.replace(/(-|_)/g,"").toLowerCase():o}function ar(o,r="",n={}){const e=go(r).split("."),t=e.shift();if(t){if(B(o)){const i=Object.keys(o).find(a=>go(a)===t)||"";return ar(D(o[i],n),e.join("."),n)}return}return D(o,n)}function yo(o,r=!0){return Array.isArray(o)&&(r||o.length!==0)}function ir(o){return g(o)&&!isNaN(o)}function Nl(o=""){return g(o)&&o.length===1&&!!o.match(/\S| /)}function Tl(){return new Intl.Collator(void 0,{numeric:!0}).compare}function C(o,r){if(r){const n=r.test(o);return r.lastIndex=0,n}return!1}function Pl(...o){return tr(...o)}function fo(o){return o&&o.replace(/\/\*(?:(?!\*\/)[\s\S])*\*\/|[\r\n\t]+/g,"").replace(/ {2,}/g," ").replace(/ ([{:}]) /g,"$1").replace(/([;,]) /g,"$1").replace(/ !/g,"!").replace(/: /g,":")}function jl(o){if(o&&/[\xC0-\xFF\u0100-\u017E]/.test(o)){const n={A:/[\xC0-\xC5\u0100\u0102\u0104]/g,AE:/[\xC6]/g,C:/[\xC7\u0106\u0108\u010A\u010C]/g,D:/[\xD0\u010E\u0110]/g,E:/[\xC8-\xCB\u0112\u0114\u0116\u0118\u011A]/g,G:/[\u011C\u011E\u0120\u0122]/g,H:/[\u0124\u0126]/g,I:/[\xCC-\xCF\u0128\u012A\u012C\u012E\u0130]/g,IJ:/[\u0132]/g,J:/[\u0134]/g,K:/[\u0136]/g,L:/[\u0139\u013B\u013D\u013F\u0141]/g,N:/[\xD1\u0143\u0145\u0147\u014A]/g,O:/[\xD2-\xD6\xD8\u014C\u014E\u0150]/g,OE:/[\u0152]/g,R:/[\u0154\u0156\u0158]/g,S:/[\u015A\u015C\u015E\u0160]/g,T:/[\u0162\u0164\u0166]/g,U:/[\xD9-\xDC\u0168\u016A\u016C\u016E\u0170\u0172]/g,W:/[\u0174]/g,Y:/[\xDD\u0176\u0178]/g,Z:/[\u0179\u017B\u017D]/g,a:/[\xE0-\xE5\u0101\u0103\u0105]/g,ae:/[\xE6]/g,c:/[\xE7\u0107\u0109\u010B\u010D]/g,d:/[\u010F\u0111]/g,e:/[\xE8-\xEB\u0113\u0115\u0117\u0119\u011B]/g,g:/[\u011D\u011F\u0121\u0123]/g,i:/[\xEC-\xEF\u0129\u012B\u012D\u012F\u0131]/g,ij:/[\u0133]/g,j:/[\u0135]/g,k:/[\u0137,\u0138]/g,l:/[\u013A\u013C\u013E\u0140\u0142]/g,n:/[\xF1\u0144\u0146\u0148\u014B]/g,p:/[\xFE]/g,o:/[\xF2-\xF6\xF8\u014D\u014F\u0151]/g,oe:/[\u0153]/g,r:/[\u0155\u0157\u0159]/g,s:/[\u015B\u015D\u015F\u0161]/g,t:/[\u0163\u0165\u0167]/g,u:/[\xF9-\xFC\u0169\u016B\u016D\u016F\u0171\u0173]/g,w:/[\u0175]/g,y:/[\xFD\xFF\u0177]/g,z:/[\u017A\u017C\u017E]/g};for(const e in n)o=o.replace(n[e],e)}return o}function Hl(o,r,n){o&&r!==n&&(n>=o.length&&(n%=o.length,r%=o.length),o.splice(n,0,o.splice(r,1)[0]))}function Vl(o,r,n=1,e,t=1){const i=rr(o,r,e,n);let a=n;return(F(o)||F(r))&&(a=t===1?n:t),a*i}function Yl(o){return z(o,!1)?o[0].toUpperCase()+o.slice(1):o}function wo(o){return z(o)?o.replace(/(_)/g,"-").replace(/[A-Z]/g,(r,n)=>n===0?r:"-"+r.toLowerCase()).toLowerCase():o}function ho(o){return z(o)?o.replace(/[A-Z]/g,(r,n)=>n===0?r:"."+r.toLowerCase()).toLowerCase():o}function dr(){const o=new Map;return{on(r,n){let e=o.get(r);return e?e.push(n):e=[n],o.set(r,e),this},off(r,n){const e=o.get(r);return e&&e.splice(e.indexOf(n)>>>0,1),this},emit(r,n){const e=o.get(r);e&&e.forEach(t=>{t(n)})},clear(){o.clear()}}}function cr(...o){if(o){let r=[];for(let n=0;n<o.length;n++){const e=o[n];if(!e)continue;const t=typeof e;if(t==="string"||t==="number")r.push(e);else if(t==="object"){const i=Array.isArray(e)?[cr(...e)]:Object.entries(e).map(([a,d])=>d?a:void 0);r=i.length?r.concat(i.filter(a=>!!a)):r}}return r.join(" ").trim()}}function lr(o,r){return o?o.classList?o.classList.contains(r):new RegExp("(^| )"+r+"( |$)","gi").test(o.className):!1}function Xl(o,r){if(o&&r){const n=e=>{lr(o,e)||(o.classList?o.classList.add(e):o.className+=" "+e)};[r].flat().filter(Boolean).forEach(e=>e.split(" ").forEach(n))}}function sr(o){if(o){const r=document.createElement("a");if(r.download!==void 0){const{name:n,src:e}=o;return r.setAttribute("href",e),r.setAttribute("download",n),r.style.display="none",document.body.appendChild(r),r.click(),document.body.removeChild(r),!0}}return!1}function Ml(o,r){const n=new Blob([o],{type:"application/csv;charset=utf-8;"});window.navigator.msSaveOrOpenBlob?navigator.msSaveOrOpenBlob(n,r+".csv"):sr({name:r+".csv",src:URL.createObjectURL(n)})||(o="data:text/csv;charset=utf-8,"+o,window.open(encodeURI(o)))}function Gl(o,r){if(o&&r){const n=e=>{o.classList?o.classList.remove(e):o.className=o.className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," ")};[r].flat().filter(Boolean).forEach(e=>e.split(" ").forEach(n))}}function J(o){for(const r of document==null?void 0:document.styleSheets)try{for(const n of r==null?void 0:r.cssRules)for(const e of n==null?void 0:n.style)if(o.test(e))return{name:e,value:n.style.getPropertyValue(e).trim()}}catch{}return null}function Co(o){const r={width:0,height:0};return o&&(o.style.visibility="hidden",o.style.display="block",r.width=o.offsetWidth,r.height=o.offsetHeight,o.style.display="none",o.style.visibility="visible"),r}function Bo(){const o=window,r=document,n=r.documentElement,e=r.getElementsByTagName("body")[0],t=o.innerWidth||n.clientWidth||e.clientWidth,i=o.innerHeight||n.clientHeight||e.clientHeight;return{width:t,height:i}}function eo(o){return o?Math.abs(o.scrollLeft):0}function ur(){const o=document.documentElement;return(window.pageXOffset||eo(o))-(o.clientLeft||0)}function br(){const o=document.documentElement;return(window.pageYOffset||o.scrollTop)-(o.clientTop||0)}function pr(o){return o?getComputedStyle(o).direction==="rtl":!1}function Kl(o,r,n=!0){var e,t,i,a;if(o){const d=o.offsetParent?{width:o.offsetWidth,height:o.offsetHeight}:Co(o),c=d.height,l=d.width,s=r.offsetHeight,p=r.offsetWidth,u=r.getBoundingClientRect(),b=br(),f=ur(),m=Bo();let h,k,v="top";u.top+s+c>m.height?(h=u.top+b-c,v="bottom",h<0&&(h=b)):h=s+u.top+b,u.left+l>m.width?k=Math.max(0,u.left+f+p-l):k=u.left+f,pr(o)?o.style.insetInlineEnd=k+"px":o.style.insetInlineStart=k+"px",o.style.top=h+"px",o.style.transformOrigin=v,n&&(o.style.marginTop=v==="bottom"?`calc(${(t=(e=J(/-anchor-gutter$/))==null?void 0:e.value)!=null?t:"2px"} * -1)`:(a=(i=J(/-anchor-gutter$/))==null?void 0:i.value)!=null?a:"")}}function Zl(o,r){o&&(typeof r=="string"?o.style.cssText=r:Object.entries(r||{}).forEach(([n,e])=>o.style[n]=e))}function ql(o,r){return o instanceof HTMLElement?o.offsetWidth:0}function Ul(o,r,n=!0){var e,t,i,a;if(o){const d=o.offsetParent?{width:o.offsetWidth,height:o.offsetHeight}:Co(o),c=r.offsetHeight,l=r.getBoundingClientRect(),s=Bo();let p,u,b="top";l.top+c+d.height>s.height?(p=-1*d.height,b="bottom",l.top+p<0&&(p=-1*l.top)):p=c,d.width>s.width?u=l.left*-1:l.left+d.width>s.width?u=(l.left+d.width-s.width)*-1:u=0,o.style.top=p+"px",o.style.insetInlineStart=u+"px",o.style.transformOrigin=b,n&&(o.style.marginTop=b==="bottom"?`calc(${(t=(e=J(/-anchor-gutter$/))==null?void 0:e.value)!=null?t:"2px"} * -1)`:(a=(i=J(/-anchor-gutter$/))==null?void 0:i.value)!=null?a:"")}}function ao(o){if(o){let r=o.parentNode;return r&&r instanceof ShadowRoot&&r.host&&(r=r.host),r}return null}function Jl(o){return!!(o!==null&&typeof o<"u"&&o.nodeName&&ao(o))}function _(o){return typeof Element<"u"?o instanceof Element:o!==null&&typeof o=="object"&&o.nodeType===1&&typeof o.nodeName=="string"}function Ql(){if(window.getSelection){const o=window.getSelection()||{};o.empty?o.empty():o.removeAllRanges&&o.rangeCount>0&&o.getRangeAt(0).getClientRects().length>0&&o.removeAllRanges()}}function zo(o,r={}){if(_(o)){const n=(e,t)=>{var i,a;const d=(i=o==null?void 0:o.$attrs)!=null&&i[e]?[(a=o==null?void 0:o.$attrs)==null?void 0:a[e]]:[];return[t].flat().reduce((c,l)=>{if(l!=null){const s=typeof l;if(s==="string"||s==="number")c.push(l);else if(s==="object"){const p=Array.isArray(l)?n(e,l):Object.entries(l).map(([u,b])=>e==="style"&&(b||b===0)?`${u.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}:${b}`:b?u:void 0);c=p.length?c.concat(p.filter(u=>!!u)):c}}return c},d)};Object.entries(r).forEach(([e,t])=>{if(t!=null){const i=e.match(/^on(.+)/);i?o.addEventListener(i[1].toLowerCase(),t):e==="p-bind"||e==="pBind"?zo(o,t):(t=e==="class"?[...new Set(n("class",t))].join(" ").trim():e==="style"?n("style",t).join(";").trim():t,(o.$attrs=o.$attrs||{})&&(o.$attrs[e]=t),o.setAttribute(e,t))}})}}function os(o,r={},...n){if(o){const e=document.createElement(o);return zo(e,r),e.append(...n),e}}function rs(o,r){if(o){o.style.opacity="0";let n=+new Date,e="0";const t=function(){e=`${+o.style.opacity+(new Date().getTime()-n)/r}`,o.style.opacity=e,n=+new Date,+e<1&&("requestAnimationFrame"in window?requestAnimationFrame(t):setTimeout(t,16))};t()}}function gr(o,r){return _(o)?Array.from(o.querySelectorAll(r)):[]}function fr(o,r){return _(o)?o.matches(r)?o:o.querySelector(r):null}function ns(o,r){o&&document.activeElement!==o&&o.focus(r)}function es(o,r){if(_(o)){const n=o.getAttribute(r);return isNaN(n)?n==="true"||n==="false"?n==="true":n:+n}}function Ro(o,r=""){const n=gr(o,`button:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${r},
            [href][clientHeight][clientWidth]:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${r},
            input:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${r},
            select:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${r},
            textarea:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${r},
            [tabIndex]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${r},
            [contenteditable]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${r}`),e=[];for(const t of n)getComputedStyle(t).display!="none"&&getComputedStyle(t).visibility!="hidden"&&e.push(t);return e}function ts(o,r){const n=Ro(o,r);return n.length>0?n[0]:null}function as(o){if(o){let r=o.offsetHeight;const n=getComputedStyle(o);return r-=parseFloat(n.paddingTop)+parseFloat(n.paddingBottom)+parseFloat(n.borderTopWidth)+parseFloat(n.borderBottomWidth),r}return 0}function is(o){if(o){o.style.visibility="hidden",o.style.display="block";const r=o.offsetHeight;return o.style.display="none",o.style.visibility="visible",r}return 0}function ds(o){if(o){o.style.visibility="hidden",o.style.display="block";const r=o.offsetWidth;return o.style.display="none",o.style.visibility="visible",r}return 0}function cs(o){var r;if(o){const n=(r=ao(o))==null?void 0:r.childNodes;let e=0;if(n)for(let t=0;t<n.length;t++){if(n[t]===o)return e;n[t].nodeType===1&&e++}}return-1}function ls(o,r){const n=Ro(o,r);return n.length>0?n[n.length-1]:null}function ss(o,r){let n=o.nextElementSibling;for(;n;){if(n.matches(r))return n;n=n.nextElementSibling}return null}function us(o){if(o){const r=o.getBoundingClientRect();return{top:r.top+(window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0),left:r.left+(window.pageXOffset||eo(document.documentElement)||eo(document.body)||0)}}return{top:"auto",left:"auto"}}function bs(o,r){return o?o.offsetHeight:0}function So(o,r=[]){const n=ao(o);return n===null?r:So(n,r.concat([n]))}function ps(o,r){let n=o.previousElementSibling;for(;n;){if(n.matches(r))return n;n=n.previousElementSibling}return null}function gs(o){const r=[];if(o){const n=So(o),e=/(auto|scroll)/,t=i=>{try{const a=window.getComputedStyle(i,null);return e.test(a.getPropertyValue("overflow"))||e.test(a.getPropertyValue("overflowX"))||e.test(a.getPropertyValue("overflowY"))}catch{return!1}};for(const i of n){const a=i.nodeType===1&&i.dataset.scrollselectors;if(a){const d=a.split(",");for(const c of d){const l=fr(i,c);l&&t(l)&&r.push(l)}}i.nodeType!==9&&t(i)&&r.push(i)}}return r}function fs(){if(window.getSelection)return window.getSelection().toString();if(document.getSelection)return document.getSelection().toString()}function hs(o){if(o){let r=o.offsetWidth;const n=getComputedStyle(o);return r-=parseFloat(n.paddingLeft)+parseFloat(n.paddingRight)+parseFloat(n.borderLeftWidth)+parseFloat(n.borderRightWidth),r}return 0}function ms(o,r,n){const e=o[r];typeof e=="function"&&e.apply(o,[])}function ks(){return/(android)/i.test(navigator.userAgent)}function vs(o){if(o){const r=o.nodeName,n=o.parentElement&&o.parentElement.nodeName;return r==="INPUT"||r==="TEXTAREA"||r==="BUTTON"||r==="A"||n==="INPUT"||n==="TEXTAREA"||n==="BUTTON"||n==="A"||!!o.closest(".p-button, .p-checkbox, .p-radiobutton")}return!1}function $s(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function xs(o,r=""){return _(o)?o.matches(`button:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${r},
            [href][clientHeight][clientWidth]:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${r},
            input:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${r},
            select:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${r},
            textarea:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${r},
            [tabIndex]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${r},
            [contenteditable]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${r}`):!1}function ys(o){return!!(o&&o.offsetParent!=null)}function ws(){return"ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0}function Cs(o,r="",n){_(o)&&n!==null&&n!==void 0&&o.setAttribute(r,n)}var U={};function Bs(o="pui_id_"){return Object.hasOwn(U,o)||(U[o]=0),U[o]++,`${o}${U[o]}`}function hr(){let o=[];const r=(a,d,c=999)=>{const l=t(a,d,c),s=l.value+(l.key===a?0:c)+1;return o.push({key:a,value:s}),s},n=a=>{o=o.filter(d=>d.value!==a)},e=(a,d)=>t(a).value,t=(a,d,c=0)=>[...o].reverse().find(l=>!0)||{key:a,value:c},i=a=>a&&parseInt(a.style.zIndex,10)||0;return{get:i,set:(a,d,c)=>{d&&(d.style.zIndex=String(r(a,!0,c)))},clear:a=>{a&&(n(i(a)),a.style.zIndex="")},getCurrent:a=>e(a)}}var zs=hr(),mr=Object.defineProperty,kr=Object.defineProperties,vr=Object.getOwnPropertyDescriptors,Q=Object.getOwnPropertySymbols,Fo=Object.prototype.hasOwnProperty,Eo=Object.prototype.propertyIsEnumerable,mo=(o,r,n)=>r in o?mr(o,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):o[r]=n,y=(o,r)=>{for(var n in r||(r={}))Fo.call(r,n)&&mo(o,n,r[n]);if(Q)for(var n of Q(r))Eo.call(r,n)&&mo(o,n,r[n]);return o},ro=(o,r)=>kr(o,vr(r)),w=(o,r)=>{var n={};for(var e in o)Fo.call(o,e)&&r.indexOf(e)<0&&(n[e]=o[e]);if(o!=null&&Q)for(var e of Q(o))r.indexOf(e)<0&&Eo.call(o,e)&&(n[e]=o[e]);return n},$r=dr(),S=$r;function ko(o,r){yo(o)?o.push(...r||[]):B(o)&&Object.assign(o,r)}function xr(o){return B(o)&&o.hasOwnProperty("$value")&&o.hasOwnProperty("$type")?o.$value:o}function yr(o){return o.replaceAll(/ /g,"").replace(/[^\w]/g,"-")}function to(o="",r=""){return yr(`${z(o,!1)&&z(r,!1)?`${o}-`:o}${r}`)}function Do(o="",r=""){return`--${to(o,r)}`}function wr(o=""){const r=(o.match(/{/g)||[]).length,n=(o.match(/}/g)||[]).length;return(r+n)%2!==0}function _o(o,r="",n="",e=[],t){if(z(o)){const i=/{([^}]*)}/g,a=o.trim();if(wr(a))return;if(C(a,i)){const d=a.replaceAll(i,s=>{const u=s.replace(/{|}/g,"").split(".").filter(b=>!e.some(f=>C(b,f)));return`var(${Do(n,wo(u.join("-")))}${g(t)?`, ${t}`:""})`}),c=/(\d+\s+[\+\-\*\/]\s+\d+)/g,l=/var\([^)]+\)/g;return C(d.replace(l,"0"),c)?`calc(${d})`:d}return a}else if(ir(o))return o}function Cr(o,r,n){z(r,!1)&&o.push(`${r}:${n};`)}function E(o,r){return o?`${o}{${r}}`:""}var vo=(...o)=>Br(oo.getTheme(),...o),Br=(o={},r,n,e)=>{if(r){const{variable:t,options:i}=oo.defaults||{},{prefix:a,transform:d}=(o==null?void 0:o.options)||i||{},l=C(r,/{([^}]*)}/g)?r:`{${r}}`;return e==="value"||F(e)&&d==="strict"?oo.getTokenValue(r):_o(l,void 0,a,[t.excludedKeyRegex],n)}return""};function zr(o,r={}){const n=oo.defaults.variable,{prefix:e=n.prefix,selector:t=n.selector,excludedKeyRegex:i=n.excludedKeyRegex}=r,a=(l,s="")=>Object.entries(l).reduce((p,[u,b])=>{const f=C(u,i)?to(s):to(s,wo(u)),m=xr(b);if(B(m)){const{variables:h,tokens:k}=a(m,f);ko(p.tokens,k),ko(p.variables,h)}else p.tokens.push((e?f.replace(`${e}-`,""):f).replaceAll("-",".")),Cr(p.variables,Do(f),_o(m,f,e,[i]));return p},{variables:[],tokens:[]}),{variables:d,tokens:c}=a(o,e);return{value:d,tokens:c,declarations:d.join(""),css:E(t,d.join(""))}}var x={regex:{rules:{class:{pattern:/^\.([a-zA-Z][\w-]*)$/,resolve(o){return{type:"class",selector:o,matched:this.pattern.test(o.trim())}}},attr:{pattern:/^\[(.*)\]$/,resolve(o){return{type:"attr",selector:`:root${o}`,matched:this.pattern.test(o.trim())}}},media:{pattern:/^@media (.*)$/,resolve(o){return{type:"media",selector:`${o}{:root{[CSS]}}`,matched:this.pattern.test(o.trim())}}},system:{pattern:/^system$/,resolve(o){return{type:"system",selector:"@media (prefers-color-scheme: dark){:root{[CSS]}}",matched:this.pattern.test(o.trim())}}},custom:{resolve(o){return{type:"custom",selector:o,matched:!0}}}},resolve(o){const r=Object.keys(this.rules).filter(n=>n!=="custom").map(n=>this.rules[n]);return[o].flat().map(n=>{var e;return(e=r.map(t=>t.resolve(n)).find(t=>t.matched))!=null?e:this.rules.custom.resolve(n)})}},_toVariables(o,r){return zr(o,{prefix:r==null?void 0:r.prefix})},getCommon({name:o="",theme:r={},params:n,set:e,defaults:t}){var i,a,d,c,l,s,p;const{preset:u,options:b}=r;let f,m,h,k,v,R,$;if(g(u)&&b.transform!=="strict"){const{primitive:I,semantic:L,extend:N}=u,A=L||{},{colorScheme:T}=A,P=w(A,["colorScheme"]),j=N||{},{colorScheme:H}=j,W=w(j,["colorScheme"]),O=T||{},{dark:V}=O,Y=w(O,["dark"]),X=H||{},{dark:M}=X,G=w(X,["dark"]),K=g(I)?this._toVariables({primitive:I},b):{},Z=g(P)?this._toVariables({semantic:P},b):{},q=g(Y)?this._toVariables({light:Y},b):{},io=g(V)?this._toVariables({dark:V},b):{},co=g(W)?this._toVariables({semantic:W},b):{},lo=g(G)?this._toVariables({light:G},b):{},so=g(M)?this._toVariables({dark:M},b):{},[Ao,Wo]=[(i=K.declarations)!=null?i:"",K.tokens],[Oo,Io]=[(a=Z.declarations)!=null?a:"",Z.tokens||[]],[Lo,No]=[(d=q.declarations)!=null?d:"",q.tokens||[]],[To,Po]=[(c=io.declarations)!=null?c:"",io.tokens||[]],[jo,Ho]=[(l=co.declarations)!=null?l:"",co.tokens||[]],[Vo,Yo]=[(s=lo.declarations)!=null?s:"",lo.tokens||[]],[Xo,Mo]=[(p=so.declarations)!=null?p:"",so.tokens||[]];f=this.transformCSS(o,Ao,"light","variable",b,e,t),m=Wo;const Go=this.transformCSS(o,`${Oo}${Lo}`,"light","variable",b,e,t),Ko=this.transformCSS(o,`${To}`,"dark","variable",b,e,t);h=`${Go}${Ko}`,k=[...new Set([...Io,...No,...Po])];const Zo=this.transformCSS(o,`${jo}${Vo}color-scheme:light`,"light","variable",b,e,t),qo=this.transformCSS(o,`${Xo}color-scheme:dark`,"dark","variable",b,e,t);v=`${Zo}${qo}`,R=[...new Set([...Ho,...Yo,...Mo])],$=D(u.css,{dt:vo})}return{primitive:{css:f,tokens:m},semantic:{css:h,tokens:k},global:{css:v,tokens:R},style:$}},getPreset({name:o="",preset:r={},options:n,params:e,set:t,defaults:i,selector:a}){var d,c,l;let s,p,u;if(g(r)&&n.transform!=="strict"){const b=o.replace("-directive",""),f=r,{colorScheme:m,extend:h,css:k}=f,v=w(f,["colorScheme","extend","css"]),R=h||{},{colorScheme:$}=R,I=w(R,["colorScheme"]),L=m||{},{dark:N}=L,A=w(L,["dark"]),T=$||{},{dark:P}=T,j=w(T,["dark"]),H=g(v)?this._toVariables({[b]:y(y({},v),I)},n):{},W=g(A)?this._toVariables({[b]:y(y({},A),j)},n):{},O=g(N)?this._toVariables({[b]:y(y({},N),P)},n):{},[V,Y]=[(d=H.declarations)!=null?d:"",H.tokens||[]],[X,M]=[(c=W.declarations)!=null?c:"",W.tokens||[]],[G,K]=[(l=O.declarations)!=null?l:"",O.tokens||[]],Z=this.transformCSS(b,`${V}${X}`,"light","variable",n,t,i,a),q=this.transformCSS(b,G,"dark","variable",n,t,i,a);s=`${Z}${q}`,p=[...new Set([...Y,...M,...K])],u=D(k,{dt:vo})}return{css:s,tokens:p,style:u}},getPresetC({name:o="",theme:r={},params:n,set:e,defaults:t}){var i;const{preset:a,options:d}=r,c=(i=a==null?void 0:a.components)==null?void 0:i[o];return this.getPreset({name:o,preset:c,options:d,params:n,set:e,defaults:t})},getPresetD({name:o="",theme:r={},params:n,set:e,defaults:t}){var i,a;const d=o.replace("-directive",""),{preset:c,options:l}=r,s=((i=c==null?void 0:c.components)==null?void 0:i[d])||((a=c==null?void 0:c.directives)==null?void 0:a[d]);return this.getPreset({name:d,preset:s,options:l,params:n,set:e,defaults:t})},applyDarkColorScheme(o){return!(o.darkModeSelector==="none"||o.darkModeSelector===!1)},getColorSchemeOption(o,r){var n;return this.applyDarkColorScheme(o)?this.regex.resolve(o.darkModeSelector===!0?r.options.darkModeSelector:(n=o.darkModeSelector)!=null?n:r.options.darkModeSelector):[]},getLayerOrder(o,r={},n,e){const{cssLayer:t}=r;return t?`@layer ${D(t.order||"primeui",n)}`:""},getCommonStyleSheet({name:o="",theme:r={},params:n,props:e={},set:t,defaults:i}){const a=this.getCommon({name:o,theme:r,params:n,set:t,defaults:i}),d=Object.entries(e).reduce((c,[l,s])=>c.push(`${l}="${s}"`)&&c,[]).join(" ");return Object.entries(a||{}).reduce((c,[l,s])=>{if(s!=null&&s.css){const p=fo(s==null?void 0:s.css),u=`${l}-variables`;c.push(`<style type="text/css" data-primevue-style-id="${u}" ${d}>${p}</style>`)}return c},[]).join("")},getStyleSheet({name:o="",theme:r={},params:n,props:e={},set:t,defaults:i}){var a;const d={name:o,theme:r,params:n,set:t,defaults:i},c=(a=o.includes("-directive")?this.getPresetD(d):this.getPresetC(d))==null?void 0:a.css,l=Object.entries(e).reduce((s,[p,u])=>s.push(`${p}="${u}"`)&&s,[]).join(" ");return c?`<style type="text/css" data-primevue-style-id="${o}-variables" ${l}>${fo(c)}</style>`:""},createTokens(o={},r,n="",e="",t={}){return Object.entries(o).forEach(([i,a])=>{const d=C(i,r.variable.excludedKeyRegex)?n:n?`${n}.${ho(i)}`:ho(i),c=e?`${e}.${i}`:i;B(a)?this.createTokens(a,r,d,c,t):(t[d]||(t[d]={paths:[],computed(l,s={}){var p,u;return this.paths.length===1?(p=this.paths[0])==null?void 0:p.computed(this.paths[0].scheme,s.binding):l&&l!=="none"?(u=this.paths.find(b=>b.scheme===l))==null?void 0:u.computed(l,s.binding):this.paths.map(b=>b.computed(b.scheme,s[b.scheme]))}}),t[d].paths.push({path:c,value:a,scheme:c.includes("colorScheme.light")?"light":c.includes("colorScheme.dark")?"dark":"none",computed(l,s={}){const p=/{([^}]*)}/g;let u=a;if(s.name=this.path,s.binding||(s.binding={}),C(a,p)){const f=a.trim().replaceAll(p,k=>{var v;const R=k.replace(/{|}/g,""),$=(v=t[R])==null?void 0:v.computed(l,s);return yo($)&&$.length===2?`light-dark(${$[0].value},${$[1].value})`:$==null?void 0:$.value}),m=/(\d+\w*\s+[\+\-\*\/]\s+\d+\w*)/g,h=/var\([^)]+\)/g;u=C(f.replace(h,"0"),m)?`calc(${f})`:f}return F(s.binding)&&delete s.binding,{colorScheme:l,path:this.path,paths:s,value:u.includes("undefined")?void 0:u}}}))}),t},getTokenValue(o,r,n){var e;const i=(c=>c.split(".").filter(s=>!C(s.toLowerCase(),n.variable.excludedKeyRegex)).join("."))(r),a=r.includes("colorScheme.light")?"light":r.includes("colorScheme.dark")?"dark":void 0,d=[(e=o[i])==null?void 0:e.computed(a)].flat().filter(c=>c);return d.length===1?d[0].value:d.reduce((c={},l)=>{const s=l,{colorScheme:p}=s,u=w(s,["colorScheme"]);return c[p]=u,c},void 0)},getSelectorRule(o,r,n,e){return n==="class"||n==="attr"?E(g(r)?`${o}${r},${o} ${r}`:o,e):E(o,g(r)?E(r,e):e)},transformCSS(o,r,n,e,t={},i,a,d){if(g(r)){const{cssLayer:c}=t;if(e!=="style"){const l=this.getColorSchemeOption(t,a);r=n==="dark"?l.reduce((s,{type:p,selector:u})=>(g(u)&&(s+=u.includes("[CSS]")?u.replace("[CSS]",r):this.getSelectorRule(u,d,p,r)),s),""):E(d??":root",r)}if(c){const l={name:"primeui"};B(c)&&(l.name=D(c.name,{name:o,type:e})),g(l.name)&&(r=E(`@layer ${l.name}`,r),i==null||i.layerNames(l.name))}return r}return""}},oo={defaults:{variable:{prefix:"p",selector:":root",excludedKeyRegex:/^(primitive|semantic|components|directives|variables|colorscheme|light|dark|common|root|states|extend|css)$/gi},options:{prefix:"p",darkModeSelector:"system",cssLayer:!1}},_theme:void 0,_layerNames:new Set,_loadedStyleNames:new Set,_loadingStyles:new Set,_tokens:{},update(o={}){const{theme:r}=o;r&&(this._theme=ro(y({},r),{options:y(y({},this.defaults.options),r.options)}),this._tokens=x.createTokens(this.preset,this.defaults),this.clearLoadedStyleNames())},get theme(){return this._theme},get preset(){var o;return((o=this.theme)==null?void 0:o.preset)||{}},get options(){var o;return((o=this.theme)==null?void 0:o.options)||{}},get tokens(){return this._tokens},getTheme(){return this.theme},setTheme(o){this.update({theme:o}),S.emit("theme:change",o)},getPreset(){return this.preset},setPreset(o){this._theme=ro(y({},this.theme),{preset:o}),this._tokens=x.createTokens(o,this.defaults),this.clearLoadedStyleNames(),S.emit("preset:change",o),S.emit("theme:change",this.theme)},getOptions(){return this.options},setOptions(o){this._theme=ro(y({},this.theme),{options:o}),this.clearLoadedStyleNames(),S.emit("options:change",o),S.emit("theme:change",this.theme)},getLayerNames(){return[...this._layerNames]},setLayerNames(o){this._layerNames.add(o)},getLoadedStyleNames(){return this._loadedStyleNames},isStyleNameLoaded(o){return this._loadedStyleNames.has(o)},setLoadedStyleName(o){this._loadedStyleNames.add(o)},deleteLoadedStyleName(o){this._loadedStyleNames.delete(o)},clearLoadedStyleNames(){this._loadedStyleNames.clear()},getTokenValue(o){return x.getTokenValue(this.tokens,o,this.defaults)},getCommon(o="",r){return x.getCommon({name:o,theme:this.theme,params:r,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},getComponent(o="",r){const n={name:o,theme:this.theme,params:r,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return x.getPresetC(n)},getDirective(o="",r){const n={name:o,theme:this.theme,params:r,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return x.getPresetD(n)},getCustomPreset(o="",r,n,e){const t={name:o,preset:r,options:this.options,selector:n,params:e,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return x.getPreset(t)},getLayerOrderCSS(o=""){return x.getLayerOrder(o,this.options,{names:this.getLayerNames()},this.defaults)},transformCSS(o="",r,n="style",e){return x.transformCSS(o,r,e,n,this.options,{layerNames:this.setLayerNames.bind(this)},this.defaults)},getCommonStyleSheet(o="",r,n={}){return x.getCommonStyleSheet({name:o,theme:this.theme,params:r,props:n,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},getStyleSheet(o,r,n={}){return x.getStyleSheet({name:o,theme:this.theme,params:r,props:n,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},onStyleMounted(o){this._loadingStyles.add(o)},onStyleUpdated(o){this._loadingStyles.add(o)},onStyleLoaded(o,{name:r}){this._loadingStyles.size&&(this._loadingStyles.delete(r),S.emit(`theme:${r}:load`,o),!this._loadingStyles.size&&S.emit("theme:load"))}},Rs=({dt:o})=>`
*,
::before,
::after {
    box-sizing: border-box;
}

/* Non vue overlay animations */
.p-connected-overlay {
    opacity: 0;
    transform: scaleY(0.8);
    transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1),
        opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
}

.p-connected-overlay-visible {
    opacity: 1;
    transform: scaleY(1);
}

.p-connected-overlay-hidden {
    opacity: 0;
    transform: scaleY(1);
    transition: opacity 0.1s linear;
}

/* Vue based overlay animations */
.p-connected-overlay-enter-from {
    opacity: 0;
    transform: scaleY(0.8);
}

.p-connected-overlay-leave-to {
    opacity: 0;
}

.p-connected-overlay-enter-active {
    transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1),
        opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
}

.p-connected-overlay-leave-active {
    transition: opacity 0.1s linear;
}

/* Toggleable Content */
.p-toggleable-content-enter-from,
.p-toggleable-content-leave-to {
    max-height: 0;
}

.p-toggleable-content-enter-to,
.p-toggleable-content-leave-from {
    max-height: 1000px;
}

.p-toggleable-content-leave-active {
    overflow: hidden;
    transition: max-height 0.45s cubic-bezier(0, 1, 0, 1);
}

.p-toggleable-content-enter-active {
    overflow: hidden;
    transition: max-height 1s ease-in-out;
}

.p-disabled,
.p-disabled * {
    cursor: default;
    pointer-events: none;
    user-select: none;
}

.p-disabled,
.p-component:disabled {
    opacity: ${o("disabled.opacity")};
}

.pi {
    font-size: ${o("icon.size")};
}

.p-icon {
    width: ${o("icon.size")};
    height: ${o("icon.size")};
}

.p-overlay-mask {
    background: ${o("mask.background")};
    color: ${o("mask.color")};
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.p-overlay-mask-enter {
    animation: p-overlay-mask-enter-animation ${o("mask.transition.duration")} forwards;
}

.p-overlay-mask-leave {
    animation: p-overlay-mask-leave-animation ${o("mask.transition.duration")} forwards;
}

@keyframes p-overlay-mask-enter-animation {
    from {
        background: transparent;
    }
    to {
        background: ${o("mask.background")};
    }
}
@keyframes p-overlay-mask-leave-animation {
    from {
        background: ${o("mask.background")};
    }
    to {
        background: transparent;
    }
}
`,Ss=({dt:o})=>`
.p-tooltip {
    position: absolute;
    display: none;
    max-width: ${o("tooltip.max.width")};
}

.p-tooltip-right,
.p-tooltip-left {
    padding: 0 ${o("tooltip.gutter")};
}

.p-tooltip-top,
.p-tooltip-bottom {
    padding: ${o("tooltip.gutter")} 0;
}

.p-tooltip-text {
    white-space: pre-line;
    word-break: break-word;
    background: ${o("tooltip.background")};
    color: ${o("tooltip.color")};
    padding: ${o("tooltip.padding")};
    box-shadow: ${o("tooltip.shadow")};
    border-radius: ${o("tooltip.border.radius")};
}

.p-tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
}

.p-tooltip-right .p-tooltip-arrow {
    margin-top: calc(-1 * ${o("tooltip.gutter")});
    border-width: ${o("tooltip.gutter")} ${o("tooltip.gutter")} ${o("tooltip.gutter")} 0;
    border-right-color: ${o("tooltip.background")};
}

.p-tooltip-left .p-tooltip-arrow {
    margin-top: calc(-1 * ${o("tooltip.gutter")});
    border-width: ${o("tooltip.gutter")} 0 ${o("tooltip.gutter")} ${o("tooltip.gutter")};
    border-left-color: ${o("tooltip.background")};
}

.p-tooltip-top .p-tooltip-arrow {
    margin-left: calc(-1 * ${o("tooltip.gutter")});
    border-width: ${o("tooltip.gutter")} ${o("tooltip.gutter")} 0 ${o("tooltip.gutter")};
    border-top-color: ${o("tooltip.background")};
    border-bottom-color: ${o("tooltip.background")};
}

.p-tooltip-bottom .p-tooltip-arrow {
    margin-left: calc(-1 * ${o("tooltip.gutter")});
    border-width: 0 ${o("tooltip.gutter")} ${o("tooltip.gutter")} ${o("tooltip.gutter")};
    border-top-color: ${o("tooltip.background")};
    border-bottom-color: ${o("tooltip.background")};
}
`,Rr={transitionDuration:"{transition.duration}"},Sr={borderWidth:"0",borderColor:"{content.border.color}"},Fr={color:"{text.color}",hoverColor:"{text.color}",activeColor:"{text.color}",padding:"1.25rem",fontWeight:"600",borderRadius:"0",borderWidth:"0",borderColor:"{content.border.color}",background:"{content.background}",hoverBackground:"{content.hover.background}",activeBackground:"{content.background}",activeHoverBackground:"{content.background}",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"},toggleIcon:{color:"{text.muted.color}",hoverColor:"{text.muted.color}",activeColor:"{text.muted.color}",activeHoverColor:"{text.muted.color}"},first:{topBorderRadius:"{content.border.radius}",borderWidth:"0"},last:{bottomBorderRadius:"{content.border.radius}",activeBottomBorderRadius:"0"}},Er={borderWidth:"0",borderColor:"{content.border.color}",background:"{content.background}",color:"{text.color}",padding:"0 1.25rem 1.25rem 1.25rem"},Dr=({dt:o})=>`
.p-accordionpanel {
    box-shadow: 0 3px 1px -2px rgba(0,0,0,.2), 0 2px 2px 0 rgba(0,0,0,.14), 0 1px 5px 0 rgba(0,0,0,.12);
    transition: margin ${o("accordion.transition.duration")};
}

.p-accordionpanel-active {
    margin: 1rem 0;
}

.p-accordionpanel:first-child {
    border-top-left-radius: ${o("content.border.radius")};
    border-top-right-radius: ${o("content.border.radius")};
    margin-top: 0;
}

.p-accordionpanel:last-child {
    border-bottom-left-radius: ${o("content.border.radius")};
    border-bottom-right-radius: ${o("content.border.radius")};
    margin-bottom: 0;
}

.p-accordionpanel:not(.p-disabled) .p-accordionheader:focus-visible {
    background: ${o("navigation.item.active.background")};
}
`,Fs={root:Rr,panel:Sr,header:Fr,content:Er,css:Dr},_r={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},Ar={background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},Wr={padding:"{list.padding}",gap:"{list.gap}"},Or={focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},Ir={background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},Lr={width:"3rem",sm:{width:"2.5rem"},lg:{width:"3.5rem"},borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",borderRadius:"{form.field.border.radius}",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"}},Nr={borderRadius:"{border.radius.sm}"},Tr={padding:"{list.option.padding}"},Pr={light:{chip:{focusBackground:"{surface.300}",focusColor:"{surface.950}"},dropdown:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}"}},dark:{chip:{focusBackground:"{surface.600}",focusColor:"{surface.0}"},dropdown:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}"}}},jr=({dt:o})=>`
.p-autocomplete-dropdown:focus-visible {
    background: ${o("autocomplete.dropdown.hover.background")};
    border-color: ${o("autocomplete.dropdown.hover.border.color")};
    color: ${o("autocomplete.dropdown.hover.color")};
}

.p-variant-filled.p-autocomplete-input-multiple {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 1px solid transparent;
    background: ${o("autocomplete.filled.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("autocomplete.focus.border.color")}, ${o("autocomplete.focus.border.color")}), linear-gradient(to bottom, ${o("autocomplete.border.color")}, ${o("autocomplete.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    transition: background-size 0.3s cubic-bezier(0.64, 0.09, 0.08, 1);
}

.p-autocomplete:not(.p-disabled):hover .p-variant-filled.p-autocomplete-input-multiple {
    background: ${o("autocomplete.filled.hover.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("autocomplete.focus.border.color")}, ${o("autocomplete.focus.border.color")}), linear-gradient(to bottom, ${o("autocomplete.hover.border.color")}, ${o("autocomplete.hover.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-autocomplete:not(.p-disabled).p-focus .p-variant-filled.p-autocomplete-input-multiple {
    outline: 0 none;
    background: ${o("autocomplete.filled.focus.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("autocomplete.focus.border.color")}, ${o("autocomplete.focus.border.color")}), linear-gradient(to bottom, ${o("autocomplete.border.color")}, ${o("autocomplete.border.color")});
    background-size: 100% 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-autocomplete:not(.p-disabled).p-focus:hover .p-variant-filled.p-autocomplete-input-multiple {
    background-image: linear-gradient(to bottom, ${o("autocomplete.focus.border.color")}, ${o("autocomplete.focus.border.color")}), linear-gradient(to bottom, ${o("autocomplete.hover.border.color")}, ${o("autocomplete.hover.border.color")});
}

.p-autocomplete.p-invalid .p-autocomplete-input-multiple {
    background-image: linear-gradient(to bottom, ${o("autocomplete.invalid.border.color")}, ${o("autocomplete.invalid.border.color")}), linear-gradient(to bottom, ${o("autocomplete.invalid.border.color")}, ${o("autocomplete.invalid.border.color")});
}

.p-autocomplete.p-invalid.p-focus .p-autocomplete-input-multiple  {
    background-image: linear-gradient(to bottom, ${o("autocomplete.invalid.border.color")}, ${o("autocomplete.invalid.border.color")}), linear-gradient(to bottom, ${o("autocomplete.invalid.border.color")}, ${o("autocomplete.invalid.border.color")});
}

.p-autocomplete-option {
    transition: none;
}
`,Es={root:_r,overlay:Ar,list:Wr,option:Or,optionGroup:Ir,dropdown:Lr,chip:Nr,emptyMessage:Tr,colorScheme:Pr,css:jr},Hr={width:"2rem",height:"2rem",fontSize:"1rem",background:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"},Vr={size:"1rem"},Yr={borderColor:"{content.background}",offset:"-0.75rem"},Xr={width:"3rem",height:"3rem",fontSize:"1.5rem",icon:{size:"1.5rem"},group:{offset:"-1rem"}},Mr={width:"4rem",height:"4rem",fontSize:"2rem",icon:{size:"2rem"},group:{offset:"-1.5rem"}},Ds={root:Hr,icon:Vr,group:Yr,lg:Xr,xl:Mr,css:""},Gr={borderRadius:"{border.radius.md}",padding:"0 0.5rem",fontSize:"0.75rem",fontWeight:"700",minWidth:"1.5rem",height:"1.5rem"},Kr={size:"0.5rem"},Zr={fontSize:"0.625rem",minWidth:"1.25rem",height:"1.25rem"},qr={fontSize:"0.875rem",minWidth:"1.75rem",height:"1.75rem"},Ur={fontSize:"1rem",minWidth:"2rem",height:"2rem"},Jr={light:{primary:{background:"{primary.color}",color:"{primary.contrast.color}"},secondary:{background:"{surface.100}",color:"{surface.600}"},success:{background:"{green.500}",color:"{surface.0}"},info:{background:"{sky.500}",color:"{surface.0}"},warn:{background:"{orange.500}",color:"{surface.0}"},danger:{background:"{red.500}",color:"{surface.0}"},contrast:{background:"{surface.950}",color:"{surface.0}"}},dark:{primary:{background:"{primary.color}",color:"{primary.contrast.color}"},secondary:{background:"{surface.800}",color:"{surface.300}"},success:{background:"{green.400}",color:"{green.950}"},info:{background:"{sky.400}",color:"{sky.950}"},warn:{background:"{orange.400}",color:"{orange.950}"},danger:{background:"{red.400}",color:"{red.950}"},contrast:{background:"{surface.0}",color:"{surface.950}"}}},_s={root:Gr,dot:Kr,sm:Zr,lg:qr,xl:Ur,colorScheme:Jr,css:""},Qr={borderRadius:{none:"0",xs:"2px",sm:"4px",md:"6px",lg:"8px",xl:"12px"},emerald:{50:"#E8F6F1",100:"#C5EBE1",200:"#9EDFCF",300:"#76D3BD",400:"#58C9AF",500:"#3BBFA1",600:"#35AF94",700:"#2D9B83",800:"#268873",900:"#1A6657",950:"#0d3329"},green:{50:"#E8F5E9",100:"#C8E6C9",200:"#A5D6A7",300:"#81C784",400:"#66BB6A",500:"#4CAF50",600:"#43A047",700:"#388E3C",800:"#2E7D32",900:"#1B5E20",950:"#0e2f10"},lime:{50:"#F9FBE7",100:"#F0F4C3",200:"#E6EE9C",300:"#DCE775",400:"#D4E157",500:"#CDDC39",600:"#C0CA33",700:"#AFB42B",800:"#9E9D24",900:"#827717",950:"#413c0c"},red:{50:"#FFEBEE",100:"#FFCDD2",200:"#EF9A9A",300:"#E57373",400:"#EF5350",500:"#F44336",600:"#E53935",700:"#D32F2F",800:"#C62828",900:"#B71C1C",950:"#5c0e0e"},orange:{50:"#FFF3E0",100:"#FFE0B2",200:"#FFCC80",300:"#FFB74D",400:"#FFA726",500:"#FF9800",600:"#FB8C00",700:"#F57C00",800:"#EF6C00",900:"#E65100",950:"#732900"},amber:{50:"#FFF8E1",100:"#FFECB3",200:"#FFE082",300:"#FFD54F",400:"#FFCA28",500:"#FFC107",600:"#FFB300",700:"#FFA000",800:"#FF8F00",900:"#FF6F00",950:"#803800"},yellow:{50:"#FFFDE7",100:"#FFF9C4",200:"#FFF59D",300:"#FFF176",400:"#FFEE58",500:"#FFEB3B",600:"#FDD835",700:"#FBC02D",800:"#F9A825",900:"#F57F17",950:"#7b400c"},teal:{50:"#E0F2F1",100:"#B2DFDB",200:"#80CBC4",300:"#4DB6AC",400:"#26A69A",500:"#009688",600:"#00897B",700:"#00796B",800:"#00695C",900:"#004D40",950:"#002720"},cyan:{50:"#E0F7FA",100:"#B2EBF2",200:"#80DEEA",300:"#4DD0E1",400:"#26C6DA",500:"#00BCD4",600:"#00ACC1",700:"#0097A7",800:"#00838F",900:"#006064",950:"#003032"},sky:{50:"#E1F5FE",100:"#B3E5FC",200:"#81D4FA",300:"#4FC3F7",400:"#29B6F6",500:"#03A9F4",600:"#039BE5",700:"#0288D1",800:"#0277BD",900:"#01579B",950:"#012c4e"},blue:{50:"#E3F2FD",100:"#BBDEFB",200:"#90CAF9",300:"#64B5F6",400:"#42A5F5",500:"#2196F3",600:"#1E88E5",700:"#1976D2",800:"#1565C0",900:"#0D47A1",950:"#072451"},indigo:{50:"#E8EAF6",100:"#C5CAE9",200:"#9FA8DA",300:"#7986CB",400:"#5C6BC0",500:"#3F51B5",600:"#3949AB",700:"#303F9F",800:"#283593",900:"#1A237E",950:"#0d123f"},violet:{50:"#EDE7F6",100:"#D1C4E9",200:"#B39DDB",300:"#9575CD",400:"#7E57C2",500:"#673AB7",600:"#5E35B1",700:"#512DA8",800:"#4527A0",900:"#311B92",950:"#190e49"},purple:{50:"#F3E5F5",100:"#E1BEE7",200:"#CE93D8",300:"#BA68C8",400:"#AB47BC",500:"#9C27B0",600:"#8E24AA",700:"#7B1FA2",800:"#6A1B9A",900:"#4A148C",950:"#250a46"},fuchsia:{50:"#FDE6F3",100:"#FBC1E3",200:"#F897D1",300:"#F56DBF",400:"#F34DB2",500:"#F12DA5",600:"#E0289D",700:"#CC2392",800:"#B81E88",900:"#951777",950:"#4b0c3c"},pink:{50:"#FCE4EC",100:"#F8BBD0",200:"#F48FB1",300:"#F06292",400:"#EC407A",500:"#E91E63",600:"#D81B60",700:"#C2185B",800:"#AD1457",900:"#880E4F",950:"#440728"},rose:{50:"#FFF0F0",100:"#FFD9D9",200:"#FFC0C0",300:"#FFA7A7",400:"#FF8E8E",500:"#FF7575",600:"#FF5252",700:"#FF3838",800:"#F71C1C",900:"#D50000",950:"#3E0000"},slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b",950:"#09090b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917",950:"#0c0a09"}},on={transitionDuration:"0.2s",focusRing:{width:"0",style:"none",color:"unset",offset:"0"},disabledOpacity:"0.38",iconSize:"1rem",anchorGutter:"0",primary:{50:"{emerald.50}",100:"{emerald.100}",200:"{emerald.200}",300:"{emerald.300}",400:"{emerald.400}",500:"{emerald.500}",600:"{emerald.600}",700:"{emerald.700}",800:"{emerald.800}",900:"{emerald.900}",950:"{emerald.950}"},formField:{paddingX:"0.75rem",paddingY:"0.75rem",sm:{fontSize:"0.875rem",paddingX:"0.625rem",paddingY:"0.625rem"},lg:{fontSize:"1.125rem",paddingX:"0.825rem",paddingY:"0.825rem"},borderRadius:"{border.radius.sm}",focusRing:{width:"2px",style:"solid",color:"{primary.color}",offset:"-2px",shadow:"none"},transitionDuration:"{transition.duration}"},list:{padding:"0.5rem 0",gap:"0",header:{padding:"0.75rem 1rem"},option:{padding:"0.75rem 1rem",borderRadius:"{border.radius.none}"},optionGroup:{padding:"0.75rem 1rem",fontWeight:"700"}},content:{borderRadius:"{border.radius.sm}"},mask:{transitionDuration:"0.15s"},navigation:{list:{padding:"0.5rem 0",gap:"0"},item:{padding:"0.75rem 1rem",borderRadius:"{border.radius.none}",gap:"0.5rem"},submenuLabel:{padding:"0.75rem 1rem",fontWeight:"700"},submenuIcon:{size:"0.875rem"}},overlay:{select:{borderRadius:"{border.radius.sm}",shadow:"0 5px 5px -3px rgba(0,0,0,.2), 0 8px 10px 1px rgba(0,0,0,.14), 0 3px 14px 2px rgba(0,0,0,.12)"},popover:{borderRadius:"{border.radius.sm}",padding:"1rem",shadow:"0 11px 15px -7px rgba(0,0,0,.2), 0 24px 38px 3px rgba(0,0,0,.14), 0 9px 46px 8px rgba(0,0,0,.12)"},modal:{borderRadius:"{border.radius.sm}",padding:"1.5rem",shadow:"0 11px 15px -7px rgba(0,0,0,.2), 0 24px 38px 3px rgba(0,0,0,.14), 0 9px 46px 8px rgba(0,0,0,.12)"},navigation:{shadow:"0 2px 4px -1px rgba(0,0,0,.2), 0 4px 5px 0 rgba(0,0,0,.14), 0 1px 10px 0 rgba(0,0,0,.12)"}},colorScheme:{light:{focusRing:{shadow:"0 0 1px 4px {surface.200}"},surface:{0:"#ffffff",50:"{slate.50}",100:"{slate.100}",200:"{slate.200}",300:"{slate.300}",400:"{slate.400}",500:"{slate.500}",600:"{slate.600}",700:"{slate.700}",800:"{slate.800}",900:"{slate.900}",950:"{slate.950}"},primary:{color:"{primary.500}",contrastColor:"#ffffff",hoverColor:"{primary.400}",activeColor:"{primary.300}"},highlight:{background:"color-mix(in srgb, {primary.color}, transparent 88%)",focusBackground:"color-mix(in srgb, {primary.color}, transparent 76%)",color:"{primary.700}",focusColor:"{primary.800}"},mask:{background:"rgba(0,0,0,0.32)",color:"{surface.200}"},formField:{background:"{surface.0}",disabledBackground:"{surface.300}",filledBackground:"{surface.100}",filledHoverBackground:"{surface.200}",filledFocusBackground:"{surface.100}",borderColor:"{surface.400}",hoverBorderColor:"{surface.900}",focusBorderColor:"{primary.color}",invalidBorderColor:"{red.800}",color:"{surface.900}",disabledColor:"{surface.600}",placeholderColor:"{surface.600}",invalidPlaceholderColor:"{red.800}",floatLabelColor:"{surface.600}",floatLabelFocusColor:"{primary.600}",floatLabelActiveColor:"{surface.600}",floatLabelInvalidColor:"{form.field.invalid.placeholder.color}",iconColor:"{surface.600}",shadow:"none"},text:{color:"{surface.900}",hoverColor:"{surface.900}",mutedColor:"{surface.600}",hoverMutedColor:"{surface.600}"},content:{background:"{surface.0}",hoverBackground:"{surface.100}",borderColor:"{surface.300}",color:"{text.color}",hoverColor:"{text.hover.color}"},overlay:{select:{background:"{surface.0}",borderColor:"{surface.0}",color:"{text.color}"},popover:{background:"{surface.0}",borderColor:"{surface.0}",color:"{text.color}"},modal:{background:"{surface.0}",borderColor:"{surface.0}",color:"{text.color}"}},list:{option:{focusBackground:"{surface.100}",selectedBackground:"{highlight.background}",selectedFocusBackground:"{highlight.focus.background}",color:"{text.color}",focusColor:"{text.hover.color}",selectedColor:"{highlight.color}",selectedFocusColor:"{highlight.focus.color}",icon:{color:"{surface.600}",focusColor:"{surface.600}"}},optionGroup:{background:"transparent",color:"{text.color}"}},navigation:{item:{focusBackground:"{surface.100}",activeBackground:"{surface.200}",color:"{text.color}",focusColor:"{text.hover.color}",activeColor:"{text.hover.color}",icon:{color:"{surface.600}",focusColor:"{surface.600}",activeColor:"{surface.600}"}},submenuLabel:{background:"transparent",color:"{text.color}"},submenuIcon:{color:"{surface.600}",focusColor:"{surface.600}",activeColor:"{surface.600}"}}},dark:{focusRing:{shadow:"0 0 1px 4px {surface.700}"},surface:{0:"#ffffff",50:"{zinc.50}",100:"{zinc.100}",200:"{zinc.200}",300:"{zinc.300}",400:"{zinc.400}",500:"{zinc.500}",600:"{zinc.600}",700:"{zinc.700}",800:"{zinc.800}",900:"{zinc.900}",950:"{zinc.950}"},primary:{color:"{primary.400}",contrastColor:"{surface.900}",hoverColor:"{primary.300}",activeColor:"{primary.200}"},highlight:{background:"color-mix(in srgb, {primary.400}, transparent 84%)",focusBackground:"color-mix(in srgb, {primary.400}, transparent 76%)",color:"rgba(255,255,255,.87)",focusColor:"rgba(255,255,255,.87)"},mask:{background:"rgba(0,0,0,0.6)",color:"{surface.200}"},formField:{background:"{surface.950}",disabledBackground:"{surface.700}",filledBackground:"{surface.800}",filledHoverBackground:"{surface.700}",filledFocusBackground:"{surface.800}",borderColor:"{surface.600}",hoverBorderColor:"{surface.400}",focusBorderColor:"{primary.color}",invalidBorderColor:"{red.300}",color:"{surface.0}",disabledColor:"{surface.400}",placeholderColor:"{surface.400}",invalidPlaceholderColor:"{red.300}",floatLabelColor:"{surface.400}",floatLabelFocusColor:"{primary.color}",floatLabelActiveColor:"{surface.400}",floatLabelInvalidColor:"{form.field.invalid.placeholder.color}",iconColor:"{surface.400}",shadow:"none"},text:{color:"{surface.0}",hoverColor:"{surface.0}",mutedColor:"{surface.400}",hoverMutedColor:"{surface.400}"},content:{background:"{surface.900}",hoverBackground:"{surface.800}",borderColor:"{surface.700}",color:"{text.color}",hoverColor:"{text.hover.color}"},overlay:{select:{background:"{surface.900}",borderColor:"{surface.900}",color:"{text.color}"},popover:{background:"{surface.900}",borderColor:"{surface.900}",color:"{text.color}"},modal:{background:"{surface.900}",borderColor:"{surface.900}",color:"{text.color}"}},list:{option:{focusBackground:"{surface.800}",selectedBackground:"{highlight.background}",selectedFocusBackground:"{highlight.focus.background}",color:"{text.color}",focusColor:"{text.hover.color}",selectedColor:"{highlight.color}",selectedFocusColor:"{highlight.focus.color}",icon:{color:"{surface.400}",focusColor:"{surface.400}"}},optionGroup:{background:"transparent",color:"{text.muted.color}"}},navigation:{item:{focusBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{text.color}",focusColor:"{text.hover.color}",activeColor:"{text.hover.color}",icon:{color:"{surface.400}",focusColor:"{surface.400}",activeColor:"{surface.400}"}},submenuLabel:{background:"transparent",color:"{text.muted.color}"},submenuIcon:{color:"{surface.400}",focusColor:"{surface.400}",activeColor:"{surface.400}"}}}}},As={primitive:Qr,semantic:on},rn={borderRadius:"{content.border.radius}"},Ws={root:rn,css:""},nn={padding:"1rem",background:"{content.background}",gap:"0.5rem",transitionDuration:"{transition.duration}"},en={color:"{text.muted.color}",hoverColor:"{text.color}",borderRadius:"{content.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",hoverColor:"{navigation.item.icon.focus.color}"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},tn={color:"{navigation.item.icon.color}"},Os={root:nn,item:en,separator:tn,css:""},an={borderRadius:"{form.field.border.radius}",roundedBorderRadius:"2rem",gap:"0.5rem",paddingX:"1rem",paddingY:"0.625rem",iconOnlyWidth:"3rem",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}",iconOnlyWidth:"2.5rem"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}",iconOnlyWidth:"3.5rem"},label:{fontWeight:"500"},raisedShadow:"0 3px 1px -2px rgba(0,0,0,.2), 0 2px 2px 0 rgba(0,0,0,.14), 0 1px 5px 0 rgba(0,0,0,.12)",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"},badgeSize:"1rem",transitionDuration:"{form.field.transition.duration}"},dn={light:{root:{primary:{background:"{primary.color}",hoverBackground:"{primary.hover.color}",activeBackground:"{primary.active.color}",borderColor:"{primary.color}",hoverBorderColor:"{primary.hover.color}",activeBorderColor:"{primary.active.color}",color:"{primary.contrast.color}",hoverColor:"{primary.contrast.color}",activeColor:"{primary.contrast.color}",focusRing:{color:"{primary.color}",shadow:"none"}},secondary:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",borderColor:"{surface.100}",hoverBorderColor:"{surface.200}",activeBorderColor:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}",focusRing:{color:"{surface.600}",shadow:"none"}},info:{background:"{sky.500}",hoverBackground:"{sky.400}",activeBackground:"{sky.300}",borderColor:"{sky.500}",hoverBorderColor:"{sky.400}",activeBorderColor:"{sky.300}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{sky.500}",shadow:"none"}},success:{background:"{green.500}",hoverBackground:"{green.400}",activeBackground:"{green.300}",borderColor:"{green.500}",hoverBorderColor:"{green.400}",activeBorderColor:"{green.300}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{green.500}",shadow:"none"}},warn:{background:"{orange.500}",hoverBackground:"{orange.400}",activeBackground:"{orange.300}",borderColor:"{orange.500}",hoverBorderColor:"{orange.400}",activeBorderColor:"{orange.300}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{orange.500}",shadow:"none"}},help:{background:"{purple.500}",hoverBackground:"{purple.400}",activeBackground:"{purple.300}",borderColor:"{purple.500}",hoverBorderColor:"{purple.400}",activeBorderColor:"{purple.300}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{purple.500}",shadow:"none"}},danger:{background:"{red.500}",hoverBackground:"{red.400}",activeBackground:"{red.300}",borderColor:"{red.500}",hoverBorderColor:"{red.400}",activeBorderColor:"{red.300}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{red.500}",shadow:"none"}},contrast:{background:"{surface.950}",hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{surface.950}",hoverBorderColor:"{surface.800}",activeBorderColor:"{surface.700}",color:"{surface.0}",hoverColor:"{surface.0}",activeColor:"{surface.0}",focusRing:{color:"{surface.950}",shadow:"none"}}},outlined:{primary:{hoverBackground:"{primary.50}",activeBackground:"{primary.100}",borderColor:"{primary.color}",color:"{primary.color}"},secondary:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.600}",color:"{surface.600}"},success:{hoverBackground:"{green.50}",activeBackground:"{green.100}",borderColor:"{green.500}",color:"{green.500}"},info:{hoverBackground:"{sky.50}",activeBackground:"{sky.100}",borderColor:"{sky.500}",color:"{sky.500}"},warn:{hoverBackground:"{orange.50}",activeBackground:"{orange.100}",borderColor:"{orange.500}",color:"{orange.500}"},help:{hoverBackground:"{purple.50}",activeBackground:"{purple.100}",borderColor:"{purple.500}",color:"{purple.500}"},danger:{hoverBackground:"{red.50}",activeBackground:"{red.100}",borderColor:"{red.500}",color:"{red.500}"},contrast:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.950}",color:"{surface.950}"},plain:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.900}",color:"{surface.900}"}},text:{primary:{hoverBackground:"{primary.50}",activeBackground:"{primary.100}",color:"{primary.color}"},secondary:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",color:"{surface.600}"},success:{hoverBackground:"{green.50}",activeBackground:"{green.100}",color:"{green.500}"},info:{hoverBackground:"{sky.50}",activeBackground:"{sky.100}",color:"{sky.500}"},warn:{hoverBackground:"{orange.50}",activeBackground:"{orange.100}",color:"{orange.500}"},help:{hoverBackground:"{purple.50}",activeBackground:"{purple.100}",color:"{purple.500}"},danger:{hoverBackground:"{red.50}",activeBackground:"{red.100}",color:"{red.500}"},contrast:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",color:"{surface.950}"},plain:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",color:"{surface.900}"}},link:{color:"{primary.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"}},dark:{root:{primary:{background:"{primary.color}",hoverBackground:"{primary.hover.color}",activeBackground:"{primary.active.color}",borderColor:"{primary.color}",hoverBorderColor:"{primary.hover.color}",activeBorderColor:"{primary.active.color}",color:"{primary.contrast.color}",hoverColor:"{primary.contrast.color}",activeColor:"{primary.contrast.color}",focusRing:{color:"{primary.color}",shadow:"none"}},secondary:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",borderColor:"{surface.800}",hoverBorderColor:"{surface.700}",activeBorderColor:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}",focusRing:{color:"{surface.300}",shadow:"none"}},info:{background:"{sky.400}",hoverBackground:"{sky.300}",activeBackground:"{sky.200}",borderColor:"{sky.400}",hoverBorderColor:"{sky.300}",activeBorderColor:"{sky.200}",color:"{sky.950}",hoverColor:"{sky.950}",activeColor:"{sky.950}",focusRing:{color:"{sky.400}",shadow:"none"}},success:{background:"{green.400}",hoverBackground:"{green.300}",activeBackground:"{green.200}",borderColor:"{green.400}",hoverBorderColor:"{green.300}",activeBorderColor:"{green.200}",color:"{green.950}",hoverColor:"{green.950}",activeColor:"{green.950}",focusRing:{color:"{green.400}",shadow:"none"}},warn:{background:"{orange.400}",hoverBackground:"{orange.300}",activeBackground:"{orange.200}",borderColor:"{orange.400}",hoverBorderColor:"{orange.300}",activeBorderColor:"{orange.200}",color:"{orange.950}",hoverColor:"{orange.950}",activeColor:"{orange.950}",focusRing:{color:"{orange.400}",shadow:"none"}},help:{background:"{purple.400}",hoverBackground:"{purple.300}",activeBackground:"{purple.200}",borderColor:"{purple.400}",hoverBorderColor:"{purple.300}",activeBorderColor:"{purple.200}",color:"{purple.950}",hoverColor:"{purple.950}",activeColor:"{purple.950}",focusRing:{color:"{purple.400}",shadow:"none"}},danger:{background:"{red.400}",hoverBackground:"{red.300}",activeBackground:"{red.200}",borderColor:"{red.400}",hoverBorderColor:"{red.300}",activeBorderColor:"{red.200}",color:"{red.950}",hoverColor:"{red.950}",activeColor:"{red.950}",focusRing:{color:"{red.400}",shadow:"none"}},contrast:{background:"{surface.0}",hoverBackground:"{surface.100}",activeBackground:"{surface.200}",borderColor:"{surface.0}",hoverBorderColor:"{surface.100}",activeBorderColor:"{surface.200}",color:"{surface.950}",hoverColor:"{surface.950}",activeColor:"{surface.950}",focusRing:{color:"{surface.0}",shadow:"none"}}},outlined:{primary:{hoverBackground:"color-mix(in srgb, {primary.color}, transparent 96%)",activeBackground:"color-mix(in srgb, {primary.color}, transparent 84%)",borderColor:"{primary.700}",color:"{primary.color}"},secondary:{hoverBackground:"rgba(255,255,255,0.04)",activeBackground:"rgba(255,255,255,0.16)",borderColor:"{surface.700}",color:"{surface.400}"},success:{hoverBackground:"color-mix(in srgb, {green.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {green.400}, transparent 84%)",borderColor:"{green.700}",color:"{green.400}"},info:{hoverBackground:"color-mix(in srgb, {sky.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {sky.400}, transparent 84%)",borderColor:"{sky.700}",color:"{sky.400}"},warn:{hoverBackground:"color-mix(in srgb, {orange.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {orange.400}, transparent 84%)",borderColor:"{orange.700}",color:"{orange.400}"},help:{hoverBackground:"color-mix(in srgb, {purple.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {purple.400}, transparent 84%)",borderColor:"{purple.700}",color:"{purple.400}"},danger:{hoverBackground:"color-mix(in srgb, {red.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {red.400}, transparent 84%)",borderColor:"{red.700}",color:"{red.400}"},contrast:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{surface.500}",color:"{surface.0}"},plain:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{surface.600}",color:"{surface.0}"}},text:{primary:{hoverBackground:"color-mix(in srgb, {primary.color}, transparent 96%)",activeBackground:"color-mix(in srgb, {primary.color}, transparent 84%)",color:"{primary.color}"},secondary:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{surface.400}"},success:{hoverBackground:"color-mix(in srgb, {green.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {green.400}, transparent 84%)",color:"{green.400}"},info:{hoverBackground:"color-mix(in srgb, {sky.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {sky.400}, transparent 84%)",color:"{sky.400}"},warn:{hoverBackground:"color-mix(in srgb, {orange.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {orange.400}, transparent 84%)",color:"{orange.400}"},help:{hoverBackground:"color-mix(in srgb, {purple.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {purple.400}, transparent 84%)",color:"{purple.400}"},danger:{hoverBackground:"color-mix(in srgb, {red.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {red.400}, transparent 84%)",color:"{red.400}"},contrast:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{surface.0}"},plain:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{surface.0}"}},link:{color:"{primary.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"}}},cn=({dt:o})=>`
.p-button:focus-visible {
    background: ${o("button.primary.active.background")};
    border-color: ${o("button.primary.active.background")};
}

.p-button-secondary:focus-visible {
    background: ${o("button.secondary.active.background")};
    border-color: ${o("button.secondary.active.background")};
}

.p-button-success:focus-visible {
    background: ${o("button.success.active.background")};
    border-color: ${o("button.success.active.background")};
}

.p-button-info:focus-visible {
    background: ${o("button.info.active.background")};
    border-color: ${o("button.info.active.background")};
}

.p-button-warn:focus-visible {
    background: ${o("button.warn.active.background")};
    border-color: ${o("button.warn.active.background")};
}

.p-button-help:focus-visible {
    background: ${o("button.help.active.background")};
    border-color: ${o("button.help.active.background")};
}

.p-button-danger:focus-visible {
    background: ${o("button.danger.active.background")};
    border-color: ${o("button.danger.active.background")};
}

.p-button-contrast:focus-visible {
    background: ${o("button.contrast.active.background")};
    border-color: ${o("button.contrast.active.background")};
}

.p-button-link:focus-visible {
    background: color-mix(in srgb, ${o("primary.color")}, transparent 84%);
    border-color: transparent;
}

.p-button-text:focus-visible {
    background: ${o("button.text.primary.active.background")};
    border-color: transparent;
}

.p-button-secondary.p-button-text:focus-visible {
    background: ${o("button.text.secondary.active.background")};
    border-color: transparent;
}

.p-button-success.p-button-text:focus-visible {
    background: ${o("button.text.success.active.background")};
    border-color: transparent;
}

.p-button-info.p-button-text:focus-visible {
    background: ${o("button.text.info.active.background")};
    border-color: transparent;
}

.p-button-warn.p-button-text:focus-visible {
    background: ${o("button.text.warn.active.background")};
    border-color: transparent;
}

.p-button-help.p-button-text:focus-visible {
    background: ${o("button.text.help.active.background")};
    border-color: transparent;
}

.p-button-danger.p-button-text:focus-visible {
    background: ${o("button.text.danger.active.background")};
    border-color: transparent;
}

.p-button-contrast.p-button-text:focus-visible {
    background: ${o("button.text.contrast.active.background")};
    border-color: transparent;
}

.p-button-plain.p-button-text:focus-visible {
    background: ${o("button.text.plain.active.background")};
    border-color: transparent;
}

.p-button-outlined:focus-visible {
    background: ${o("button.outlined.primary.active.background")};
}

.p-button-secondary.p-button-outlined:focus-visible {
    background: ${o("button.outlined.secondary.active.background")};
    border-color: ${o("button.outlined.secondary.border.color")};
}

.p-button-success.p-button-outlined:focus-visible {
    background: ${o("button.outlined.success.active.background")};
}

.p-button-info.p-button-outlined:focus-visible {
    background: ${o("button.outlined.info.active.background")};
}

.p-button-warn.p-button-outlined:focus-visible {
    background: ${o("button.outlined.warn.active.background")};
}

.p-button-help.p-button-outlined:focus-visible {
    background: ${o("button.outlined.help.active.background")};
}

.p-button-danger.p-button-outlined:focus-visible {
    background: ${o("button.outlined.danger.active.background")};
}

.p-button-contrast.p-button-outlined:focus-visible {
    background: ${o("button.outlined.contrast.active.background")};
}

.p-button-plain.p-button-outlined:focus-visible {
    background: ${o("button.outlined.plain.active.background")};
}
`,Is={root:an,colorScheme:dn,css:cn},ln={background:"{content.background}",borderRadius:"{content.border.radius}",color:"{content.color}",shadow:"0 2px 1px -1px rgba(0,0,0,.2), 0 1px 1px 0 rgba(0,0,0,.14), 0 1px 3px 0 rgba(0,0,0,.12)"},sn={padding:"1.5rem",gap:"0.75rem"},un={gap:"0.5rem"},bn={fontSize:"1.25rem",fontWeight:"500"},pn={color:"{text.muted.color}"},Ls={root:ln,body:sn,caption:un,title:bn,subtitle:pn,css:""},gn={transitionDuration:"{transition.duration}"},fn={gap:"0.25rem"},hn={padding:"1rem",gap:"1rem"},mn={width:"1.25rem",height:"1.25rem",borderRadius:"50%",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"}},kn={light:{indicator:{background:"{surface.200}",hoverBackground:"{surface.300}",activeBackground:"{primary.color}"}},dark:{indicator:{background:"{surface.700}",hoverBackground:"{surface.600}",activeBackground:"{primary.color}"}}},vn=({dt:o})=>`
.p-carousel-indicator-button:hover {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o("text.color")}, transparent 96%);
}

.p-carousel-indicator-button:focus-visible {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o("text.color")}, transparent 96%);
}

.p-carousel-indicator-active .p-carousel-indicator-button:hover {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o("carousel.indicator.active.background")}, transparent 92%);
}

.p-carousel-indicator-active .p-carousel-indicator-button:focus-visible {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o("carousel.indicator.active.background")}, transparent 84%);
}
`,Ns={root:gn,content:fn,indicatorList:hn,indicator:mn,colorScheme:kn,css:vn},$n={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},xn={width:"2.5rem",color:"{form.field.icon.color}"},yn={background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},wn={padding:"{list.padding}",gap:"{list.gap}",mobileIndent:"1rem"},Cn={focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}",icon:{color:"{list.option.icon.color}",focusColor:"{list.option.icon.focus.color}",size:"0.875rem"}},Bn={color:"{form.field.icon.color}"},zn=({dt:o})=>`
.p-cascadeselect.p-variant-filled {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 1px solid transparent;
    background: ${o("cascadeselect.filled.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("cascadeselect.focus.border.color")}, ${o("cascadeselect.focus.border.color")}), linear-gradient(to bottom, ${o("cascadeselect.border.color")}, ${o("cascadeselect.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    transition: background-size 0.3s cubic-bezier(0.64, 0.09, 0.08, 1);
}

.p-cascadeselect.p-variant-filled:not(.p-disabled):hover {
    background: ${o("cascadeselect.filled.hover.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("cascadeselect.focus.border.color")}, ${o("cascadeselect.focus.border.color")}), linear-gradient(to bottom, ${o("cascadeselect.hover.border.color")}, ${o("cascadeselect.hover.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-cascadeselect.p-variant-filled:not(.p-disabled).p-focus {
    outline: 0 none;
    background: ${o("cascadeselect.filled.focus.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("cascadeselect.focus.border.color")}, ${o("cascadeselect.focus.border.color")}), linear-gradient(to bottom, ${o("cascadeselect.border.color")}, ${o("cascadeselect.border.color")});
    background-size: 100% 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-cascadeselect.p-variant-filled:not(.p-disabled).p-focus:hover {
    background-image: linear-gradient(to bottom, ${o("cascadeselect.focus.border.color")}, ${o("cascadeselect.focus.border.color")}), linear-gradient(to bottom, ${o("cascadeselect.hover.border.color")}, ${o("cascadeselect.hover.border.color")});
}

.p-cascadeselect.p-variant-filled.p-invalid {
    background-image: linear-gradient(to bottom, ${o("cascadeselect.invalid.border.color")}, ${o("cascadeselect.invalid.border.color")}), linear-gradient(to bottom, ${o("cascadeselect.invalid.border.color")}, ${o("cascadeselect.invalid.border.color")});
}

.p-cascadeselect.p-variant-filled.p-invalid:not(.p-disabled).p-focus  {
    background-image: linear-gradient(to bottom, ${o("cascadeselect.invalid.border.color")}, ${o("cascadeselect.invalid.border.color")}), linear-gradient(to bottom, ${o("cascadeselect.invalid.border.color")}, ${o("cascadeselect.invalid.border.color")});
}

.p-cascadeselect-option {
    transition: none;
}
`,Ts={root:$n,dropdown:xn,overlay:yn,list:wn,option:Cn,clearIcon:Bn,css:zn},Rn={borderRadius:"{border.radius.xs}",width:"18px",height:"18px",background:"{form.field.background}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.color}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",checkedBorderColor:"{primary.color}",checkedHoverBorderColor:"{primary.color}",checkedFocusBorderColor:"{primary.color}",checkedDisabledBorderColor:"{form.field.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",shadow:"{form.field.shadow}",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"},transitionDuration:"{form.field.transition.duration}",sm:{width:"14px",height:"14px"},lg:{width:"22px",height:"22px"}},Sn={size:"0.875rem",color:"{form.field.color}",checkedColor:"{primary.contrast.color}",checkedHoverColor:"{primary.contrast.color}",disabledColor:"{form.field.disabled.color}",sm:{size:"0.75rem"},lg:{size:"1rem"}},Fn=({dt:o})=>`
.p-checkbox {
    border-radius: 50%;
    transition: box-shadow ${o("checkbox.transition.duration")};
}

.p-checkbox-box {
    border-width: 2px;
}

.p-checkbox:not(.p-disabled):has(.p-checkbox-input:hover) {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o("text.color")}, transparent 96%);
}

.p-checkbox:not(.p-disabled):has(.p-checkbox-input:focus-visible) {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o("text.color")}, transparent 88%);
}

.p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:hover) {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o("checkbox.checked.background")}, transparent 92%);
}

.p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:focus-visible) {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o("checkbox.checked.background")}, transparent 84%);
}

.p-checkbox-checked .p-checkbox-box:before  {
    content: "";
    position: absolute;
    top: var(--p-md-check-icon-t);
    left: 2px;
    border-right: 2px solid transparent;
    border-bottom: 2px solid transparent;
    transform: rotate(45deg);
    transform-origin: 0% 100%;
    animation: p-md-check 125ms 50ms linear forwards;
}

.p-checkbox-checked .p-checkbox-icon {
    display: none;
}

.p-checkbox {
    --p-md-check-icon-t: 10px;
    --p-md-check-icon-w: 6px;
    --p-md-check-icon-h: 12px;
}

.p-checkbox-sm {
    --p-md-check-icon-t: 8px;
    --p-md-check-icon-w: 4px;
    --p-md-check-icon-h: 10px;
}

.p-checkbox-lg {
    --p-md-check-icon-t: 12px;
    --p-md-check-icon-w: 8px;
    --p-md-check-icon-h: 16px;
}

@keyframes p-md-check {
    0%{
      width: 0;
      height: 0;
      border-color: ${o("checkbox.icon.checked.color")};
      transform: translate3d(0,0,0) rotate(45deg);
    }
    33%{
      width: var(--p-md-check-icon-w);
      height: 0;
      transform: translate3d(0,0,0) rotate(45deg);
    }
    100%{
      width: var(--p-md-check-icon-w);
      height: var(--p-md-check-icon-h);
      border-color: ${o("checkbox.icon.checked.color")};
      transform: translate3d(0,calc(-1 * var(--p-md-check-icon-h)),0) rotate(45deg);
    }
}
`,Ps={root:Rn,icon:Sn,css:Fn},En={borderRadius:"2rem",paddingX:"0.75rem",paddingY:"0.75rem",gap:"0.5rem",transitionDuration:"{transition.duration}"},Dn={width:"2.25rem",height:"2.25rem"},_n={size:"1rem"},An={size:"1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}"}},Wn={light:{root:{background:"{surface.200}",color:"{surface.900}"},icon:{color:"{surface.600}"},removeIcon:{color:"{surface.600}",focusRing:{shadow:"0 0 1px 4px {surface.300}"}}},dark:{root:{background:"{surface.700}",color:"{surface.0}"},icon:{color:"{surface.0}"},removeIcon:{color:"{surface.0}",focusRing:{shadow:"0 0 1px 4px {surface.600}"}}}},js={root:En,image:Dn,icon:_n,removeIcon:An,colorScheme:Wn,css:""},On={transitionDuration:"{transition.duration}"},In={width:"2rem",height:"2rem",borderRadius:"{form.field.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},Ln={shadow:"{overlay.popover.shadow}",borderRadius:"{overlay.popover.borderRadius}"},Nn={light:{panel:{background:"{surface.800}",borderColor:"{surface.900}"},handle:{color:"{surface.0}"}},dark:{panel:{background:"{surface.900}",borderColor:"{surface.700}"},handle:{color:"{surface.0}"}}},Hs={root:On,preview:In,panel:Ln,colorScheme:Nn,css:""},Tn={size:"2rem",color:"{overlay.modal.color}"},Pn={gap:"1rem"},Vs={icon:Tn,content:Pn,css:""},jn={background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",color:"{overlay.popover.color}",borderRadius:"{overlay.popover.border.radius}",shadow:"{overlay.popover.shadow}",gutter:"10px",arrowOffset:"1.25rem"},Hn={padding:"{overlay.popover.padding}",gap:"1rem"},Vn={size:"1.5rem",color:"{overlay.popover.color}"},Yn={gap:"0.5rem",padding:"0 {overlay.popover.padding} {overlay.popover.padding} {overlay.popover.padding}"},Ys={root:jn,content:Hn,icon:Vn,footer:Yn,css:""},Xn={background:"{content.background}",borderColor:"transparent",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},Mn={padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},Gn={focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},Kn={mobileIndent:"1rem"},Zn={size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},qn={borderColor:"{content.border.color}"},Xs={root:Xn,list:Mn,item:Gn,submenu:Kn,submenuIcon:Zn,separator:qn,css:""},Un={transitionDuration:"{transition.duration}"},Jn={background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},Qn={background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{datatable.border.color}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",gap:"0.5rem",padding:"0.75rem 1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"},sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},oe={fontWeight:"600"},re={background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},ne={borderColor:"{datatable.border.color}",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},ee={background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},te={fontWeight:"600"},ae={background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},ie={color:"{primary.color}"},de={width:"0.5rem"},ce={width:"1px",color:"{primary.color}"},le={color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",size:"0.875rem"},se={size:"2rem"},ue={hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",selectedHoverColor:"{primary.color}",size:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},be={inlineGap:"0.5rem",overlaySelect:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},overlayPopover:{background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",borderRadius:"{overlay.popover.border.radius}",color:"{overlay.popover.color}",shadow:"{overlay.popover.shadow}",padding:"{overlay.popover.padding}",gap:"0.5rem"},rule:{borderColor:"{content.border.color}"},constraintList:{padding:"{list.padding}",gap:"{list.gap}"},constraint:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",separator:{borderColor:"{content.border.color}"},padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"}},pe={borderColor:"{datatable.border.color}",borderWidth:"0 0 1px 0"},ge={borderColor:"{datatable.border.color}",borderWidth:"0 0 1px 0"},fe={light:{root:{borderColor:"{content.border.color}"},row:{stripedBackground:"{surface.50}"},bodyCell:{selectedBorderColor:"{primary.100}"}},dark:{root:{borderColor:"{surface.800}"},row:{stripedBackground:"{surface.950}"},bodyCell:{selectedBorderColor:"{primary.900}"}}},he=`
.p-datatable-header-cell,
.p-datatable-tbody > tr {
    transition: none;
}
`,Ms={root:Un,header:Jn,headerCell:Qn,columnTitle:oe,row:re,bodyCell:ne,footerCell:ee,columnFooter:te,footer:ae,dropPoint:ie,columnResizer:de,resizeIndicator:ce,sortIcon:le,loadingIcon:se,rowToggleButton:ue,filter:be,paginatorTop:pe,paginatorBottom:ge,colorScheme:fe,css:he},me={borderColor:"transparent",borderWidth:"0",borderRadius:"0",padding:"0"},ke={background:"{content.background}",color:"{content.color}",borderColor:"{content.border.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem",borderRadius:"0"},ve={background:"{content.background}",color:"{content.color}",borderColor:"transparent",borderWidth:"0",padding:"0",borderRadius:"0"},$e={background:"{content.background}",color:"{content.color}",borderColor:"{content.border.color}",borderWidth:"1px 0 0 0",padding:"0.75rem 1rem",borderRadius:"0"},xe={borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},ye={borderColor:"{content.border.color}",borderWidth:"1px 0 0 0"},Gs={root:me,header:ke,content:ve,footer:$e,paginatorTop:xe,paginatorBottom:ye,css:""},we={transitionDuration:"{transition.duration}"},Ce={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.popover.shadow}",padding:"0.5rem"},Be={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",padding:"0 0 0.5rem 0"},ze={gap:"0.5rem",fontWeight:"700"},Re={width:"3rem",sm:{width:"2.5rem"},lg:{width:"3.5rem"},borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",borderRadius:"{form.field.border.radius}",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"}},Se={color:"{form.field.icon.color}"},Fe={hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",padding:"0.5rem 0.75rem",borderRadius:"{content.border.radius}"},Ee={hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",padding:"0.5rem 0.75rem",borderRadius:"{content.border.radius}"},De={borderColor:"{content.border.color}",gap:"{overlay.popover.padding}"},_e={margin:"0.5rem 0 0 0"},Ae={padding:"0.5rem",fontWeight:"700",color:"{content.color}"},We={hoverBackground:"{content.hover.background}",selectedBackground:"{primary.color}",rangeSelectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{primary.contrast.color}",rangeSelectedColor:"{highlight.color}",width:"2.5rem",height:"2.5rem",borderRadius:"50%",padding:"0.125rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},Oe={margin:"0.5rem 0 0 0"},Ie={padding:"0.625rem",borderRadius:"{content.border.radius}"},Le={margin:"0.5rem 0 0 0"},Ne={padding:"0.625rem",borderRadius:"{content.border.radius}"},Te={padding:"0.5rem 0 0 0",borderColor:"{content.border.color}"},Pe={padding:"0.5rem 0 0 0",borderColor:"{content.border.color}",gap:"0.5rem",buttonGap:"0.25rem"},je={light:{dropdown:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}"},today:{background:"{surface.200}",color:"{surface.900}"}},dark:{dropdown:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}"},today:{background:"{surface.700}",color:"{surface.0}"}}},He=({dt:o})=>`
.p-datepicker-header {
    justify-content: start;
}

.p-datepicker-title {
    order: 1;
}

.p-datepicker-prev-button {
    order: 2;
    margin-inline-start: auto;
}

.p-datepicker-next-button {
    order: 2;
    margin-inline-start: 0.5rem;
}

.p-datepicker-select-month:focus-visible {
    background: ${o("datepicker.select.month.hover.background")};
    color: ${o("datepicker.select.month.hover.color")};
    outline: 0 none;
}

.p-datepicker-select-year:focus-visible {
    background: ${o("datepicker.select.year.hover.background")};
    color: ${o("datepicker.select.year.hover.color")};
    outline: 0 none;
}

.p-datepicker-dropdown:focus-visible {
    outline: 0 none;
    background: ${o("datepicker.dropdown.hover.background")};
    border-color: ${o("datepicker.dropdown.hover.border.color")};
    color: ${o("datepicker.dropdown.hover.color")};
}
`,Ks={root:we,panel:Ce,header:Be,title:ze,dropdown:Re,inputIcon:Se,selectMonth:Fe,selectYear:Ee,group:De,dayView:_e,weekDay:Ae,date:We,monthView:Oe,month:Ie,yearView:Le,year:Ne,buttonbar:Te,timePicker:Pe,colorScheme:je,css:He},Ve={background:"{overlay.modal.background}",borderColor:"{overlay.modal.border.color}",color:"{overlay.modal.color}",borderRadius:"{overlay.modal.border.radius}",shadow:"{overlay.modal.shadow}"},Ye={padding:"{overlay.modal.padding}",gap:"0.5rem"},Xe={fontSize:"1.25rem",fontWeight:"600"},Me={padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}"},Ge={padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}",gap:"0.5rem"},Zs={root:Ve,header:Ye,title:Xe,content:Me,footer:Ge,css:""},Ke={borderColor:"{content.border.color}"},Ze={background:"{content.background}",color:"{text.color}"},qe={margin:"1rem 0",padding:"0 1rem",content:{padding:"0 0.5rem"}},Ue={margin:"0 1rem",padding:"0.5rem 0",content:{padding:"0.5rem 0"}},qs={root:Ke,content:Ze,horizontal:qe,vertical:Ue,css:""},Je={background:"rgba(255, 255, 255, 0.1)",borderColor:"rgba(255, 255, 255, 0.2)",padding:"0.5rem",borderRadius:"{border.radius.xl}"},Qe={borderRadius:"{content.border.radius}",padding:"0.5rem",size:"3rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},Us={root:Je,item:Qe,css:""},ot={background:"{overlay.modal.background}",borderColor:"{overlay.modal.border.color}",color:"{overlay.modal.color}",shadow:"{overlay.modal.shadow}"},rt={padding:"{overlay.modal.padding}"},nt={fontSize:"1.5rem",fontWeight:"600"},et={padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}"},tt={padding:"{overlay.modal.padding}"},Js={root:ot,header:rt,title:nt,content:et,footer:tt,css:""},at={background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}"},it={color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},dt={background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}",padding:"{list.padding}"},ct={focusBackground:"{list.option.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},lt={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"},st=`
.p-editor .p-editor-toolbar {
    padding: 0.75rem
}
`,Qs={toolbar:at,toolbarItem:it,overlay:dt,overlayOption:ct,content:lt,css:st},ut={background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",padding:"0 1.25rem 1.25rem 1.25rem",transitionDuration:"{transition.duration}"},bt={background:"{content.background}",hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",borderRadius:"{content.border.radius}",borderWidth:"1px",borderColor:"transparent",padding:"0.75rem 1rem",gap:"0.5rem",fontWeight:"600",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"}},pt={color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}"},gt={padding:"0"},ft=({dt:o})=>`
.p-fieldset-toggle-button:focus-visible {
    background: ${o("navigation.item.active.background")};
}
`,ou={root:ut,legend:bt,toggleIcon:pt,content:gt,css:ft},ht={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",transitionDuration:"{transition.duration}"},mt={background:"transparent",color:"{text.color}",padding:"1.25rem",borderColor:"unset",borderWidth:"0",borderRadius:"0",gap:"0.5rem"},kt={highlightBorderColor:"{primary.color}",padding:"0 1.25rem 1.25rem 1.25rem",gap:"1rem"},vt={padding:"1rem",gap:"1rem",borderColor:"{content.border.color}",info:{gap:"0.5rem"}},$t={gap:"0.5rem"},xt={height:"0.25rem"},yt={gap:"0.5rem"},ru={root:ht,header:mt,content:kt,file:vt,fileList:$t,progressbar:xt,basic:yt,css:""},wt={color:"{form.field.float.label.color}",focusColor:"{form.field.float.label.focus.color}",activeColor:"{form.field.float.label.active.color}",invalidColor:"{form.field.float.label.invalid.color}",transitionDuration:"0.2s",positionX:"{form.field.padding.x}",positionY:"{form.field.padding.y}",fontWeight:"500",active:{fontSize:"0.75rem",fontWeight:"400"}},Ct={active:{top:"-1.25rem"}},Bt={input:{paddingTop:"1.5rem",paddingBottom:"0.5rem"},active:{top:"0.5rem"}},zt={borderRadius:"{border.radius.xs}",active:{background:"{form.field.background}",padding:"0 0.125rem"}},nu={root:wt,over:Ct,in:Bt,on:zt,css:""},Rt={borderWidth:"1px",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",transitionDuration:"{transition.duration}"},St={background:"rgba(255, 255, 255, 0.1)",hoverBackground:"rgba(255, 255, 255, 0.2)",color:"{surface.100}",hoverColor:"{surface.0}",size:"3rem",gutter:"0.5rem",prev:{borderRadius:"50%"},next:{borderRadius:"50%"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},Ft={size:"1.5rem"},Et={background:"{content.background}",padding:"1rem 0.25rem"},Dt={size:"2rem",borderRadius:"50%",gutter:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},_t={size:"1rem"},At={background:"rgba(0, 0, 0, 0.5)",color:"{surface.100}",padding:"1rem"},Wt={gap:"0.5rem",padding:"1rem"},Ot={width:"1rem",height:"1rem",activeBackground:"{primary.color}",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},It={background:"rgba(0, 0, 0, 0.5)"},Lt={background:"rgba(255, 255, 255, 0.4)",hoverBackground:"rgba(255, 255, 255, 0.6)",activeBackground:"rgba(255, 255, 255, 0.9)"},Nt={size:"3rem",gutter:"0.5rem",background:"rgba(255, 255, 255, 0.1)",hoverBackground:"rgba(255, 255, 255, 0.2)",color:"{surface.50}",hoverColor:"{surface.0}",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},Tt={size:"1.5rem"},Pt={light:{thumbnailNavButton:{hoverBackground:"{surface.100}",color:"{surface.600}",hoverColor:"{surface.700}"},indicatorButton:{background:"{surface.200}",hoverBackground:"{surface.300}"}},dark:{thumbnailNavButton:{hoverBackground:"{surface.700}",color:"{surface.400}",hoverColor:"{surface.0}"},indicatorButton:{background:"{surface.700}",hoverBackground:"{surface.600}"}}},eu={root:Rt,navButton:St,navIcon:Ft,thumbnailsContent:Et,thumbnailNavButton:Dt,thumbnailNavButtonIcon:_t,caption:At,indicatorList:Wt,indicatorButton:Ot,insetIndicatorList:It,insetIndicatorButton:Lt,closeButton:Nt,closeButtonIcon:Tt,colorScheme:Pt,css:""},jt={color:"{form.field.icon.color}"},tu={icon:jt,css:""},Ht={color:"{form.field.float.label.color}",focusColor:"{form.field.float.label.focus.color}",invalidColor:"{form.field.float.label.invalid.color}",transitionDuration:"0.2s",positionX:"{form.field.padding.x}",top:"0.5rem",fontSize:"0.75rem",fontWeight:"400"},Vt={paddingTop:"1.5rem",paddingBottom:"0.5rem"},au={root:Ht,input:Vt,css:""},Yt={transitionDuration:"{transition.duration}"},Xt={icon:{size:"1.5rem"},mask:{background:"{mask.background}",color:"{mask.color}"}},Mt={position:{left:"auto",right:"1rem",top:"1rem",bottom:"auto"},blur:"8px",background:"rgba(255,255,255,0.1)",borderColor:"rgba(255,255,255,0.2)",borderWidth:"1px",borderRadius:"30px",padding:".5rem",gap:"0.5rem"},Gt={hoverBackground:"rgba(255,255,255,0.1)",color:"{surface.50}",hoverColor:"{surface.0}",size:"3rem",iconSize:"1.5rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},iu={root:Yt,preview:Xt,toolbar:Mt,action:Gt,css:""},Kt={size:"20px",hoverSize:"40px",background:"rgba(255,255,255,0.4)",hoverBackground:"rgba(255,255,255,0.6)",borderColor:"unset",hoverBorderColor:"unset",borderWidth:"0",borderRadius:"50%",transitionDuration:"{transition.duration}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"rgba(255,255,255,0.3)",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},du={handle:Kt,css:""},Zt={padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{content.border.radius}",gap:"0.5rem"},qt={fontWeight:"500"},Ut={size:"1rem"},Jt={light:{info:{background:"color-mix(in srgb, {blue.50}, transparent 5%)",borderColor:"{blue.200}",color:"{blue.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)"},success:{background:"color-mix(in srgb, {green.50}, transparent 5%)",borderColor:"{green.200}",color:"{green.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)"},warn:{background:"color-mix(in srgb,{yellow.50}, transparent 5%)",borderColor:"{yellow.200}",color:"{yellow.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)"},error:{background:"color-mix(in srgb, {red.50}, transparent 5%)",borderColor:"{red.200}",color:"{red.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)"},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)"},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)"}},dark:{info:{background:"color-mix(in srgb, {blue.500}, transparent 84%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{blue.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)"},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{green.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)"},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 84%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{yellow.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)"},error:{background:"color-mix(in srgb, {red.500}, transparent 84%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{red.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)"},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)"},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)"}}},cu={root:Zt,text:qt,icon:Ut,colorScheme:Jt,css:""},Qt={padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{transition.duration}"},oa={hoverBackground:"{content.hover.background}",hoverColor:"{content.hover.color}"},lu={root:Qt,display:oa,css:""},ra={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},na={borderRadius:"{border.radius.sm}"},ea={light:{chip:{focusBackground:"{surface.200}",color:"{surface.800}"}},dark:{chip:{focusBackground:"{surface.700}",color:"{surface.0}"}}},su={root:ra,chip:na,colorScheme:ea,css:""},ta={background:"{form.field.background}",borderColor:"{form.field.border.color}",color:"{form.field.icon.color}",borderRadius:"{form.field.border.radius}",padding:"0.75rem",minWidth:"3rem"},aa=({dt:o})=>`
.p-inputgroup:has(.p-variant-filled) .p-inputgroupaddon {
    border-block-start-color: ${o("inputtext.filled.background")};
    border-inline-color: ${o("inputtext.filled.background")};
    background: ${o("inputtext.filled.background")} no-repeat;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
`,uu={addon:ta,css:aa},ia={transitionDuration:"{transition.duration}"},da={width:"3rem",borderRadius:"{form.field.border.radius}",verticalPadding:"{form.field.padding.y}"},ca={light:{button:{background:"transparent",hoverBackground:"{surface.100}",activeBackground:"{surface.200}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",color:"{surface.400}",hoverColor:"{surface.500}",activeColor:"{surface.600}"}},dark:{button:{background:"transparent",hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",color:"{surface.400}",hoverColor:"{surface.300}",activeColor:"{surface.200}"}}},la=({dt:o})=>`
.p-inputnumber-stacked .p-inputnumber-button-group {
    top: 2px;
    right: 2px;
    height: calc(100% - 4px);
}

.p-inputnumber-horizontal:has(.p-variant-filled) .p-inputnumber-button {
    border-block-start-color: ${o("inputtext.filled.background")};
    border-inline-color: ${o("inputtext.filled.background")};
    background: ${o("inputtext.filled.background")} no-repeat;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.p-inputnumber-vertical:has(.p-variant-filled) .p-inputnumber-button {
    border-block-color: ${o("inputtext.filled.background")};
    border-inline-color: ${o("inputtext.filled.background")};
    background: ${o("inputtext.filled.background")} no-repeat;
}

.p-inputnumber-vertical:has(.p-variant-filled) .p-inputnumber-increment-button {
    border-block-end: 1px solid ${o("inputtext.border.color")}
}
`,bu={root:ia,button:da,colorScheme:ca,css:la},sa={gap:"0.5rem"},ua={width:"3rem",sm:{width:"2.5rem"},lg:{width:"3.5rem"}},pu={root:sa,input:ua,css:""},ba={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},pa=({dt:o})=>`
.p-inputtext.p-variant-filled {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 1px solid transparent;
    background: ${o("inputtext.filled.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("inputtext.focus.border.color")}, ${o("inputtext.focus.border.color")}), linear-gradient(to bottom, ${o("inputtext.border.color")}, ${o("inputtext.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    transition: background-size 0.3s cubic-bezier(0.64, 0.09, 0.08, 1);
}

.p-inputtext.p-variant-filled:enabled:hover {
    background: ${o("inputtext.filled.hover.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("inputtext.focus.border.color")}, ${o("inputtext.focus.border.color")}), linear-gradient(to bottom, ${o("inputtext.hover.border.color")}, ${o("inputtext.hover.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-inputtext.p-variant-filled:enabled:focus {
    outline: 0 none;
    background: ${o("inputtext.filled.focus.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("inputtext.focus.border.color")}, ${o("inputtext.focus.border.color")}), linear-gradient(to bottom, ${o("inputtext.border.color")}, ${o("inputtext.border.color")});
    background-size: 100% 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-inputtext.p-variant-filled:enabled:hover:focus {
    background-image: linear-gradient(to bottom, ${o("inputtext.focus.border.color")}, ${o("inputtext.focus.border.color")}), linear-gradient(to bottom, ${o("inputtext.hover.border.color")}, ${o("inputtext.hover.border.color")});
}

.p-inputtext.p-variant-filled.p-invalid {
    background-image: linear-gradient(to bottom, ${o("inputtext.invalid.border.color")}, ${o("inputtext.invalid.border.color")}), linear-gradient(to bottom, ${o("inputtext.invalid.border.color")}, ${o("inputtext.invalid.border.color")});
}

.p-inputtext.p-variant-filled.p-invalid:enabled:focus {
    background-image: linear-gradient(to bottom, ${o("inputtext.invalid.border.color")}, ${o("inputtext.invalid.border.color")}), linear-gradient(to bottom, ${o("inputtext.invalid.border.color")}, ${o("inputtext.invalid.border.color")});
}
`,gu={root:ba,css:pa},ga={transitionDuration:"{transition.duration}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},fa={background:"{primary.color}"},ha={background:"{content.border.color}"},ma={color:"{text.muted.color}"},fu={root:ga,value:fa,range:ha,text:ma,css:""},ka={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",borderColor:"{form.field.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",shadow:"{form.field.shadow}",borderRadius:"{form.field.border.radius}",transitionDuration:"{form.field.transition.duration}"},va={padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},$a={focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},xa={background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},ya={color:"{list.option.color}",gutterStart:"-0.375rem",gutterEnd:"0.375rem"},wa={padding:"{list.option.padding}"},Ca={light:{option:{stripedBackground:"{surface.50}"}},dark:{option:{stripedBackground:"{surface.900}"}}},Ba=`
.p-listbox-option {
    transition: none;
}
`,hu={root:ka,list:va,option:$a,optionGroup:xa,checkmark:ya,emptyMessage:wa,colorScheme:Ca,css:Ba},za={background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",gap:"0.5rem",verticalOrientation:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},horizontalOrientation:{padding:"0.5rem 0.75rem",gap:"0.5rem"},transitionDuration:"{transition.duration}"},Ra={borderRadius:"{content.border.radius}",padding:"{navigation.item.padding}"},Sa={focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},Fa={padding:"0",background:"{content.background}",borderColor:"transparent",borderRadius:"{content.border.radius}",color:"{content.color}",shadow:"{overlay.navigation.shadow}",gap:"0.5rem"},Ea={padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},Da={padding:"{navigation.submenu.label.padding}",fontWeight:"{navigation.submenu.label.font.weight}",background:"{navigation.submenu.label.background.}",color:"{navigation.submenu.label.color}"},_a={size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},Aa={borderColor:"{content.border.color}"},Wa={borderRadius:"50%",size:"2.5rem",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",hoverBackground:"{content.hover.background}",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"}},Oa=({dt:o})=>`
.p-megamenu-button:focus-visible {
    background: ${o("navigation.item.active.background")};
}
`,mu={root:za,baseItem:Ra,item:Sa,overlay:Fa,submenu:Ea,submenuLabel:Da,submenuIcon:_a,separator:Aa,mobileButton:Wa,css:Oa},Ia={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},La={padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},Na={focusBackground:"{navigation.item.focus.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}"}},Ta={padding:"{navigation.submenu.label.padding}",fontWeight:"{navigation.submenu.label.font.weight}",background:"{navigation.submenu.label.background}",color:"{navigation.submenu.label.color}"},Pa={borderColor:"{content.border.color}"},ja=`
.p-menu-overlay {
    border-color: transparent;
}
`,ku={root:Ia,list:La,item:Na,submenuLabel:Ta,separator:Pa,css:ja},Ha={background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",gap:"0.5rem",padding:"0.5rem 0.75rem",transitionDuration:"{transition.duration}"},Va={borderRadius:"{content.border.radius}",padding:"{navigation.item.padding}"},Ya={focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},Xa={padding:"{navigation.list.padding}",gap:"{navigation.list.gap}",background:"{content.background}",borderColor:"transparent",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",mobileIndent:"1rem",icon:{size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"}},Ma={borderColor:"{content.border.color}"},Ga={borderRadius:"50%",size:"2.5rem",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",hoverBackground:"{content.hover.background}",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"}},Ka=({dt:o})=>`
.p-menubar-button:focus-visible {
    background: ${o("navigation.item.active.background")};
}
`,vu={root:Ha,baseItem:Va,item:Ya,submenu:Xa,separator:Ma,mobileButton:Ga,css:Ka},Za={borderRadius:"{content.border.radius}",borderWidth:"0",transitionDuration:"{transition.duration}"},qa={padding:"1rem 1.25rem",gap:"0.5rem",sm:{padding:"0.625rem 0.625rem"},lg:{padding:"0.825rem 0.825rem"}},Ua={fontSize:"1rem",fontWeight:"500",sm:{fontSize:"0.875rem"},lg:{fontSize:"1.125rem"}},Ja={size:"1.25rem",sm:{size:"1rem"},lg:{size:"1.5rem"}},Qa={width:"2rem",height:"2rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"}},oi={size:"1rem",sm:{size:"0.875rem"},lg:{size:"1.125rem"}},ri={root:{borderWidth:"1px"}},ni={content:{padding:"0"}},ei={light:{info:{background:"color-mix(in srgb, {blue.50}, transparent 5%)",borderColor:"{blue.200}",color:"{blue.600}",shadow:"none",closeButton:{hoverBackground:"{blue.100}",focusRing:{color:"{blue.600}",shadow:"none"}},outlined:{color:"{blue.600}",borderColor:"{blue.600}"},simple:{color:"{blue.600}"}},success:{background:"color-mix(in srgb, {green.50}, transparent 5%)",borderColor:"{green.200}",color:"{green.600}",shadow:"none",closeButton:{hoverBackground:"{green.100}",focusRing:{color:"{green.600}",shadow:"none"}},outlined:{color:"{green.600}",borderColor:"{green.600}"},simple:{color:"{green.600}"}},warn:{background:"color-mix(in srgb,{yellow.50}, transparent 5%)",borderColor:"{yellow.200}",color:"{yellow.900}",shadow:"none",closeButton:{hoverBackground:"{yellow.100}",focusRing:{color:"{yellow.600}",shadow:"none"}},outlined:{color:"{yellow.900}",borderColor:"{yellow.900}"},simple:{color:"{yellow.900}"}},error:{background:"color-mix(in srgb, {red.50}, transparent 5%)",borderColor:"{red.200}",color:"{red.600}",shadow:"none",closeButton:{hoverBackground:"{red.100}",focusRing:{color:"{red.600}",shadow:"none"}},outlined:{color:"{red.600}",borderColor:"{red.600}"},simple:{color:"{red.600}"}},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",shadow:"none",closeButton:{hoverBackground:"{surface.200}",focusRing:{color:"{surface.600}",shadow:"none"}},outlined:{color:"{surface.600}",borderColor:"{surface.600}"},simple:{color:"{surface.600}"}},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",shadow:"none",closeButton:{hoverBackground:"{surface.800}",focusRing:{color:"{surface.50}",shadow:"none"}},outlined:{color:"{surface.950}",borderColor:"{surface.950}"},simple:{color:"{surface.950}"}}},dark:{info:{background:"color-mix(in srgb, {blue.500}, transparent 84%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{blue.500}",shadow:"none",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{blue.500}",shadow:"none"}},outlined:{color:"{blue.500}",borderColor:"{blue.500}"},simple:{color:"{blue.500}"}},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{green.500}",shadow:"none",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{green.500}",shadow:"none"}},outlined:{color:"{green.500}",borderColor:"{green.500}"},simple:{color:"{green.500}"}},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 84%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{yellow.500}",shadow:"none",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{yellow.500}",shadow:"none"}},outlined:{color:"{yellow.500}",borderColor:"{yellow.500}"},simple:{color:"{yellow.500}"}},error:{background:"color-mix(in srgb, {red.500}, transparent 84%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{red.500}",shadow:"none",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{red.500}",shadow:"none"}},outlined:{color:"{red.500}",borderColor:"{red.500}"},simple:{color:"{red.500}"}},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",shadow:"none",closeButton:{hoverBackground:"{surface.700}",focusRing:{color:"{surface.300}",shadow:"none"}},outlined:{color:"{surface.400}",borderColor:"{surface.400}"},simple:{color:"{surface.400}"}},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",shadow:"none",closeButton:{hoverBackground:"{surface.100}",focusRing:{color:"{surface.950}",shadow:"none"}},outlined:{color:"{surface.0}",borderColor:"{surface.0}"},simple:{color:"{surface.0}"}}}},$u={root:Za,content:qa,text:Ua,icon:Ja,closeButton:Qa,closeIcon:oi,outlined:ri,simple:ni,colorScheme:ei,css:""},ti={borderRadius:"{content.border.radius}",gap:"1rem"},ai={background:"{content.border.color}",size:"0.5rem"},ii={gap:"0.5rem"},di={size:"0.5rem"},ci={size:"1rem"},li={verticalGap:"0.5rem",horizontalGap:"1rem"},xu={root:ti,meters:ai,label:ii,labelMarker:di,labelIcon:ci,labelList:li,css:""},si={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},ui={width:"2.5rem",color:"{form.field.icon.color}"},bi={background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},pi={padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},gi={focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}",gap:"0.75rem"},fi={background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},hi={color:"{form.field.icon.color}"},mi={borderRadius:"{border.radius.sm}"},ki={padding:"{list.option.padding}"},vi=({dt:o})=>`
.p-multiselect.p-variant-filled {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 1px solid transparent;
    background: ${o("multiselect.filled.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("multiselect.focus.border.color")}, ${o("multiselect.focus.border.color")}), linear-gradient(to bottom, ${o("multiselect.border.color")}, ${o("multiselect.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    transition: background-size 0.3s cubic-bezier(0.64, 0.09, 0.08, 1);
}

.p-multiselect.p-variant-filled:not(.p-disabled):hover {
    background: ${o("multiselect.filled.hover.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("multiselect.focus.border.color")}, ${o("multiselect.focus.border.color")}), linear-gradient(to bottom, ${o("multiselect.hover.border.color")}, ${o("multiselect.hover.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-multiselect.p-variant-filled:not(.p-disabled).p-focus {
    outline: 0 none;
    background: ${o("multiselect.filled.focus.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("multiselect.focus.border.color")}, ${o("multiselect.focus.border.color")}), linear-gradient(to bottom, ${o("multiselect.border.color")}, ${o("multiselect.border.color")});
    background-size: 100% 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-multiselect.p-variant-filled:not(.p-disabled).p-focus:hover {
    background-image: linear-gradient(to bottom, ${o("multiselect.focus.border.color")}, ${o("multiselect.focus.border.color")}), linear-gradient(to bottom, ${o("multiselect.hover.border.color")}, ${o("multiselect.hover.border.color")});
}

.p-multiselect.p-variant-filled.p-invalid {
    background-image: linear-gradient(to bottom, ${o("multiselect.invalid.border.color")}, ${o("multiselect.invalid.border.color")}), linear-gradient(to bottom, ${o("multiselect.invalid.border.color")}, ${o("multiselect.invalid.border.color")});
}

.p-multiselect.p-variant-filled.p-invalid:not(.p-disabled).p-focus  {
    background-image: linear-gradient(to bottom, ${o("multiselect.invalid.border.color")}, ${o("multiselect.invalid.border.color")}), linear-gradient(to bottom, ${o("multiselect.invalid.border.color")}, ${o("multiselect.invalid.border.color")});
}

.p-multiselect-option {
    transition: none;
}
`,yu={root:si,dropdown:ui,overlay:bi,list:pi,option:gi,optionGroup:fi,chip:mi,clearIcon:hi,emptyMessage:ki,css:vi},$i={gap:"1.125rem"},xi={gap:"0.5rem"},wu={root:$i,controls:xi,css:""},yi={gutter:"0.75rem",transitionDuration:"{transition.duration}"},wi={background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{content.border.color}",color:"{content.color}",selectedColor:"{highlight.color}",hoverColor:"{content.hover.color}",padding:"1rem 1.25rem",toggleablePadding:"1rem 1.25rem 1.5rem 1.25rem",borderRadius:"{content.border.radius}"},Ci={background:"{content.background}",hoverBackground:"{content.hover.background}",borderColor:"{content.border.color}",color:"{text.muted.color}",hoverColor:"{text.color}",size:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},Bi={color:"{content.border.color}",borderRadius:"{content.border.radius}",height:"24px"},Cu={root:yi,node:wi,nodeToggleButton:Ci,connector:Bi,css:""},zi={outline:{width:"2px",color:"{content.background}"}},Bu={root:zi,css:""},Ri={padding:"0.5rem 1rem",gap:"0.25rem",borderRadius:"{content.border.radius}",background:"{content.background}",color:"{content.color}",transitionDuration:"{transition.duration}"},Si={background:"transparent",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedColor:"{highlight.color}",width:"2.5rem",height:"2.5rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},Fi={color:"{text.muted.color}"},Ei={maxWidth:"2.5rem"},zu={root:Ri,navButton:Si,currentPageReport:Fi,jumpToPageInput:Ei,css:""},Di={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"},_i={background:"transparent",color:"{text.color}",padding:"1.25rem",borderColor:"{content.border.color}",borderWidth:"0",borderRadius:"0"},Ai={padding:"0.5rem 1.25rem"},Wi={fontWeight:"600"},Oi={padding:"0 1.25rem 1.25rem 1.25rem"},Ii={padding:"0 1.25rem 1.25rem 1.25rem"},Ru={root:Di,header:_i,toggleableHeader:Ai,title:Wi,content:Oi,footer:Ii,css:""},Li={gap:"0",transitionDuration:"{transition.duration}"},Ni={background:"{content.background}",borderColor:"{content.border.color}",borderWidth:"0",color:"{content.color}",padding:"0",borderRadius:"0",first:{borderWidth:"0",topBorderRadius:"{content.border.radius}"},last:{borderWidth:"0",bottomBorderRadius:"{content.border.radius}"}},Ti={focusBackground:"{navigation.item.focus.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",gap:"0.5rem",padding:"{navigation.item.padding}",borderRadius:"{content.border.radius}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}"}},Pi={indent:"1rem"},ji={color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}"},Hi=({dt:o})=>`
.p-panelmenu-panel {
    box-shadow: 0 0 0 1px ${o("panelmenu.panel.border.color")};
    transition: margin ${o("panelmenu.transition.duration")};
}

.p-panelmenu-panel:has(.p-panelmenu-header-active) {
    margin: 1rem 0;
}

.p-panelmenu-panel:first-child {
    border-top-left-radius: ${o("content.border.radius")};
    border-top-right-radius: ${o("content.border.radius")};
    margin-top: 0;
}

.p-panelmenu-panel:last-child {
    border-bottom-left-radius: ${o("content.border.radius")};
    border-bottom-right-radius: ${o("content.border.radius")};
    margin-bottom: 0;
}

.p-accordionpanel:not(.p-disabled) .p-accordionheader:focus-visible {
    background: ${o("navigation.item.active.background")};
}
`,Su={root:Li,panel:Ni,item:Ti,submenu:Pi,submenuIcon:ji,css:Hi},Vi={background:"{content.border.color}",borderRadius:"{content.border.radius}",height:".75rem"},Yi={color:"{form.field.icon.color}"},Xi={background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",borderRadius:"{overlay.popover.border.radius}",color:"{overlay.popover.color}",padding:"{overlay.popover.padding}",shadow:"{overlay.popover.shadow}"},Mi={gap:"0.5rem"},Gi={light:{strength:{weakBackground:"{red.500}",mediumBackground:"{amber.500}",strongBackground:"{green.500}"}},dark:{strength:{weakBackground:"{red.400}",mediumBackground:"{amber.400}",strongBackground:"{green.400}"}}},Fu={meter:Vi,icon:Yi,overlay:Xi,content:Mi,colorScheme:Gi,css:""},Ki={gap:"1.125rem"},Zi={gap:"0.5rem"},Eu={root:Ki,controls:Zi,css:""},qi={background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",color:"{overlay.popover.color}",borderRadius:"{overlay.popover.border.radius}",shadow:"{overlay.popover.shadow}",gutter:"10px",arrowOffset:"1.25rem"},Ui={padding:"{overlay.popover.padding}"},Du={root:qi,content:Ui,css:""},Ji={background:"{content.border.color}",borderRadius:"{content.border.radius}",height:"1rem"},Qi={background:"{primary.color}"},od={color:"{primary.contrast.color}",fontSize:"0.75rem",fontWeight:"600"},_u={root:Ji,value:Qi,label:od,css:""},rd={light:{root:{colorOne:"{red.500}",colorTwo:"{blue.500}",colorThree:"{green.500}",colorFour:"{yellow.500}"}},dark:{root:{colorOne:"{red.400}",colorTwo:"{blue.400}",colorThree:"{green.400}",colorFour:"{yellow.400}"}}},Au={colorScheme:rd,css:""},nd={width:"20px",height:"20px",background:"{form.field.background}",checkedBackground:"{primary.contrast.color}",checkedHoverBackground:"{primary.contrast.color}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",checkedBorderColor:"{primary.color}",checkedHoverBorderColor:"{primary.color}",checkedFocusBorderColor:"{primary.color}",checkedDisabledBorderColor:"{form.field.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",shadow:"{form.field.shadow}",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"},transitionDuration:"{form.field.transition.duration}",sm:{width:"16px",height:"16px"},lg:{width:"24px",height:"24px"}},ed={size:"10px",checkedColor:"{primary.color}",checkedHoverColor:"{primary.color}",disabledColor:"{form.field.disabled.color}",sm:{size:"8px"},lg:{size:"12px"}},Wu={root:nd,icon:ed},td={gap:"0.5rem",transitionDuration:"{transition.duration}",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"}},ad={size:"1.125rem",color:"{text.muted.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"},id=({dt:o})=>`
.p-rating:not(.p-disabled):not(.p-readonly) .p-rating-option:hover {
    background: color-mix(in srgb, ${o("rating.icon.color")}, transparent 96%);
    box-shadow: 0 0 1px 8px color-mix(in srgb, ${o("rating.icon.color")}, transparent 96%);
}

.p-rating:not(.p-disabled):not(.p-readonly) .p-rating-option-active:hover {
    background: color-mix(in srgb, ${o("rating.icon.active.color")}, transparent 92%);
    box-shadow: 0 0 1px 8px color-mix(in srgb, ${o("rating.icon.active.color")}, transparent 92%);
}

.p-rating-option.p-focus-visible {
    background: color-mix(in srgb, ${o("rating.icon.active.color")}, transparent 84%);
    box-shadow: 0 0 1px 8px color-mix(in srgb, ${o("rating.icon.active.color")}, transparent 84%);
}
`,Ou={root:td,icon:ad,css:id},dd={light:{root:{background:"rgba(0,0,0,0.1)"}},dark:{root:{background:"rgba(255,255,255,0.3)"}}},Iu={colorScheme:dd,css:""},cd={transitionDuration:"{transition.duration}"},ld={size:"9px",borderRadius:"{border.radius.sm}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},sd={light:{bar:{background:"{surface.200}"}},dark:{bar:{background:"{surface.700}"}}},Lu={root:cd,bar:ld,colorScheme:sd,css:""},ud={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},bd={width:"2.5rem",color:"{form.field.icon.color}"},pd={background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},gd={padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},fd={focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},hd={background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},md={color:"{form.field.icon.color}"},kd={color:"{list.option.color}",gutterStart:"-0.375rem",gutterEnd:"0.375rem"},vd={padding:"{list.option.padding}"},$d=({dt:o})=>`
.p-select.p-variant-filled {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 1px solid transparent;
    background: ${o("select.filled.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("select.focus.border.color")}, ${o("select.focus.border.color")}), linear-gradient(to bottom, ${o("select.border.color")}, ${o("select.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    transition: background-size 0.3s cubic-bezier(0.64, 0.09, 0.08, 1);
}

.p-select.p-variant-filled:not(.p-disabled):hover {
    background: ${o("select.filled.hover.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("select.focus.border.color")}, ${o("select.focus.border.color")}), linear-gradient(to bottom, ${o("select.hover.border.color")}, ${o("select.hover.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-select.p-variant-filled:not(.p-disabled).p-focus {
    outline: 0 none;
    background: ${o("select.filled.focus.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("select.focus.border.color")}, ${o("select.focus.border.color")}), linear-gradient(to bottom, ${o("select.border.color")}, ${o("select.border.color")});
    background-size: 100% 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-select.p-variant-filled:not(.p-disabled).p-focus:hover {
    background-image: linear-gradient(to bottom, ${o("select.focus.border.color")}, ${o("select.focus.border.color")}), linear-gradient(to bottom, ${o("select.hover.border.color")}, ${o("select.hover.border.color")});
}

.p-select.p-variant-filled.p-invalid {
    background-image: linear-gradient(to bottom, ${o("select.invalid.border.color")}, ${o("select.invalid.border.color")}), linear-gradient(to bottom, ${o("select.invalid.border.color")}, ${o("select.invalid.border.color")});
}

.p-select.p-variant-filled.p-invalid:not(.p-disabled).p-focus  {
    background-image: linear-gradient(to bottom, ${o("select.invalid.border.color")}, ${o("select.invalid.border.color")}), linear-gradient(to bottom, ${o("select.invalid.border.color")}, ${o("select.invalid.border.color")});
}

.p-select-option {
    transition: none;
}
`,Nu={root:ud,dropdown:bd,overlay:pd,list:gd,option:fd,optionGroup:hd,clearIcon:md,checkmark:kd,emptyMessage:vd,css:$d},xd={borderRadius:"{form.field.border.radius}"},yd={light:{root:{invalidBorderColor:"{form.field.invalid.border.color}"}},dark:{root:{invalidBorderColor:"{form.field.invalid.border.color}"}}},Tu={root:xd,colorScheme:yd,css:""},wd={borderRadius:"{content.border.radius}"},Cd={light:{root:{background:"{surface.200}",animationBackground:"rgba(255,255,255,0.4)"}},dark:{root:{background:"rgba(255, 255, 255, 0.06)",animationBackground:"rgba(255, 255, 255, 0.04)"}}},Pu={root:wd,colorScheme:Cd,css:""},Bd={transitionDuration:"{transition.duration}"},zd={background:"{content.border.color}",borderRadius:"{border.radius.xs}",size:"2px"},Rd={background:"{primary.color}"},Sd={width:"18px",height:"18px",borderRadius:"50%",background:"{primary.color}",hoverBackground:"{primary.color}",content:{borderRadius:"50%",background:"{primary.color}",hoverBackground:"{primary.color}",width:"18px",height:"18px",shadow:"0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12)"},focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"}},Fd=({dt:o})=>`
.p-slider-handle {
    transition: box-shadow ${o("slider.transition.duration")};
}

.p-slider:not(.p-disabled) .p-slider-handle:hover {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o("slider.handle.background")}, transparent 92%);
}

.p-slider-handle:focus-visible,
.p-slider:not(.p-disabled) .p-slider-handle:focus:hover {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o("slider.handle.background")}, transparent 84%);
}
`,ju={root:Bd,track:zd,range:Rd,handle:Sd,css:Fd},Ed={gap:"0.5rem",transitionDuration:"{transition.duration}"},Hu={root:Ed,css:""},Dd={borderRadius:"{form.field.border.radius}",roundedBorderRadius:"2rem",raisedShadow:"0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)"},Vu={root:Dd,css:""},_d={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",transitionDuration:"{transition.duration}"},Ad={background:"{content.border.color}"},Wd={size:"24px",background:"transparent",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},Yu={root:_d,gutter:Ad,handle:Wd,css:""},Od={transitionDuration:"{transition.duration}"},Id={background:"{content.border.color}",activeBackground:"{primary.color}",margin:"0 0 0 1.625rem",size:"2px"},Ld={padding:"0.5rem",gap:"1rem"},Nd={padding:"0.75rem 1rem",borderRadius:"{content.border.radius}",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"},gap:"0.5rem"},Td={color:"{text.muted.color}",activeColor:"{text.color}",fontWeight:"500"},Pd={activeBackground:"{primary.color}",activeBorderColor:"{primary.color}",activeColor:"{primary.contrast.color}",size:"2rem",fontSize:"1.143rem",fontWeight:"500",borderRadius:"50%",shadow:"none"},jd={padding:"0.875rem 0.5rem 1.125rem 0.5rem"},Hd={background:"{content.background}",color:"{content.color}",padding:"0",indent:"1rem"},Vd={light:{stepNumber:{background:"{surface.400}",borderColor:"{surface.400}",color:"{surface.0}"}},dark:{stepNumber:{background:"{surface.200}",borderColor:"{surface.200}",color:"{surface.900}"}}},Yd=({dt:o})=>`
.p-step-header:focus-visible {
    background: ${o("navigation.item.active.background")};
}
`,Xu={root:Od,separator:Id,step:Ld,stepHeader:Nd,stepTitle:Td,stepNumber:Pd,steppanels:jd,steppanel:Hd,colorScheme:Vd,css:Yd},Xd={transitionDuration:"{transition.duration}"},Md={background:"{content.border.color}"},Gd={borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},gap:"0.5rem"},Kd={color:"{text.muted.color}",activeColor:"{primary.color}",fontWeight:"500"},Zd={background:"{content.background}",activeBackground:"{content.background}",borderColor:"{content.border.color}",activeBorderColor:"{content.border.color}",color:"{text.muted.color}",activeColor:"{primary.color}",size:"2rem",fontSize:"1.143rem",fontWeight:"500",borderRadius:"50%",shadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12)"},Mu={root:Xd,separator:Md,itemLink:Gd,itemLabel:Kd,itemNumber:Zd,css:""},qd={transitionDuration:"{transition.duration}"},Ud={borderWidth:"0 0 1px 0",background:"{content.background}",borderColor:"{content.border.color}"},Jd={background:"transparent",hoverBackground:"transparent",activeBackground:"transparent",borderWidth:"0 0 1px 0",borderColor:"{content.border.color}",hoverBorderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}",padding:"1rem 1.125rem",fontWeight:"600",margin:"0 0 -1px 0",gap:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},Qd={color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},oc={height:"1px",bottom:"-1px",background:"{primary.color}"},Gu={root:qd,tablist:Ud,item:Jd,itemIcon:Qd,activeBar:oc,css:""},rc={transitionDuration:"{transition.duration}"},nc={borderWidth:"0 0 1px 0",background:"{content.background}",borderColor:"{content.border.color}"},ec={background:"transparent",hoverBackground:"{content.hover.background}",activeBackground:"transparent",borderWidth:"0 0 1px 0",borderColor:"{content.border.color}",hoverBorderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.color}",hoverColor:"{text.color}",activeColor:"{primary.color}",padding:"1rem 1.25rem",fontWeight:"600",margin:"0 0 -1px 0",gap:"0.5rem",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"}},tc={background:"{content.background}",color:"{content.color}",padding:"1.25rem 1.25rem 1.25rem 1.25rem",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"}},ac={background:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",width:"3rem",shadow:"none",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"}},ic={height:"2px",bottom:"-1px",background:"{primary.color}"},dc=({dt:o})=>`
.p-tabs-scrollable .p-tab {
    flex-grow: 0
}

.p-tab-active {
    --p-ripple-background: color-mix(in srgb, ${o("primary.color")}, transparent 90%);
}

.p-tab:not(.p-disabled):focus-visible {
    background: ${o("navigation.item.active.background")};
}

.p-tablist-nav-button:focus-visible {
    background: ${o("navigation.item.active.background")};
}
`,Ku={root:rc,tablist:nc,tab:ec,tabpanel:tc,navButton:ac,activeBar:ic,css:dc},cc={transitionDuration:"{transition.duration}"},lc={background:"{content.background}",borderColor:"{content.border.color}"},sc={borderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},uc={background:"{content.background}",color:"{content.color}"},bc={background:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}"},pc={light:{navButton:{shadow:"0px 0px 10px 50px rgba(255, 255, 255, 0.6)"}},dark:{navButton:{shadow:"0px 0px 10px 50px color-mix(in srgb, {content.background}, transparent 50%)"}}},Zu={root:cc,tabList:lc,tab:sc,tabPanel:uc,navButton:bc,colorScheme:pc,css:""},gc={fontSize:"0.875rem",fontWeight:"700",padding:"0.25rem 0.5rem",gap:"0.25rem",borderRadius:"{content.border.radius}",roundedBorderRadius:"{border.radius.xl}"},fc={size:"0.75rem"},hc={light:{primary:{background:"{primary.color}",color:"{primary.contrast.color}"},secondary:{background:"{surface.100}",color:"{surface.600}"},success:{background:"{green.500}",color:"{surface.0}"},info:{background:"{sky.500}",color:"{surface.0}"},warn:{background:"{orange.500}",color:"{surface.0}"},danger:{background:"{red.500}",color:"{surface.0}"},contrast:{background:"{surface.950}",color:"{surface.0}"}},dark:{primary:{background:"{primary.color}",color:"{primary.contrast.color}"},secondary:{background:"{surface.800}",color:"{surface.300}"},success:{background:"{green.400}",color:"{green.950}"},info:{background:"{sky.400}",color:"{sky.950}"},warn:{background:"{orange.400}",color:"{orange.950}"},danger:{background:"{red.400}",color:"{red.950}"},contrast:{background:"{surface.0}",color:"{surface.950}"}}},qu={root:gc,icon:fc,colorScheme:hc,css:""},mc={background:"{form.field.background}",borderColor:"{form.field.border.color}",color:"{form.field.color}",height:"18rem",padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{form.field.border.radius}"},kc={gap:"0.25rem"},vc={margin:"2px 0"},Uu={root:mc,prompt:kc,commandResponse:vc,css:""},$c={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},xc=({dt:o})=>`
.p-textarea.p-variant-filled {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 1px solid transparent;
    background: ${o("textarea.filled.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("textarea.focus.border.color")}, ${o("textarea.focus.border.color")}), linear-gradient(to bottom, ${o("textarea.border.color")}, ${o("textarea.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    transition: background-size 0.3s cubic-bezier(0.64, 0.09, 0.08, 1);
}

.p-textarea.p-variant-filled:enabled:hover {
    background: ${o("textarea.filled.hover.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("textarea.focus.border.color")}, ${o("textarea.focus.border.color")}), linear-gradient(to bottom, ${o("textarea.hover.border.color")}, ${o("textarea.hover.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-textarea.p-variant-filled:enabled:focus {
    outline: 0 none;
    background: ${o("textarea.filled.focus.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("textarea.focus.border.color")}, ${o("textarea.focus.border.color")}), linear-gradient(to bottom, ${o("textarea.border.color")}, ${o("textarea.border.color")});
    background-size: 100% 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-textarea.p-variant-filled:enabled:hover:focus {
    background-image: linear-gradient(to bottom, ${o("textarea.focus.border.color")}, ${o("textarea.focus.border.color")}), linear-gradient(to bottom, ${o("textarea.hover.border.color")}, ${o("textarea.hover.border.color")});
}

.p-textarea.p-variant-filled.p-invalid {
    background-image: linear-gradient(to bottom, ${o("textarea.invalid.border.color")}, ${o("textarea.invalid.border.color")}), linear-gradient(to bottom, ${o("textarea.invalid.border.color")}, ${o("textarea.invalid.border.color")});
}

.p-textarea.p-variant-filled.p-invalid:enabled:focus {
    background-image: linear-gradient(to bottom, ${o("textarea.invalid.border.color")}, ${o("textarea.invalid.border.color")}), linear-gradient(to bottom, ${o("textarea.invalid.border.color")}, ${o("textarea.invalid.border.color")});
}
`,Ju={root:$c,css:xc},yc={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},wc={padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},Cc={focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},Bc={mobileIndent:"1rem"},zc={size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},Rc={borderColor:"{content.border.color}"},Sc=`
.p-tieredmenu-overlay {
    border-color: transparent;
}
`,Qu={root:yc,list:wc,item:Cc,submenu:Bc,submenuIcon:zc,separator:Rc,css:Sc},Fc={minHeight:"5rem"},Ec={eventContent:{padding:"1rem 0"}},Dc={eventContent:{padding:"0 1rem"}},_c={size:"1.5rem",borderRadius:"50%",borderWidth:"2px",background:"{primary.color}",content:{borderRadius:"50%",size:"0",background:"{primary.color}",insetShadow:"none"}},Ac={color:"{content.border.color}",size:"2px"},Wc={light:{eventMarker:{borderColor:"{surface.0}"}},dark:{eventMarker:{borderColor:"{surface.900}"}}},ob={event:Fc,horizontal:Ec,vertical:Dc,eventMarker:_c,eventConnector:Ac,colorScheme:Wc,css:""},Oc={width:"25rem",borderRadius:"{content.border.radius}",borderWidth:"0",transitionDuration:"{transition.duration}"},Ic={size:"1.25rem"},Lc={padding:"{overlay.popover.padding}",gap:"0.5rem"},Nc={gap:"0.5rem"},Tc={fontWeight:"500",fontSize:"1rem"},Pc={fontWeight:"500",fontSize:"0.875rem"},jc={width:"2rem",height:"2rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"}},Hc={size:"1rem"},Vc={light:{root:{blur:"0"},info:{background:"{blue.50}",borderColor:"{blue.200}",color:"{blue.600}",detailColor:"{surface.700}",shadow:"0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)",closeButton:{hoverBackground:"{blue.100}",focusRing:{color:"{blue.600}",shadow:"none"}}},success:{background:"{green.50}",borderColor:"{green.200}",color:"{green.600}",detailColor:"{surface.700}",shadow:"0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)",closeButton:{hoverBackground:"{green.100}",focusRing:{color:"{green.600}",shadow:"none"}}},warn:{background:"{yellow.50}",borderColor:"{yellow.200}",color:"{yellow.900}",detailColor:"{surface.700}",shadow:"0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)",closeButton:{hoverBackground:"{yellow.100}",focusRing:{color:"{yellow.600}",shadow:"none"}}},error:{background:"{red.50}",borderColor:"{red.200}",color:"{red.600}",detailColor:"{surface.700}",shadow:"0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)",closeButton:{hoverBackground:"{red.100}",focusRing:{color:"{red.600}",shadow:"none"}}},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",detailColor:"{surface.700}",shadow:"0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)",closeButton:{hoverBackground:"{surface.200}",focusRing:{color:"{surface.600}",shadow:"none"}}},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",detailColor:"{surface.0}",shadow:"0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)",closeButton:{hoverBackground:"{surface.800}",focusRing:{color:"{surface.50}",shadow:"none"}}}},dark:{root:{blur:"10px"},info:{background:"color-mix(in srgb, {blue.500}, transparent 36%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{surface.0}",detailColor:"{blue.100}",shadow:"0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{blue.500}",shadow:"none"}}},success:{background:"color-mix(in srgb, {green.500}, transparent 36%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{surface.0}",detailColor:"{green.100}",shadow:"0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{green.500}",shadow:"none"}}},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 36%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{surface.0}",detailColor:"{yellow.50}",shadow:"0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{yellow.500}",shadow:"none"}}},error:{background:"color-mix(in srgb, {red.500}, transparent 36%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{surface.0}",detailColor:"{red.100}",shadow:"0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{red.500}",shadow:"none"}}},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",detailColor:"{surface.0}",shadow:"0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)",closeButton:{hoverBackground:"{surface.700}",focusRing:{color:"{surface.300}",shadow:"none"}}},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",detailColor:"{surface.950}",shadow:"0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)",closeButton:{hoverBackground:"{surface.100}",focusRing:{color:"{surface.950}",shadow:"none"}}}}},rb={root:Oc,icon:Ic,content:Lc,text:Nc,summary:Tc,detail:Pc,closeButton:jc,closeIcon:Hc,colorScheme:Vc,css:""},Yc={padding:"0.75rem 1rem",borderRadius:"{form.field.border.radius}",gap:"0.5rem",fontWeight:"500",background:"{form.field.background}",borderColor:"{form.field.border.color}",color:"{form.field.color}",hoverColor:"{form.field.color}",checkedColor:"{form.field.color}",checkedBorderColor:"{form.field.border.color}",disabledBackground:"{form.field.disabled.background}",disabledBorderColor:"{form.field.disabled.background}",disabledColor:"{form.field.disabled.color}",invalidBorderColor:"{form.field.invalid.border.color}",focusRing:{width:"0",style:"none",offset:"0",color:"unset",shadow:"none"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",padding:"0.625rem 0.75rem"},lg:{fontSize:"{form.field.lg.font.size}",padding:"0.875rem 1.25rem"}},Xc={color:"{text.muted.color}",hoverColor:"{text.muted.color}",checkedColor:"{text.muted.color}",disabledColor:"{form.field.disabled.color}"},Mc={checkedBackground:"transparent",checkedShadow:"none",padding:"0",borderRadius:"0",sm:{padding:"0"},lg:{padding:"0"}},Gc={light:{root:{hoverBackground:"{surface.100}",checkedBackground:"{surface.200}"}},dark:{root:{hoverBackground:"{surface.800}",checkedBackground:"{surface.700}"}}},Kc=({dt:o})=>`
.p-togglebutton:focus-visible {
    background: ${o("togglebutton.hover.background")};
}
`,nb={root:Yc,icon:Xc,content:Mc,colorScheme:Gc,css:Kc},Zc={width:"2.75rem",height:"1rem",borderRadius:"30px",gap:"0px",shadow:"none",focusRing:{width:"0",style:"none",color:"unset",offset:"0",shadow:"none"},borderWidth:"1px",borderColor:"transparent",hoverBorderColor:"transparent",checkedBorderColor:"transparent",checkedHoverBorderColor:"transparent",invalidBorderColor:"{form.field.invalid.border.color}",transitionDuration:"{form.field.transition.duration}",slideDuration:"0.2s"},qc={borderRadius:"50%",size:"1.5rem"},Uc={light:{root:{background:"{surface.300}",disabledBackground:"{surface.400}",hoverBackground:"{surface.300}",checkedBackground:"{primary.200}",checkedHoverBackground:"{primary.200}"},handle:{background:"{surface.0}",disabledBackground:"{surface.200}",hoverBackground:"{surface.0}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",checkedColor:"{primary.contrast.color}",checkedHoverColor:"{primary.contrast.color}"}},dark:{root:{background:"{surface.700}",disabledBackground:"{surface.600}",hoverBackground:"{surface.700}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.color}"},handle:{background:"{surface.400}",disabledBackground:"{surface.500}",hoverBackground:"{surface.300}",checkedBackground:"{primary.200}",checkedHoverBackground:"{primary.200}",color:"{surface.800}",hoverColor:"{surface.900}",checkedColor:"{primary.contrast.color}",checkedHoverColor:"{primary.contrast.color}"}}},Jc=({dt:o})=>`
.p-toggleswitch-handle {
    box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}

.p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover) .p-toggleswitch-handle {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o("text.color")}, transparent 96%), 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}

.p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:focus-visible) .p-toggleswitch-handle {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o("text.color")}, transparent 88%), 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}

.p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover).p-toggleswitch-checked .p-toggleswitch-handle {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o("toggleswitch.handle.checked.background")}, transparent 92%), 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}

.p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:focus-visible).p-toggleswitch-checked .p-toggleswitch-handle {
    box-shadow: 0 0 1px 10px color-mix(in srgb, ${o("toggleswitch.handle.checked.background")}, transparent 84%), 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}
`,eb={root:Zc,handle:qc,colorScheme:Uc,css:Jc},Qc={color:"{content.color}",borderRadius:"{content.border.radius}",gap:"0.5rem",padding:"1rem"},ol={light:{root:{background:"{surface.100}",borderColor:"{surface.100}"}},dark:{root:{background:"{surface.800}",borderColor:"{surface.800}"}}},tb={root:Qc,colorScheme:ol,css:""},rl={background:"{surface.600}",color:"{surface.0}",maxWidth:"12.5rem",gutter:"0.25rem",shadow:"{overlay.popover.shadow}",padding:"0.5rem 0.75rem",borderRadius:"{overlay.popover.border.radius}"},ab={root:rl,css:""},nl={background:"{content.background}",color:"{content.color}",padding:"1rem",gap:"2px",indent:"2rem",transitionDuration:"{transition.duration}"},el={padding:"0.5rem 0.75rem",borderRadius:"{border.radius.xs}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{text.color}",hoverColor:"{text.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"},gap:"0.5rem"},tl={color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedColor:"{highlight.color}"},al={borderRadius:"50%",size:"2rem",hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedHoverColor:"{primary.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},il={size:"2rem"},dl={margin:"0 0 0.75rem 0"},cl=`
.p-tree-node-content {
    transition: none;
}
`,ib={root:nl,node:el,nodeIcon:tl,nodeToggleButton:al,loadingIcon:il,filter:dl,css:cl},ll={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},sl={width:"2.5rem",color:"{form.field.icon.color}"},ul={background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},bl={padding:"{list.padding}"},pl={padding:"{list.option.padding}"},gl={borderRadius:"{border.radius.sm}"},fl={color:"{form.field.icon.color}"},hl=({dt:o})=>`
.p-treeselect.p-variant-filled {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border: 1px solid transparent;
    background: ${o("treeselect.filled.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("treeselect.focus.border.color")}, ${o("treeselect.focus.border.color")}), linear-gradient(to bottom, ${o("treeselect.border.color")}, ${o("treeselect.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    transition: background-size 0.3s cubic-bezier(0.64, 0.09, 0.08, 1);
}

.p-treeselect.p-variant-filled:not(.p-disabled):hover {
    background: ${o("treeselect.filled.hover.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("treeselect.focus.border.color")}, ${o("treeselect.focus.border.color")}), linear-gradient(to bottom, ${o("treeselect.hover.border.color")}, ${o("treeselect.hover.border.color")});
    background-size: 0 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-treeselect.p-variant-filled:not(.p-disabled).p-focus {
    outline: 0 none;
    background: ${o("treeselect.filled.focus.background")} no-repeat;
    background-image: linear-gradient(to bottom, ${o("treeselect.focus.border.color")}, ${o("treeselect.focus.border.color")}), linear-gradient(to bottom, ${o("treeselect.border.color")}, ${o("treeselect.border.color")});
    background-size: 100% 2px, 100% 1px;
    background-position: 50% 100%, 50% 100%;
    background-origin: border-box;
    border-color: transparent;
}

.p-treeselect.p-variant-filled:not(.p-disabled).p-focus:hover {
    background-image: linear-gradient(to bottom, ${o("treeselect.focus.border.color")}, ${o("treeselect.focus.border.color")}), linear-gradient(to bottom, ${o("treeselect.hover.border.color")}, ${o("treeselect.hover.border.color")});
}

.p-treeselect.p-variant-filled.p-invalid {
    background-image: linear-gradient(to bottom, ${o("treeselect.invalid.border.color")}, ${o("treeselect.invalid.border.color")}), linear-gradient(to bottom, ${o("treeselect.invalid.border.color")}, ${o("treeselect.invalid.border.color")});
}

.p-treeselect.p-variant-filled.p-invalid:not(.p-disabled).p-focus  {
    background-image: linear-gradient(to bottom, ${o("treeselect.invalid.border.color")}, ${o("treeselect.invalid.border.color")}), linear-gradient(to bottom, ${o("treeselect.invalid.border.color")}, ${o("treeselect.invalid.border.color")});
}
`,db={root:ll,dropdown:sl,overlay:ul,tree:bl,emptyMessage:pl,chip:gl,clearIcon:fl,css:hl},ml={transitionDuration:"{transition.duration}"},kl={background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem"},vl={background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{treetable.border.color}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",gap:"0.5rem",padding:"0.75rem 1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},$l={fontWeight:"600"},xl={background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},yl={borderColor:"{treetable.border.color}",padding:"0.75rem 1rem",gap:"0.5rem"},wl={background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",padding:"0.75rem 1rem"},Cl={fontWeight:"600"},Bl={background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem"},zl={width:"0.5rem"},Rl={width:"1px",color:"{primary.color}"},Sl={color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",size:"0.875rem"},Fl={size:"2rem"},El={hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",selectedHoverColor:"{primary.color}",size:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},Dl={borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},_l={borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},Al={light:{root:{borderColor:"{content.border.color}"},bodyCell:{selectedBorderColor:"{primary.100}"}},dark:{root:{borderColor:"{surface.800}"},bodyCell:{selectedBorderColor:"{primary.900}"}}},cb={root:ml,header:kl,headerCell:vl,columnTitle:$l,row:xl,bodyCell:yl,footerCell:wl,columnFooter:Cl,footer:Bl,columnResizer:zl,resizeIndicator:Rl,sortIcon:Sl,loadingIcon:Fl,nodeToggleButton:El,paginatorTop:Dl,paginatorBottom:_l,colorScheme:Al},Wl={mask:{background:"{content.background}",color:"{text.muted.color}"},icon:{size:"2rem"}},lb={loader:Wl,css:""},sb=({dt:o})=>`
.p-paginator {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    background: ${o("paginator.background")};
    color: ${o("paginator.color")};
    padding: ${o("paginator.padding")};
    border-radius: ${o("paginator.border.radius")};
    gap: ${o("paginator.gap")};
}

.p-paginator-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: ${o("paginator.gap")};
}

.p-paginator-content-start {
    margin-inline-end: auto;
}

.p-paginator-content-end {
    margin-inline-start: auto;
}

.p-paginator-page,
.p-paginator-next,
.p-paginator-last,
.p-paginator-first,
.p-paginator-prev {
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    user-select: none;
    overflow: hidden;
    position: relative;
    background: ${o("paginator.nav.button.background")};
    border: 0 none;
    color: ${o("paginator.nav.button.color")};
    min-width: ${o("paginator.nav.button.width")};
    height: ${o("paginator.nav.button.height")};
    transition: background ${o("paginator.transition.duration")}, color ${o("paginator.transition.duration")}, outline-color ${o("paginator.transition.duration")}, box-shadow ${o("paginator.transition.duration")};
    border-radius: ${o("paginator.nav.button.border.radius")};
    padding: 0;
    margin: 0;
}

.p-paginator-page:focus-visible,
.p-paginator-next:focus-visible,
.p-paginator-last:focus-visible,
.p-paginator-first:focus-visible,
.p-paginator-prev:focus-visible {
    box-shadow: ${o("paginator.nav.button.focus.ring.shadow")};
    outline: ${o("paginator.nav.button.focus.ring.width")} ${o("paginator.nav.button.focus.ring.style")} ${o("paginator.nav.button.focus.ring.color")};
    outline-offset: ${o("paginator.nav.button.focus.ring.offset")};
}

.p-paginator-page:not(.p-disabled):not(.p-paginator-page-selected):hover,
.p-paginator-first:not(.p-disabled):hover,
.p-paginator-prev:not(.p-disabled):hover,
.p-paginator-next:not(.p-disabled):hover,
.p-paginator-last:not(.p-disabled):hover {
    background: ${o("paginator.nav.button.hover.background")};
    color: ${o("paginator.nav.button.hover.color")};
}

.p-paginator-page.p-paginator-page-selected {
    background: ${o("paginator.nav.button.selected.background")};
    color: ${o("paginator.nav.button.selected.color")};
}

.p-paginator-current {
    color: ${o("paginator.current.page.report.color")};
}

.p-paginator-pages {
    display: flex;
    align-items: center;
    gap: ${o("paginator.gap")};
}

.p-paginator-jtp-input .p-inputtext {
    max-width: ${o("paginator.jump.to.page.input.max.width")};
}

.p-paginator-first:dir(rtl),
.p-paginator-prev:dir(rtl),
.p-paginator-next:dir(rtl),
.p-paginator-last:dir(rtl) {
    transform: rotate(180deg);
}
`,ub=({dt:o})=>`
.p-ink {
    display: block;
    position: absolute;
    background: ${o("ripple.background")};
    border-radius: 100%;
    transform: scale(0);
    pointer-events: none;
}

.p-ink-active {
    animation: ripple 0.4s linear;
}

@keyframes ripple {
    100% {
        opacity: 0;
        transform: scale(2.5);
    }
}
`,bb=({dt:o})=>`
.p-iconfield {
    position: relative;
}

.p-inputicon {
    position: absolute;
    top: 50%;
    margin-top: calc(-1 * (${o("icon.size")} / 2));
    color: ${o("iconfield.icon.color")};
    line-height: 1;
    z-index: 1;
}

.p-iconfield .p-inputicon:first-child {
    inset-inline-start: ${o("form.field.padding.x")};
}

.p-iconfield .p-inputicon:last-child {
    inset-inline-end: ${o("form.field.padding.x")};
}

.p-iconfield .p-inputtext:not(:first-child),
.p-iconfield .p-inputwrapper:not(:first-child) .p-inputtext {
    padding-inline-start: calc((${o("form.field.padding.x")} * 2) + ${o("icon.size")});
}

.p-iconfield .p-inputtext:not(:last-child) {
    padding-inline-end: calc((${o("form.field.padding.x")} * 2) + ${o("icon.size")});
}

.p-iconfield:has(.p-inputfield-sm) .p-inputicon {
    font-size: ${o("form.field.sm.font.size")};
    width: ${o("form.field.sm.font.size")};
    height: ${o("form.field.sm.font.size")};
    margin-top: calc(-1 * (${o("form.field.sm.font.size")} / 2));
}

.p-iconfield:has(.p-inputfield-lg) .p-inputicon {
    font-size: ${o("form.field.lg.font.size")};
    width: ${o("form.field.lg.font.size")};
    height: ${o("form.field.lg.font.size")};
    margin-top: calc(-1 * (${o("form.field.lg.font.size")} / 2));
}
`,pb=({dt:o})=>`
.p-inputtext {
    font-family: inherit;
    font-feature-settings: inherit;
    font-size: 1rem;
    color: ${o("inputtext.color")};
    background: ${o("inputtext.background")};
    padding-block: ${o("inputtext.padding.y")};
    padding-inline: ${o("inputtext.padding.x")};
    border: 1px solid ${o("inputtext.border.color")};
    transition: background ${o("inputtext.transition.duration")}, color ${o("inputtext.transition.duration")}, border-color ${o("inputtext.transition.duration")}, outline-color ${o("inputtext.transition.duration")}, box-shadow ${o("inputtext.transition.duration")};
    appearance: none;
    border-radius: ${o("inputtext.border.radius")};
    outline-color: transparent;
    box-shadow: ${o("inputtext.shadow")};
}

.p-inputtext:enabled:hover {
    border-color: ${o("inputtext.hover.border.color")};
}

.p-inputtext:enabled:focus {
    border-color: ${o("inputtext.focus.border.color")};
    box-shadow: ${o("inputtext.focus.ring.shadow")};
    outline: ${o("inputtext.focus.ring.width")} ${o("inputtext.focus.ring.style")} ${o("inputtext.focus.ring.color")};
    outline-offset: ${o("inputtext.focus.ring.offset")};
}

.p-inputtext.p-invalid {
    border-color: ${o("inputtext.invalid.border.color")};
}

.p-inputtext.p-variant-filled {
    background: ${o("inputtext.filled.background")};
}

.p-inputtext.p-variant-filled:enabled:hover {
    background: ${o("inputtext.filled.hover.background")};
}

.p-inputtext.p-variant-filled:enabled:focus {
    background: ${o("inputtext.filled.focus.background")};
}

.p-inputtext:disabled {
    opacity: 1;
    background: ${o("inputtext.disabled.background")};
    color: ${o("inputtext.disabled.color")};
}

.p-inputtext::placeholder {
    color: ${o("inputtext.placeholder.color")};
}

.p-inputtext.p-invalid::placeholder {
    color: ${o("inputtext.invalid.placeholder.color")};
}

.p-inputtext-sm {
    font-size: ${o("inputtext.sm.font.size")};
    padding-block: ${o("inputtext.sm.padding.y")};
    padding-inline: ${o("inputtext.sm.padding.x")};
}

.p-inputtext-lg {
    font-size: ${o("inputtext.lg.font.size")};
    padding-block: ${o("inputtext.lg.padding.y")};
    padding-inline: ${o("inputtext.lg.padding.x")};
}

.p-inputtext-fluid {
    width: 100%;
}
`,gb=({dt:o})=>`
.p-virtualscroller-loader {
    background: ${o("virtualscroller.loader.mask.background")};
    color: ${o("virtualscroller.loader.mask.color")};
}

.p-virtualscroller-loading-icon {
    font-size: ${o("virtualscroller.loader.icon.size")};
    width: ${o("virtualscroller.loader.icon.size")};
    height: ${o("virtualscroller.loader.icon.size")};
}
`,fb=({dt:o})=>`
.p-select {
    display: inline-flex;
    cursor: pointer;
    position: relative;
    user-select: none;
    background: ${o("select.background")};
    border: 1px solid ${o("select.border.color")};
    transition: background ${o("select.transition.duration")}, color ${o("select.transition.duration")}, border-color ${o("select.transition.duration")},
        outline-color ${o("select.transition.duration")}, box-shadow ${o("select.transition.duration")};
    border-radius: ${o("select.border.radius")};
    outline-color: transparent;
    box-shadow: ${o("select.shadow")};
}

.p-select:not(.p-disabled):hover {
    border-color: ${o("select.hover.border.color")};
}

.p-select:not(.p-disabled).p-focus {
    border-color: ${o("select.focus.border.color")};
    box-shadow: ${o("select.focus.ring.shadow")};
    outline: ${o("select.focus.ring.width")} ${o("select.focus.ring.style")} ${o("select.focus.ring.color")};
    outline-offset: ${o("select.focus.ring.offset")};
}

.p-select.p-variant-filled {
    background: ${o("select.filled.background")};
}

.p-select.p-variant-filled:not(.p-disabled):hover {
    background: ${o("select.filled.hover.background")};
}

.p-select.p-variant-filled:not(.p-disabled).p-focus {
    background: ${o("select.filled.focus.background")};
}

.p-select.p-invalid {
    border-color: ${o("select.invalid.border.color")};
}

.p-select.p-disabled {
    opacity: 1;
    background: ${o("select.disabled.background")};
}

.p-select-clear-icon {
    position: absolute;
    top: 50%;
    margin-top: -0.5rem;
    color: ${o("select.clear.icon.color")};
    inset-inline-end: ${o("select.dropdown.width")};
}

.p-select-dropdown {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background: transparent;
    color: ${o("select.dropdown.color")};
    width: ${o("select.dropdown.width")};
    border-start-end-radius: ${o("select.border.radius")};
    border-end-end-radius: ${o("select.border.radius")};
}

.p-select-label {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    flex: 1 1 auto;
    width: 1%;
    padding: ${o("select.padding.y")} ${o("select.padding.x")};
    text-overflow: ellipsis;
    cursor: pointer;
    color: ${o("select.color")};
    background: transparent;
    border: 0 none;
    outline: 0 none;
}

.p-select-label.p-placeholder {
    color: ${o("select.placeholder.color")};
}

.p-select.p-invalid .p-select-label.p-placeholder {
    color: ${o("select.invalid.placeholder.color")};
}

.p-select:has(.p-select-clear-icon) .p-select-label {
    padding-inline-end: calc(1rem + ${o("select.padding.x")});
}

.p-select.p-disabled .p-select-label {
    color: ${o("select.disabled.color")};
}

.p-select-label-empty {
    overflow: hidden;
    opacity: 0;
}

input.p-select-label {
    cursor: default;
}

.p-select .p-select-overlay {
    min-width: 100%;
}

.p-select-overlay {
    position: absolute;
    top: 0;
    left: 0;
    background: ${o("select.overlay.background")};
    color: ${o("select.overlay.color")};
    border: 1px solid ${o("select.overlay.border.color")};
    border-radius: ${o("select.overlay.border.radius")};
    box-shadow: ${o("select.overlay.shadow")};
}

.p-select-header {
    padding: ${o("select.list.header.padding")};
}

.p-select-filter {
    width: 100%;
}

.p-select-list-container {
    overflow: auto;
}

.p-select-option-group {
    cursor: auto;
    margin: 0;
    padding: ${o("select.option.group.padding")};
    background: ${o("select.option.group.background")};
    color: ${o("select.option.group.color")};
    font-weight: ${o("select.option.group.font.weight")};
}

.p-select-list {
    margin: 0;
    padding: 0;
    list-style-type: none;
    padding: ${o("select.list.padding")};
    gap: ${o("select.list.gap")};
    display: flex;
    flex-direction: column;
}

.p-select-option {
    cursor: pointer;
    font-weight: normal;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    padding: ${o("select.option.padding")};
    border: 0 none;
    color: ${o("select.option.color")};
    background: transparent;
    transition: background ${o("select.transition.duration")}, color ${o("select.transition.duration")}, border-color ${o("select.transition.duration")},
            box-shadow ${o("select.transition.duration")}, outline-color ${o("select.transition.duration")};
    border-radius: ${o("select.option.border.radius")};
}

.p-select-option:not(.p-select-option-selected):not(.p-disabled).p-focus {
    background: ${o("select.option.focus.background")};
    color: ${o("select.option.focus.color")};
}

.p-select-option.p-select-option-selected {
    background: ${o("select.option.selected.background")};
    color: ${o("select.option.selected.color")};
}

.p-select-option.p-select-option-selected.p-focus {
    background: ${o("select.option.selected.focus.background")};
    color: ${o("select.option.selected.focus.color")};
}

.p-select-option-blank-icon {
    flex-shrink: 0;
}

.p-select-option-check-icon {
    position: relative;
    flex-shrink: 0;
    margin-inline-start: ${o("select.checkmark.gutter.start")};
    margin-inline-end: ${o("select.checkmark.gutter.end")};
    color: ${o("select.checkmark.color")};
}

.p-select-empty-message {
    padding: ${o("select.empty.message.padding")};
}

.p-select-fluid {
    display: flex;
    width: 100%;
}

.p-select-sm .p-select-label {
    font-size: ${o("select.sm.font.size")};
    padding-block: ${o("select.sm.padding.y")};
    padding-inline: ${o("select.sm.padding.x")};
}

.p-select-sm .p-select-dropdown .p-icon {
    font-size: ${o("select.sm.font.size")};
    width: ${o("select.sm.font.size")};
    height: ${o("select.sm.font.size")};
}

.p-select-lg .p-select-label {
    font-size: ${o("select.lg.font.size")};
    padding-block: ${o("select.lg.padding.y")};
    padding-inline: ${o("select.lg.padding.x")};
}

.p-select-lg .p-select-dropdown .p-icon {
    font-size: ${o("select.lg.font.size")};
    width: ${o("select.lg.font.size")};
    height: ${o("select.lg.font.size")};
}
`,hb=({dt:o})=>`
.p-inputnumber {
    display: inline-flex;
    position: relative;
}

.p-inputnumber-button {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 0 0 auto;
    cursor: pointer;
    background: ${o("inputnumber.button.background")};
    color: ${o("inputnumber.button.color")};
    width: ${o("inputnumber.button.width")};
    transition: background ${o("inputnumber.transition.duration")}, color ${o("inputnumber.transition.duration")}, border-color ${o("inputnumber.transition.duration")}, outline-color ${o("inputnumber.transition.duration")};
}

.p-inputnumber-button:disabled {
    cursor: auto;
}

.p-inputnumber-button:not(:disabled):hover {
    background: ${o("inputnumber.button.hover.background")};
    color: ${o("inputnumber.button.hover.color")};
}

.p-inputnumber-button:not(:disabled):active {
    background: ${o("inputnumber.button.active.background")};
    color: ${o("inputnumber.button.active.color")};
}

.p-inputnumber-stacked .p-inputnumber-button {
    position: relative;
    border: 0 none;
}

.p-inputnumber-stacked .p-inputnumber-button-group {
    display: flex;
    flex-direction: column;
    position: absolute;
    inset-block-start: 1px;
    inset-inline-end: 1px;
    height: calc(100% - 2px);
    z-index: 1;
}

.p-inputnumber-stacked .p-inputnumber-increment-button {
    padding: 0;
    border-start-end-radius: calc(${o("inputnumber.button.border.radius")} - 1px);
}

.p-inputnumber-stacked .p-inputnumber-decrement-button {
    padding: 0;
    border-end-end-radius: calc(${o("inputnumber.button.border.radius")} - 1px);
}

.p-inputnumber-stacked .p-inputnumber-button {
    flex: 1 1 auto;
    border: 0 none;
}

.p-inputnumber-horizontal .p-inputnumber-button {
    border: 1px solid ${o("inputnumber.button.border.color")};
}

.p-inputnumber-horizontal .p-inputnumber-button:hover {
    border-color: ${o("inputnumber.button.hover.border.color")};
}

.p-inputnumber-horizontal .p-inputnumber-button:active {
    border-color: ${o("inputnumber.button.active.border.color")};
}

.p-inputnumber-horizontal .p-inputnumber-increment-button {
    order: 3;
    border-start-end-radius: ${o("inputnumber.button.border.radius")};
    border-end-end-radius: ${o("inputnumber.button.border.radius")};
    border-inline-start: 0 none;
}

.p-inputnumber-horizontal .p-inputnumber-input {
    order: 2;
    border-radius: 0;
}

.p-inputnumber-horizontal .p-inputnumber-decrement-button {
    order: 1;
    border-start-start-radius: ${o("inputnumber.button.border.radius")};
    border-end-start-radius: ${o("inputnumber.button.border.radius")};
    border-inline-end: 0 none;
}

.p-floatlabel:has(.p-inputnumber-horizontal) label {
    margin-inline-start: ${o("inputnumber.button.width")};
}

.p-inputnumber-vertical {
    flex-direction: column;
}

.p-inputnumber-vertical .p-inputnumber-button {
    border: 1px solid ${o("inputnumber.button.border.color")};
    padding: ${o("inputnumber.button.vertical.padding")};
}

.p-inputnumber-vertical .p-inputnumber-button:hover {
    border-color: ${o("inputnumber.button.hover.border.color")};
}

.p-inputnumber-vertical .p-inputnumber-button:active {
    border-color: ${o("inputnumber.button.active.border.color")};
}

.p-inputnumber-vertical .p-inputnumber-increment-button {
    order: 1;
    border-start-start-radius: ${o("inputnumber.button.border.radius")};
    border-start-end-radius: ${o("inputnumber.button.border.radius")};
    width: 100%;
    border-block-end: 0 none;
}

.p-inputnumber-vertical .p-inputnumber-input {
    order: 2;
    border-radius: 0;
    text-align: center;
}

.p-inputnumber-vertical .p-inputnumber-decrement-button {
    order: 3;
    border-end-start-radius: ${o("inputnumber.button.border.radius")};
    border-end-end-radius: ${o("inputnumber.button.border.radius")};
    width: 100%;
    border-block-start: 0 none;
}

.p-inputnumber-input {
    flex: 1 1 auto;
}

.p-inputnumber-fluid {
    width: 100%;
}

.p-inputnumber-fluid .p-inputnumber-input {
    width: 1%;
}

.p-inputnumber-fluid.p-inputnumber-vertical .p-inputnumber-input {
    width: 100%;
}

.p-inputnumber:has(.p-inputtext-sm) .p-inputnumber-button .p-icon {
    font-size: ${o("form.field.sm.font.size")};
    width: ${o("form.field.sm.font.size")};
    height: ${o("form.field.sm.font.size")};
}

.p-inputnumber:has(.p-inputtext-lg) .p-inputnumber-button .p-icon {
    font-size: ${o("form.field.lg.font.size")};
    width: ${o("form.field.lg.font.size")};
    height: ${o("form.field.lg.font.size")};
}
`,mb=({dt:o})=>`
.p-datatable {
    position: relative;
}

.p-datatable-table {
    border-spacing: 0;
    border-collapse: separate;
    width: 100%;
}

.p-datatable-scrollable > .p-datatable-table-container {
    position: relative;
}

.p-datatable-scrollable-table > .p-datatable-thead {
    inset-block-start: 0;
    z-index: 1;
}

.p-datatable-scrollable-table > .p-datatable-frozen-tbody {
    position: sticky;
    z-index: 1;
}

.p-datatable-scrollable-table > .p-datatable-tfoot {
    inset-block-end: 0;
    z-index: 1;
}

.p-datatable-scrollable .p-datatable-frozen-column {
    position: sticky;
    background: ${o("datatable.header.cell.background")};
}

.p-datatable-scrollable th.p-datatable-frozen-column {
    z-index: 1;
}

.p-datatable-scrollable > .p-datatable-table-container > .p-datatable-table > .p-datatable-thead,
.p-datatable-scrollable > .p-datatable-table-container > .p-virtualscroller > .p-datatable-table > .p-datatable-thead {
    background: ${o("datatable.header.cell.background")};
}

.p-datatable-scrollable > .p-datatable-table-container > .p-datatable-table > .p-datatable-tfoot,
.p-datatable-scrollable > .p-datatable-table-container > .p-virtualscroller > .p-datatable-table > .p-datatable-tfoot {
    background: ${o("datatable.footer.cell.background")};
}

.p-datatable-flex-scrollable {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.p-datatable-flex-scrollable > .p-datatable-table-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
}

.p-datatable-scrollable-table > .p-datatable-tbody > .p-datatable-row-group-header {
    position: sticky;
    z-index: 1;
}

.p-datatable-resizable-table > .p-datatable-thead > tr > th,
.p-datatable-resizable-table > .p-datatable-tfoot > tr > td,
.p-datatable-resizable-table > .p-datatable-tbody > tr > td {
    overflow: hidden;
    white-space: nowrap;
}

.p-datatable-resizable-table > .p-datatable-thead > tr > th.p-datatable-resizable-column:not(.p-datatable-frozen-column) {
    background-clip: padding-box;
    position: relative;
}

.p-datatable-resizable-table-fit > .p-datatable-thead > tr > th.p-datatable-resizable-column:last-child .p-datatable-column-resizer {
    display: none;
}

.p-datatable-column-resizer {
    display: block;
    position: absolute;
    inset-block-start: 0;
    inset-inline-end: 0;
    margin: 0;
    width: ${o("datatable.column.resizer.width")};
    height: 100%;
    padding: 0;
    cursor: col-resize;
    border: 1px solid transparent;
}

.p-datatable-column-header-content {
    display: flex;
    align-items: center;
    gap: ${o("datatable.header.cell.gap")};
}

.p-datatable-column-resize-indicator {
    width: ${o("datatable.resize.indicator.width")};
    position: absolute;
    z-index: 10;
    display: none;
    background: ${o("datatable.resize.indicator.color")};
}

.p-datatable-row-reorder-indicator-up,
.p-datatable-row-reorder-indicator-down {
    position: absolute;
    display: none;
}

.p-datatable-reorderable-column,
.p-datatable-reorderable-row-handle {
    cursor: move;
}

.p-datatable-mask {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.p-datatable-inline-filter {
    display: flex;
    align-items: center;
    width: 100%;
    gap: ${o("datatable.filter.inline.gap")};
}

.p-datatable-inline-filter .p-datatable-filter-element-container {
    flex: 1 1 auto;
    width: 1%;
}

.p-datatable-filter-overlay {
    background: ${o("datatable.filter.overlay.select.background")};
    color: ${o("datatable.filter.overlay.select.color")};
    border: 1px solid ${o("datatable.filter.overlay.select.border.color")};
    border-radius: ${o("datatable.filter.overlay.select.border.radius")};
    box-shadow: ${o("datatable.filter.overlay.select.shadow")};
    min-width: 12.5rem;
}

.p-datatable-filter-constraint-list {
    margin: 0;
    list-style: none;
    display: flex;
    flex-direction: column;
    padding: ${o("datatable.filter.constraint.list.padding")};
    gap: ${o("datatable.filter.constraint.list.gap")};
}

.p-datatable-filter-constraint {
    padding: ${o("datatable.filter.constraint.padding")};
    color: ${o("datatable.filter.constraint.color")};
    border-radius: ${o("datatable.filter.constraint.border.radius")};
    cursor: pointer;
    transition: background ${o("datatable.transition.duration")}, color ${o("datatable.transition.duration")}, border-color ${o("datatable.transition.duration")},
        box-shadow ${o("datatable.transition.duration")};
}

.p-datatable-filter-constraint-selected {
    background: ${o("datatable.filter.constraint.selected.background")};
    color: ${o("datatable.filter.constraint.selected.color")};
}

.p-datatable-filter-constraint:not(.p-datatable-filter-constraint-selected):not(.p-disabled):hover {
    background: ${o("datatable.filter.constraint.focus.background")};
    color: ${o("datatable.filter.constraint.focus.color")};
}

.p-datatable-filter-constraint:focus-visible {
    outline: 0 none;
    background: ${o("datatable.filter.constraint.focus.background")};
    color: ${o("datatable.filter.constraint.focus.color")};
}

.p-datatable-filter-constraint-selected:focus-visible {
    outline: 0 none;
    background: ${o("datatable.filter.constraint.selected.focus.background")};
    color: ${o("datatable.filter.constraint.selected.focus.color")};
}

.p-datatable-filter-constraint-separator {
    border-block-start: 1px solid ${o("datatable.filter.constraint.separator.border.color")};
}

.p-datatable-popover-filter {
    display: inline-flex;
    margin-inline-start: auto;
}

.p-datatable-filter-overlay-popover {
    background: ${o("datatable.filter.overlay.popover.background")};
    color: ${o("datatable.filter.overlay.popover.color")};
    border: 1px solid ${o("datatable.filter.overlay.popover.border.color")};
    border-radius: ${o("datatable.filter.overlay.popover.border.radius")};
    box-shadow: ${o("datatable.filter.overlay.popover.shadow")};
    min-width: 12.5rem;
    padding: ${o("datatable.filter.overlay.popover.padding")};
    display: flex;
    flex-direction: column;
    gap: ${o("datatable.filter.overlay.popover.gap")};
}

.p-datatable-filter-operator-dropdown {
    width: 100%;
}

.p-datatable-filter-rule-list,
.p-datatable-filter-rule {
    display: flex;
    flex-direction: column;
    gap: ${o("datatable.filter.overlay.popover.gap")};
}

.p-datatable-filter-rule {
    border-block-end: 1px solid ${o("datatable.filter.rule.border.color")};
    padding-bottom: ${o("datatable.filter.overlay.popover.gap")};
}

.p-datatable-filter-rule:last-child {
    border-block-end: 0 none;
    padding-bottom: 0;
}

.p-datatable-filter-add-rule-button {
    width: 100%;
}

.p-datatable-filter-remove-rule-button {
    width: 100%;
}

.p-datatable-filter-buttonbar {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.p-datatable-virtualscroller-spacer {
    display: flex;
}

.p-datatable .p-virtualscroller .p-virtualscroller-loading {
    transform: none !important;
    min-height: 0;
    position: sticky;
    inset-block-start: 0;
    inset-inline-start: 0;
}

.p-datatable-paginator-top {
    border-color: ${o("datatable.paginator.top.border.color")};
    border-style: solid;
    border-width: ${o("datatable.paginator.top.border.width")};
}

.p-datatable-paginator-bottom {
    border-color: ${o("datatable.paginator.bottom.border.color")};
    border-style: solid;
    border-width: ${o("datatable.paginator.bottom.border.width")};
}

.p-datatable-header {
    background: ${o("datatable.header.background")};
    color: ${o("datatable.header.color")};
    border-color: ${o("datatable.header.border.color")};
    border-style: solid;
    border-width: ${o("datatable.header.border.width")};
    padding: ${o("datatable.header.padding")};
}

.p-datatable-footer {
    background: ${o("datatable.footer.background")};
    color: ${o("datatable.footer.color")};
    border-color: ${o("datatable.footer.border.color")};
    border-style: solid;
    border-width: ${o("datatable.footer.border.width")};
    padding: ${o("datatable.footer.padding")};
}

.p-datatable-header-cell {
    padding: ${o("datatable.header.cell.padding")};
    background: ${o("datatable.header.cell.background")};
    border-color: ${o("datatable.header.cell.border.color")};
    border-style: solid;
    border-width: 0 0 1px 0;
    color: ${o("datatable.header.cell.color")};
    font-weight: normal;
    text-align: start;
    transition: background ${o("datatable.transition.duration")}, color ${o("datatable.transition.duration")}, border-color ${o("datatable.transition.duration")},
            outline-color ${o("datatable.transition.duration")}, box-shadow ${o("datatable.transition.duration")};
}

.p-datatable-column-title {
    font-weight: ${o("datatable.column.title.font.weight")};
}

.p-datatable-tbody > tr {
    outline-color: transparent;
    background: ${o("datatable.row.background")};
    color: ${o("datatable.row.color")};
    transition: background ${o("datatable.transition.duration")}, color ${o("datatable.transition.duration")}, border-color ${o("datatable.transition.duration")},
            outline-color ${o("datatable.transition.duration")}, box-shadow ${o("datatable.transition.duration")};
}

.p-datatable-tbody > tr > td {
    text-align: start;
    border-color: ${o("datatable.body.cell.border.color")};
    border-style: solid;
    border-width: 0 0 1px 0;
    padding: ${o("datatable.body.cell.padding")};
}

.p-datatable-hoverable .p-datatable-tbody > tr:not(.p-datatable-row-selected):hover {
    background: ${o("datatable.row.hover.background")};
    color: ${o("datatable.row.hover.color")};
}

.p-datatable-tbody > tr.p-datatable-row-selected {
    background: ${o("datatable.row.selected.background")};
    color: ${o("datatable.row.selected.color")};
}

.p-datatable-tbody > tr:has(+ .p-datatable-row-selected) > td {
    border-block-end-color: ${o("datatable.body.cell.selected.border.color")};
}

.p-datatable-tbody > tr.p-datatable-row-selected > td {
    border-block-end-color: ${o("datatable.body.cell.selected.border.color")};
}

.p-datatable-tbody > tr:focus-visible,
.p-datatable-tbody > tr.p-datatable-contextmenu-row-selected {
    box-shadow: ${o("datatable.row.focus.ring.shadow")};
    outline: ${o("datatable.row.focus.ring.width")} ${o("datatable.row.focus.ring.style")} ${o("datatable.row.focus.ring.color")};
    outline-offset: ${o("datatable.row.focus.ring.offset")};
}

.p-datatable-tfoot > tr > td {
    text-align: start;
    padding: ${o("datatable.footer.cell.padding")};
    border-color: ${o("datatable.footer.cell.border.color")};
    border-style: solid;
    border-width: 0 0 1px 0;
    color: ${o("datatable.footer.cell.color")};
    background: ${o("datatable.footer.cell.background")};
}

.p-datatable-column-footer {
    font-weight: ${o("datatable.column.footer.font.weight")};
}

.p-datatable-sortable-column {
    cursor: pointer;
    user-select: none;
    outline-color: transparent;
}

.p-datatable-column-title,
.p-datatable-sort-icon,
.p-datatable-sort-badge {
    vertical-align: middle;
}

.p-datatable-sort-icon {
    color: ${o("datatable.sort.icon.color")};
    font-size: ${o("datatable.sort.icon.size")};
    width: ${o("datatable.sort.icon.size")};
    height: ${o("datatable.sort.icon.size")};
    transition: color ${o("datatable.transition.duration")};
}

.p-datatable-sortable-column:not(.p-datatable-column-sorted):hover {
    background: ${o("datatable.header.cell.hover.background")};
    color: ${o("datatable.header.cell.hover.color")};
}

.p-datatable-sortable-column:not(.p-datatable-column-sorted):hover .p-datatable-sort-icon {
    color: ${o("datatable.sort.icon.hover.color")};
}

.p-datatable-column-sorted {
    background: ${o("datatable.header.cell.selected.background")};
    color: ${o("datatable.header.cell.selected.color")};
}

.p-datatable-column-sorted .p-datatable-sort-icon {
    color: ${o("datatable.header.cell.selected.color")};
}

.p-datatable-sortable-column:focus-visible {
    box-shadow: ${o("datatable.header.cell.focus.ring.shadow")};
    outline: ${o("datatable.header.cell.focus.ring.width")} ${o("datatable.header.cell.focus.ring.style")} ${o("datatable.header.cell.focus.ring.color")};
    outline-offset: ${o("datatable.header.cell.focus.ring.offset")};
}

.p-datatable-hoverable .p-datatable-selectable-row {
    cursor: pointer;
}

.p-datatable-tbody > tr.p-datatable-dragpoint-top > td {
    box-shadow: inset 0 2px 0 0 ${o("datatable.drop.point.color")};
}

.p-datatable-tbody > tr.p-datatable-dragpoint-bottom > td {
    box-shadow: inset 0 -2px 0 0 ${o("datatable.drop.point.color")};
}

.p-datatable-loading-icon {
    font-size: ${o("datatable.loading.icon.size")};
    width: ${o("datatable.loading.icon.size")};
    height: ${o("datatable.loading.icon.size")};
}

.p-datatable-gridlines .p-datatable-header {
    border-width: 1px 1px 0 1px;
}

.p-datatable-gridlines .p-datatable-footer {
    border-width: 0 1px 1px 1px;
}

.p-datatable-gridlines .p-datatable-paginator-top {
    border-width: 1px 1px 0 1px;
}

.p-datatable-gridlines .p-datatable-paginator-bottom {
    border-width: 0 1px 1px 1px;
}

.p-datatable-gridlines .p-datatable-thead > tr > th {
    border-width: 1px 0 1px 1px;
}

.p-datatable-gridlines .p-datatable-thead > tr > th:last-child {
    border-width: 1px;
}

.p-datatable-gridlines .p-datatable-tbody > tr > td {
    border-width: 1px 0 0 1px;
}

.p-datatable-gridlines .p-datatable-tbody > tr > td:last-child {
    border-width: 1px 1px 0 1px;
}

.p-datatable-gridlines .p-datatable-tbody > tr:last-child > td {
    border-width: 1px 0 1px 1px;
}

.p-datatable-gridlines .p-datatable-tbody > tr:last-child > td:last-child {
    border-width: 1px;
}

.p-datatable-gridlines .p-datatable-tfoot > tr > td {
    border-width: 1px 0 1px 1px;
}

.p-datatable-gridlines .p-datatable-tfoot > tr > td:last-child {
    border-width: 1px 1px 1px 1px;
}

.p-datatable.p-datatable-gridlines .p-datatable-thead + .p-datatable-tfoot > tr > td {
    border-width: 0 0 1px 1px;
}

.p-datatable.p-datatable-gridlines .p-datatable-thead + .p-datatable-tfoot > tr > td:last-child {
    border-width: 0 1px 1px 1px;
}

.p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody > tr > td {
    border-width: 0 0 1px 1px;
}

.p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody > tr > td:last-child {
    border-width: 0 1px 1px 1px;
}

.p-datatable.p-datatable-gridlines:has(.p-datatable-tbody):has(.p-datatable-tfoot) .p-datatable-tbody > tr:last-child > td {
    border-width: 0 0 0 1px;
}

.p-datatable.p-datatable-gridlines:has(.p-datatable-tbody):has(.p-datatable-tfoot) .p-datatable-tbody > tr:last-child > td:last-child {
    border-width: 0 1px 0 1px;
}

.p-datatable.p-datatable-striped .p-datatable-tbody > tr.p-row-odd {
    background: ${o("datatable.row.striped.background")};
}

.p-datatable.p-datatable-striped .p-datatable-tbody > tr.p-row-odd.p-datatable-row-selected {
    background: ${o("datatable.row.selected.background")};
    color: ${o("datatable.row.selected.color")};
}

.p-datatable-striped.p-datatable-hoverable .p-datatable-tbody > tr:not(.p-datatable-row-selected):hover {
    background: ${o("datatable.row.hover.background")};
    color: ${o("datatable.row.hover.color")};
}

.p-datatable.p-datatable-sm .p-datatable-header {
    padding: ${o("datatable.header.sm.padding")};
}

.p-datatable.p-datatable-sm .p-datatable-thead > tr > th {
    padding: ${o("datatable.header.cell.sm.padding")};
}

.p-datatable.p-datatable-sm .p-datatable-tbody > tr > td {
    padding: ${o("datatable.body.cell.sm.padding")};
}

.p-datatable.p-datatable-sm .p-datatable-tfoot > tr > td {
    padding: ${o("datatable.footer.cell.sm.padding")};
}

.p-datatable.p-datatable-sm .p-datatable-footer {
    padding: ${o("datatable.footer.sm.padding")};
}

.p-datatable.p-datatable-lg .p-datatable-header {
    padding: ${o("datatable.header.lg.padding")};
}

.p-datatable.p-datatable-lg .p-datatable-thead > tr > th {
    padding: ${o("datatable.header.cell.lg.padding")};
}

.p-datatable.p-datatable-lg .p-datatable-tbody > tr > td {
    padding: ${o("datatable.body.cell.lg.padding")};
}

.p-datatable.p-datatable-lg .p-datatable-tfoot > tr > td {
    padding: ${o("datatable.footer.cell.lg.padding")};
}

.p-datatable.p-datatable-lg .p-datatable-footer {
    padding: ${o("datatable.footer.lg.padding")};
}

.p-datatable-row-toggle-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    width: ${o("datatable.row.toggle.button.size")};
    height: ${o("datatable.row.toggle.button.size")};
    color: ${o("datatable.row.toggle.button.color")};
    border: 0 none;
    background: transparent;
    cursor: pointer;
    border-radius: ${o("datatable.row.toggle.button.border.radius")};
    transition: background ${o("datatable.transition.duration")}, color ${o("datatable.transition.duration")}, border-color ${o("datatable.transition.duration")},
            outline-color ${o("datatable.transition.duration")}, box-shadow ${o("datatable.transition.duration")};
    outline-color: transparent;
    user-select: none;
}

.p-datatable-row-toggle-button:enabled:hover {
    color: ${o("datatable.row.toggle.button.hover.color")};
    background: ${o("datatable.row.toggle.button.hover.background")};
}

.p-datatable-tbody > tr.p-datatable-row-selected .p-datatable-row-toggle-button:hover {
    background: ${o("datatable.row.toggle.button.selected.hover.background")};
    color: ${o("datatable.row.toggle.button.selected.hover.color")};
}

.p-datatable-row-toggle-button:focus-visible {
    box-shadow: ${o("datatable.row.toggle.button.focus.ring.shadow")};
    outline: ${o("datatable.row.toggle.button.focus.ring.width")} ${o("datatable.row.toggle.button.focus.ring.style")} ${o("datatable.row.toggle.button.focus.ring.color")};
    outline-offset: ${o("datatable.row.toggle.button.focus.ring.offset")};
}

.p-datatable-row-toggle-icon:dir(rtl) {
    transform: rotate(180deg);
}
`,kb=({dt:o})=>`
.p-badge {
    display: inline-flex;
    border-radius: ${o("badge.border.radius")};
    align-items: center;
    justify-content: center;
    padding: ${o("badge.padding")};
    background: ${o("badge.primary.background")};
    color: ${o("badge.primary.color")};
    font-size: ${o("badge.font.size")};
    font-weight: ${o("badge.font.weight")};
    min-width: ${o("badge.min.width")};
    height: ${o("badge.height")};
}

.p-badge-dot {
    width: ${o("badge.dot.size")};
    min-width: ${o("badge.dot.size")};
    height: ${o("badge.dot.size")};
    border-radius: 50%;
    padding: 0;
}

.p-badge-circle {
    padding: 0;
    border-radius: 50%;
}

.p-badge-secondary {
    background: ${o("badge.secondary.background")};
    color: ${o("badge.secondary.color")};
}

.p-badge-success {
    background: ${o("badge.success.background")};
    color: ${o("badge.success.color")};
}

.p-badge-info {
    background: ${o("badge.info.background")};
    color: ${o("badge.info.color")};
}

.p-badge-warn {
    background: ${o("badge.warn.background")};
    color: ${o("badge.warn.color")};
}

.p-badge-danger {
    background: ${o("badge.danger.background")};
    color: ${o("badge.danger.color")};
}

.p-badge-contrast {
    background: ${o("badge.contrast.background")};
    color: ${o("badge.contrast.color")};
}

.p-badge-sm {
    font-size: ${o("badge.sm.font.size")};
    min-width: ${o("badge.sm.min.width")};
    height: ${o("badge.sm.height")};
}

.p-badge-lg {
    font-size: ${o("badge.lg.font.size")};
    min-width: ${o("badge.lg.min.width")};
    height: ${o("badge.lg.height")};
}

.p-badge-xl {
    font-size: ${o("badge.xl.font.size")};
    min-width: ${o("badge.xl.min.width")};
    height: ${o("badge.xl.height")};
}
`,vb=({dt:o})=>`
.p-button {
    display: inline-flex;
    cursor: pointer;
    user-select: none;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    color: ${o("button.primary.color")};
    background: ${o("button.primary.background")};
    border: 1px solid ${o("button.primary.border.color")};
    padding: ${o("button.padding.y")} ${o("button.padding.x")};
    font-size: 1rem;
    font-family: inherit;
    font-feature-settings: inherit;
    transition: background ${o("button.transition.duration")}, color ${o("button.transition.duration")}, border-color ${o("button.transition.duration")},
            outline-color ${o("button.transition.duration")}, box-shadow ${o("button.transition.duration")};
    border-radius: ${o("button.border.radius")};
    outline-color: transparent;
    gap: ${o("button.gap")};
}

.p-button:disabled {
    cursor: default;
}

.p-button-icon-right {
    order: 1;
}

.p-button-icon-right:dir(rtl) {
    order: -1;
}

.p-button:not(.p-button-vertical) .p-button-icon:not(.p-button-icon-right):dir(rtl) {
    order: 1;
}

.p-button-icon-bottom {
    order: 2;
}

.p-button-icon-only {
    width: ${o("button.icon.only.width")};
    padding-inline-start: 0;
    padding-inline-end: 0;
    gap: 0;
}

.p-button-icon-only.p-button-rounded {
    border-radius: 50%;
    height: ${o("button.icon.only.width")};
}

.p-button-icon-only .p-button-label {
    visibility: hidden;
    width: 0;
}

.p-button-sm {
    font-size: ${o("button.sm.font.size")};
    padding: ${o("button.sm.padding.y")} ${o("button.sm.padding.x")};
}

.p-button-sm .p-button-icon {
    font-size: ${o("button.sm.font.size")};
}

.p-button-sm.p-button-icon-only {
    width: ${o("button.sm.icon.only.width")};
}

.p-button-sm.p-button-icon-only.p-button-rounded {
    height: ${o("button.sm.icon.only.width")};
}

.p-button-lg {
    font-size: ${o("button.lg.font.size")};
    padding: ${o("button.lg.padding.y")} ${o("button.lg.padding.x")};
}

.p-button-lg .p-button-icon {
    font-size: ${o("button.lg.font.size")};
}

.p-button-lg.p-button-icon-only {
    width: ${o("button.lg.icon.only.width")};
}

.p-button-lg.p-button-icon-only.p-button-rounded {
    height: ${o("button.lg.icon.only.width")};
}

.p-button-vertical {
    flex-direction: column;
}

.p-button-label {
    font-weight: ${o("button.label.font.weight")};
}

.p-button-fluid {
    width: 100%;
}

.p-button-fluid.p-button-icon-only {
    width: ${o("button.icon.only.width")};
}

.p-button:not(:disabled):hover {
    background: ${o("button.primary.hover.background")};
    border: 1px solid ${o("button.primary.hover.border.color")};
    color: ${o("button.primary.hover.color")};
}

.p-button:not(:disabled):active {
    background: ${o("button.primary.active.background")};
    border: 1px solid ${o("button.primary.active.border.color")};
    color: ${o("button.primary.active.color")};
}

.p-button:focus-visible {
    box-shadow: ${o("button.primary.focus.ring.shadow")};
    outline: ${o("button.focus.ring.width")} ${o("button.focus.ring.style")} ${o("button.primary.focus.ring.color")};
    outline-offset: ${o("button.focus.ring.offset")};
}

.p-button .p-badge {
    min-width: ${o("button.badge.size")};
    height: ${o("button.badge.size")};
    line-height: ${o("button.badge.size")};
}

.p-button-raised {
    box-shadow: ${o("button.raised.shadow")};
}

.p-button-rounded {
    border-radius: ${o("button.rounded.border.radius")};
}

.p-button-secondary {
    background: ${o("button.secondary.background")};
    border: 1px solid ${o("button.secondary.border.color")};
    color: ${o("button.secondary.color")};
}

.p-button-secondary:not(:disabled):hover {
    background: ${o("button.secondary.hover.background")};
    border: 1px solid ${o("button.secondary.hover.border.color")};
    color: ${o("button.secondary.hover.color")};
}

.p-button-secondary:not(:disabled):active {
    background: ${o("button.secondary.active.background")};
    border: 1px solid ${o("button.secondary.active.border.color")};
    color: ${o("button.secondary.active.color")};
}

.p-button-secondary:focus-visible {
    outline-color: ${o("button.secondary.focus.ring.color")};
    box-shadow: ${o("button.secondary.focus.ring.shadow")};
}

.p-button-success {
    background: ${o("button.success.background")};
    border: 1px solid ${o("button.success.border.color")};
    color: ${o("button.success.color")};
}

.p-button-success:not(:disabled):hover {
    background: ${o("button.success.hover.background")};
    border: 1px solid ${o("button.success.hover.border.color")};
    color: ${o("button.success.hover.color")};
}

.p-button-success:not(:disabled):active {
    background: ${o("button.success.active.background")};
    border: 1px solid ${o("button.success.active.border.color")};
    color: ${o("button.success.active.color")};
}

.p-button-success:focus-visible {
    outline-color: ${o("button.success.focus.ring.color")};
    box-shadow: ${o("button.success.focus.ring.shadow")};
}

.p-button-info {
    background: ${o("button.info.background")};
    border: 1px solid ${o("button.info.border.color")};
    color: ${o("button.info.color")};
}

.p-button-info:not(:disabled):hover {
    background: ${o("button.info.hover.background")};
    border: 1px solid ${o("button.info.hover.border.color")};
    color: ${o("button.info.hover.color")};
}

.p-button-info:not(:disabled):active {
    background: ${o("button.info.active.background")};
    border: 1px solid ${o("button.info.active.border.color")};
    color: ${o("button.info.active.color")};
}

.p-button-info:focus-visible {
    outline-color: ${o("button.info.focus.ring.color")};
    box-shadow: ${o("button.info.focus.ring.shadow")};
}

.p-button-warn {
    background: ${o("button.warn.background")};
    border: 1px solid ${o("button.warn.border.color")};
    color: ${o("button.warn.color")};
}

.p-button-warn:not(:disabled):hover {
    background: ${o("button.warn.hover.background")};
    border: 1px solid ${o("button.warn.hover.border.color")};
    color: ${o("button.warn.hover.color")};
}

.p-button-warn:not(:disabled):active {
    background: ${o("button.warn.active.background")};
    border: 1px solid ${o("button.warn.active.border.color")};
    color: ${o("button.warn.active.color")};
}

.p-button-warn:focus-visible {
    outline-color: ${o("button.warn.focus.ring.color")};
    box-shadow: ${o("button.warn.focus.ring.shadow")};
}

.p-button-help {
    background: ${o("button.help.background")};
    border: 1px solid ${o("button.help.border.color")};
    color: ${o("button.help.color")};
}

.p-button-help:not(:disabled):hover {
    background: ${o("button.help.hover.background")};
    border: 1px solid ${o("button.help.hover.border.color")};
    color: ${o("button.help.hover.color")};
}

.p-button-help:not(:disabled):active {
    background: ${o("button.help.active.background")};
    border: 1px solid ${o("button.help.active.border.color")};
    color: ${o("button.help.active.color")};
}

.p-button-help:focus-visible {
    outline-color: ${o("button.help.focus.ring.color")};
    box-shadow: ${o("button.help.focus.ring.shadow")};
}

.p-button-danger {
    background: ${o("button.danger.background")};
    border: 1px solid ${o("button.danger.border.color")};
    color: ${o("button.danger.color")};
}

.p-button-danger:not(:disabled):hover {
    background: ${o("button.danger.hover.background")};
    border: 1px solid ${o("button.danger.hover.border.color")};
    color: ${o("button.danger.hover.color")};
}

.p-button-danger:not(:disabled):active {
    background: ${o("button.danger.active.background")};
    border: 1px solid ${o("button.danger.active.border.color")};
    color: ${o("button.danger.active.color")};
}

.p-button-danger:focus-visible {
    outline-color: ${o("button.danger.focus.ring.color")};
    box-shadow: ${o("button.danger.focus.ring.shadow")};
}

.p-button-contrast {
    background: ${o("button.contrast.background")};
    border: 1px solid ${o("button.contrast.border.color")};
    color: ${o("button.contrast.color")};
}

.p-button-contrast:not(:disabled):hover {
    background: ${o("button.contrast.hover.background")};
    border: 1px solid ${o("button.contrast.hover.border.color")};
    color: ${o("button.contrast.hover.color")};
}

.p-button-contrast:not(:disabled):active {
    background: ${o("button.contrast.active.background")};
    border: 1px solid ${o("button.contrast.active.border.color")};
    color: ${o("button.contrast.active.color")};
}

.p-button-contrast:focus-visible {
    outline-color: ${o("button.contrast.focus.ring.color")};
    box-shadow: ${o("button.contrast.focus.ring.shadow")};
}

.p-button-outlined {
    background: transparent;
    border-color: ${o("button.outlined.primary.border.color")};
    color: ${o("button.outlined.primary.color")};
}

.p-button-outlined:not(:disabled):hover {
    background: ${o("button.outlined.primary.hover.background")};
    border-color: ${o("button.outlined.primary.border.color")};
    color: ${o("button.outlined.primary.color")};
}

.p-button-outlined:not(:disabled):active {
    background: ${o("button.outlined.primary.active.background")};
    border-color: ${o("button.outlined.primary.border.color")};
    color: ${o("button.outlined.primary.color")};
}

.p-button-outlined.p-button-secondary {
    border-color: ${o("button.outlined.secondary.border.color")};
    color: ${o("button.outlined.secondary.color")};
}

.p-button-outlined.p-button-secondary:not(:disabled):hover {
    background: ${o("button.outlined.secondary.hover.background")};
    border-color: ${o("button.outlined.secondary.border.color")};
    color: ${o("button.outlined.secondary.color")};
}

.p-button-outlined.p-button-secondary:not(:disabled):active {
    background: ${o("button.outlined.secondary.active.background")};
    border-color: ${o("button.outlined.secondary.border.color")};
    color: ${o("button.outlined.secondary.color")};
}

.p-button-outlined.p-button-success {
    border-color: ${o("button.outlined.success.border.color")};
    color: ${o("button.outlined.success.color")};
}

.p-button-outlined.p-button-success:not(:disabled):hover {
    background: ${o("button.outlined.success.hover.background")};
    border-color: ${o("button.outlined.success.border.color")};
    color: ${o("button.outlined.success.color")};
}

.p-button-outlined.p-button-success:not(:disabled):active {
    background: ${o("button.outlined.success.active.background")};
    border-color: ${o("button.outlined.success.border.color")};
    color: ${o("button.outlined.success.color")};
}

.p-button-outlined.p-button-info {
    border-color: ${o("button.outlined.info.border.color")};
    color: ${o("button.outlined.info.color")};
}

.p-button-outlined.p-button-info:not(:disabled):hover {
    background: ${o("button.outlined.info.hover.background")};
    border-color: ${o("button.outlined.info.border.color")};
    color: ${o("button.outlined.info.color")};
}

.p-button-outlined.p-button-info:not(:disabled):active {
    background: ${o("button.outlined.info.active.background")};
    border-color: ${o("button.outlined.info.border.color")};
    color: ${o("button.outlined.info.color")};
}

.p-button-outlined.p-button-warn {
    border-color: ${o("button.outlined.warn.border.color")};
    color: ${o("button.outlined.warn.color")};
}

.p-button-outlined.p-button-warn:not(:disabled):hover {
    background: ${o("button.outlined.warn.hover.background")};
    border-color: ${o("button.outlined.warn.border.color")};
    color: ${o("button.outlined.warn.color")};
}

.p-button-outlined.p-button-warn:not(:disabled):active {
    background: ${o("button.outlined.warn.active.background")};
    border-color: ${o("button.outlined.warn.border.color")};
    color: ${o("button.outlined.warn.color")};
}

.p-button-outlined.p-button-help {
    border-color: ${o("button.outlined.help.border.color")};
    color: ${o("button.outlined.help.color")};
}

.p-button-outlined.p-button-help:not(:disabled):hover {
    background: ${o("button.outlined.help.hover.background")};
    border-color: ${o("button.outlined.help.border.color")};
    color: ${o("button.outlined.help.color")};
}

.p-button-outlined.p-button-help:not(:disabled):active {
    background: ${o("button.outlined.help.active.background")};
    border-color: ${o("button.outlined.help.border.color")};
    color: ${o("button.outlined.help.color")};
}

.p-button-outlined.p-button-danger {
    border-color: ${o("button.outlined.danger.border.color")};
    color: ${o("button.outlined.danger.color")};
}

.p-button-outlined.p-button-danger:not(:disabled):hover {
    background: ${o("button.outlined.danger.hover.background")};
    border-color: ${o("button.outlined.danger.border.color")};
    color: ${o("button.outlined.danger.color")};
}

.p-button-outlined.p-button-danger:not(:disabled):active {
    background: ${o("button.outlined.danger.active.background")};
    border-color: ${o("button.outlined.danger.border.color")};
    color: ${o("button.outlined.danger.color")};
}

.p-button-outlined.p-button-contrast {
    border-color: ${o("button.outlined.contrast.border.color")};
    color: ${o("button.outlined.contrast.color")};
}

.p-button-outlined.p-button-contrast:not(:disabled):hover {
    background: ${o("button.outlined.contrast.hover.background")};
    border-color: ${o("button.outlined.contrast.border.color")};
    color: ${o("button.outlined.contrast.color")};
}

.p-button-outlined.p-button-contrast:not(:disabled):active {
    background: ${o("button.outlined.contrast.active.background")};
    border-color: ${o("button.outlined.contrast.border.color")};
    color: ${o("button.outlined.contrast.color")};
}

.p-button-outlined.p-button-plain {
    border-color: ${o("button.outlined.plain.border.color")};
    color: ${o("button.outlined.plain.color")};
}

.p-button-outlined.p-button-plain:not(:disabled):hover {
    background: ${o("button.outlined.plain.hover.background")};
    border-color: ${o("button.outlined.plain.border.color")};
    color: ${o("button.outlined.plain.color")};
}

.p-button-outlined.p-button-plain:not(:disabled):active {
    background: ${o("button.outlined.plain.active.background")};
    border-color: ${o("button.outlined.plain.border.color")};
    color: ${o("button.outlined.plain.color")};
}

.p-button-text {
    background: transparent;
    border-color: transparent;
    color: ${o("button.text.primary.color")};
}

.p-button-text:not(:disabled):hover {
    background: ${o("button.text.primary.hover.background")};
    border-color: transparent;
    color: ${o("button.text.primary.color")};
}

.p-button-text:not(:disabled):active {
    background: ${o("button.text.primary.active.background")};
    border-color: transparent;
    color: ${o("button.text.primary.color")};
}

.p-button-text.p-button-secondary {
    background: transparent;
    border-color: transparent;
    color: ${o("button.text.secondary.color")};
}

.p-button-text.p-button-secondary:not(:disabled):hover {
    background: ${o("button.text.secondary.hover.background")};
    border-color: transparent;
    color: ${o("button.text.secondary.color")};
}

.p-button-text.p-button-secondary:not(:disabled):active {
    background: ${o("button.text.secondary.active.background")};
    border-color: transparent;
    color: ${o("button.text.secondary.color")};
}

.p-button-text.p-button-success {
    background: transparent;
    border-color: transparent;
    color: ${o("button.text.success.color")};
}

.p-button-text.p-button-success:not(:disabled):hover {
    background: ${o("button.text.success.hover.background")};
    border-color: transparent;
    color: ${o("button.text.success.color")};
}

.p-button-text.p-button-success:not(:disabled):active {
    background: ${o("button.text.success.active.background")};
    border-color: transparent;
    color: ${o("button.text.success.color")};
}

.p-button-text.p-button-info {
    background: transparent;
    border-color: transparent;
    color: ${o("button.text.info.color")};
}

.p-button-text.p-button-info:not(:disabled):hover {
    background: ${o("button.text.info.hover.background")};
    border-color: transparent;
    color: ${o("button.text.info.color")};
}

.p-button-text.p-button-info:not(:disabled):active {
    background: ${o("button.text.info.active.background")};
    border-color: transparent;
    color: ${o("button.text.info.color")};
}

.p-button-text.p-button-warn {
    background: transparent;
    border-color: transparent;
    color: ${o("button.text.warn.color")};
}

.p-button-text.p-button-warn:not(:disabled):hover {
    background: ${o("button.text.warn.hover.background")};
    border-color: transparent;
    color: ${o("button.text.warn.color")};
}

.p-button-text.p-button-warn:not(:disabled):active {
    background: ${o("button.text.warn.active.background")};
    border-color: transparent;
    color: ${o("button.text.warn.color")};
}

.p-button-text.p-button-help {
    background: transparent;
    border-color: transparent;
    color: ${o("button.text.help.color")};
}

.p-button-text.p-button-help:not(:disabled):hover {
    background: ${o("button.text.help.hover.background")};
    border-color: transparent;
    color: ${o("button.text.help.color")};
}

.p-button-text.p-button-help:not(:disabled):active {
    background: ${o("button.text.help.active.background")};
    border-color: transparent;
    color: ${o("button.text.help.color")};
}

.p-button-text.p-button-danger {
    background: transparent;
    border-color: transparent;
    color: ${o("button.text.danger.color")};
}

.p-button-text.p-button-danger:not(:disabled):hover {
    background: ${o("button.text.danger.hover.background")};
    border-color: transparent;
    color: ${o("button.text.danger.color")};
}

.p-button-text.p-button-danger:not(:disabled):active {
    background: ${o("button.text.danger.active.background")};
    border-color: transparent;
    color: ${o("button.text.danger.color")};
}

.p-button-text.p-button-contrast {
    background: transparent;
    border-color: transparent;
    color: ${o("button.text.contrast.color")};
}

.p-button-text.p-button-contrast:not(:disabled):hover {
    background: ${o("button.text.contrast.hover.background")};
    border-color: transparent;
    color: ${o("button.text.contrast.color")};
}

.p-button-text.p-button-contrast:not(:disabled):active {
    background: ${o("button.text.contrast.active.background")};
    border-color: transparent;
    color: ${o("button.text.contrast.color")};
}

.p-button-text.p-button-plain {
    background: transparent;
    border-color: transparent;
    color: ${o("button.text.plain.color")};
}

.p-button-text.p-button-plain:not(:disabled):hover {
    background: ${o("button.text.plain.hover.background")};
    border-color: transparent;
    color: ${o("button.text.plain.color")};
}

.p-button-text.p-button-plain:not(:disabled):active {
    background: ${o("button.text.plain.active.background")};
    border-color: transparent;
    color: ${o("button.text.plain.color")};
}

.p-button-link {
    background: transparent;
    border-color: transparent;
    color: ${o("button.link.color")};
}

.p-button-link:not(:disabled):hover {
    background: transparent;
    border-color: transparent;
    color: ${o("button.link.hover.color")};
}

.p-button-link:not(:disabled):hover .p-button-label {
    text-decoration: underline;
}

.p-button-link:not(:disabled):active {
    background: transparent;
    border-color: transparent;
    color: ${o("button.link.active.color")};
}
`,$b=({dt:o})=>`
.p-checkbox {
    position: relative;
    display: inline-flex;
    user-select: none;
    vertical-align: bottom;
    width: ${o("checkbox.width")};
    height: ${o("checkbox.height")};
}

.p-checkbox-input {
    cursor: pointer;
    appearance: none;
    position: absolute;
    inset-block-start: 0;
    inset-inline-start: 0;
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    opacity: 0;
    z-index: 1;
    outline: 0 none;
    border: 1px solid transparent;
    border-radius: ${o("checkbox.border.radius")};
}

.p-checkbox-box {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: ${o("checkbox.border.radius")};
    border: 1px solid ${o("checkbox.border.color")};
    background: ${o("checkbox.background")};
    width: ${o("checkbox.width")};
    height: ${o("checkbox.height")};
    transition: background ${o("checkbox.transition.duration")}, color ${o("checkbox.transition.duration")}, border-color ${o("checkbox.transition.duration")}, box-shadow ${o("checkbox.transition.duration")}, outline-color ${o("checkbox.transition.duration")};
    outline-color: transparent;
    box-shadow: ${o("checkbox.shadow")};
}

.p-checkbox-icon {
    transition-duration: ${o("checkbox.transition.duration")};
    color: ${o("checkbox.icon.color")};
    font-size: ${o("checkbox.icon.size")};
    width: ${o("checkbox.icon.size")};
    height: ${o("checkbox.icon.size")};
}

.p-checkbox:not(.p-disabled):has(.p-checkbox-input:hover) .p-checkbox-box {
    border-color: ${o("checkbox.hover.border.color")};
}

.p-checkbox-checked .p-checkbox-box {
    border-color: ${o("checkbox.checked.border.color")};
    background: ${o("checkbox.checked.background")};
}

.p-checkbox-checked .p-checkbox-icon {
    color: ${o("checkbox.icon.checked.color")};
}

.p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:hover) .p-checkbox-box {
    background: ${o("checkbox.checked.hover.background")};
    border-color: ${o("checkbox.checked.hover.border.color")};
}

.p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:hover) .p-checkbox-icon {
    color: ${o("checkbox.icon.checked.hover.color")};
}

.p-checkbox:not(.p-disabled):has(.p-checkbox-input:focus-visible) .p-checkbox-box {
    border-color: ${o("checkbox.focus.border.color")};
    box-shadow: ${o("checkbox.focus.ring.shadow")};
    outline: ${o("checkbox.focus.ring.width")} ${o("checkbox.focus.ring.style")} ${o("checkbox.focus.ring.color")};
    outline-offset: ${o("checkbox.focus.ring.offset")};
}

.p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:focus-visible) .p-checkbox-box {
    border-color: ${o("checkbox.checked.focus.border.color")};
}

.p-checkbox.p-invalid > .p-checkbox-box {
    border-color: ${o("checkbox.invalid.border.color")};
}

.p-checkbox.p-variant-filled .p-checkbox-box {
    background: ${o("checkbox.filled.background")};
}

.p-checkbox-checked.p-variant-filled .p-checkbox-box {
    background: ${o("checkbox.checked.background")};
}

.p-checkbox-checked.p-variant-filled:not(.p-disabled):has(.p-checkbox-input:hover) .p-checkbox-box {
    background: ${o("checkbox.checked.hover.background")};
}

.p-checkbox.p-disabled {
    opacity: 1;
}

.p-checkbox.p-disabled .p-checkbox-box {
    background: ${o("checkbox.disabled.background")};
    border-color: ${o("checkbox.checked.disabled.border.color")};
}

.p-checkbox.p-disabled .p-checkbox-box .p-checkbox-icon {
    color: ${o("checkbox.icon.disabled.color")};
}

.p-checkbox-sm,
.p-checkbox-sm .p-checkbox-box {
    width: ${o("checkbox.sm.width")};
    height: ${o("checkbox.sm.height")};
}

.p-checkbox-sm .p-checkbox-icon {
    font-size: ${o("checkbox.icon.sm.size")};
    width: ${o("checkbox.icon.sm.size")};
    height: ${o("checkbox.icon.sm.size")};
}

.p-checkbox-lg,
.p-checkbox-lg .p-checkbox-box {
    width: ${o("checkbox.lg.width")};
    height: ${o("checkbox.lg.height")};
}

.p-checkbox-lg .p-checkbox-icon {
    font-size: ${o("checkbox.icon.lg.size")};
    width: ${o("checkbox.icon.lg.size")};
    height: ${o("checkbox.icon.lg.size")};
}
`,xb=({dt:o})=>`
.p-radiobutton {
    position: relative;
    display: inline-flex;
    user-select: none;
    vertical-align: bottom;
    width: ${o("radiobutton.width")};
    height: ${o("radiobutton.height")};
}

.p-radiobutton-input {
    cursor: pointer;
    appearance: none;
    position: absolute;
    top: 0;
    inset-inline-start: 0;
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    opacity: 0;
    z-index: 1;
    outline: 0 none;
    border: 1px solid transparent;
    border-radius: 50%;
}

.p-radiobutton-box {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    border: 1px solid ${o("radiobutton.border.color")};
    background: ${o("radiobutton.background")};
    width: ${o("radiobutton.width")};
    height: ${o("radiobutton.height")};
    transition: background ${o("radiobutton.transition.duration")}, color ${o("radiobutton.transition.duration")}, border-color ${o("radiobutton.transition.duration")}, box-shadow ${o("radiobutton.transition.duration")}, outline-color ${o("radiobutton.transition.duration")};
    outline-color: transparent;
    box-shadow: ${o("radiobutton.shadow")};
}

.p-radiobutton-icon {
    transition-duration: ${o("radiobutton.transition.duration")};
    background: transparent;
    font-size: ${o("radiobutton.icon.size")};
    width: ${o("radiobutton.icon.size")};
    height: ${o("radiobutton.icon.size")};
    border-radius: 50%;
    backface-visibility: hidden;
    transform: translateZ(0) scale(0.1);
}

.p-radiobutton:not(.p-disabled):has(.p-radiobutton-input:hover) .p-radiobutton-box {
    border-color: ${o("radiobutton.hover.border.color")};
}

.p-radiobutton-checked .p-radiobutton-box {
    border-color: ${o("radiobutton.checked.border.color")};
    background: ${o("radiobutton.checked.background")};
}

.p-radiobutton-checked .p-radiobutton-box .p-radiobutton-icon {
    background: ${o("radiobutton.icon.checked.color")};
    transform: translateZ(0) scale(1, 1);
    visibility: visible;
}

.p-radiobutton-checked:not(.p-disabled):has(.p-radiobutton-input:hover) .p-radiobutton-box {
    border-color: ${o("radiobutton.checked.hover.border.color")};
    background: ${o("radiobutton.checked.hover.background")};
}

.p-radiobutton:not(.p-disabled):has(.p-radiobutton-input:hover).p-radiobutton-checked .p-radiobutton-box .p-radiobutton-icon {
    background: ${o("radiobutton.icon.checked.hover.color")};
}

.p-radiobutton:not(.p-disabled):has(.p-radiobutton-input:focus-visible) .p-radiobutton-box {
    border-color: ${o("radiobutton.focus.border.color")};
    box-shadow: ${o("radiobutton.focus.ring.shadow")};
    outline: ${o("radiobutton.focus.ring.width")} ${o("radiobutton.focus.ring.style")} ${o("radiobutton.focus.ring.color")};
    outline-offset: ${o("radiobutton.focus.ring.offset")};
}

.p-radiobutton-checked:not(.p-disabled):has(.p-radiobutton-input:focus-visible) .p-radiobutton-box {
    border-color: ${o("radiobutton.checked.focus.border.color")};
}

.p-radiobutton.p-invalid > .p-radiobutton-box {
    border-color: ${o("radiobutton.invalid.border.color")};
}

.p-radiobutton.p-variant-filled .p-radiobutton-box {
    background: ${o("radiobutton.filled.background")};
}

.p-radiobutton.p-variant-filled.p-radiobutton-checked .p-radiobutton-box {
    background: ${o("radiobutton.checked.background")};
}

.p-radiobutton.p-variant-filled:not(.p-disabled):has(.p-radiobutton-input:hover).p-radiobutton-checked .p-radiobutton-box {
    background: ${o("radiobutton.checked.hover.background")};
}

.p-radiobutton.p-disabled {
    opacity: 1;
}

.p-radiobutton.p-disabled .p-radiobutton-box {
    background: ${o("radiobutton.disabled.background")};
    border-color: ${o("radiobutton.checked.disabled.border.color")};
}

.p-radiobutton-checked.p-disabled .p-radiobutton-box .p-radiobutton-icon {
    background: ${o("radiobutton.icon.disabled.color")};
}

.p-radiobutton-sm,
.p-radiobutton-sm .p-radiobutton-box {
    width: ${o("radiobutton.sm.width")};
    height: ${o("radiobutton.sm.height")};
}

.p-radiobutton-sm .p-radiobutton-icon {
    font-size: ${o("radiobutton.icon.sm.size")};
    width: ${o("radiobutton.icon.sm.size")};
    height: ${o("radiobutton.icon.sm.size")};
}

.p-radiobutton-lg,
.p-radiobutton-lg .p-radiobutton-box {
    width: ${o("radiobutton.lg.width")};
    height: ${o("radiobutton.lg.height")};
}

.p-radiobutton-lg .p-radiobutton-icon {
    font-size: ${o("radiobutton.icon.lg.size")};
    width: ${o("radiobutton.icon.lg.size")};
    height: ${o("radiobutton.icon.lg.size")};
}
`,yb=({dt:o})=>`
.p-datepicker {
    display: inline-flex;
    max-width: 100%;
}

.p-datepicker-input {
    flex: 1 1 auto;
    width: 1%;
}

.p-datepicker:has(.p-datepicker-dropdown) .p-datepicker-input {
    border-start-end-radius: 0;
    border-end-end-radius: 0;
}

.p-datepicker-dropdown {
    cursor: pointer;
    display: inline-flex;
    user-select: none;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    width: ${o("datepicker.dropdown.width")};
    border-start-end-radius: ${o("datepicker.dropdown.border.radius")};
    border-end-end-radius: ${o("datepicker.dropdown.border.radius")};
    background: ${o("datepicker.dropdown.background")};
    border: 1px solid ${o("datepicker.dropdown.border.color")};
    border-inline-start: 0 none;
    color: ${o("datepicker.dropdown.color")};
    transition: background ${o("datepicker.transition.duration")}, color ${o("datepicker.transition.duration")}, border-color ${o("datepicker.transition.duration")}, outline-color ${o("datepicker.transition.duration")};
    outline-color: transparent;
}

.p-datepicker-dropdown:not(:disabled):hover {
    background: ${o("datepicker.dropdown.hover.background")};
    border-color: ${o("datepicker.dropdown.hover.border.color")};
    color: ${o("datepicker.dropdown.hover.color")};
}

.p-datepicker-dropdown:not(:disabled):active {
    background: ${o("datepicker.dropdown.active.background")};
    border-color: ${o("datepicker.dropdown.active.border.color")};
    color: ${o("datepicker.dropdown.active.color")};
}

.p-datepicker-dropdown:focus-visible {
    box-shadow: ${o("datepicker.dropdown.focus.ring.shadow")};
    outline: ${o("datepicker.dropdown.focus.ring.width")} ${o("datepicker.dropdown.focus.ring.style")} ${o("datepicker.dropdown.focus.ring.color")};
    outline-offset: ${o("datepicker.dropdown.focus.ring.offset")};
}

.p-datepicker:has(.p-datepicker-input-icon-container) {
    position: relative;
}

.p-datepicker:has(.p-datepicker-input-icon-container) .p-datepicker-input {
    padding-inline-end: calc((${o("form.field.padding.x")} * 2) + ${o("icon.size")});
}

.p-datepicker-input-icon-container {
    cursor: pointer;
    position: absolute;
    top: 50%;
    inset-inline-end: ${o("form.field.padding.x")};
    margin-block-start: calc(-1 * (${o("icon.size")} / 2));
    color: ${o("datepicker.input.icon.color")};
    line-height: 1;
}

.p-datepicker-fluid {
    display: flex;
}

.p-datepicker-fluid .p-datepicker-input {
    width: 1%;
}

.p-datepicker .p-datepicker-panel {
    min-width: 100%;
}

.p-datepicker-panel {
    width: auto;
    padding: ${o("datepicker.panel.padding")};
    background: ${o("datepicker.panel.background")};
    color: ${o("datepicker.panel.color")};
    border: 1px solid ${o("datepicker.panel.border.color")};
    border-radius: ${o("datepicker.panel.border.radius")};
    box-shadow: ${o("datepicker.panel.shadow")};
}

.p-datepicker-panel-inline {
    display: inline-block;
    overflow-x: auto;
    box-shadow: none;
}

.p-datepicker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: ${o("datepicker.header.padding")};
    background: ${o("datepicker.header.background")};
    color: ${o("datepicker.header.color")};
    border-block-end: 1px solid ${o("datepicker.header.border.color")};
}

.p-datepicker-next-button:dir(rtl) {
    order: -1;
}

.p-datepicker-prev-button:dir(rtl) {
    order: 1;
}

.p-datepicker-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: ${o("datepicker.title.gap")};
    font-weight: ${o("datepicker.title.font.weight")};
}

.p-datepicker-select-year,
.p-datepicker-select-month {
    border: none;
    background: transparent;
    margin: 0;
    cursor: pointer;
    font-weight: inherit;
    transition: background ${o("datepicker.transition.duration")}, color ${o("datepicker.transition.duration")}, border-color ${o("datepicker.transition.duration")}, outline-color ${o("datepicker.transition.duration")}, box-shadow ${o("datepicker.transition.duration")};
}

.p-datepicker-select-month {
    padding: ${o("datepicker.select.month.padding")};
    color: ${o("datepicker.select.month.color")};
    border-radius: ${o("datepicker.select.month.border.radius")};
}

.p-datepicker-select-year {
    padding: ${o("datepicker.select.year.padding")};
    color: ${o("datepicker.select.year.color")};
    border-radius: ${o("datepicker.select.year.border.radius")};
}

.p-datepicker-select-month:enabled:hover {
    background: ${o("datepicker.select.month.hover.background")};
    color: ${o("datepicker.select.month.hover.color")};
}

.p-datepicker-select-year:enabled:hover {
    background: ${o("datepicker.select.year.hover.background")};
    color: ${o("datepicker.select.year.hover.color")};
}

.p-datepicker-select-month:focus-visible,
.p-datepicker-select-year:focus-visible {
    box-shadow: ${o("datepicker.date.focus.ring.shadow")};
    outline: ${o("datepicker.date.focus.ring.width")} ${o("datepicker.date.focus.ring.style")} ${o("datepicker.date.focus.ring.color")};
    outline-offset: ${o("datepicker.date.focus.ring.offset")};
}

.p-datepicker-calendar-container {
    display: flex;
}

.p-datepicker-calendar-container .p-datepicker-calendar {
    flex: 1 1 auto;
    border-inline-start: 1px solid ${o("datepicker.group.border.color")};
    padding-inline-end: ${o("datepicker.group.gap")};
    padding-inline-start: ${o("datepicker.group.gap")};
}

.p-datepicker-calendar-container .p-datepicker-calendar:first-child {
    padding-inline-start: 0;
    border-inline-start: 0 none;
}

.p-datepicker-calendar-container .p-datepicker-calendar:last-child {
    padding-inline-end: 0;
}

.p-datepicker-day-view {
    width: 100%;
    border-collapse: collapse;
    font-size: 1rem;
    margin: ${o("datepicker.day.view.margin")};
}

.p-datepicker-weekday-cell {
    padding: ${o("datepicker.week.day.padding")};
}

.p-datepicker-weekday {
    font-weight: ${o("datepicker.week.day.font.weight")};
    color: ${o("datepicker.week.day.color")};
}

.p-datepicker-day-cell {
    padding: ${o("datepicker.date.padding")};
}

.p-datepicker-day {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
    width: ${o("datepicker.date.width")};
    height: ${o("datepicker.date.height")};
    border-radius: ${o("datepicker.date.border.radius")};
    transition: background ${o("datepicker.transition.duration")}, color ${o("datepicker.transition.duration")}, border-color ${o("datepicker.transition.duration")}, box-shadow ${o("datepicker.transition.duration")}, outline-color ${o("datepicker.transition.duration")};
    border: 1px solid transparent;
    outline-color: transparent;
    color: ${o("datepicker.date.color")};
}

.p-datepicker-day:not(.p-datepicker-day-selected):not(.p-disabled):hover {
    background: ${o("datepicker.date.hover.background")};
    color: ${o("datepicker.date.hover.color")};
}

.p-datepicker-day:focus-visible {
    box-shadow: ${o("datepicker.date.focus.ring.shadow")};
    outline: ${o("datepicker.date.focus.ring.width")} ${o("datepicker.date.focus.ring.style")} ${o("datepicker.date.focus.ring.color")};
    outline-offset: ${o("datepicker.date.focus.ring.offset")};
}

.p-datepicker-day-selected {
    background: ${o("datepicker.date.selected.background")};
    color: ${o("datepicker.date.selected.color")};
}

.p-datepicker-day-selected-range {
    background: ${o("datepicker.date.range.selected.background")};
    color: ${o("datepicker.date.range.selected.color")};
}

.p-datepicker-today > .p-datepicker-day {
    background: ${o("datepicker.today.background")};
    color: ${o("datepicker.today.color")};
}

.p-datepicker-today > .p-datepicker-day-selected {
    background: ${o("datepicker.date.selected.background")};
    color: ${o("datepicker.date.selected.color")};
}

.p-datepicker-today > .p-datepicker-day-selected-range {
    background: ${o("datepicker.date.range.selected.background")};
    color: ${o("datepicker.date.range.selected.color")};
}

.p-datepicker-weeknumber {
    text-align: center;
}

.p-datepicker-month-view {
    margin: ${o("datepicker.month.view.margin")};
}

.p-datepicker-month {
    width: 33.3%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    overflow: hidden;
    position: relative;
    padding: ${o("datepicker.month.padding")};
    transition: background ${o("datepicker.transition.duration")}, color ${o("datepicker.transition.duration")}, border-color ${o("datepicker.transition.duration")}, box-shadow ${o("datepicker.transition.duration")}, outline-color ${o("datepicker.transition.duration")};
    border-radius: ${o("datepicker.month.border.radius")};
    outline-color: transparent;
    color: ${o("datepicker.date.color")};
}

.p-datepicker-month:not(.p-disabled):not(.p-datepicker-month-selected):hover {
    color: ${o("datepicker.date.hover.color")};
    background: ${o("datepicker.date.hover.background")};
}

.p-datepicker-month-selected {
    color: ${o("datepicker.date.selected.color")};
    background: ${o("datepicker.date.selected.background")};
}

.p-datepicker-month:not(.p-disabled):focus-visible {
    box-shadow: ${o("datepicker.date.focus.ring.shadow")};
    outline: ${o("datepicker.date.focus.ring.width")} ${o("datepicker.date.focus.ring.style")} ${o("datepicker.date.focus.ring.color")};
    outline-offset: ${o("datepicker.date.focus.ring.offset")};
}

.p-datepicker-year-view {
    margin: ${o("datepicker.year.view.margin")};
}

.p-datepicker-year {
    width: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    overflow: hidden;
    position: relative;
    padding: ${o("datepicker.year.padding")};
    transition: background ${o("datepicker.transition.duration")}, color ${o("datepicker.transition.duration")}, border-color ${o("datepicker.transition.duration")}, box-shadow ${o("datepicker.transition.duration")}, outline-color ${o("datepicker.transition.duration")};
    border-radius: ${o("datepicker.year.border.radius")};
    outline-color: transparent;
    color: ${o("datepicker.date.color")};
}

.p-datepicker-year:not(.p-disabled):not(.p-datepicker-year-selected):hover {
    color: ${o("datepicker.date.hover.color")};
    background: ${o("datepicker.date.hover.background")};
}

.p-datepicker-year-selected {
    color: ${o("datepicker.date.selected.color")};
    background: ${o("datepicker.date.selected.background")};
}

.p-datepicker-year:not(.p-disabled):focus-visible {
    box-shadow: ${o("datepicker.date.focus.ring.shadow")};
    outline: ${o("datepicker.date.focus.ring.width")} ${o("datepicker.date.focus.ring.style")} ${o("datepicker.date.focus.ring.color")};
    outline-offset: ${o("datepicker.date.focus.ring.offset")};
}

.p-datepicker-buttonbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: ${o("datepicker.buttonbar.padding")};
    border-block-start: 1px solid ${o("datepicker.buttonbar.border.color")};
}

.p-datepicker-buttonbar .p-button {
    width: auto;
}

.p-datepicker-time-picker {
    display: flex;
    justify-content: center;
    align-items: center;
    border-block-start: 1px solid ${o("datepicker.time.picker.border.color")};
    padding: 0;
    gap: ${o("datepicker.time.picker.gap")};
}

.p-datepicker-calendar-container + .p-datepicker-time-picker {
    padding: ${o("datepicker.time.picker.padding")};
}

.p-datepicker-time-picker > div {
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: ${o("datepicker.time.picker.button.gap")};
}

.p-datepicker-time-picker span {
    font-size: 1rem;
}

.p-datepicker-timeonly .p-datepicker-time-picker {
    border-block-start: 0 none;
}

.p-datepicker:has(.p-inputtext-sm) .p-datepicker-dropdown {
    width: ${o("datepicker.dropdown.sm.width")};
}

.p-datepicker:has(.p-inputtext-sm) .p-datepicker-dropdown .p-icon,
.p-datepicker:has(.p-inputtext-sm) .p-datepicker-input-icon {
    font-size: ${o("form.field.sm.font.size")};
    width: ${o("form.field.sm.font.size")};
    height: ${o("form.field.sm.font.size")};
}

.p-datepicker:has(.p-inputtext-lg) .p-datepicker-dropdown {
    width: ${o("datepicker.dropdown.lg.width")};
}

.p-datepicker:has(.p-inputtext-lg) .p-datepicker-dropdown .p-icon,
.p-datepicker:has(.p-inputtext-lg) .p-datepicker-input-icon {
    font-size: ${o("form.field.lg.font.size")};
    width: ${o("form.field.lg.font.size")};
    height: ${o("form.field.lg.font.size")};
}
`,wb=({dt:o})=>`
.p-tabs {
    display: flex;
    flex-direction: column;
}

.p-tablist {
    display: flex;
    position: relative;
}

.p-tabs-scrollable > .p-tablist {
    overflow: hidden;
}

.p-tablist-viewport {
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    scrollbar-width: none;
    overscroll-behavior: contain auto;
}

.p-tablist-viewport::-webkit-scrollbar {
    display: none;
}

.p-tablist-tab-list {
    position: relative;
    display: flex;
    background: ${o("tabs.tablist.background")};
    border-style: solid;
    border-color: ${o("tabs.tablist.border.color")};
    border-width: ${o("tabs.tablist.border.width")};
}

.p-tablist-content {
    flex-grow: 1;
}

.p-tablist-nav-button {
    all: unset;
    position: absolute !important;
    flex-shrink: 0;
    inset-block-start: 0;
    z-index: 2;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: ${o("tabs.nav.button.background")};
    color: ${o("tabs.nav.button.color")};
    width: ${o("tabs.nav.button.width")};
    transition: color ${o("tabs.transition.duration")}, outline-color ${o("tabs.transition.duration")}, box-shadow ${o("tabs.transition.duration")};
    box-shadow: ${o("tabs.nav.button.shadow")};
    outline-color: transparent;
    cursor: pointer;
}

.p-tablist-nav-button:focus-visible {
    z-index: 1;
    box-shadow: ${o("tabs.nav.button.focus.ring.shadow")};
    outline: ${o("tabs.nav.button.focus.ring.width")} ${o("tabs.nav.button.focus.ring.style")} ${o("tabs.nav.button.focus.ring.color")};
    outline-offset: ${o("tabs.nav.button.focus.ring.offset")};
}

.p-tablist-nav-button:hover {
    color: ${o("tabs.nav.button.hover.color")};
}

.p-tablist-prev-button {
    inset-inline-start: 0;
}

.p-tablist-next-button {
    inset-inline-end: 0;
}

.p-tablist-prev-button:dir(rtl),
.p-tablist-next-button:dir(rtl) {
    transform: rotate(180deg);
}

.p-tab {
    flex-shrink: 0;
    cursor: pointer;
    user-select: none;
    position: relative;
    border-style: solid;
    white-space: nowrap;
    gap: ${o("tabs.tab.gap")};
    background: ${o("tabs.tab.background")};
    border-width: ${o("tabs.tab.border.width")};
    border-color: ${o("tabs.tab.border.color")};
    color: ${o("tabs.tab.color")};
    padding: ${o("tabs.tab.padding")};
    font-weight: ${o("tabs.tab.font.weight")};
    transition: background ${o("tabs.transition.duration")}, border-color ${o("tabs.transition.duration")}, color ${o("tabs.transition.duration")}, outline-color ${o("tabs.transition.duration")}, box-shadow ${o("tabs.transition.duration")};
    margin: ${o("tabs.tab.margin")};
    outline-color: transparent;
}

.p-tab:not(.p-disabled):focus-visible {
    z-index: 1;
    box-shadow: ${o("tabs.tab.focus.ring.shadow")};
    outline: ${o("tabs.tab.focus.ring.width")} ${o("tabs.tab.focus.ring.style")} ${o("tabs.tab.focus.ring.color")};
    outline-offset: ${o("tabs.tab.focus.ring.offset")};
}

.p-tab:not(.p-tab-active):not(.p-disabled):hover {
    background: ${o("tabs.tab.hover.background")};
    border-color: ${o("tabs.tab.hover.border.color")};
    color: ${o("tabs.tab.hover.color")};
}

.p-tab-active {
    background: ${o("tabs.tab.active.background")};
    border-color: ${o("tabs.tab.active.border.color")};
    color: ${o("tabs.tab.active.color")};
}

.p-tabpanels {
    background: ${o("tabs.tabpanel.background")};
    color: ${o("tabs.tabpanel.color")};
    padding: ${o("tabs.tabpanel.padding")};
    outline: 0 none;
}

.p-tabpanel:focus-visible {
    box-shadow: ${o("tabs.tabpanel.focus.ring.shadow")};
    outline: ${o("tabs.tabpanel.focus.ring.width")} ${o("tabs.tabpanel.focus.ring.style")} ${o("tabs.tabpanel.focus.ring.color")};
    outline-offset: ${o("tabs.tabpanel.focus.ring.offset")};
}

.p-tablist-active-bar {
    z-index: 1;
    display: block;
    position: absolute;
    inset-block-end: ${o("tabs.active.bar.bottom")};
    height: ${o("tabs.active.bar.height")};
    background: ${o("tabs.active.bar.background")};
    transition: 250ms cubic-bezier(0.35, 0, 0.25, 1);
}
`;export{Lu as $,lb as A,ab as B,tb as C,rb as D,dr as E,cb as F,db as G,ib as H,eb as I,nb as J,ob as K,Uu as L,qu as M,Qu as N,Ju as O,Zu as P,Ku as Q,Gu as R,Mu as S,Xu as T,Vu as U,Yu as V,Hu as W,ju as X,Pu as Y,Tu as Z,Nu as _,po as a,Ss as a$,Iu as a0,Ou as a1,Wu as a2,Au as a3,_u as a4,Eu as a5,Su as a6,Ru as a7,Fu as a8,zu as a9,ou as aA,Qs as aB,Js as aC,Us as aD,qs as aE,Zs as aF,Ms as aG,Gs as aH,Xs as aI,Ys as aJ,Vs as aK,Hs as aL,js as aM,Ps as aN,Ts as aO,Ns as aP,Ls as aQ,Ks as aR,Is as aS,Os as aT,Ws as aU,_s as aV,Ds as aW,Es as aX,Fs as aY,fr as aZ,_ as a_,Du as aa,Bu as ab,Cu as ac,wu as ad,yu as ae,xu as af,$u as ag,vu as ah,ku as ai,mu as aj,hu as ak,fu as al,gu as am,pu as an,bu as ao,uu as ap,su as aq,lu as ar,cu as as,du as at,iu as au,tu as av,eu as aw,nu as ax,au as ay,ru as az,Cs as b,lr as b0,ql as b1,bs as b2,Bo as b3,Gl as b4,Xl as b5,ur as b6,br as b7,zs as b8,os as b9,fs as bA,mb as bB,kb as bC,vb as bD,$b as bE,Ol as bF,xb as bG,xs as bH,gr as bI,Hl as bJ,cs as bK,ds as bL,is as bM,pr as bN,Ml as bO,Il as bP,vs as bQ,Tl as bR,Vl as bS,ss as bT,ps as bU,ms as bV,yb as bW,wb as bX,rs as ba,ws as bb,es as bc,sb as bd,ub as be,as as bf,hs as bg,us as bh,bb as bi,pb as bj,cr as bk,gb as bl,ys as bm,fb as bn,Ll as bo,Ro as bp,ns as bq,Ul as br,Kl as bs,Zl as bt,ls as bu,ts as bv,ks as bw,Nl as bx,hb as by,Ql as bz,$s as c,Rs as d,er as e,oo as f,D as g,g as h,Jl as i,vo as j,Pl as k,S as l,fo as m,gs as n,ar as o,$o as p,z as q,jl as r,zo as s,Yl as t,Bs as u,go as v,B as w,F as x,yo as y,As as z};
