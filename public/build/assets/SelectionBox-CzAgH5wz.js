import{v as T,b as k,r as j,j as w,c,o,l as h,K as m,S as b,a as u,R as f,k as p,a4 as d,P as C,F as S,U as E,M as F}from"./@vue-BnW70ngI.js";import{_ as D}from"./LoadingIcon-CesYxFkK.js";import{r as I,a as M,b as N}from"./@heroicons-BLousAGu.js";import{r as P}from"./@element-plus-CyTLADhX.js";import{_ as U}from"./TextInput-C52bsWxF.js";import{E as H,j as z,A as K,F as O,I as q}from"./@headlessui-gOb5_P77.js";const G={class:"flex items-stretch"},J=["innerHTML"],Q={class:"absolute inset-y-0 right-0 flex items-center pr-2"},W={key:0,class:"p-2 -mt-1"},X={class:"max-h-60 overflow-auto"},Y=["textContent"],Z=["onClick"],ee=["textContent"],te={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-sky-600"},ae={key:0,class:"flex flex-col ml-1.5"},ce={__name:"SelectionBox",props:{label:{type:String,default:""},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},showSelected:{type:Boolean,default:!0},placeholder:{type:String,default:""},modelValue:{default:"",validator(a){return a===null||typeof a=="string"||typeof a=="number"}},canRefresh:{type:Boolean,default:!0},canClear:{type:Boolean,default:!0},valueType:{type:String,default:"integer"},options:{type:Array,default:[]},enableSearch:{type:Boolean,default:!1},searchPlaceholder:{type:String,default:""}},emits:["update:modelValue","refresh","selected","cleared"],setup(a,{emit:B}){const l=a,y=B,t=T({loading:k(()=>l.loading),search:"",options:Array.isArray(l.options)?k(()=>{const e=[];return l.options.forEach(s=>{s.label.toLowerCase().includes(t.search.toLowerCase())&&e.push(s)}),e}):l.options,disabled:k(()=>l.loading||l.disabled)}),r=j({}),_=e=>l.valueType==="integer"?parseInt(e):e,x=e=>{r.value=t.options.find(s=>s.value===_(e))??{}};w(()=>l.modelValue,e=>{e?x(e):r.value={}},{immediate:!0}),w(()=>l.options,e=>{e?x(l.modelValue):r.value={}},{immediate:!0}),l.modelValue&&t.options.length&&x(l.modelValue);const A=()=>{if(t.disabled)return!1;y("refresh")},R=e=>{if(e.preventDefault(),t.disabled)return!1;r.value=null,y("update:modelValue",null),y("cleared")},$=e=>{t.search="",y("update:modelValue",e.value),y("selected",e)},L=(e,s)=>{const i=["relative cursor-default select-none py-2 pr-4"];return i.push(e?"bg-sky-100 text-sky-900":"text-gray-900"),i.push(l.showSelected?"pl-10":"pl-4"),s>0&&i.push("border-t border-dashed"),i};return(e,s)=>(o(),c("div",G,[h(d(q),{modelValue:r.value,"onUpdate:modelValue":s[1]||(s[1]=i=>r.value=i),disabled:a.disabled},{default:b(({open:i})=>[u("div",{class:f(["relative flex-auto",[a.canRefresh?"select-box-refresh":"w-full"]])},[a.label?(o(),p(d(H),{key:0,as:"label",textContent:C(a.label)},null,8,["textContent"])):m("",!0),h(d(z),{class:f(["listbox-button",[t.disabled?"bg-gray-200":"bg-white",i&&!a.enableSearch?"border-sky-400 ring ring-sky-200":""]])},{default:b(()=>{var n,g,v;return[u("span",{class:f(["block truncate",{"text-gray-500":!((n=r.value)!=null&&n.value)}]),innerHTML:((g=r.value)==null?void 0:g.label)??a.placeholder},null,10,J),u("span",Q,[t.loading?(o(),p(D,{key:0,class:"opacity-50",color:"#000000"})):(o(),c(S,{key:1},[(v=r.value)!=null&&v.value&&a.canClear?(o(),p(d(I),{key:0,class:f(["w-4 h-4 text-gray-400 hover:text-orange-700 hover:cursor-pointer",{"pointer-events-none":t.loading}]),"aria-hidden":"true",onClick:R},null,8,["class"])):(o(),p(d(M),{key:1,class:"h-5 w-5 text-gray-400","aria-hidden":"true"}))],64))])]}),_:2},1032,["class"]),h(E,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:b(()=>[t.options.length>0||t.search!==""?(o(),p(d(K),{key:0,class:"listbox-options"},{default:b(()=>[a.enableSearch?(o(),c("div",W,[h(U,{autofocus:"",class:"w-full",modelValue:t.search,"onUpdate:modelValue":s[0]||(s[0]=n=>t.search=n),placeholder:a.searchPlaceholder},null,8,["modelValue","placeholder"])])):m("",!0),u("div",X,[t.search!==""&&t.options.length===0?(o(),c("div",{key:0,class:"p-2 text-center",textContent:C(e.$t("emptyResult"))},null,8,Y)):m("",!0),(o(!0),c(S,null,F(t.options,(n,g)=>(o(),p(d(O),{key:n.value,value:n,as:"template"},{default:b(({active:v,selected:V})=>[u("li",{class:f(L(v,g)),onClick:le=>$(n)},[u("span",{class:f([V?"font-semibold":"font-normal","block"]),textContent:C(n.label)},null,10,ee),a.showSelected&&V?(o(),c("span",te,[h(d(N),{class:"h-5 w-5","aria-hidden":"true"})])):m("",!0)],10,Z)]),_:2},1032,["value"]))),128))])]),_:1})):m("",!0)]),_:1})],2)]),_:1},8,["modelValue","disabled"]),a.canRefresh?(o(),c("div",ae,[u("button",{class:f(["rounded-md border border-gray-300 p-2.5 focus:outline-none",{"bg-gray-200 cursor-default":t.disabled,"bg-white hover:bg-gray-100":!t.disabled}]),onClick:s[2]||(s[2]=()=>A())},[h(d(P),{class:"w-5 h-5","aria-hidden":"true"})],2)])):m("",!0)]))}};export{ce as _};
