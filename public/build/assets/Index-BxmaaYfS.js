import{u as K}from"./vue-i18n-kWKo0idO.js";import{T}from"./@inertiajs-Dt0-hqjZ.js";import{e as L,d as O}from"./@element-plus-CyTLADhX.js";import{_ as R}from"./AppLayout-_qQ0AdHn.js";import{_ as I}from"./Checkbox-BW6Lzxs4.js";import{_ as q}from"./ConfirmationModal-ClaGRyF5.js";import{_ as A}from"./DialogModal-LfgJQ09a.js";import{_ as P}from"./LoadingIcon-CLD0VpVl.js";import{_ as y}from"./PrimaryButton-DE9sqoJj.js";import{_ as h}from"./SecondaryButton-BoI1NwE9.js";import{_ as B}from"./InputLabel-BTXevqr4.js";import{_ as G}from"./TextInput-DUNPEFms.js";import{_ as H}from"./InputError-gQdwtcoE.js";import{i as J,r as _,c as u,o as i,l,S as r,a as e,P as n,F as b,M as S,k as w,K as v,a4 as a,R as F,O as x}from"./@vue-BnW70ngI.js";import"./@intlify-xvnhHnag.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";/* empty css                                                    */const Q=["textContent"],W={class:"max-w-7xl mx-auto p-6"},X={class:"bg-white rounded-md shadow overflow-x-auto"},Y={class:"w-full whitespace-nowrap"},Z={class:"text-left"},tt=["textContent"],et=["textContent"],st=["textContent"],ot=["textContent"],nt={class:"px-4 py-3 flex items-center justify-end"},lt={key:1},at=["textContent"],rt={class:"mt-0"},it={key:0,class:"mt-4"},mt={class:"grid grid-cols-2 gap-4 mt-4"},ut={class:"flex items-center"},dt=["textContent"],pt=["textContent"],ct={class:"grid grid-cols-2 gap-4"},ft={class:"flex items-center"},vt=["textContent"],kt=["textContent"],Ct=["textContent"],xt=["textContent"],$t=["textContent"],ie={__name:"Index",props:{tokens:Array,availablePermissions:Array,defaultPermissions:Array},setup(d){const N=J("$toast"),{t:V}=K(),m=T({name:"",permissions:d.defaultPermissions}),$=_(!1),k=_(!1),D=()=>{k.value=!0},U=()=>{m.post(route("api-tokens.store"),{preserveScroll:!0,onSuccess:()=>{$.value=!0,k.value=!1,m.reset()}})},c=_(null),p=T({permissions:[]}),j=t=>{p.permissions=t.abilities,c.value=t},M=()=>{p.put(route("api-tokens.update",c.value),{preserveScroll:!0,preserveState:!0,onSuccess:()=>{c.value=null,N.success(V("tokenPermissionUpdatedSuccessfully"))}})},g=T({}),f=_(null),z=t=>{f.value=t},E=()=>{g.delete(route("api-tokens.destroy",f.value),{preserveScroll:!0,preserveState:!0,onSuccess:()=>{f.value=null,N.success(V("tokenDeletedSuccessfully"))}})};return(t,s)=>(i(),u(b,null,[l(R,{title:t.$t("API-Tokens")},{header:r(()=>[e("h2",{class:"font-semibold text-xl text-gray-800 leading-tight mr-auto",textContent:n(t.$t("API-Tokens"))},null,8,Q),l(y,{class:"text-sm py-1",textContent:n(t.$t("addNew")),onClick:D},null,8,["textContent"])]),default:r(()=>[e("div",W,[e("div",X,[e("table",Y,[e("thead",null,[e("tr",Z,[e("th",{class:"pt-4 pb-3 px-4",textContent:n(t.$t("tokenName"))},null,8,tt),e("th",{class:"pt-4 pb-3 px-4 w-60",textContent:n(t.$t("lastUsed"))},null,8,et),s[11]||(s[11]=e("th",{class:"w-40"},null,-1))])]),e("tbody",null,[d.tokens.length>0?(i(!0),u(b,{key:0},S(d.tokens,o=>(i(),u("tr",{key:o.id,class:"border-t hover:bg-gray-100 focus-within:bg-gray-100"},[e("td",{class:"px-4 py-3 text-left",textContent:n(o.name)},null,8,st),e("td",{class:"px-4 py-3 text-left",textContent:n(o.last_used_ago)},null,8,ot),e("td",nt,[d.availablePermissions.length>0?(i(),w(a(L),{key:0,class:"mr-3 w-5 mt-1 text-sky-400 transition hover:text-sky-700 hover:cursor-pointer",onClick:C=>j(o)},null,8,["onClick"])):v("",!0),l(a(O),{class:"w-5 mt-1 text-red-400 transition hover:text-red-700 hover:cursor-pointer",onClick:C=>z(o)},null,8,["onClick"])])]))),128)):(i(),u("tr",lt,[e("td",{colspan:"3",class:"border-t px-4 py-5 text-center text-md",textContent:n(t.$t("emptyData"))},null,8,at)]))])])])])]),_:1},8,["title"]),l(A,{show:k.value,onClose:s[3]||(s[3]=o=>k.value=!1)},{title:r(()=>[x(n(t.$t("createToken")),1)]),content:r(()=>[e("div",rt,[l(B,{for:"name",value:t.$t("tokenName")},null,8,["value"]),l(G,{id:"name",class:"mt-1 block w-full",modelValue:a(m).name,"onUpdate:modelValue":s[0]||(s[0]=o=>a(m).name=o),type:"text",autofocus:""},null,8,["modelValue"]),l(H,{class:"mt-2",message:a(m).errors.name},null,8,["message"])]),d.availablePermissions.length>0?(i(),u("div",it,[l(B,{for:"permissions",value:t.$t("permissions")},null,8,["value"]),e("div",mt,[(i(!0),u(b,null,S(d.availablePermissions,o=>(i(),u("div",{key:o},[e("label",ut,[l(I,{checked:a(m).permissions,"onUpdate:checked":s[1]||(s[1]=C=>a(m).permissions=C),value:o},null,8,["checked","value"]),e("span",{class:"ms-2 text-gray-600",textContent:n(t.$t(o))},null,8,dt)])]))),128))])])):v("",!0)]),footer:r(()=>[l(h,{onClick:s[2]||(s[2]=o=>k.value=!1),textContent:n(t.$t("cancel"))},null,8,["textContent"]),l(y,{class:F(["ms-3",{"opacity-25":a(m).processing}]),disabled:a(m).processing,onClick:U},{default:r(()=>[a(m).processing?(i(),w(P,{key:0,class:"mr-2"})):v("",!0),e("span",{class:"text-sm",textContent:n(t.$t("create"))},null,8,pt)]),_:1},8,["class","disabled"])]),_:1},8,["show"]),l(A,{show:c.value!=null,onClose:s[6]||(s[6]=o=>c.value=null)},{title:r(()=>[x(n(t.$t("API-TokenPermissions")),1)]),content:r(()=>[e("div",ct,[(i(!0),u(b,null,S(d.availablePermissions,o=>(i(),u("div",{key:o},[e("label",ft,[l(I,{checked:a(p).permissions,"onUpdate:checked":s[4]||(s[4]=C=>a(p).permissions=C),value:o},null,8,["checked","value"]),e("span",{class:"ms-2 text-gray-600",textContent:n(t.$t(o))},null,8,vt)])]))),128))])]),footer:r(()=>[l(h,{onClick:s[5]||(s[5]=o=>c.value=null),textContent:n(t.$t("cancel"))},null,8,["textContent"]),l(y,{class:F(["ms-3",{"opacity-25":a(p).processing}]),disabled:a(p).processing,onClick:M},{default:r(()=>[a(p).processing?(i(),w(P,{key:0,class:"mr-2"})):v("",!0),e("span",{class:"text-sm",textContent:n(t.$t("update"))},null,8,kt)]),_:1},8,["class","disabled"])]),_:1},8,["show"]),l(q,{show:f.value!=null,onClose:s[8]||(s[8]=o=>f.value=null)},{title:r(()=>[x(n(t.$t("deleteToken")),1)]),content:r(()=>[x(n(t.$t("deleteTokenMessage")),1)]),footer:r(()=>[l(h,{onClick:s[7]||(s[7]=o=>f.value=null),textContent:n(t.$t("cancel"))},null,8,["textContent"]),l(y,{class:F(["ms-3",{"opacity-25":a(g).processing}]),disabled:a(g).processing,onClick:E},{default:r(()=>[a(g).processing?(i(),w(P,{key:0,class:"mr-2"})):v("",!0),e("span",{class:"text-sm",textContent:n(t.$t("delete"))},null,8,Ct)]),_:1},8,["class","disabled"])]),_:1},8,["show"]),l(A,{show:$.value,onClose:s[10]||(s[10]=o=>$.value=!1)},{title:r(()=>[x(n(t.$t("API-Tokens")),1)]),content:r(()=>[e("div",{class:"mt-0",textContent:n(t.$t("tokenCopyMessage"))},null,8,xt),t.$page.props.jetstream.flash.token?(i(),u("div",{key:0,class:"mt-4 bg-gray-100 px-4 py-3 rounded-md font-mono text-sm text-gray-500 break-all",textContent:n(t.$page.props.jetstream.flash.token)},null,8,$t)):v("",!0)]),footer:r(()=>[l(h,{onClick:s[9]||(s[9]=o=>$.value=!1),textContent:n(t.$t("close"))},null,8,["textContent"])]),_:1},8,["show"])],64))}};export{ie as default};
