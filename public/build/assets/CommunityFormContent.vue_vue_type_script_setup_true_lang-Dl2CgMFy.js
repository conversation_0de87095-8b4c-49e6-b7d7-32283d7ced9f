import{u as b}from"./vue-i18n-kWKo0idO.js";import{_}from"./ImageInput-BV1wAASf.js";import{_ as y}from"./TextAreaInput-DHjed6qD.js";import{_ as u}from"./InputError-gQdwtcoE.js";import{_ as V}from"./TextInput-DUNPEFms.js";import{T as v}from"./@inertiajs-Dt0-hqjZ.js";import{s as w}from"./ziggy-js-C7EU8ifa.js";import{d as x,i as C,j,c as S,o as $,a as m,l as r,P as c,a4 as s}from"./@vue-BnW70ngI.js";const k={class:"border-t border-b px-6 pt-4 pb-6 space-y-4"},B=["textContent"],F=["textContent"],T=x({__name:"CommunityFormContent",props:{community:Object,submit:Boolean},emits:["communitySaved","processing"],setup(p,{emit:d}){const n=d,i=p,e=v(i.community),{t:a}=b(),f=C("$toast"),g=async()=>{if(e.processing)return!1;n("processing",!0),e.errors={},e.transform(o=>(typeof o.image=="string"&&delete o.image,o)).post(w("community.store"),{preserveScroll:!0,preserveState:!0,onSuccess:o=>{o.props.jetstream.flash.message&&f.success(o.props.jetstream.flash.message),o.props.jetstream.flash.community&&n("communitySaved",o.props.jetstream.flash.community)},onFinish:()=>{n("processing",!1)}})};return j(()=>i.submit,o=>{o&&g()}),(o,t)=>($(),S("div",k,[m("div",null,[m("label",{textContent:c(s(a)("communityName")),class:"w-full"},null,8,B),r(V,{class:"block w-full mt-1",modelValue:s(e).name,"onUpdate:modelValue":t[0]||(t[0]=l=>s(e).name=l),disabled:s(e).processing,rows:"4"},null,8,["modelValue","disabled"]),r(u,{class:"w-full mt-1",message:s(e).errors.name},null,8,["message"])]),m("div",null,[m("label",{textContent:c(s(a)("communityDescription")),class:"w-full"},null,8,F),r(y,{class:"block w-full mt-1",modelValue:s(e).description,"onUpdate:modelValue":t[1]||(t[1]=l=>s(e).description=l),disabled:s(e).processing,rows:"4"},null,8,["modelValue","disabled"]),r(u,{class:"w-full mt-1",message:s(e).errors.description},null,8,["message"])]),m("div",null,[r(_,{class:"flex-1",label:s(a)("image"),error:s(e).errors.image,disabled:s(e).processing,modelValue:s(e).image,"onUpdate:modelValue":[t[2]||(t[2]=l=>s(e).image=l),t[3]||(t[3]=l=>s(e).image=l)]},null,8,["label","error","disabled","modelValue"])])]))}});export{T as _};
