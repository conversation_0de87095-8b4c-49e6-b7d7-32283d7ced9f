/* empty css                                                    */import{b as l,c as n,o as r,Y as s}from"./@vue-BnW70ngI.js";const a=16,i="#ffffff",m={__name:"LoadingIcon",props:{size:{type:Number,default:null},color:{type:String,default:null}},setup(t){const o=t,c=l(()=>{const e={};return o.size&&o.size!==a&&(e["--icon-size"]=`${o.size}px`),o.color&&o.color!==i&&(e["--background-color"]=`${o.color}`),e});return(e,u)=>(r(),n("i",{class:"loading-icon",style:s(c.value)},null,4))}};export{m as _};
