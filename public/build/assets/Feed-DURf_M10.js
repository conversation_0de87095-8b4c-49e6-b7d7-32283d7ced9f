import{f as y}from"./index-DHV2tfOS.js";import{_ as b}from"./AppLayout-m_I9gnvX.js";import{_ as x}from"./PrimaryButton-DE9sqoJj.js";import{_ as m}from"./LoadingIcon-CesYxFkK.js";import{v as $,k as r,o,S as i,a as e,c,K as d,R as k,P as n,F as w,M as N,a4 as T,l as _}from"./@vue-BnW70ngI.js";import"./moment-C5S46NFB.js";import"./@inertiajs-BhKdJayA.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BWHXZF7Q.js";const v={class:"flex-1 flex items-center"},M=["textContent"],P=["textContent"],B={class:"mx-auto py-6 px-6"},D={class:"w-full feed-table"},F={class:"text-left flex"},L=["textContent"],E=["textContent"],H=["innerHTML"],q=["textContent"],A=["textContent"],S=["textContent"],V={class:"flex border-t"},z=["textContent"],j=["textContent"],I=["innerHTML"],K=["textContent"],O=["textContent"],R=["textContent"],G={key:1,class:"flex border-t"},J=["textContent"],Q={key:0,class:"mt-6"},U=["textContent"],ht={__name:"Feed",props:{posts:Array,hasNextPage:Boolean,user:Object},setup(l){const u=l,t=$({refreshing:!1,loading:!1,hasNextPage:u.hasNextPage,posts:[],next:0});u.posts.forEach(s=>t.posts.push(s));const h=async()=>{if(t.loading)return!1;t.loading=!0,await window.axios.get(route("user.feed",{user:u.user.user_id}),{params:{next:t.next,axios:!0}}).then(s=>{t.hasNextPage=s.data.hasNextPage,s.data.posts.forEach(f=>t.posts.push(f))}).catch(s=>{}).finally(()=>{t.loading=!1,t.refreshing=!1})},p=async()=>{if(t.loading)return!1;t.posts=[],t.refreshing=!0,t.next=0,await h()},g=async()=>{if(t.loading)return!1;t.refreshing=!1,t.next=1,await h()};return(s,f)=>(o(),r(b,{title:s.$t("userFeed")},{header:i(()=>[e("div",v,[e("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:n(s.$t("userFeedExtra",{name:l.user.name.length?l.user.name:"N/A",id:l.user.user_id}))},null,8,M),_(x,{class:"normal-case",disabled:t.loading,onClick:p},{default:i(()=>[t.refreshing?(o(),r(m,{key:0,class:"mr-2"})):d("",!0),e("span",{class:"text-sm",textContent:n(s.$t("refresh"))},null,8,P)]),_:1},8,["disabled"])])]),default:i(()=>[e("div",B,[e("div",{class:k(["bg-white rounded-md shadow flex flex-col overflow-auto",{"grid-loading":t.loading}])},[t.loading?(o(),r(m,{key:0,class:"grid-loading-icon",size:24,color:"rgb(55 65 81)"})):d("",!0),e("table",D,[e("tbody",null,[e("tr",F,[e("th",{class:"number-column extra",textContent:n(s.$t("STT"))},null,8,L),e("th",{class:"number-column",textContent:n(s.$t("postID"))},null,8,E),e("th",{class:"title-flex-column",innerHTML:s.$t("postContent")},null,8,H),e("th",{class:"post-type-column",textContent:n(s.$t("postType"))},null,8,q),e("th",{class:"time-column",textContent:n(s.$t("postCreatedAt"))},null,8,A),e("th",{class:"post-type-column",textContent:n(s.$t("qaType"))},null,8,S)]),t.posts.length>0?(o(!0),c(w,{key:0},N(t.posts,(a,C)=>(o(),c("tr",V,[e("td",{class:"number-column extra",textContent:n(C+1)},null,8,z),e("td",{class:"number-column",textContent:n(a.post_id)},null,8,j),e("td",{class:"title-flex-column",innerHTML:a.content},null,8,I),e("td",{class:"post-type-column",textContent:n(a.tag)},null,8,K),e("td",{class:"time-column",textContent:n(T(y)(a.created_at))},null,8,O),e("td",{class:"post-type-column",textContent:n(s.$t("qaType."+a.qa_type))},null,8,R)]))),256)):(o(),c("tr",G,[e("td",{colspan:"6",textContent:n(s.$t(t.loading?"loading":"emptyData"))},null,8,J)]))])])],2),t.hasNextPage?(o(),c("div",Q,[_(x,{class:"normal-case mx-auto",disabled:t.loading,onClick:g},{default:i(()=>[t.loading&&!t.refreshing?(o(),r(m,{key:0,class:"mr-2"})):d("",!0),e("span",{class:"text-sm",textContent:n(s.$t("loadMore"))},null,8,U)]),_:1},8,["disabled"])])):d("",!0)])]),_:1},8,["title"]))}};export{ht as default};
