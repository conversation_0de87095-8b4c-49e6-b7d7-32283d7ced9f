import{r as u,c as r,o as a,l as o,a4 as t,S as d,a as m,F as i,O as n,_ as y,R as b,n as C}from"./@vue-BnW70ngI.js";import{T as h,Z as V}from"./@inertiajs-Dt0-hqjZ.js";import{A as w,a as T}from"./AuthenticationCardLogo-B-NI73cE.js";import{_}from"./InputError-gQdwtcoE.js";import{_ as v}from"./InputLabel-BTXevqr4.js";import{_ as I}from"./PrimaryButton-DE9sqoJj.js";import{_ as g}from"./TextInput-DUNPEFms.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const U={class:"mb-4 text-sm text-gray-600"},$={key:0},A={key:1},B={class:"flex items-center justify-end mt-4"},de={__name:"TwoFactorChallenge",setup(F){const c=u(!1),e=h({code:"",recovery_code:""}),p=u(null),f=u(null),k=async()=>{c.value^=!0,await C(),c.value?(p.value.focus(),e.code=""):(f.value.focus(),e.recovery_code="")},x=()=>{e.post(route("two-factor.login"))};return(N,s)=>(a(),r(i,null,[o(t(V),{title:"Two-factor Confirmation"}),o(T,null,{logo:d(()=>[o(w)]),default:d(()=>[m("div",U,[c.value?(a(),r(i,{key:1},[n(" Please confirm access to your account by entering one of your emergency recovery codes. ")],64)):(a(),r(i,{key:0},[n(" Please confirm access to your account by entering the authentication code provided by your authenticator application. ")],64))]),m("form",{onSubmit:y(x,["prevent"])},[c.value?(a(),r("div",A,[o(v,{for:"recovery_code",value:"Recovery Code"}),o(g,{id:"recovery_code",ref_key:"recoveryCodeInput",ref:p,modelValue:t(e).recovery_code,"onUpdate:modelValue":s[1]||(s[1]=l=>t(e).recovery_code=l),type:"text",class:"mt-1 block w-full",autocomplete:"one-time-code"},null,8,["modelValue"]),o(_,{class:"mt-2",message:t(e).errors.recovery_code},null,8,["message"])])):(a(),r("div",$,[o(v,{for:"code",value:"Code"}),o(g,{id:"code",ref_key:"codeInput",ref:f,modelValue:t(e).code,"onUpdate:modelValue":s[0]||(s[0]=l=>t(e).code=l),type:"text",inputmode:"numeric",class:"mt-1 block w-full",autofocus:"",autocomplete:"one-time-code"},null,8,["modelValue"]),o(_,{class:"mt-2",message:t(e).errors.code},null,8,["message"])])),m("div",B,[m("button",{type:"button",class:"text-sm text-gray-600 hover:text-gray-900 underline cursor-pointer",onClick:y(k,["prevent"])},[c.value?(a(),r(i,{key:1},[n(" Use an authentication code ")],64)):(a(),r(i,{key:0},[n(" Use a recovery code ")],64))]),o(I,{class:b(["ms-4",{"opacity-25":t(e).processing}]),disabled:t(e).processing},{default:d(()=>s[2]||(s[2]=[n(" Log in ")])),_:1},8,["class","disabled"])])],32)]),_:1})],64))}};export{de as default};
