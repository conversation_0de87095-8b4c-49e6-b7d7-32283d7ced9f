import{d as $t,b as rt}from"./call-bind-apply-helpers-B4ICrQ1R.js";const Mt={},Wt=Object.freeze(Object.defineProperty({__proto__:null,default:Mt},Symbol.toStringTag,{value:"Module"})),It=$t(Wt);var G=typeof Map=="function"&&Map.prototype,B=Object.getOwnPropertyDescriptor&&G?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,I=G&&B&&typeof B.get=="function"?B.get:null,nt=G&&Map.prototype.forEach,K=typeof Set=="function"&&Set.prototype,C=Object.getOwnPropertyDescriptor&&K?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,T=K&&C&&typeof C.get=="function"?C.get:null,at=K&&Set.prototype.forEach,Tt=typeof WeakMap=="function"&&WeakMap.prototype,w=Tt?WeakMap.prototype.has:null,qt=typeof WeakSet=="function"&&WeakSet.prototype,_=qt?WeakSet.prototype.has:null,Rt=typeof WeakRef=="function"&&WeakRef.prototype,it=Rt?WeakRef.prototype.deref:null,Lt=Boolean.prototype.valueOf,At=Object.prototype.toString,kt=Function.prototype.toString,Nt=String.prototype.match,Q=String.prototype.slice,y=String.prototype.replace,Dt=String.prototype.toUpperCase,ot=String.prototype.toLowerCase,vt=RegExp.prototype.test,lt=Array.prototype.concat,u=Array.prototype.join,Bt=Array.prototype.slice,ut=Math.floor,F=typeof BigInt=="function"?BigInt.prototype.valueOf:null,P=Object.getOwnPropertySymbols,H=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol.prototype.toString:null,m=typeof Symbol=="function"&&typeof Symbol.iterator=="object",E=typeof Symbol=="function"&&Symbol.toStringTag&&(typeof Symbol.toStringTag===m||!0)?Symbol.toStringTag:null,St=Object.prototype.propertyIsEnumerable,ft=(typeof Reflect=="function"?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(t){return t.__proto__}:null);function ct(t,e){if(t===1/0||t===-1/0||t!==t||t&&t>-1e3&&t<1e3||vt.call(/e/,e))return e;var n=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if(typeof t=="number"){var a=t<0?-ut(-t):ut(t);if(a!==t){var i=String(a),r=Q.call(e,i.length+1);return y.call(i,n,"$&_")+"."+y.call(y.call(r,/([0-9]{3})/g,"$&_"),/_$/,"")}}return y.call(e,n,"$&_")}var V=It,pt=V.custom,yt=dt(pt)?pt:null,ht={__proto__:null,double:'"',single:"'"},Ct={__proto__:null,double:/(["\\])/g,single:/(['\\])/g},ne=function t(e,n,a,i){var r=n||{};if(f(r,"quoteStyle")&&!f(ht,r.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(f(r,"maxStringLength")&&(typeof r.maxStringLength=="number"?r.maxStringLength<0&&r.maxStringLength!==1/0:r.maxStringLength!==null))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var c=f(r,"customInspect")?r.customInspect:!0;if(typeof c!="boolean"&&c!=="symbol")throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(f(r,"indent")&&r.indent!==null&&r.indent!=="	"&&!(parseInt(r.indent,10)===r.indent&&r.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(f(r,"numericSeparator")&&typeof r.numericSeparator!="boolean")throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var g=r.numericSeparator;if(typeof e>"u")return"undefined";if(e===null)return"null";if(typeof e=="boolean")return e?"true":"false";if(typeof e=="string")return wt(e,r);if(typeof e=="number"){if(e===0)return 1/0/e>0?"0":"-0";var o=String(e);return g?ct(e,o):o}if(typeof e=="bigint"){var p=String(e)+"n";return g?ct(e,p):p}var q=typeof r.depth>"u"?5:r.depth;if(typeof a>"u"&&(a=0),a>=q&&q>0&&typeof e=="object")return J(e)?"[Array]":"[Object]";var S=ee(r,a);if(typeof i>"u")i=[];else if(Ot(i,e)>=0)return"[Circular]";function l(h,M,Et){if(M&&(i=Bt.call(i),i.push(M)),Et){var et={depth:r.depth};return f(r,"quoteStyle")&&(et.quoteStyle=r.quoteStyle),t(h,et,a+1,i)}return t(h,r,a+1,i)}if(typeof e=="function"&&!st(e)){var X=Kt(e),Y=W(e,l);return"[Function"+(X?": "+X:" (anonymous)")+"]"+(Y.length>0?" { "+u.call(Y,", ")+" }":"")}if(dt(e)){var Z=m?y.call(String(e),/^(Symbol\(.*\))_[^)]*$/,"$1"):H.call(e);return typeof e=="object"&&!m?O(Z):Z}if(bt(e)){for(var d="<"+ot.call(String(e.nodeName)),R=e.attributes||[],$=0;$<R.length;$++)d+=" "+R[$].name+"="+mt(Pt(R[$].value),"double",r);return d+=">",e.childNodes&&e.childNodes.length&&(d+="..."),d+="</"+ot.call(String(e.nodeName))+">",d}if(J(e)){if(e.length===0)return"[]";var L=W(e,l);return S&&!te(L)?"["+U(L,S)+"]":"[ "+u.call(L,", ")+" ]"}if(Ft(e)){var A=W(e,l);return!("cause"in Error.prototype)&&"cause"in e&&!St.call(e,"cause")?"{ ["+String(e)+"] "+u.call(lt.call("[cause]: "+l(e.cause),A),", ")+" }":A.length===0?"["+String(e)+"]":"{ ["+String(e)+"] "+u.call(A,", ")+" }"}if(typeof e=="object"&&c){if(yt&&typeof e[yt]=="function"&&V)return V(e,{depth:q-a});if(c!=="symbol"&&typeof e.inspect=="function")return e.inspect()}if(Qt(e)){var x=[];return nt&&nt.call(e,function(h,M){x.push(l(M,e,!0)+" => "+l(h,e))}),gt("Map",I.call(e),x,S)}if(Zt(e)){var b=[];return at&&at.call(e,function(h){b.push(l(h,e))}),gt("Set",T.call(e),b,S)}if(Xt(e))return z("WeakMap");if(xt(e))return z("WeakSet");if(Yt(e))return z("WeakRef");if(Vt(e))return O(l(Number(e)));if(Ut(e))return O(l(F.call(e)));if(Jt(e))return O(Lt.call(e));if(Ht(e))return O(l(String(e)));if(typeof window<"u"&&e===window)return"{ [object Window] }";if(typeof globalThis<"u"&&e===globalThis||typeof rt<"u"&&e===rt)return"{ [object globalThis] }";if(!zt(e)&&!st(e)){var k=W(e,l),j=ft?ft(e)===Object.prototype:e instanceof Object||e.constructor===Object,N=e instanceof Object?"":"null prototype",tt=!j&&E&&Object(e)===e&&E in e?Q.call(s(e),8,-1):N?"Object":"",_t=j||typeof e.constructor!="function"?"":e.constructor.name?e.constructor.name+" ":"",D=_t+(tt||N?"["+u.call(lt.call([],tt||[],N||[]),": ")+"] ":"");return k.length===0?D+"{}":S?D+"{"+U(k,S)+"}":D+"{ "+u.call(k,", ")+" }"}return String(e)};function mt(t,e,n){var a=n.quoteStyle||e,i=ht[a];return i+t+i}function Pt(t){return y.call(String(t),/"/g,"&quot;")}function v(t){return!E||!(typeof t=="object"&&(E in t||typeof t[E]<"u"))}function J(t){return s(t)==="[object Array]"&&v(t)}function zt(t){return s(t)==="[object Date]"&&v(t)}function st(t){return s(t)==="[object RegExp]"&&v(t)}function Ft(t){return s(t)==="[object Error]"&&v(t)}function Ht(t){return s(t)==="[object String]"&&v(t)}function Vt(t){return s(t)==="[object Number]"&&v(t)}function Jt(t){return s(t)==="[object Boolean]"&&v(t)}function dt(t){if(m)return t&&typeof t=="object"&&t instanceof Symbol;if(typeof t=="symbol")return!0;if(!t||typeof t!="object"||!H)return!1;try{return H.call(t),!0}catch{}return!1}function Ut(t){if(!t||typeof t!="object"||!F)return!1;try{return F.call(t),!0}catch{}return!1}var Gt=Object.prototype.hasOwnProperty||function(t){return t in this};function f(t,e){return Gt.call(t,e)}function s(t){return At.call(t)}function Kt(t){if(t.name)return t.name;var e=Nt.call(kt.call(t),/^function\s*([\w$]+)/);return e?e[1]:null}function Ot(t,e){if(t.indexOf)return t.indexOf(e);for(var n=0,a=t.length;n<a;n++)if(t[n]===e)return n;return-1}function Qt(t){if(!I||!t||typeof t!="object")return!1;try{I.call(t);try{T.call(t)}catch{return!0}return t instanceof Map}catch{}return!1}function Xt(t){if(!w||!t||typeof t!="object")return!1;try{w.call(t,w);try{_.call(t,_)}catch{return!0}return t instanceof WeakMap}catch{}return!1}function Yt(t){if(!it||!t||typeof t!="object")return!1;try{return it.call(t),!0}catch{}return!1}function Zt(t){if(!T||!t||typeof t!="object")return!1;try{T.call(t);try{I.call(t)}catch{return!0}return t instanceof Set}catch{}return!1}function xt(t){if(!_||!t||typeof t!="object")return!1;try{_.call(t,_);try{w.call(t,w)}catch{return!0}return t instanceof WeakSet}catch{}return!1}function bt(t){return!t||typeof t!="object"?!1:typeof HTMLElement<"u"&&t instanceof HTMLElement?!0:typeof t.nodeName=="string"&&typeof t.getAttribute=="function"}function wt(t,e){if(t.length>e.maxStringLength){var n=t.length-e.maxStringLength,a="... "+n+" more character"+(n>1?"s":"");return wt(Q.call(t,0,e.maxStringLength),e)+a}var i=Ct[e.quoteStyle||"single"];i.lastIndex=0;var r=y.call(y.call(t,i,"\\$1"),/[\x00-\x1f]/g,jt);return mt(r,"single",e)}function jt(t){var e=t.charCodeAt(0),n={8:"b",9:"t",10:"n",12:"f",13:"r"}[e];return n?"\\"+n:"\\x"+(e<16?"0":"")+Dt.call(e.toString(16))}function O(t){return"Object("+t+")"}function z(t){return t+" { ? }"}function gt(t,e,n,a){var i=a?U(n,a):u.call(n,", ");return t+" ("+e+") {"+i+"}"}function te(t){for(var e=0;e<t.length;e++)if(Ot(t[e],`
`)>=0)return!1;return!0}function ee(t,e){var n;if(t.indent==="	")n="	";else if(typeof t.indent=="number"&&t.indent>0)n=u.call(Array(t.indent+1)," ");else return null;return{base:n,prev:u.call(Array(e+1),n)}}function U(t,e){if(t.length===0)return"";var n=`
`+e.prev+e.base;return n+u.call(t,","+n)+`
`+e.prev}function W(t,e){var n=J(t),a=[];if(n){a.length=t.length;for(var i=0;i<t.length;i++)a[i]=f(t,i)?e(t[i],t):""}var r=typeof P=="function"?P(t):[],c;if(m){c={};for(var g=0;g<r.length;g++)c["$"+r[g]]=r[g]}for(var o in t)f(t,o)&&(n&&String(Number(o))===o&&o<t.length||m&&c["$"+o]instanceof Symbol||(vt.call(/[^\w$]/,o)?a.push(e(o,t)+": "+e(t[o],t)):a.push(o+": "+e(t[o],t))));if(typeof P=="function")for(var p=0;p<r.length;p++)St.call(t,r[p])&&a.push("["+e(r[p])+"]: "+e(t[r[p]],t));return a}export{ne as o};
