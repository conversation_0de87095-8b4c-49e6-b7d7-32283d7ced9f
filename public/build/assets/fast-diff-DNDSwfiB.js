var S=-1,w=1,I=0;function P(r,a,n,v){if(r===a)return r?[[I,r]]:[];if(n!=null){var i=N(r,a,n);if(i)return i}var l=j(r,a),e=r.substring(0,l);r=r.substring(l),a=a.substring(l),l=z(r,a);var g=r.substring(r.length-l);r=r.substring(0,r.length-l),a=a.substring(0,a.length-l);var h=X(r,a);return e&&h.unshift([I,e]),g&&h.push([I,g]),H(h,v),h}function X(r,a){var n;if(!r)return[[w,a]];if(!a)return[[S,r]];var v=r.length>a.length?r:a,i=r.length>a.length?a:r,l=v.indexOf(i);if(l!==-1)return n=[[w,v.substring(0,l)],[I,i],[w,v.substring(l+i.length)]],r.length>a.length&&(n[0][0]=n[2][0]=S),n;if(i.length===1)return[[S,r],[w,a]];var e=Z(r,a);if(e){var g=e[0],h=e[1],u=e[2],t=e[3],c=e[4],o=P(g,u),F=P(h,t);return o.concat([[I,c]],F)}return Y(r,a)}function Y(r,a){for(var n=r.length,v=a.length,i=Math.ceil((n+v)/2),l=i,e=2*i,g=new Array(e),h=new Array(e),u=0;u<e;u++)g[u]=-1,h[u]=-1;g[l+1]=0,h[l+1]=0;for(var t=n-v,c=t%2!==0,o=0,F=0,m=0,E=0,D=0;D<i;D++){for(var b=-D+o;b<=D-F;b+=2){var s=l+b,_;b===-D||b!==D&&g[s-1]<g[s+1]?_=g[s+1]:_=g[s-1]+1;for(var p=_-b;_<n&&p<v&&r.charAt(_)===a.charAt(p);)_++,p++;if(g[s]=_,_>n)F+=2;else if(p>v)o+=2;else if(c){var M=l+t-b;if(M>=0&&M<e&&h[M]!==-1){var A=n-h[M];if(_>=A)return G(r,a,_,p)}}}for(var R=-D+m;R<=D-E;R+=2){var M=l+R,A;R===-D||R!==D&&h[M-1]<h[M+1]?A=h[M+1]:A=h[M-1]+1;for(var O=A-R;A<n&&O<v&&r.charAt(n-A-1)===a.charAt(v-O-1);)A++,O++;if(h[M]=A,A>n)E+=2;else if(O>v)m+=2;else if(!c){var s=l+t-R;if(s>=0&&s<e&&g[s]!==-1){var _=g[s],p=l+_-s;if(A=n-A,_>=A)return G(r,a,_,p)}}}}return[[S,r],[w,a]]}function G(r,a,n,v){var i=r.substring(0,n),l=a.substring(0,v),e=r.substring(n),g=a.substring(v),h=P(i,l),u=P(e,g);return h.concat(u)}function j(r,a){if(!r||!a||r.charAt(0)!==a.charAt(0))return 0;for(var n=0,v=Math.min(r.length,a.length),i=v,l=0;n<i;)r.substring(l,i)==a.substring(l,i)?(n=i,l=n):v=i,i=Math.floor((v-n)/2+n);return J(r.charCodeAt(i-1))&&i--,i}function z(r,a){if(!r||!a||r.slice(-1)!==a.slice(-1))return 0;for(var n=0,v=Math.min(r.length,a.length),i=v,l=0;n<i;)r.substring(r.length-i,r.length-l)==a.substring(a.length-i,a.length-l)?(n=i,l=n):v=i,i=Math.floor((v-n)/2+n);return K(r.charCodeAt(r.length-i))&&i--,i}function Z(r,a){var n=r.length>a.length?r:a,v=r.length>a.length?a:r;if(n.length<4||v.length*2<n.length)return null;function i(F,m,E){for(var D=F.substring(E,E+Math.floor(F.length/4)),b=-1,s="",_,p,M,A;(b=m.indexOf(D,b+1))!==-1;){var R=j(F.substring(E),m.substring(b)),O=z(F.substring(0,E),m.substring(0,b));s.length<O+R&&(s=m.substring(b-O,b)+m.substring(b,b+R),_=F.substring(0,E-O),p=F.substring(E+R),M=m.substring(0,b-O),A=m.substring(b+R))}return s.length*2>=F.length?[_,p,M,A,s]:null}var l=i(n,v,Math.ceil(n.length/4)),e=i(n,v,Math.ceil(n.length/2)),g;if(!l&&!e)return null;e?l?g=l[4].length>e[4].length?l:e:g=e:g=l;var h,u,t,c;r.length>a.length?(h=g[0],u=g[1],t=g[2],c=g[3]):(t=g[0],c=g[1],h=g[2],u=g[3]);var o=g[4];return[h,u,t,c,o]}function H(r,a){r.push([I,""]);for(var n=0,v=0,i=0,l="",e="",g;n<r.length;){if(n<r.length-1&&!r[n][1]){r.splice(n,1);continue}switch(r[n][0]){case w:i++,e+=r[n][1],n++;break;case S:v++,l+=r[n][1],n++;break;case I:var h=n-i-v-1;if(a){if(h>=0&&W(r[h][1])){var u=r[h][1].slice(-1);if(r[h][1]=r[h][1].slice(0,-1),l=u+l,e=u+e,!r[h][1]){r.splice(h,1),n--;var t=h-1;r[t]&&r[t][0]===w&&(i++,e=r[t][1]+e,t--),r[t]&&r[t][0]===S&&(v++,l=r[t][1]+l,t--),h=t}}if(V(r[n][1])){var u=r[n][1].charAt(0);r[n][1]=r[n][1].slice(1),l+=u,e+=u}}if(n<r.length-1&&!r[n][1]){r.splice(n,1);break}if(l.length>0||e.length>0){l.length>0&&e.length>0&&(g=j(e,l),g!==0&&(h>=0?r[h][1]+=e.substring(0,g):(r.splice(0,0,[I,e.substring(0,g)]),n++),e=e.substring(g),l=l.substring(g)),g=z(e,l),g!==0&&(r[n][1]=e.substring(e.length-g)+r[n][1],e=e.substring(0,e.length-g),l=l.substring(0,l.length-g)));var c=i+v;l.length===0&&e.length===0?(r.splice(n-c,c),n=n-c):l.length===0?(r.splice(n-c,c,[w,e]),n=n-c+1):e.length===0?(r.splice(n-c,c,[S,l]),n=n-c+1):(r.splice(n-c,c,[S,l],[w,e]),n=n-c+2)}n!==0&&r[n-1][0]===I?(r[n-1][1]+=r[n][1],r.splice(n,1)):n++,i=0,v=0,l="",e="";break}}r[r.length-1][1]===""&&r.pop();var o=!1;for(n=1;n<r.length-1;)r[n-1][0]===I&&r[n+1][0]===I&&(r[n][1].substring(r[n][1].length-r[n-1][1].length)===r[n-1][1]?(r[n][1]=r[n-1][1]+r[n][1].substring(0,r[n][1].length-r[n-1][1].length),r[n+1][1]=r[n-1][1]+r[n+1][1],r.splice(n-1,1),o=!0):r[n][1].substring(0,r[n+1][1].length)==r[n+1][1]&&(r[n-1][1]+=r[n+1][1],r[n][1]=r[n][1].substring(r[n+1][1].length)+r[n+1][1],r.splice(n+1,1),o=!0)),n++;o&&H(r,a)}function J(r){return r>=55296&&r<=56319}function K(r){return r>=56320&&r<=57343}function V(r){return K(r.charCodeAt(0))}function W(r){return J(r.charCodeAt(r.length-1))}function $(r){for(var a=[],n=0;n<r.length;n++)r[n][1].length>0&&a.push(r[n]);return a}function U(r,a,n,v){return W(r)||V(v)?null:$([[I,r],[S,a],[w,n],[I,v]])}function N(r,a,n){var v=typeof n=="number"?{index:n,length:0}:n.oldRange,i=typeof n=="number"?null:n.newRange,l=r.length,e=a.length;if(v.length===0&&(i===null||i.length===0)){var g=v.index,h=r.slice(0,g),u=r.slice(g),t=i?i.index:null;r:{var c=g+e-l;if(t!==null&&t!==c||c<0||c>e)break r;var o=a.slice(0,c),F=a.slice(c);if(F!==u)break r;var m=Math.min(g,c),E=h.slice(0,m),D=o.slice(0,m);if(E!==D)break r;var b=h.slice(m),s=o.slice(m);return U(E,b,s,u)}r:{if(t!==null&&t!==g)break r;var _=g,o=a.slice(0,_),F=a.slice(_);if(o!==h)break r;var p=Math.min(l-_,e-_),M=u.slice(u.length-p),A=F.slice(F.length-p);if(M!==A)break r;var b=u.slice(0,u.length-p),s=F.slice(0,F.length-p);return U(h,b,s,M)}}if(v.length>0&&i&&i.length===0)r:{var E=r.slice(0,v.index),M=r.slice(v.index+v.length),m=E.length,p=M.length;if(e<m+p)break r;var D=a.slice(0,m),A=a.slice(e-p);if(E!==D||M!==A)break r;var b=r.slice(m,l-p),s=a.slice(m,e-p);return U(E,b,s,M)}return null}function Q(r,a,n){return P(r,a,n,!0)}Q.INSERT=w;Q.DELETE=S;Q.EQUAL=I;var B=Q;export{B as d};
