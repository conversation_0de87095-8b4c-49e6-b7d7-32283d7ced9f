import{i as _}from"./@inertiajs-Dt0-hqjZ.js";import{_ as x}from"./AppLayout-CTb2MMqd.js";import{_ as v}from"./RedButton-D21iPtqa.js";import{_ as y}from"./LoadingIcon-CLD0VpVl.js";import{_ as h}from"./CommunityFormContent.vue_vue_type_script_setup_true_lang-Dl2CgMFy.js";import{r as l,k as p,o as c,S as m,a as e,l as r,P as s,a4 as C,K as g}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";import"./PrimaryButton-DE9sqoJj.js";/* empty css                                                    */import"./ImageInput-BV1wAASf.js";import"./InputError-gQdwtcoE.js";import"./TextAreaInput-DHjed6qD.js";import"./TextInput-DUNPEFms.js";import"./ziggy-js-C7EU8ifa.js";const b={class:"flex-1 flex items-center"},$=["textContent"],k=["textContent"],w={class:"max-w-7xl mx-auto py-6 px-6"},S={class:"bg-white rounded-md shadow flex flex-col overflow-auto community-form-container"},_t={__name:"Form",props:{community:Object,action:String},setup(a){const n=a,i=l(!1),o=l(!1),u=()=>{o.value||(i.value=!0)},f=()=>{o.value=!1},d=t=>{o.value=t,t||(i.value=!1)};return(t,B)=>(c(),p(x,{title:t.$t(n.action+"Community")},{header:m(()=>[e("div",b,[e("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:s(t.$t(n.action+"Community"))},null,8,$),r(C(_),{class:"primary-button text-sm mr-3",href:t.route("community.list"),textContent:s(t.$t("list"))},null,8,["href","textContent"]),r(v,{class:"normal-case",disabled:o.value,onClick:u},{default:m(()=>[o.value?(c(),p(y,{key:0,class:"mr-2"})):g("",!0),e("span",{class:"text-sm",textContent:s(t.$t("save"))},null,8,k)]),_:1},8,["disabled"])])]),default:m(()=>[e("div",w,[e("div",S,[r(h,{community:a.community,submit:i.value,action:"create",onCommunitySaved:f,onProcessing:d},null,8,["community","submit"])])])]),_:1},8,["title"]))}};export{_t as default};
