import{Q as $,T as b}from"./@inertiajs-Dt0-hqjZ.js";import{u as w}from"./vue-i18n-kWKo0idO.js";import{g as k}from"./index-k9QJQake.js";import{c as I}from"./@element-plus-CyTLADhX.js";import{_ as V}from"./AppLayout-CTb2MMqd.js";import{_ as j}from"./RedButton-D21iPtqa.js";import{_ as D}from"./LoadingIcon-CLD0VpVl.js";import{_ as B}from"./SurveySelectionBox-82zS6qsI.js";import{i as E,v as N,b as f,k as _,o as i,S as m,a as e,c as h,P as r,F as T,M as F,a4 as n,R as v,l as p,K as P}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@intlify-xvnhHnag.js";import"./moment-C5S46NFB.js";/* empty css                                                    *//* empty css                                                                     */import"./app-65VXU7yX.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./ziggy-js-C7EU8ifa.js";import"./pinia-Ddsh4R0D.js";import"./primevue-CrCPcMFN.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./izitoast-CYQMso0-.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";import"./PrimaryButton-DE9sqoJj.js";import"./SelectionBox-D4JR3fGi.js";import"./@heroicons-BLousAGu.js";import"./TextInput-DUNPEFms.js";import"./@headlessui-gOb5_P77.js";const z=["textContent"],K=["textContent"],L={class:"max-w-7xl mx-auto py-6 px-6"},M={class:"bg-white rounded-md shadow"},O={class:"w-full"},Q={class:"flex"},R=["textContent"],U=["textContent"],q=["textContent"],A=["textContent"],G=["textContent"],H=["textContent"],J={class:"flex items-center justify-end"},W={class:"flex border-t"},X=["textContent"],Y={class:"flex-1",colspan:"3"},oe={__name:"Sort",props:{formData:Object},setup(x){const{t:Z}=w(),y=x,u=E("$toast"),d=$(),s=b(y.formData),l=N({total:f(()=>s.surveys.length+1),currentSurveyId:"",clearSurvey:!1,exclude:f(()=>{const t=[];return s.surveys.forEach(o=>{t.push(o.survey_id)}),t})}),C=()=>{if(s.processing)return!1;s.errors={},s.transform(t=>{const o=[];return t.surveys.forEach(a=>{o.push(a.survey_id)}),{ids:o}}).post(route("surveySort"),{onSuccess:()=>{d.props.jetstream.flash.message&&u.success(d.props.jetstream.flash.message)},onError:t=>{t.ids&&u.error(t.ids)}})},S=t=>{s.surveys.splice(t,1)},g=t=>{s.surveys.push({survey_id:t.value,title:t.title}),setTimeout(()=>{l.currentSurveyId="",l.clearSurvey=!0},0)};return(t,o)=>(i(),_(V,{title:t.$t("surveySort")},{header:m(()=>[e("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:r(t.$t("surveySort"))},null,8,z),p(j,{class:v(["ml-auto",{"opacity-25":n(s).processing}]),disabled:n(s).processing,onClick:C},{default:m(()=>[n(s).processing?(i(),_(D,{key:0,class:"mr-2"})):P("",!0),e("span",{class:"text-sm",textContent:r(t.$t("save"))},null,8,K)]),_:1},8,["class","disabled"])]),default:m(()=>[e("div",L,[e("div",M,[e("table",O,[e("tr",Q,[e("th",{class:"w-32",textContent:r(t.$t("number"))},null,8,R),e("th",{class:"w-32",textContent:r(t.$t("surveyID"))},null,8,U),e("th",{class:"flex-1",textContent:r(t.$t("surveyTitle"))},null,8,q),o[2]||(o[2]=e("th",{class:"w-10"},null,-1))]),(i(!0),h(T,null,F(n(s).surveys,(a,c)=>(i(),h("tr",{class:v(["flex border-t",[c%2===0?"bg-blue-50":""]])},[e("td",{class:"w-32",textContent:r(c+1)},null,8,A),e("td",{class:"w-32",textContent:r(n(k)(a.survey_id,"S"))},null,8,G),e("td",{class:"flex-1",textContent:r(a.title)},null,8,H),e("td",J,[p(n(I),{class:"w-5 text-gray-300 transition ease-in-out duration-150 hover:cursor-pointer hover:text-red-500",onClick:tt=>S(c)},null,8,["onClick"])])],2))),256)),e("tr",W,[e("td",{class:"w-32",textContent:r(l.total)},null,8,X),e("td",Y,[p(B,{class:"w-[500px]",modelValue:l.currentSurveyId,"onUpdate:modelValue":o[0]||(o[0]=a=>l.currentSurveyId=a),placeholder:t.$t("selectSurvey"),"clear-data":l.clearSurvey,disabled:n(s).processing,"show-selected":!1,exclude:l.exclude,"enable-search":!0,"search-placeholder":t.$t("searchSurveyPlaceholder"),onSelected:g,onDataCleared:o[1]||(o[1]=a=>l.clearSurvey=!1)},null,8,["modelValue","placeholder","clear-data","disabled","exclude","search-placeholder"])])])])])])]),_:1},8,["title"]))}};export{oe as default};
