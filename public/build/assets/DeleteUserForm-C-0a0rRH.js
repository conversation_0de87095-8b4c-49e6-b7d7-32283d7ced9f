import{r as m,k as y,o as w,S as o,a as u,l as r,O as s,a4 as l,R as _,$ as k}from"./@vue-BnW70ngI.js";import{T as v}from"./@inertiajs-BhKdJayA.js";import{_ as x}from"./ActionSection-d716unDa.js";import{_ as p}from"./DangerButton-C49GvHso.js";import{_ as g}from"./DialogModal-CP7TKBlg.js";import{_ as C}from"./InputError-gQdwtcoE.js";import{_ as D}from"./SecondaryButton-BWHXZF7Q.js";import{_ as $}from"./TextInput-C52bsWxF.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const V={class:"mt-5"},b={class:"mt-4"},G={__name:"DeleteUserForm",setup(A){const a=m(!1),n=m(null),t=v({password:""}),c=()=>{a.value=!0,setTimeout(()=>n.value.focus(),250)},d=()=>{t.delete(route("current-user.destroy"),{preserveScroll:!0,onSuccess:()=>i(),onError:()=>n.value.focus(),onFinish:()=>t.reset()})},i=()=>{a.value=!1,t.reset()};return(U,e)=>(w(),y(x,null,{title:o(()=>e[1]||(e[1]=[s(" Delete Account ")])),description:o(()=>e[2]||(e[2]=[s(" Permanently delete your account. ")])),content:o(()=>[e[8]||(e[8]=u("div",{class:"max-w-xl text-sm text-gray-600"}," Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain. ",-1)),u("div",V,[r(p,{onClick:c},{default:o(()=>e[3]||(e[3]=[s(" Delete Account ")])),_:1})]),r(g,{show:a.value,onClose:i},{title:o(()=>e[4]||(e[4]=[s(" Delete Account ")])),content:o(()=>[e[5]||(e[5]=s(" Are you sure you want to delete your account? Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account. ")),u("div",b,[r($,{ref_key:"passwordInput",ref:n,modelValue:l(t).password,"onUpdate:modelValue":e[0]||(e[0]=f=>l(t).password=f),type:"password",class:"mt-1 block w-3/4",placeholder:"Password",autocomplete:"current-password",onKeyup:k(d,["enter"])},null,8,["modelValue"]),r(C,{message:l(t).errors.password,class:"mt-2"},null,8,["message"])])]),footer:o(()=>[r(D,{onClick:i},{default:o(()=>e[6]||(e[6]=[s(" Cancel ")])),_:1}),r(p,{class:_(["ms-3",{"opacity-25":l(t).processing}]),disabled:l(t).processing,onClick:d},{default:o(()=>e[7]||(e[7]=[s(" Delete Account ")])),_:1},8,["class","disabled"])]),_:1},8,["show"])]),_:1}))}};export{G as default};
