import{Q as M,T,i as _,N as V}from"./@inertiajs-BhKdJayA.js";import{s as L,a as c}from"./primevue-u0EmObz-.js";import{_ as j}from"./AppLayout-m_I9gnvX.js";import{_ as y}from"./InputLabel-BTXevqr4.js";import{_ as I}from"./SearchInput-CdoSYJL3.js";import{_ as N}from"./Pagination-DDsmbrzN.js";import{_ as P}from"./LoadingIcon-CesYxFkK.js";import{_ as D,a as O}from"./SecondaryButton-BWHXZF7Q.js";import{_ as H}from"./RedButton-D21iPtqa.js";import{_ as Q}from"./FixedSelectionBox-CwNS68U7.js";import{c as E}from"./index-DHV2tfOS.js";import{s as p}from"./ziggy-js-RmARJSO4.js";import{_ as F}from"./GridContainer-n7ZDMxOZ.js";import{i as R,v as U,b as z,c as A,o as f,l as s,k as g,K as b,S as n,a as o,a4 as a,R as K,O as G,P as m,F as J}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./@primeuix-CNwdBq9K.js";import"./@primevue-Bw51iWDD.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./PrimaryButton-DE9sqoJj.js";import"./SelectionBox-58uvzdoT.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-ccBf1-WH.js";import"./lodash-DBgjQQU6.js";import"./TextInput-C52bsWxF.js";import"./@headlessui-gOb5_P77.js";import"./moment-C5S46NFB.js";const W={class:"flex-1 flex items-center"},X=["textContent"],Y={class:"p-6 sm:mx-2"},Z={class:"flex items-stretch"},ee={class:"mt-0 w-80 mr-8"},te={class:"mt-0 w-64 mr-8"},se=["innerHTML"],le=["innerHTML"],ae=["src"],oe=["onClick"],re=["textContent"],ne=["textContent"],ie={class:"flex items-center justify-end px-3 py-3"},ue=["textContent"],Ue={__name:"Index",props:{filters:Object,quests:Object},setup(r){const C=R("$toast"),$=M(),h=r,i=T({search:h.filters.search??"",status:h.filters.status??"",limit:h.filters.limit??10}),e=U({showSearchClearButton:(h.filters.search??"").trim().length>0,searching:!1,showModal:!1,open:!1,toggleId:null,toggling:!1,selectOptions:[{value:"enable",label:"アクティブ"},{value:"disable",label:"削除"}],activePage:null,busy:z(()=>e.activePage!==null||e.searching||i.processing)}),d=()=>{if(i.processing)return!1;i.transform(t=>E(t)).get(p("quest.list"),{preserveScroll:!0,onSuccess:()=>{}})},x=()=>{e.showSearchClearButton=!0,d()},w=()=>{i.search="",e.showSearchClearButton=!1,d()},q=t=>{e.toggleId=t,e.showModal=!0,setTimeout(()=>e.open=!0,150)},v=async()=>{e.open=!1,setTimeout(()=>e.showModal=!1,150)},k=()=>{e.toggling||V.post(p("quest.toggle"),{id:e.toggleId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{e.toggling=!0},onSuccess:()=>{e.toggleId=null,$.props.jetstream.flash.message&&C.success($.props.jetstream.flash.message),v()},onFinish:()=>{e.toggling=!1}})},S=t=>{e.activePage=t,e.searching=!0},B=t=>{i.limit=t,i.search="",i.status="",d()};return(t,u)=>(f(),A(J,null,[s(j,{title:t.$t("questList")},{header:n(()=>[o("div",W,[o("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:m(t.$t("questList"))},null,8,X),s(a(_),{class:"primary-button text-sm",href:a(p)("quest.form",{quest:""}),textContent:m(t.$t("addNew"))},null,8,["href","textContent"])])]),default:n(()=>[o("div",Y,[o("div",Z,[o("div",ee,[s(y,{for:"search",value:t.$t("search")},null,8,["value"]),s(I,{id:"search",class:"block w-full",modelValue:a(i).search,"onUpdate:modelValue":u[0]||(u[0]=l=>a(i).search=l),placeholder:t.$t("questSearch"),disabled:e.busy,"show-clear-button":e.showSearchClearButton,onInput:u[1]||(u[1]=l=>e.showSearchClearButton=!1),onClearSearch:w,onEnter:x},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),o("div",te,[s(y,{for:"status-search",value:t.$t("status")},null,8,["value"]),s(Q,{modelValue:a(i).status,"onUpdate:modelValue":u[2]||(u[2]=l=>a(i).status=l),placeholder:t.$t("all"),disabled:e.busy,options:e.selectOptions,clearable:!0,onCleared:d,onSelected:d},null,8,["modelValue","placeholder","disabled","options"])])]),s(F,{loading:e.busy},{default:n(()=>[s(a(L),{value:r.quests.data},{empty:n(()=>[G(m(t.$t(r.filters.search&&r.filters.search!==""||r.filters.status&&r.filters.status!==""?"emptyResult":"emptyData")),1)]),default:n(()=>[s(a(c),{class:"number-column small",field:"id",header:t.$t("ID")},null,8,["header"]),s(a(c),{class:"title-flex-column",field:"title",header:t.$t("title")},null,8,["header"]),s(a(c),{class:"title-flex-column",header:t.$t("questDescription")},{body:n(({data:l})=>[o("div",{innerHTML:l.description.replace(/(\r\n|\n|\r)/g,"<br />")},null,8,se)]),_:1},8,["header"]),s(a(c),{class:"status-column",header:t.$t("questAmount")},{body:n(({data:l})=>[o("div",{innerHTML:l.amount_label},null,8,le)]),_:1},8,["header"]),s(a(c),{class:"status-column",header:t.$t("image")},{body:n(({data:l})=>[o("img",{class:"max-w-full max-h-full",src:l.image},null,8,ae)]),_:1},8,["header"]),s(a(c),{class:"status-column",field:"status_label",header:t.$t("status")},null,8,["header"]),s(a(c),{class:"action-column small"},{body:n(({data:l})=>[l.status!==0?(f(),g(a(_),{key:0,href:a(p)("quest.form",{quest:l.id})},{default:n(()=>u[3]||(u[3]=[o("i",{class:"pi pi-pen-to-square text-blue-400 hover:text-blue-600"},null,-1)])),_:2},1032,["href"])):b("",!0),o("i",{class:K(["pi hover:cursor-pointer",{"pi-eye text-sky-400 hover:text-sky-600":l.status,"pi-eye-slash text-red-400 hover:text-red-600":!l.status}]),onClick:ce=>q(l.id)},null,10,oe)]),_:1})]),_:1},8,["value"])]),_:1},8,["loading"]),r.quests.data.length>0?(f(),g(N,{key:0,class:"mt-5 flex items-center justify-center mx-auto",links:r.quests.links,active:e.activePage,disabled:e.busy,limit:r.quests.per_page,total:r.quests.total,from:r.quests.from,to:r.quests.to,onProgress:S,onChangeLimit:B},null,8,["links","active","disabled","limit","total","from","to"])):b("",!0)])]),_:1},8,["title"]),e.showModal?(f(),g(O,{key:0,show:e.open,onClose:v},{default:n(()=>[o("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:m(t.$t("confirm"))},null,8,re),o("div",{class:"border-t border-b p-4",textContent:m(t.$t("confirmToggleQuestMessage"))},null,8,ne),o("div",ie,[s(D,{class:"mr-3 text-sm",textContent:m(t.$t("cancel")),onClick:v},null,8,["textContent"]),s(H,{class:"text-sm overflow-hidden h-[34px]",onClick:k},{default:n(()=>[e.toggling?(f(),g(P,{key:0,class:"mr-1"})):b("",!0),o("span",{class:"text-sm text-white",textContent:m(t.$t("confirm"))},null,8,ue)]),_:1})])]),_:1},8,["show"])):b("",!0)],64))}};export{Ue as default};
