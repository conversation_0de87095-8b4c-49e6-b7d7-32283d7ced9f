/*!
  * shared v9.14.4
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */const Lr=typeof window<"u",Tr=(e,t=!1)=>t?Symbol.for(e):Symbol(e),Kt=(e,t,n)=>$t({l:e,k:t,s:n}),$t=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),$=e=>typeof e=="number"&&isFinite(e),Xt=e=>Ze(e)==="[object Date]",Fe=e=>Ze(e)==="[object RegExp]",ge=e=>U(e)&&Object.keys(e).length===0,oe=Object.assign,Gt=Object.create,W=(e=null)=>Gt(e);let ve;const he=()=>ve||(ve=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:W());function We(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const Vt=Object.prototype.hasOwnProperty;function se(e,t){return Vt.call(e,t)}const j=Array.isArray,v=e=>typeof e=="function",P=e=>typeof e=="string",X=e=>typeof e=="boolean",F=e=>e!==null&&typeof e=="object",Ht=e=>F(e)&&v(e.then)&&v(e.catch),qe=Object.prototype.toString,Ze=e=>qe.call(e),U=e=>{if(!F(e))return!1;const t=Object.getPrototypeOf(e);return t===null||t.constructor===Object},jt=e=>e==null?"":j(e)||U(e)&&e.toString===qe?JSON.stringify(e,null,2):String(e);function Bt(e,t=""){return e.reduce((n,s,c)=>c===0?n+s:n+t+s,"")}function ze(e){let t=e;return()=>++t}function xt(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const Ee=e=>!F(e)||j(e);function pr(e,t){if(Ee(e)||Ee(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:s,des:c}=n.pop();Object.keys(s).forEach(l=>{l!=="__proto__"&&(F(s[l])&&!F(c[l])&&(c[l]=Array.isArray(s[l])?[]:W()),Ee(c[l])||Ee(s[l])?c[l]=s[l]:n.push({src:s[l],des:c[l]}))})}}/*!
  * message-compiler v9.14.4
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function Jt(e,t,n){return{line:e,column:t,offset:n}}function Ne(e,t,n){return{start:e,end:t}}const Qt=/\{([0-9a-zA-Z]+)\}/g;function et(e,...t){return t.length===1&&qt(t[0])&&(t=t[0]),(!t||!t.hasOwnProperty)&&(t={}),e.replace(Qt,(n,s)=>t.hasOwnProperty(s)?t[s]:"")}const tt=Object.assign,Ye=e=>typeof e=="string",qt=e=>e!==null&&typeof e=="object";function nt(e,t=""){return e.reduce((n,s,c)=>c===0?n+s:n+t+s,"")}const Pe={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2},Zt={[Pe.USE_MODULO_SYNTAX]:"Use modulo before '{{0}}'."};function zt(e,t,...n){const s=et(Zt[e],...n||[]),c={message:String(s),code:e};return t&&(c.location=t),c}const I={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17},en={[I.EXPECTED_TOKEN]:"Expected token: '{0}'",[I.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[I.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[I.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[I.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[I.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[I.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[I.EMPTY_PLACEHOLDER]:"Empty placeholder",[I.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[I.INVALID_LINKED_FORMAT]:"Invalid linked format",[I.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[I.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[I.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[I.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[I.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[I.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function _e(e,t,n={}){const{domain:s,messages:c,args:l}=n,f=et((c||en)[e]||"",...l||[]),d=new SyntaxError(String(f));return d.code=e,t&&(d.location=t),d.domain=s,d}function tn(e){throw e}const Q=" ",nn="\r",K=`
`,rn="\u2028",sn="\u2029";function an(e){const t=e;let n=0,s=1,c=1,l=0;const f=N=>t[N]===nn&&t[N+1]===K,d=N=>t[N]===K,E=N=>t[N]===sn,L=N=>t[N]===rn,S=N=>f(N)||d(N)||E(N)||L(N),O=()=>n,T=()=>s,y=()=>c,b=()=>l,C=N=>f(N)||E(N)||L(N)?K:t[N],A=()=>C(n),g=()=>C(n+l);function D(){return l=0,S(n)&&(s++,c=0),f(n)&&n++,n++,c++,t[n]}function i(){return f(n+l)&&l++,l++,t[n+l]}function o(){n=0,s=1,c=1,l=0}function m(N=0){l=N}function _(){const N=n+l;for(;N!==n;)D();l=0}return{index:O,line:T,column:y,peekOffset:b,charAt:C,currentChar:A,currentPeek:g,next:D,peek:i,reset:o,resetPeek:m,skipToPeek:_}}const Z=void 0,cn=".",Ke="'",ln="tokenizer";function on(e,t={}){const n=t.location!==!1,s=an(e),c=()=>s.index(),l=()=>Jt(s.line(),s.column(),s.index()),f=l(),d=c(),E={currentType:14,offset:d,startLoc:f,endLoc:f,lastType:14,lastOffset:d,lastStartLoc:f,lastEndLoc:f,braceNest:0,inLinked:!1,text:""},L=()=>E,{onError:S}=t;function O(r,a,u,...p){const w=L();if(a.column+=u,a.offset+=u,S){const M=n?Ne(w.startLoc,a):null,q=_e(r,M,{domain:ln,args:p});S(q)}}function T(r,a,u){r.endLoc=l(),r.currentType=a;const p={type:a};return n&&(p.loc=Ne(r.startLoc,r.endLoc)),u!=null&&(p.value=u),p}const y=r=>T(r,14);function b(r,a){return r.currentChar()===a?(r.next(),a):(O(I.EXPECTED_TOKEN,l(),0,a),"")}function C(r){let a="";for(;r.currentPeek()===Q||r.currentPeek()===K;)a+=r.currentPeek(),r.peek();return a}function A(r){const a=C(r);return r.skipToPeek(),a}function g(r){if(r===Z)return!1;const a=r.charCodeAt(0);return a>=97&&a<=122||a>=65&&a<=90||a===95}function D(r){if(r===Z)return!1;const a=r.charCodeAt(0);return a>=48&&a<=57}function i(r,a){const{currentType:u}=a;if(u!==2)return!1;C(r);const p=g(r.currentPeek());return r.resetPeek(),p}function o(r,a){const{currentType:u}=a;if(u!==2)return!1;C(r);const p=r.currentPeek()==="-"?r.peek():r.currentPeek(),w=D(p);return r.resetPeek(),w}function m(r,a){const{currentType:u}=a;if(u!==2)return!1;C(r);const p=r.currentPeek()===Ke;return r.resetPeek(),p}function _(r,a){const{currentType:u}=a;if(u!==8)return!1;C(r);const p=r.currentPeek()===".";return r.resetPeek(),p}function N(r,a){const{currentType:u}=a;if(u!==9)return!1;C(r);const p=g(r.currentPeek());return r.resetPeek(),p}function h(r,a){const{currentType:u}=a;if(!(u===8||u===12))return!1;C(r);const p=r.currentPeek()===":";return r.resetPeek(),p}function R(r,a){const{currentType:u}=a;if(u!==10)return!1;const p=()=>{const M=r.currentPeek();return M==="{"?g(r.peek()):M==="@"||M==="%"||M==="|"||M===":"||M==="."||M===Q||!M?!1:M===K?(r.peek(),p()):k(r,!1)},w=p();return r.resetPeek(),w}function Y(r){C(r);const a=r.currentPeek()==="|";return r.resetPeek(),a}function J(r){const a=C(r),u=r.currentPeek()==="%"&&r.peek()==="{";return r.resetPeek(),{isModulo:u,hasSpace:a.length>0}}function k(r,a=!0){const u=(w=!1,M="",q=!1)=>{const te=r.currentPeek();return te==="{"?M==="%"?!1:w:te==="@"||!te?M==="%"?!0:w:te==="%"?(r.peek(),u(w,"%",!0)):te==="|"?M==="%"||q?!0:!(M===Q||M===K):te===Q?(r.peek(),u(!0,Q,q)):te===K?(r.peek(),u(!0,K,q)):!0},p=u();return a&&r.resetPeek(),p}function G(r,a){const u=r.currentChar();return u===Z?Z:a(u)?(r.next(),u):null}function Ot(r){const a=r.charCodeAt(0);return a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57||a===95||a===36}function It(r){return G(r,Ot)}function At(r){const a=r.charCodeAt(0);return a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57||a===95||a===36||a===45}function Ct(r){return G(r,At)}function St(r){const a=r.charCodeAt(0);return a>=48&&a<=57}function gt(r){return G(r,St)}function Pt(r){const a=r.charCodeAt(0);return a>=48&&a<=57||a>=65&&a<=70||a>=97&&a<=102}function yt(r){return G(r,Pt)}function Me(r){let a="",u="";for(;a=gt(r);)u+=a;return u}function bt(r){A(r);const a=r.currentChar();return a!=="%"&&O(I.EXPECTED_TOKEN,l(),0,a),r.next(),"%"}function Ue(r){let a="";for(;;){const u=r.currentChar();if(u==="{"||u==="}"||u==="@"||u==="|"||!u)break;if(u==="%")if(k(r))a+=u,r.next();else break;else if(u===Q||u===K)if(k(r))a+=u,r.next();else{if(Y(r))break;a+=u,r.next()}else a+=u,r.next()}return a}function Dt(r){A(r);let a="",u="";for(;a=Ct(r);)u+=a;return r.currentChar()===Z&&O(I.UNTERMINATED_CLOSING_BRACE,l(),0),u}function kt(r){A(r);let a="";return r.currentChar()==="-"?(r.next(),a+=`-${Me(r)}`):a+=Me(r),r.currentChar()===Z&&O(I.UNTERMINATED_CLOSING_BRACE,l(),0),a}function Rt(r){return r!==Ke&&r!==K}function Mt(r){A(r),b(r,"'");let a="",u="";for(;a=G(r,Rt);)a==="\\"?u+=Ut(r):u+=a;const p=r.currentChar();return p===K||p===Z?(O(I.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,l(),0),p===K&&(r.next(),b(r,"'")),u):(b(r,"'"),u)}function Ut(r){const a=r.currentChar();switch(a){case"\\":case"'":return r.next(),`\\${a}`;case"u":return we(r,a,4);case"U":return we(r,a,6);default:return O(I.UNKNOWN_ESCAPE_SEQUENCE,l(),0,a),""}}function we(r,a,u){b(r,a);let p="";for(let w=0;w<u;w++){const M=yt(r);if(!M){O(I.INVALID_UNICODE_ESCAPE_SEQUENCE,l(),0,`\\${a}${p}${r.currentChar()}`);break}p+=M}return`\\${a}${p}`}function wt(r){return r!=="{"&&r!=="}"&&r!==Q&&r!==K}function Ft(r){A(r);let a="",u="";for(;a=G(r,wt);)u+=a;return u}function vt(r){let a="",u="";for(;a=It(r);)u+=a;return u}function Wt(r){const a=u=>{const p=r.currentChar();return p==="{"||p==="%"||p==="@"||p==="|"||p==="("||p===")"||!p||p===Q?u:(u+=p,r.next(),a(u))};return a("")}function Le(r){A(r);const a=b(r,"|");return A(r),a}function Te(r,a){let u=null;switch(r.currentChar()){case"{":return a.braceNest>=1&&O(I.NOT_ALLOW_NEST_PLACEHOLDER,l(),0),r.next(),u=T(a,2,"{"),A(r),a.braceNest++,u;case"}":return a.braceNest>0&&a.currentType===2&&O(I.EMPTY_PLACEHOLDER,l(),0),r.next(),u=T(a,3,"}"),a.braceNest--,a.braceNest>0&&A(r),a.inLinked&&a.braceNest===0&&(a.inLinked=!1),u;case"@":return a.braceNest>0&&O(I.UNTERMINATED_CLOSING_BRACE,l(),0),u=de(r,a)||y(a),a.braceNest=0,u;default:{let w=!0,M=!0,q=!0;if(Y(r))return a.braceNest>0&&O(I.UNTERMINATED_CLOSING_BRACE,l(),0),u=T(a,1,Le(r)),a.braceNest=0,a.inLinked=!1,u;if(a.braceNest>0&&(a.currentType===5||a.currentType===6||a.currentType===7))return O(I.UNTERMINATED_CLOSING_BRACE,l(),0),a.braceNest=0,pe(r,a);if(w=i(r,a))return u=T(a,5,Dt(r)),A(r),u;if(M=o(r,a))return u=T(a,6,kt(r)),A(r),u;if(q=m(r,a))return u=T(a,7,Mt(r)),A(r),u;if(!w&&!M&&!q)return u=T(a,13,Ft(r)),O(I.INVALID_TOKEN_IN_PLACEHOLDER,l(),0,u.value),A(r),u;break}}return u}function de(r,a){const{currentType:u}=a;let p=null;const w=r.currentChar();switch((u===8||u===9||u===12||u===10)&&(w===K||w===Q)&&O(I.INVALID_LINKED_FORMAT,l(),0),w){case"@":return r.next(),p=T(a,8,"@"),a.inLinked=!0,p;case".":return A(r),r.next(),T(a,9,".");case":":return A(r),r.next(),T(a,10,":");default:return Y(r)?(p=T(a,1,Le(r)),a.braceNest=0,a.inLinked=!1,p):_(r,a)||h(r,a)?(A(r),de(r,a)):N(r,a)?(A(r),T(a,12,vt(r))):R(r,a)?(A(r),w==="{"?Te(r,a)||p:T(a,11,Wt(r))):(u===8&&O(I.INVALID_LINKED_FORMAT,l(),0),a.braceNest=0,a.inLinked=!1,pe(r,a))}}function pe(r,a){let u={type:14};if(a.braceNest>0)return Te(r,a)||y(a);if(a.inLinked)return de(r,a)||y(a);switch(r.currentChar()){case"{":return Te(r,a)||y(a);case"}":return O(I.UNBALANCED_CLOSING_BRACE,l(),0),r.next(),T(a,3,"}");case"@":return de(r,a)||y(a);default:{if(Y(r))return u=T(a,1,Le(r)),a.braceNest=0,a.inLinked=!1,u;const{isModulo:w,hasSpace:M}=J(r);if(w)return M?T(a,0,Ue(r)):T(a,4,bt(r));if(k(r))return T(a,0,Ue(r));break}}return u}function Yt(){const{currentType:r,offset:a,startLoc:u,endLoc:p}=E;return E.lastType=r,E.lastOffset=a,E.lastStartLoc=u,E.lastEndLoc=p,E.offset=c(),E.startLoc=l(),s.currentChar()===Z?T(E,14):pe(s,E)}return{nextToken:Yt,currentOffset:c,currentPosition:l,context:L}}const un="parser",fn=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function _n(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const s=parseInt(t||n,16);return s<=55295||s>=57344?String.fromCodePoint(s):"�"}}}function dn(e={}){const t=e.location!==!1,{onError:n,onWarn:s}=e;function c(i,o,m,_,...N){const h=i.currentPosition();if(h.offset+=_,h.column+=_,n){const R=t?Ne(m,h):null,Y=_e(o,R,{domain:un,args:N});n(Y)}}function l(i,o,m,_,...N){const h=i.currentPosition();if(h.offset+=_,h.column+=_,s){const R=t?Ne(m,h):null;s(zt(o,R,N))}}function f(i,o,m){const _={type:i};return t&&(_.start=o,_.end=o,_.loc={start:m,end:m}),_}function d(i,o,m,_){t&&(i.end=o,i.loc&&(i.loc.end=m))}function E(i,o){const m=i.context(),_=f(3,m.offset,m.startLoc);return _.value=o,d(_,i.currentOffset(),i.currentPosition()),_}function L(i,o){const m=i.context(),{lastOffset:_,lastStartLoc:N}=m,h=f(5,_,N);return h.index=parseInt(o,10),i.nextToken(),d(h,i.currentOffset(),i.currentPosition()),h}function S(i,o,m){const _=i.context(),{lastOffset:N,lastStartLoc:h}=_,R=f(4,N,h);return R.key=o,m===!0&&(R.modulo=!0),i.nextToken(),d(R,i.currentOffset(),i.currentPosition()),R}function O(i,o){const m=i.context(),{lastOffset:_,lastStartLoc:N}=m,h=f(9,_,N);return h.value=o.replace(fn,_n),i.nextToken(),d(h,i.currentOffset(),i.currentPosition()),h}function T(i){const o=i.nextToken(),m=i.context(),{lastOffset:_,lastStartLoc:N}=m,h=f(8,_,N);return o.type!==12?(c(i,I.UNEXPECTED_EMPTY_LINKED_MODIFIER,m.lastStartLoc,0),h.value="",d(h,_,N),{nextConsumeToken:o,node:h}):(o.value==null&&c(i,I.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,V(o)),h.value=o.value||"",d(h,i.currentOffset(),i.currentPosition()),{node:h})}function y(i,o){const m=i.context(),_=f(7,m.offset,m.startLoc);return _.value=o,d(_,i.currentOffset(),i.currentPosition()),_}function b(i){const o=i.context(),m=f(6,o.offset,o.startLoc);let _=i.nextToken();if(_.type===9){const N=T(i);m.modifier=N.node,_=N.nextConsumeToken||i.nextToken()}switch(_.type!==10&&c(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(_)),_=i.nextToken(),_.type===2&&(_=i.nextToken()),_.type){case 11:_.value==null&&c(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(_)),m.key=y(i,_.value||"");break;case 5:_.value==null&&c(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(_)),m.key=S(i,_.value||"");break;case 6:_.value==null&&c(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(_)),m.key=L(i,_.value||"");break;case 7:_.value==null&&c(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(_)),m.key=O(i,_.value||"");break;default:{c(i,I.UNEXPECTED_EMPTY_LINKED_KEY,o.lastStartLoc,0);const N=i.context(),h=f(7,N.offset,N.startLoc);return h.value="",d(h,N.offset,N.startLoc),m.key=h,d(m,N.offset,N.startLoc),{nextConsumeToken:_,node:m}}}return d(m,i.currentOffset(),i.currentPosition()),{node:m}}function C(i){const o=i.context(),m=o.currentType===1?i.currentOffset():o.offset,_=o.currentType===1?o.endLoc:o.startLoc,N=f(2,m,_);N.items=[];let h=null,R=null;do{const k=h||i.nextToken();switch(h=null,k.type){case 0:k.value==null&&c(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(k)),N.items.push(E(i,k.value||""));break;case 6:k.value==null&&c(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(k)),N.items.push(L(i,k.value||""));break;case 4:R=!0;break;case 5:k.value==null&&c(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(k)),N.items.push(S(i,k.value||"",!!R)),R&&(l(i,Pe.USE_MODULO_SYNTAX,o.lastStartLoc,0,V(k)),R=null);break;case 7:k.value==null&&c(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(k)),N.items.push(O(i,k.value||""));break;case 8:{const G=b(i);N.items.push(G.node),h=G.nextConsumeToken||null;break}}}while(o.currentType!==14&&o.currentType!==1);const Y=o.currentType===1?o.lastOffset:i.currentOffset(),J=o.currentType===1?o.lastEndLoc:i.currentPosition();return d(N,Y,J),N}function A(i,o,m,_){const N=i.context();let h=_.items.length===0;const R=f(1,o,m);R.cases=[],R.cases.push(_);do{const Y=C(i);h||(h=Y.items.length===0),R.cases.push(Y)}while(N.currentType!==14);return h&&c(i,I.MUST_HAVE_MESSAGES_IN_PLURAL,m,0),d(R,i.currentOffset(),i.currentPosition()),R}function g(i){const o=i.context(),{offset:m,startLoc:_}=o,N=C(i);return o.currentType===14?N:A(i,m,_,N)}function D(i){const o=on(i,tt({},e)),m=o.context(),_=f(0,m.offset,m.startLoc);return t&&_.loc&&(_.loc.source=i),_.body=g(o),e.onCacheKey&&(_.cacheKey=e.onCacheKey(i)),m.currentType!==14&&c(o,I.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,i[m.offset]||""),d(_,o.currentOffset(),o.currentPosition()),_}return{parse:D}}function V(e){if(e.type===14)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function En(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:l=>(n.helpers.add(l),l)}}function $e(e,t){for(let n=0;n<e.length;n++)ye(e[n],t)}function ye(e,t){switch(e.type){case 1:$e(e.cases,t),t.helper("plural");break;case 2:$e(e.items,t);break;case 6:{ye(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function mn(e,t={}){const n=En(e);n.helper("normalize"),e.body&&ye(e.body,n);const s=n.context();e.helpers=Array.from(s.helpers)}function Nn(e){const t=e.body;return t.type===2?Xe(t):t.cases.forEach(n=>Xe(n)),e}function Xe(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const s=e.items[n];if(!(s.type===3||s.type===9)||s.value==null)break;t.push(s.value)}if(t.length===e.items.length){e.static=nt(t);for(let n=0;n<e.items.length;n++){const s=e.items[n];(s.type===3||s.type===9)&&delete s.value}}}}const Ln="minifier";function ae(e){switch(e.t=e.type,e.type){case 0:{const t=e;ae(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let s=0;s<n.length;s++)ae(n[s]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let s=0;s<n.length;s++)ae(n[s]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;ae(t.key),t.k=t.key,delete t.key,t.modifier&&(ae(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}default:throw _e(I.UNHANDLED_MINIFIER_NODE_TYPE,null,{domain:Ln,args:[e.type]})}delete e.type}const Tn="parser";function pn(e,t){const{filename:n,breakLineCode:s,needIndent:c}=t,l=t.location!==!1,f={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:s,needIndent:c,indentLevel:0};l&&e.loc&&(f.source=e.loc.source);const d=()=>f;function E(C,A){f.code+=C}function L(C,A=!0){const g=A?s:"";E(c?g+"  ".repeat(C):g)}function S(C=!0){const A=++f.indentLevel;C&&L(A)}function O(C=!0){const A=--f.indentLevel;C&&L(A)}function T(){L(f.indentLevel)}return{context:d,push:E,indent:S,deindent:O,newline:T,helper:C=>`_${C}`,needIndent:()=>f.needIndent}}function hn(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),le(e,t.key),t.modifier?(e.push(", "),le(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function On(e,t){const{helper:n,needIndent:s}=e;e.push(`${n("normalize")}([`),e.indent(s());const c=t.items.length;for(let l=0;l<c&&(le(e,t.items[l]),l!==c-1);l++)e.push(", ");e.deindent(s()),e.push("])")}function In(e,t){const{helper:n,needIndent:s}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(s());const c=t.cases.length;for(let l=0;l<c&&(le(e,t.cases[l]),l!==c-1);l++)e.push(", ");e.deindent(s()),e.push("])")}}function An(e,t){t.body?le(e,t.body):e.push("null")}function le(e,t){const{helper:n}=e;switch(t.type){case 0:An(e,t);break;case 1:In(e,t);break;case 2:On(e,t);break;case 6:hn(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break;default:throw _e(I.UNHANDLED_CODEGEN_NODE_TYPE,null,{domain:Tn,args:[t.type]})}}const Cn=(e,t={})=>{const n=Ye(t.mode)?t.mode:"normal",s=Ye(t.filename)?t.filename:"message.intl";t.sourceMap;const c=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":`
`,l=t.needIndent?t.needIndent:n!=="arrow",f=e.helpers||[],d=pn(e,{filename:s,breakLineCode:c,needIndent:l});d.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),d.indent(l),f.length>0&&(d.push(`const { ${nt(f.map(S=>`${S}: _${S}`),", ")} } = ctx`),d.newline()),d.push("return "),le(d,e),d.deindent(l),d.push("}"),delete e.helpers;const{code:E,map:L}=d.context();return{ast:e,code:E,map:L?L.toJSON():void 0}};function Sn(e,t={}){const n=tt({},t),s=!!n.jit,c=!!n.minify,l=n.optimize==null?!0:n.optimize,d=dn(n).parse(e);return s?(l&&Nn(d),c&&ae(d),{ast:d,code:""}):(mn(d,n),Cn(d,n))}/*!
  * core-base v9.14.4
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function gn(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(he().__INTLIFY_PROD_DEVTOOLS__=!1),typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(he().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(he().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}function ie(e){return F(e)&&be(e)===0&&(se(e,"b")||se(e,"body"))}const rt=["b","body"];function Pn(e){return z(e,rt)}const st=["c","cases"];function yn(e){return z(e,st,[])}const at=["s","static"];function bn(e){return z(e,at)}const ct=["i","items"];function Dn(e){return z(e,ct,[])}const lt=["t","type"];function be(e){return z(e,lt)}const it=["v","value"];function me(e,t){const n=z(e,it);if(n!=null)return n;throw ue(t)}const ot=["m","modifier"];function kn(e){return z(e,ot)}const ut=["k","key"];function Rn(e){const t=z(e,ut);if(t)return t;throw ue(6)}function z(e,t,n){for(let s=0;s<t.length;s++){const c=t[s];if(se(e,c)&&e[c]!=null)return e[c]}return n}const Mn=[...rt,...st,...at,...ct,...ut,...ot,...it,...lt];function ue(e){return new Error(`unhandled node type: ${e}`)}const ee=[];ee[0]={w:[0],i:[3,0],"[":[4],o:[7]};ee[1]={w:[1],".":[2],"[":[4],o:[7]};ee[2]={w:[2],i:[3,0],0:[3,0]};ee[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};ee[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};ee[5]={"'":[4,0],o:8,l:[5,0]};ee[6]={'"':[4,0],o:8,l:[6,0]};const Un=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function wn(e){return Un.test(e)}function Fn(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function vn(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function Wn(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:wn(t)?Fn(t):"*"+t}function Yn(e){const t=[];let n=-1,s=0,c=0,l,f,d,E,L,S,O;const T=[];T[0]=()=>{f===void 0?f=d:f+=d},T[1]=()=>{f!==void 0&&(t.push(f),f=void 0)},T[2]=()=>{T[0](),c++},T[3]=()=>{if(c>0)c--,s=4,T[0]();else{if(c=0,f===void 0||(f=Wn(f),f===!1))return!1;T[1]()}};function y(){const b=e[n+1];if(s===5&&b==="'"||s===6&&b==='"')return n++,d="\\"+b,T[0](),!0}for(;s!==null;)if(n++,l=e[n],!(l==="\\"&&y())){if(E=vn(l),O=ee[s],L=O[E]||O.l||8,L===8||(s=L[0],L[1]!==void 0&&(S=T[L[1]],S&&(d=l,S()===!1))))return;if(s===7)return t}}const Ge=new Map;function Kn(e,t){return F(e)?e[t]:null}function hr(e,t){if(!F(e))return null;let n=Ge.get(t);if(n||(n=Yn(t),n&&Ge.set(t,n)),!n)return null;const s=n.length;let c=e,l=0;for(;l<s;){const f=n[l];if(Mn.includes(f)&&ie(c))return null;const d=c[f];if(d===void 0||v(c))return null;c=d,l++}return c}const $n=e=>e,Xn=e=>"",Gn="text",Vn=e=>e.length===0?"":Bt(e),Hn=jt;function Ve(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function jn(e){const t=$(e.pluralIndex)?e.pluralIndex:-1;return e.named&&($(e.named.count)||$(e.named.n))?$(e.named.count)?e.named.count:$(e.named.n)?e.named.n:t:t}function Bn(e,t){t.count||(t.count=e),t.n||(t.n=e)}function xn(e={}){const t=e.locale,n=jn(e),s=F(e.pluralRules)&&P(t)&&v(e.pluralRules[t])?e.pluralRules[t]:Ve,c=F(e.pluralRules)&&P(t)&&v(e.pluralRules[t])?Ve:void 0,l=g=>g[s(n,g.length,c)],f=e.list||[],d=g=>f[g],E=e.named||W();$(e.pluralIndex)&&Bn(n,E);const L=g=>E[g];function S(g){const D=v(e.messages)?e.messages(g):F(e.messages)?e.messages[g]:!1;return D||(e.parent?e.parent.message(g):Xn)}const O=g=>e.modifiers?e.modifiers[g]:$n,T=U(e.processor)&&v(e.processor.normalize)?e.processor.normalize:Vn,y=U(e.processor)&&v(e.processor.interpolate)?e.processor.interpolate:Hn,b=U(e.processor)&&P(e.processor.type)?e.processor.type:Gn,A={list:d,named:L,plural:l,linked:(g,...D)=>{const[i,o]=D;let m="text",_="";D.length===1?F(i)?(_=i.modifier||_,m=i.type||m):P(i)&&(_=i||_):D.length===2&&(P(i)&&(_=i||_),P(o)&&(m=o||m));const N=S(g)(A),h=m==="vnode"&&j(N)&&_?N[0]:N;return _?O(_)(h,m):h},message:S,type:b,interpolate:y,normalize:T,values:oe(W(),f,E)};return A}let fe=null;function Or(e){fe=e}function Jn(e,t,n){fe&&fe.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}const Qn=qn("function:translate");function qn(e){return t=>fe&&fe.emit(e,t)}const Zn=Pe.__EXTEND_POINT__,ne=ze(Zn),Ir={FALLBACK_TO_TRANSLATE:ne(),CANNOT_FORMAT_NUMBER:ne(),FALLBACK_TO_NUMBER_FORMAT:ne(),CANNOT_FORMAT_DATE:ne(),FALLBACK_TO_DATE_FORMAT:ne(),EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:ne(),__EXTEND_POINT__:ne()},ft=I.__EXTEND_POINT__,re=ze(ft),B={INVALID_ARGUMENT:ft,INVALID_DATE_ARGUMENT:re(),INVALID_ISO_DATE_ARGUMENT:re(),NOT_SUPPORT_NON_STRING_MESSAGE:re(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:re(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:re(),NOT_SUPPORT_LOCALE_TYPE:re(),__EXTEND_POINT__:re()};function x(e){return _e(e,null,void 0)}function De(e,t){return t.locale!=null?He(t.locale):He(e.locale)}let Oe;function He(e){if(P(e))return e;if(v(e)){if(e.resolvedOnce&&Oe!=null)return Oe;if(e.constructor.name==="Function"){const t=e();if(Ht(t))throw x(B.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return Oe=t}else throw x(B.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw x(B.NOT_SUPPORT_LOCALE_TYPE)}function zn(e,t,n){return[...new Set([n,...j(t)?t:F(t)?Object.keys(t):P(t)?[t]:[n]])]}function Ar(e,t,n){const s=P(n)?n:Ce,c=e;c.__localeChainCache||(c.__localeChainCache=new Map);let l=c.__localeChainCache.get(s);if(!l){l=[];let f=[n];for(;j(f);)f=je(l,f,t);const d=j(t)||!U(t)?t:t.default?t.default:null;f=P(d)?[d]:d,j(f)&&je(l,f,!1),c.__localeChainCache.set(s,l)}return l}function je(e,t,n){let s=!0;for(let c=0;c<t.length&&X(s);c++){const l=t[c];P(l)&&(s=er(e,t[c],n))}return s}function er(e,t,n){let s;const c=t.split("-");do{const l=c.join("-");s=tr(e,l,n),c.splice(-1,1)}while(c.length&&s===!0);return s}function tr(e,t,n){let s=!1;if(!e.includes(t)&&(s=!0,t)){s=t[t.length-1]!=="!";const c=t.replace(/!/g,"");e.push(c),(j(n)||U(n))&&n[c]&&(s=n[c])}return s}const nr="9.14.4",ke=-1,Ce="en-US",Cr="",Be=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function rr(){return{upper:(e,t)=>t==="text"&&P(e)?e.toUpperCase():t==="vnode"&&F(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&P(e)?e.toLowerCase():t==="vnode"&&F(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&P(e)?Be(e):t==="vnode"&&F(e)&&"__v_isVNode"in e?Be(e.children):e}}let _t;function Sr(e){_t=e}let dt;function gr(e){dt=e}let Et;function Pr(e){Et=e}let mt=null;const yr=e=>{mt=e},sr=()=>mt;let Nt=null;const br=e=>{Nt=e},Dr=()=>Nt;let xe=0;function kr(e={}){const t=v(e.onWarn)?e.onWarn:xt,n=P(e.version)?e.version:nr,s=P(e.locale)||v(e.locale)?e.locale:Ce,c=v(s)?Ce:s,l=j(e.fallbackLocale)||U(e.fallbackLocale)||P(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:c,f=U(e.messages)?e.messages:Ie(c),d=U(e.datetimeFormats)?e.datetimeFormats:Ie(c),E=U(e.numberFormats)?e.numberFormats:Ie(c),L=oe(W(),e.modifiers,rr()),S=e.pluralRules||W(),O=v(e.missing)?e.missing:null,T=X(e.missingWarn)||Fe(e.missingWarn)?e.missingWarn:!0,y=X(e.fallbackWarn)||Fe(e.fallbackWarn)?e.fallbackWarn:!0,b=!!e.fallbackFormat,C=!!e.unresolving,A=v(e.postTranslation)?e.postTranslation:null,g=U(e.processor)?e.processor:null,D=X(e.warnHtmlMessage)?e.warnHtmlMessage:!0,i=!!e.escapeParameter,o=v(e.messageCompiler)?e.messageCompiler:_t,m=v(e.messageResolver)?e.messageResolver:dt||Kn,_=v(e.localeFallbacker)?e.localeFallbacker:Et||zn,N=F(e.fallbackContext)?e.fallbackContext:void 0,h=e,R=F(h.__datetimeFormatters)?h.__datetimeFormatters:new Map,Y=F(h.__numberFormatters)?h.__numberFormatters:new Map,J=F(h.__meta)?h.__meta:{};xe++;const k={version:n,cid:xe,locale:s,fallbackLocale:l,messages:f,modifiers:L,pluralRules:S,missing:O,missingWarn:T,fallbackWarn:y,fallbackFormat:b,unresolving:C,postTranslation:A,processor:g,warnHtmlMessage:D,escapeParameter:i,messageCompiler:o,messageResolver:m,localeFallbacker:_,fallbackContext:N,onWarn:t,__meta:J};return k.datetimeFormats=d,k.numberFormats=E,k.__datetimeFormatters=R,k.__numberFormatters=Y,__INTLIFY_PROD_DEVTOOLS__&&Jn(k,n,J),k}const Ie=e=>({[e]:W()});function Re(e,t,n,s,c){const{missing:l,onWarn:f}=e;if(l!==null){const d=l(e,n,t,c);return P(d)?d:t}else return t}function Rr(e,t,n){const s=e;s.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function ar(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function cr(e,t){const n=t.indexOf(e);if(n===-1)return!1;for(let s=n+1;s<t.length;s++)if(ar(e,t[s]))return!0;return!1}function Ae(e){return n=>lr(n,e)}function lr(e,t){const n=Pn(t);if(n==null)throw ue(0);if(be(n)===1){const l=yn(n);return e.plural(l.reduce((f,d)=>[...f,Je(e,d)],[]))}else return Je(e,n)}function Je(e,t){const n=bn(t);if(n!=null)return e.type==="text"?n:e.normalize([n]);{const s=Dn(t).reduce((c,l)=>[...c,Se(e,l)],[]);return e.normalize(s)}}function Se(e,t){const n=be(t);switch(n){case 3:return me(t,n);case 9:return me(t,n);case 4:{const s=t;if(se(s,"k")&&s.k)return e.interpolate(e.named(s.k));if(se(s,"key")&&s.key)return e.interpolate(e.named(s.key));throw ue(n)}case 5:{const s=t;if(se(s,"i")&&$(s.i))return e.interpolate(e.list(s.i));if(se(s,"index")&&$(s.index))return e.interpolate(e.list(s.index));throw ue(n)}case 6:{const s=t,c=kn(s),l=Rn(s);return e.linked(Se(e,l),c?Se(e,c):void 0,e.type)}case 7:return me(t,n);case 8:return me(t,n);default:throw new Error(`unhandled node on format message part: ${n}`)}}const Lt=e=>e;let ce=W();function Tt(e,t={}){let n=!1;const s=t.onError||tn;return t.onError=c=>{n=!0,s(c)},{...Sn(e,t),detectError:n}}const Mr=(e,t)=>{if(!P(e))throw x(B.NOT_SUPPORT_NON_STRING_MESSAGE);{X(t.warnHtmlMessage)&&t.warnHtmlMessage;const s=(t.onCacheKey||Lt)(e),c=ce[s];if(c)return c;const{code:l,detectError:f}=Tt(e,t),d=new Function(`return ${l}`)();return f?d:ce[s]=d}};function Ur(e,t){if(__INTLIFY_JIT_COMPILATION__&&!__INTLIFY_DROP_MESSAGE_COMPILER__&&P(e)){X(t.warnHtmlMessage)&&t.warnHtmlMessage;const s=(t.onCacheKey||Lt)(e),c=ce[s];if(c)return c;const{ast:l,detectError:f}=Tt(e,{...t,location:!1,jit:!0}),d=Ae(l);return f?d:ce[s]=d}else{const n=e.cacheKey;if(n){const s=ce[n];return s||(ce[n]=Ae(e))}else return Ae(e)}}const Qe=()=>"",H=e=>v(e);function wr(e,...t){const{fallbackFormat:n,postTranslation:s,unresolving:c,messageCompiler:l,fallbackLocale:f,messages:d}=e,[E,L]=ur(...t),S=X(L.missingWarn)?L.missingWarn:e.missingWarn,O=X(L.fallbackWarn)?L.fallbackWarn:e.fallbackWarn,T=X(L.escapeParameter)?L.escapeParameter:e.escapeParameter,y=!!L.resolvedMessage,b=P(L.default)||X(L.default)?X(L.default)?l?E:()=>E:L.default:n?l?E:()=>E:"",C=n||b!=="",A=De(e,L);T&&ir(L);let[g,D,i]=y?[E,A,d[A]||W()]:pt(e,E,A,f,O,S),o=g,m=E;if(!y&&!(P(o)||ie(o)||H(o))&&C&&(o=b,m=o),!y&&(!(P(o)||ie(o)||H(o))||!P(D)))return c?ke:E;let _=!1;const N=()=>{_=!0},h=H(o)?o:ht(e,E,D,o,m,N);if(_)return o;const R=_r(e,D,i,L),Y=xn(R),J=or(e,h,Y),k=s?s(J,E):J;if(__INTLIFY_PROD_DEVTOOLS__){const G={timestamp:Date.now(),key:P(E)?E:H(o)?o.key:"",locale:D||(H(o)?o.locale:""),format:P(o)?o:H(o)?o.source:"",message:k};G.meta=oe({},e.__meta,sr()||{}),Qn(G)}return k}function ir(e){j(e.list)?e.list=e.list.map(t=>P(t)?We(t):t):F(e.named)&&Object.keys(e.named).forEach(t=>{P(e.named[t])&&(e.named[t]=We(e.named[t]))})}function pt(e,t,n,s,c,l){const{messages:f,onWarn:d,messageResolver:E,localeFallbacker:L}=e,S=L(e,s,n);let O=W(),T,y=null;const b="translate";for(let C=0;C<S.length&&(T=S[C],O=f[T]||W(),(y=E(O,t))===null&&(y=O[t]),!(P(y)||ie(y)||H(y)));C++)if(!cr(T,S)){const A=Re(e,t,T,l,b);A!==t&&(y=A)}return[y,T,O]}function ht(e,t,n,s,c,l){const{messageCompiler:f,warnHtmlMessage:d}=e;if(H(s)){const L=s;return L.locale=L.locale||n,L.key=L.key||t,L}if(f==null){const L=()=>s;return L.locale=n,L.key=t,L}const E=f(s,fr(e,n,c,s,d,l));return E.locale=n,E.key=t,E.source=s,E}function or(e,t,n){return t(n)}function ur(...e){const[t,n,s]=e,c=W();if(!P(t)&&!$(t)&&!H(t)&&!ie(t))throw x(B.INVALID_ARGUMENT);const l=$(t)?String(t):(H(t),t);return $(n)?c.plural=n:P(n)?c.default=n:U(n)&&!ge(n)?c.named=n:j(n)&&(c.list=n),$(s)?c.plural=s:P(s)?c.default=s:U(s)&&oe(c,s),[l,c]}function fr(e,t,n,s,c,l){return{locale:t,key:n,warnHtmlMessage:c,onError:f=>{throw l&&l(f),f},onCacheKey:f=>Kt(t,n,f)}}function _r(e,t,n,s){const{modifiers:c,pluralRules:l,messageResolver:f,fallbackLocale:d,fallbackWarn:E,missingWarn:L,fallbackContext:S}=e,T={locale:t,modifiers:c,pluralRules:l,messages:y=>{let b=f(n,y);if(b==null&&S){const[,,C]=pt(S,y,t,d,E,L);b=f(C,y)}if(P(b)||ie(b)){let C=!1;const g=ht(e,y,t,b,y,()=>{C=!0});return C?Qe:g}else return H(b)?b:Qe}};return e.processor&&(T.processor=e.processor),s.list&&(T.list=s.list),s.named&&(T.named=s.named),$(s.plural)&&(T.pluralIndex=s.plural),T}function Fr(e,...t){const{datetimeFormats:n,unresolving:s,fallbackLocale:c,onWarn:l,localeFallbacker:f}=e,{__datetimeFormatters:d}=e,[E,L,S,O]=Er(...t),T=X(S.missingWarn)?S.missingWarn:e.missingWarn;X(S.fallbackWarn)?S.fallbackWarn:e.fallbackWarn;const y=!!S.part,b=De(e,S),C=f(e,c,b);if(!P(E)||E==="")return new Intl.DateTimeFormat(b,O).format(L);let A={},g,D=null;const i="datetime format";for(let _=0;_<C.length&&(g=C[_],A=n[g]||{},D=A[E],!U(D));_++)Re(e,E,g,T,i);if(!U(D)||!P(g))return s?ke:E;let o=`${g}__${E}`;ge(O)||(o=`${o}__${JSON.stringify(O)}`);let m=d.get(o);return m||(m=new Intl.DateTimeFormat(g,oe({},D,O)),d.set(o,m)),y?m.formatToParts(L):m.format(L)}const dr=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Er(...e){const[t,n,s,c]=e,l=W();let f=W(),d;if(P(t)){const E=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!E)throw x(B.INVALID_ISO_DATE_ARGUMENT);const L=E[3]?E[3].trim().startsWith("T")?`${E[1].trim()}${E[3].trim()}`:`${E[1].trim()}T${E[3].trim()}`:E[1].trim();d=new Date(L);try{d.toISOString()}catch{throw x(B.INVALID_ISO_DATE_ARGUMENT)}}else if(Xt(t)){if(isNaN(t.getTime()))throw x(B.INVALID_DATE_ARGUMENT);d=t}else if($(t))d=t;else throw x(B.INVALID_ARGUMENT);return P(n)?l.key=n:U(n)&&Object.keys(n).forEach(E=>{dr.includes(E)?f[E]=n[E]:l[E]=n[E]}),P(s)?l.locale=s:U(s)&&(f=s),U(c)&&(f=c),[l.key||"",d,l,f]}function vr(e,t,n){const s=e;for(const c in n){const l=`${t}__${c}`;s.__datetimeFormatters.has(l)&&s.__datetimeFormatters.delete(l)}}function Wr(e,...t){const{numberFormats:n,unresolving:s,fallbackLocale:c,onWarn:l,localeFallbacker:f}=e,{__numberFormatters:d}=e,[E,L,S,O]=Nr(...t),T=X(S.missingWarn)?S.missingWarn:e.missingWarn;X(S.fallbackWarn)?S.fallbackWarn:e.fallbackWarn;const y=!!S.part,b=De(e,S),C=f(e,c,b);if(!P(E)||E==="")return new Intl.NumberFormat(b,O).format(L);let A={},g,D=null;const i="number format";for(let _=0;_<C.length&&(g=C[_],A=n[g]||{},D=A[E],!U(D));_++)Re(e,E,g,T,i);if(!U(D)||!P(g))return s?ke:E;let o=`${g}__${E}`;ge(O)||(o=`${o}__${JSON.stringify(O)}`);let m=d.get(o);return m||(m=new Intl.NumberFormat(g,oe({},D,O)),d.set(o,m)),y?m.formatToParts(L):m.format(L)}const mr=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Nr(...e){const[t,n,s,c]=e,l=W();let f=W();if(!$(t))throw x(B.INVALID_ARGUMENT);const d=t;return P(n)?l.key=n:U(n)&&Object.keys(n).forEach(E=>{mr.includes(E)?f[E]=n[E]:l[E]=n[E]}),P(s)?l.locale=s:U(s)&&(f=s),U(c)&&(f=c),[l.key||"",d,l,f]}function Yr(e,t,n){const s=e;for(const c in n){const l=`${t}__${c}`;s.__numberFormatters.has(l)&&s.__numberFormatters.delete(l)}}gn();export{Mn as A,wr as B,B as C,Ce as D,Er as E,Fr as F,Nr as G,Wr as H,H as I,mr as J,dr as K,ge as L,Cr as M,ke as N,Sr as O,gr as P,Pr as Q,he as R,Or as S,Ir as T,Ur as U,Mr as V,hr as W,U as a,P as b,j as c,Fe as d,v as e,Lr as f,_e as g,oe as h,X as i,$ as j,W as k,pr as l,Tr as m,se as n,kr as o,F as p,vr as q,Yr as r,ie as s,yr as t,Rr as u,Dr as v,Ar as w,ze as x,br as y,ur as z};
