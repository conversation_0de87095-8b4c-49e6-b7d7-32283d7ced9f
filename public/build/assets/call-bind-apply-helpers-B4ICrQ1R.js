import{r as p}from"./function-bind-CHqF18-c.js";import{t as f}from"./es-errors-CFxpeikN.js";var O=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function q(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function R(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var r=function n(){return this instanceof n?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(n){var a=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(r,n,a.get?a:{enumerable:!0,get:function(){return e[n]}})}),r}var o,u;function c(){return u||(u=1,o=Function.prototype.call),o}var l,i;function s(){return i||(i=1,l=Function.prototype.apply),l}var d=typeof Reflect<"u"&&Reflect&&Reflect.apply,y=p(),v=s(),b=c(),g=d,m=g||y.call(b,v),h=p(),A=f,w=c(),F=m,_=function(t){if(t.length<1||typeof t[0]!="function")throw new A("a function is required");return F(h,w,t)};export{c as a,O as b,_ as c,R as d,q as g,s as r};
