import{f as y}from"./index-DHV2tfOS.js";import{_ as b}from"./AppLayout-DKZEmXIb.js";import{_ as f}from"./PrimaryButton-DE9sqoJj.js";import{_ as u}from"./LoadingIcon-CesYxFkK.js";import{v as $,k as r,o as n,S as i,a as e,c,K as d,R as k,P as o,F as w,M as N,a4 as T,l as x}from"./@vue-BnW70ngI.js";import"./moment-C5S46NFB.js";import"./@inertiajs-Dt0-hqjZ.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BoI1NwE9.js";const v={class:"flex-1 flex items-center"},M=["textContent"],P=["textContent"],B={class:"mx-auto py-6 px-6"},D={class:"w-full feed-table"},F={class:"text-left flex"},L=["textContent"],E=["textContent"],H=["innerHTML"],q=["textContent"],A=["textContent"],S=["textContent"],V={class:"flex border-t"},z=["textContent"],j=["textContent"],I=["innerHTML"],K=["textContent"],O=["textContent"],R=["textContent"],G={key:1,class:"flex border-t"},J=["textContent"],Q={key:0,class:"mt-6"},U=["textContent"],Dt={__name:"Feed",props:{posts:Array,hasNextPage:Boolean,user:Object},setup(l){const m=l,t=$({refreshing:!1,loading:!1,hasNextPage:m.hasNextPage,posts:[],next:0});m.posts.forEach(s=>t.posts.push(s));const p=async()=>{if(t.loading)return!1;t.loading=!0,await window.axios.get(route("user.feed",{user:m.user.user_id}),{params:{next:t.next,axios:!0}}).then(s=>{t.hasNextPage=s.data.hasNextPage,s.data.posts.forEach(h=>t.posts.push(h))}).catch(s=>{}).finally(()=>{t.loading=!1,t.refreshing=!1})},_=async()=>{if(t.loading)return!1;t.posts=[],t.refreshing=!0,t.next=0,await p()},g=async()=>{if(t.loading)return!1;t.refreshing=!1,t.next=1,await p()};return(s,h)=>(n(),r(b,{title:s.$t("userFeed")},{header:i(()=>[e("div",v,[e("h2",{class:"text-xl text-gray-800 leading-tight flex-1 mr-6",textContent:o(s.$t("userFeedExtra",{name:l.user.name.length?l.user.name:"N/A",id:l.user.user_id}))},null,8,M),x(f,{class:"normal-case",disabled:t.loading,onClick:_},{default:i(()=>[t.refreshing?(n(),r(u,{key:0,class:"mr-2"})):d("",!0),e("span",{class:"text-sm",textContent:o(s.$t("refresh"))},null,8,P)]),_:1},8,["disabled"])])]),default:i(()=>[e("div",B,[e("div",{class:k(["bg-white rounded-md shadow flex flex-col overflow-auto",{"grid-loading":t.loading}])},[t.loading?(n(),r(u,{key:0,class:"grid-loading-icon",size:24,color:"rgb(55 65 81)"})):d("",!0),e("table",D,[e("tbody",null,[e("tr",F,[e("th",{class:"number-column extra",textContent:o(s.$t("STT"))},null,8,L),e("th",{class:"number-column",textContent:o(s.$t("postID"))},null,8,E),e("th",{class:"title-flex-column",innerHTML:s.$t("postContent")},null,8,H),e("th",{class:"post-type-column",textContent:o(s.$t("postType"))},null,8,q),e("th",{class:"time-column",textContent:o(s.$t("postCreatedAt"))},null,8,A),e("th",{class:"post-type-column",textContent:o(s.$t("qaType"))},null,8,S)]),t.posts.length>0?(n(!0),c(w,{key:0},N(t.posts,(a,C)=>(n(),c("tr",V,[e("td",{class:"number-column extra",textContent:o(C+1)},null,8,z),e("td",{class:"number-column",textContent:o(a.post_id)},null,8,j),e("td",{class:"title-flex-column",innerHTML:a.content},null,8,I),e("td",{class:"post-type-column",textContent:o(a.tag)},null,8,K),e("td",{class:"time-column",textContent:o(T(y)(a.created_at))},null,8,O),e("td",{class:"post-type-column",textContent:o(s.$t("qaType."+a.qa_type))},null,8,R)]))),256)):(n(),c("tr",G,[e("td",{colspan:"6",textContent:o(s.$t(t.loading?"loading":"emptyData"))},null,8,J)]))])])],2),t.hasNextPage?(n(),c("div",Q,[x(f,{class:"normal-case mx-auto",disabled:t.loading,onClick:g},{default:i(()=>[t.loading&&!t.refreshing?(n(),r(u,{key:0,class:"mr-2"})):d("",!0),e("span",{class:"text-sm",textContent:o(s.$t("loadMore"))},null,8,U)]),_:1},8,["disabled"])])):d("",!0)])]),_:1},8,["title"]))}};export{Dt as default};
