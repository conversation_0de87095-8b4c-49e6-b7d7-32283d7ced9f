/*!
  * shared v9.14.3
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */const Nr=typeof window<"u",Lr=(e,t=!1)=>t?Symbol.for(e):Symbol(e),Rt=(e,t,n)=>Mt({l:e,k:t,s:n}),Mt=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),K=e=>typeof e=="number"&&isFinite(e),Ut=e=>Ze(e)==="[object Date]",Fe=e=>Ze(e)==="[object RegExp]",ge=e=>U(e)&&Object.keys(e).length===0,ie=Object.assign,wt=Object.create,W=(e=null)=>wt(e);let ve;const he=()=>ve||(ve=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:W());function We(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const Ft=Object.prototype.hasOwnProperty;function se(e,t){return Ft.call(e,t)}const j=Array.isArray,v=e=>typeof e=="function",P=e=>typeof e=="string",X=e=>typeof e=="boolean",F=e=>e!==null&&typeof e=="object",vt=e=>F(e)&&v(e.then)&&v(e.catch),qe=Object.prototype.toString,Ze=e=>qe.call(e),U=e=>{if(!F(e))return!1;const t=Object.getPrototypeOf(e);return t===null||t.constructor===Object},Wt=e=>e==null?"":j(e)||U(e)&&e.toString===qe?JSON.stringify(e,null,2):String(e);function Yt(e,t=""){return e.reduce((n,s,l)=>l===0?n+s:n+t+s,"")}function ze(e){let t=e;return()=>++t}function $t(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const Ee=e=>!F(e)||j(e);function Tr(e,t){if(Ee(e)||Ee(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:s,des:l}=n.pop();Object.keys(s).forEach(c=>{c!=="__proto__"&&(F(s[c])&&!F(l[c])&&(l[c]=Array.isArray(s[c])?[]:W()),Ee(l[c])||Ee(s[c])?l[c]=s[c]:n.push({src:s[c],des:l[c]}))})}}/*!
  * message-compiler v9.14.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function Kt(e,t,n){return{line:e,column:t,offset:n}}function Ne(e,t,n){return{start:e,end:t}}const Xt=/\{([0-9a-zA-Z]+)\}/g;function et(e,...t){return t.length===1&&Gt(t[0])&&(t=t[0]),(!t||!t.hasOwnProperty)&&(t={}),e.replace(Xt,(n,s)=>t.hasOwnProperty(s)?t[s]:"")}const tt=Object.assign,Ye=e=>typeof e=="string",Gt=e=>e!==null&&typeof e=="object";function nt(e,t=""){return e.reduce((n,s,l)=>l===0?n+s:n+t+s,"")}const Pe={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2},Vt={[Pe.USE_MODULO_SYNTAX]:"Use modulo before '{{0}}'."};function Ht(e,t,...n){const s=et(Vt[e],...n||[]),l={message:String(s),code:e};return t&&(l.location=t),l}const I={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17},jt={[I.EXPECTED_TOKEN]:"Expected token: '{0}'",[I.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[I.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[I.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[I.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[I.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[I.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[I.EMPTY_PLACEHOLDER]:"Empty placeholder",[I.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[I.INVALID_LINKED_FORMAT]:"Invalid linked format",[I.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[I.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[I.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[I.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[I.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[I.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function _e(e,t,n={}){const{domain:s,messages:l,args:c}=n,f=et((l||jt)[e]||"",...c||[]),d=new SyntaxError(String(f));return d.code=e,t&&(d.location=t),d.domain=s,d}function Bt(e){throw e}const Q=" ",xt="\r",$=`
`,Jt="\u2028",Qt="\u2029";function qt(e){const t=e;let n=0,s=1,l=1,c=0;const f=N=>t[N]===xt&&t[N+1]===$,d=N=>t[N]===$,E=N=>t[N]===Qt,L=N=>t[N]===Jt,S=N=>f(N)||d(N)||E(N)||L(N),O=()=>n,T=()=>s,y=()=>l,b=()=>c,C=N=>f(N)||E(N)||L(N)?$:t[N],A=()=>C(n),g=()=>C(n+c);function D(){return c=0,S(n)&&(s++,l=0),f(n)&&n++,n++,l++,t[n]}function i(){return f(n+c)&&c++,c++,t[n+c]}function o(){n=0,s=1,l=1,c=0}function m(N=0){c=N}function _(){const N=n+c;for(;N!==n;)D();c=0}return{index:O,line:T,column:y,peekOffset:b,charAt:C,currentChar:A,currentPeek:g,next:D,peek:i,reset:o,resetPeek:m,skipToPeek:_}}const Z=void 0,Zt=".",$e="'",zt="tokenizer";function en(e,t={}){const n=t.location!==!1,s=qt(e),l=()=>s.index(),c=()=>Kt(s.line(),s.column(),s.index()),f=c(),d=l(),E={currentType:14,offset:d,startLoc:f,endLoc:f,lastType:14,lastOffset:d,lastStartLoc:f,lastEndLoc:f,braceNest:0,inLinked:!1,text:""},L=()=>E,{onError:S}=t;function O(r,a,u,...p){const w=L();if(a.column+=u,a.offset+=u,S){const M=n?Ne(w.startLoc,a):null,q=_e(r,M,{domain:zt,args:p});S(q)}}function T(r,a,u){r.endLoc=c(),r.currentType=a;const p={type:a};return n&&(p.loc=Ne(r.startLoc,r.endLoc)),u!=null&&(p.value=u),p}const y=r=>T(r,14);function b(r,a){return r.currentChar()===a?(r.next(),a):(O(I.EXPECTED_TOKEN,c(),0,a),"")}function C(r){let a="";for(;r.currentPeek()===Q||r.currentPeek()===$;)a+=r.currentPeek(),r.peek();return a}function A(r){const a=C(r);return r.skipToPeek(),a}function g(r){if(r===Z)return!1;const a=r.charCodeAt(0);return a>=97&&a<=122||a>=65&&a<=90||a===95}function D(r){if(r===Z)return!1;const a=r.charCodeAt(0);return a>=48&&a<=57}function i(r,a){const{currentType:u}=a;if(u!==2)return!1;C(r);const p=g(r.currentPeek());return r.resetPeek(),p}function o(r,a){const{currentType:u}=a;if(u!==2)return!1;C(r);const p=r.currentPeek()==="-"?r.peek():r.currentPeek(),w=D(p);return r.resetPeek(),w}function m(r,a){const{currentType:u}=a;if(u!==2)return!1;C(r);const p=r.currentPeek()===$e;return r.resetPeek(),p}function _(r,a){const{currentType:u}=a;if(u!==8)return!1;C(r);const p=r.currentPeek()===".";return r.resetPeek(),p}function N(r,a){const{currentType:u}=a;if(u!==9)return!1;C(r);const p=g(r.currentPeek());return r.resetPeek(),p}function h(r,a){const{currentType:u}=a;if(!(u===8||u===12))return!1;C(r);const p=r.currentPeek()===":";return r.resetPeek(),p}function R(r,a){const{currentType:u}=a;if(u!==10)return!1;const p=()=>{const M=r.currentPeek();return M==="{"?g(r.peek()):M==="@"||M==="%"||M==="|"||M===":"||M==="."||M===Q||!M?!1:M===$?(r.peek(),p()):k(r,!1)},w=p();return r.resetPeek(),w}function Y(r){C(r);const a=r.currentPeek()==="|";return r.resetPeek(),a}function J(r){const a=C(r),u=r.currentPeek()==="%"&&r.peek()==="{";return r.resetPeek(),{isModulo:u,hasSpace:a.length>0}}function k(r,a=!0){const u=(w=!1,M="",q=!1)=>{const te=r.currentPeek();return te==="{"?M==="%"?!1:w:te==="@"||!te?M==="%"?!0:w:te==="%"?(r.peek(),u(w,"%",!0)):te==="|"?M==="%"||q?!0:!(M===Q||M===$):te===Q?(r.peek(),u(!0,Q,q)):te===$?(r.peek(),u(!0,$,q)):!0},p=u();return a&&r.resetPeek(),p}function G(r,a){const u=r.currentChar();return u===Z?Z:a(u)?(r.next(),u):null}function dt(r){const a=r.charCodeAt(0);return a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57||a===95||a===36}function Et(r){return G(r,dt)}function mt(r){const a=r.charCodeAt(0);return a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57||a===95||a===36||a===45}function Nt(r){return G(r,mt)}function Lt(r){const a=r.charCodeAt(0);return a>=48&&a<=57}function Tt(r){return G(r,Lt)}function pt(r){const a=r.charCodeAt(0);return a>=48&&a<=57||a>=65&&a<=70||a>=97&&a<=102}function ht(r){return G(r,pt)}function Me(r){let a="",u="";for(;a=Tt(r);)u+=a;return u}function Ot(r){A(r);const a=r.currentChar();return a!=="%"&&O(I.EXPECTED_TOKEN,c(),0,a),r.next(),"%"}function Ue(r){let a="";for(;;){const u=r.currentChar();if(u==="{"||u==="}"||u==="@"||u==="|"||!u)break;if(u==="%")if(k(r))a+=u,r.next();else break;else if(u===Q||u===$)if(k(r))a+=u,r.next();else{if(Y(r))break;a+=u,r.next()}else a+=u,r.next()}return a}function It(r){A(r);let a="",u="";for(;a=Nt(r);)u+=a;return r.currentChar()===Z&&O(I.UNTERMINATED_CLOSING_BRACE,c(),0),u}function At(r){A(r);let a="";return r.currentChar()==="-"?(r.next(),a+=`-${Me(r)}`):a+=Me(r),r.currentChar()===Z&&O(I.UNTERMINATED_CLOSING_BRACE,c(),0),a}function Ct(r){return r!==$e&&r!==$}function St(r){A(r),b(r,"'");let a="",u="";for(;a=G(r,Ct);)a==="\\"?u+=gt(r):u+=a;const p=r.currentChar();return p===$||p===Z?(O(I.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,c(),0),p===$&&(r.next(),b(r,"'")),u):(b(r,"'"),u)}function gt(r){const a=r.currentChar();switch(a){case"\\":case"'":return r.next(),`\\${a}`;case"u":return we(r,a,4);case"U":return we(r,a,6);default:return O(I.UNKNOWN_ESCAPE_SEQUENCE,c(),0,a),""}}function we(r,a,u){b(r,a);let p="";for(let w=0;w<u;w++){const M=ht(r);if(!M){O(I.INVALID_UNICODE_ESCAPE_SEQUENCE,c(),0,`\\${a}${p}${r.currentChar()}`);break}p+=M}return`\\${a}${p}`}function Pt(r){return r!=="{"&&r!=="}"&&r!==Q&&r!==$}function yt(r){A(r);let a="",u="";for(;a=G(r,Pt);)u+=a;return u}function bt(r){let a="",u="";for(;a=Et(r);)u+=a;return u}function Dt(r){const a=u=>{const p=r.currentChar();return p==="{"||p==="%"||p==="@"||p==="|"||p==="("||p===")"||!p||p===Q?u:(u+=p,r.next(),a(u))};return a("")}function Le(r){A(r);const a=b(r,"|");return A(r),a}function Te(r,a){let u=null;switch(r.currentChar()){case"{":return a.braceNest>=1&&O(I.NOT_ALLOW_NEST_PLACEHOLDER,c(),0),r.next(),u=T(a,2,"{"),A(r),a.braceNest++,u;case"}":return a.braceNest>0&&a.currentType===2&&O(I.EMPTY_PLACEHOLDER,c(),0),r.next(),u=T(a,3,"}"),a.braceNest--,a.braceNest>0&&A(r),a.inLinked&&a.braceNest===0&&(a.inLinked=!1),u;case"@":return a.braceNest>0&&O(I.UNTERMINATED_CLOSING_BRACE,c(),0),u=de(r,a)||y(a),a.braceNest=0,u;default:{let w=!0,M=!0,q=!0;if(Y(r))return a.braceNest>0&&O(I.UNTERMINATED_CLOSING_BRACE,c(),0),u=T(a,1,Le(r)),a.braceNest=0,a.inLinked=!1,u;if(a.braceNest>0&&(a.currentType===5||a.currentType===6||a.currentType===7))return O(I.UNTERMINATED_CLOSING_BRACE,c(),0),a.braceNest=0,pe(r,a);if(w=i(r,a))return u=T(a,5,It(r)),A(r),u;if(M=o(r,a))return u=T(a,6,At(r)),A(r),u;if(q=m(r,a))return u=T(a,7,St(r)),A(r),u;if(!w&&!M&&!q)return u=T(a,13,yt(r)),O(I.INVALID_TOKEN_IN_PLACEHOLDER,c(),0,u.value),A(r),u;break}}return u}function de(r,a){const{currentType:u}=a;let p=null;const w=r.currentChar();switch((u===8||u===9||u===12||u===10)&&(w===$||w===Q)&&O(I.INVALID_LINKED_FORMAT,c(),0),w){case"@":return r.next(),p=T(a,8,"@"),a.inLinked=!0,p;case".":return A(r),r.next(),T(a,9,".");case":":return A(r),r.next(),T(a,10,":");default:return Y(r)?(p=T(a,1,Le(r)),a.braceNest=0,a.inLinked=!1,p):_(r,a)||h(r,a)?(A(r),de(r,a)):N(r,a)?(A(r),T(a,12,bt(r))):R(r,a)?(A(r),w==="{"?Te(r,a)||p:T(a,11,Dt(r))):(u===8&&O(I.INVALID_LINKED_FORMAT,c(),0),a.braceNest=0,a.inLinked=!1,pe(r,a))}}function pe(r,a){let u={type:14};if(a.braceNest>0)return Te(r,a)||y(a);if(a.inLinked)return de(r,a)||y(a);switch(r.currentChar()){case"{":return Te(r,a)||y(a);case"}":return O(I.UNBALANCED_CLOSING_BRACE,c(),0),r.next(),T(a,3,"}");case"@":return de(r,a)||y(a);default:{if(Y(r))return u=T(a,1,Le(r)),a.braceNest=0,a.inLinked=!1,u;const{isModulo:w,hasSpace:M}=J(r);if(w)return M?T(a,0,Ue(r)):T(a,4,Ot(r));if(k(r))return T(a,0,Ue(r));break}}return u}function kt(){const{currentType:r,offset:a,startLoc:u,endLoc:p}=E;return E.lastType=r,E.lastOffset=a,E.lastStartLoc=u,E.lastEndLoc=p,E.offset=l(),E.startLoc=c(),s.currentChar()===Z?T(E,14):pe(s,E)}return{nextToken:kt,currentOffset:l,currentPosition:c,context:L}}const tn="parser",nn=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function rn(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const s=parseInt(t||n,16);return s<=55295||s>=57344?String.fromCodePoint(s):"�"}}}function sn(e={}){const t=e.location!==!1,{onError:n,onWarn:s}=e;function l(i,o,m,_,...N){const h=i.currentPosition();if(h.offset+=_,h.column+=_,n){const R=t?Ne(m,h):null,Y=_e(o,R,{domain:tn,args:N});n(Y)}}function c(i,o,m,_,...N){const h=i.currentPosition();if(h.offset+=_,h.column+=_,s){const R=t?Ne(m,h):null;s(Ht(o,R,N))}}function f(i,o,m){const _={type:i};return t&&(_.start=o,_.end=o,_.loc={start:m,end:m}),_}function d(i,o,m,_){t&&(i.end=o,i.loc&&(i.loc.end=m))}function E(i,o){const m=i.context(),_=f(3,m.offset,m.startLoc);return _.value=o,d(_,i.currentOffset(),i.currentPosition()),_}function L(i,o){const m=i.context(),{lastOffset:_,lastStartLoc:N}=m,h=f(5,_,N);return h.index=parseInt(o,10),i.nextToken(),d(h,i.currentOffset(),i.currentPosition()),h}function S(i,o,m){const _=i.context(),{lastOffset:N,lastStartLoc:h}=_,R=f(4,N,h);return R.key=o,m===!0&&(R.modulo=!0),i.nextToken(),d(R,i.currentOffset(),i.currentPosition()),R}function O(i,o){const m=i.context(),{lastOffset:_,lastStartLoc:N}=m,h=f(9,_,N);return h.value=o.replace(nn,rn),i.nextToken(),d(h,i.currentOffset(),i.currentPosition()),h}function T(i){const o=i.nextToken(),m=i.context(),{lastOffset:_,lastStartLoc:N}=m,h=f(8,_,N);return o.type!==12?(l(i,I.UNEXPECTED_EMPTY_LINKED_MODIFIER,m.lastStartLoc,0),h.value="",d(h,_,N),{nextConsumeToken:o,node:h}):(o.value==null&&l(i,I.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,V(o)),h.value=o.value||"",d(h,i.currentOffset(),i.currentPosition()),{node:h})}function y(i,o){const m=i.context(),_=f(7,m.offset,m.startLoc);return _.value=o,d(_,i.currentOffset(),i.currentPosition()),_}function b(i){const o=i.context(),m=f(6,o.offset,o.startLoc);let _=i.nextToken();if(_.type===9){const N=T(i);m.modifier=N.node,_=N.nextConsumeToken||i.nextToken()}switch(_.type!==10&&l(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(_)),_=i.nextToken(),_.type===2&&(_=i.nextToken()),_.type){case 11:_.value==null&&l(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(_)),m.key=y(i,_.value||"");break;case 5:_.value==null&&l(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(_)),m.key=S(i,_.value||"");break;case 6:_.value==null&&l(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(_)),m.key=L(i,_.value||"");break;case 7:_.value==null&&l(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(_)),m.key=O(i,_.value||"");break;default:{l(i,I.UNEXPECTED_EMPTY_LINKED_KEY,o.lastStartLoc,0);const N=i.context(),h=f(7,N.offset,N.startLoc);return h.value="",d(h,N.offset,N.startLoc),m.key=h,d(m,N.offset,N.startLoc),{nextConsumeToken:_,node:m}}}return d(m,i.currentOffset(),i.currentPosition()),{node:m}}function C(i){const o=i.context(),m=o.currentType===1?i.currentOffset():o.offset,_=o.currentType===1?o.endLoc:o.startLoc,N=f(2,m,_);N.items=[];let h=null,R=null;do{const k=h||i.nextToken();switch(h=null,k.type){case 0:k.value==null&&l(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(k)),N.items.push(E(i,k.value||""));break;case 6:k.value==null&&l(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(k)),N.items.push(L(i,k.value||""));break;case 4:R=!0;break;case 5:k.value==null&&l(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(k)),N.items.push(S(i,k.value||"",!!R)),R&&(c(i,Pe.USE_MODULO_SYNTAX,o.lastStartLoc,0,V(k)),R=null);break;case 7:k.value==null&&l(i,I.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,0,V(k)),N.items.push(O(i,k.value||""));break;case 8:{const G=b(i);N.items.push(G.node),h=G.nextConsumeToken||null;break}}}while(o.currentType!==14&&o.currentType!==1);const Y=o.currentType===1?o.lastOffset:i.currentOffset(),J=o.currentType===1?o.lastEndLoc:i.currentPosition();return d(N,Y,J),N}function A(i,o,m,_){const N=i.context();let h=_.items.length===0;const R=f(1,o,m);R.cases=[],R.cases.push(_);do{const Y=C(i);h||(h=Y.items.length===0),R.cases.push(Y)}while(N.currentType!==14);return h&&l(i,I.MUST_HAVE_MESSAGES_IN_PLURAL,m,0),d(R,i.currentOffset(),i.currentPosition()),R}function g(i){const o=i.context(),{offset:m,startLoc:_}=o,N=C(i);return o.currentType===14?N:A(i,m,_,N)}function D(i){const o=en(i,tt({},e)),m=o.context(),_=f(0,m.offset,m.startLoc);return t&&_.loc&&(_.loc.source=i),_.body=g(o),e.onCacheKey&&(_.cacheKey=e.onCacheKey(i)),m.currentType!==14&&l(o,I.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,i[m.offset]||""),d(_,o.currentOffset(),o.currentPosition()),_}return{parse:D}}function V(e){if(e.type===14)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function an(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:c=>(n.helpers.add(c),c)}}function Ke(e,t){for(let n=0;n<e.length;n++)ye(e[n],t)}function ye(e,t){switch(e.type){case 1:Ke(e.cases,t),t.helper("plural");break;case 2:Ke(e.items,t);break;case 6:{ye(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function cn(e,t={}){const n=an(e);n.helper("normalize"),e.body&&ye(e.body,n);const s=n.context();e.helpers=Array.from(s.helpers)}function ln(e){const t=e.body;return t.type===2?Xe(t):t.cases.forEach(n=>Xe(n)),e}function Xe(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const s=e.items[n];if(!(s.type===3||s.type===9)||s.value==null)break;t.push(s.value)}if(t.length===e.items.length){e.static=nt(t);for(let n=0;n<e.items.length;n++){const s=e.items[n];(s.type===3||s.type===9)&&delete s.value}}}}const on="minifier";function ae(e){switch(e.t=e.type,e.type){case 0:{const t=e;ae(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let s=0;s<n.length;s++)ae(n[s]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let s=0;s<n.length;s++)ae(n[s]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;ae(t.key),t.k=t.key,delete t.key,t.modifier&&(ae(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}default:throw _e(I.UNHANDLED_MINIFIER_NODE_TYPE,null,{domain:on,args:[e.type]})}delete e.type}const un="parser";function fn(e,t){const{filename:n,breakLineCode:s,needIndent:l}=t,c=t.location!==!1,f={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:s,needIndent:l,indentLevel:0};c&&e.loc&&(f.source=e.loc.source);const d=()=>f;function E(C,A){f.code+=C}function L(C,A=!0){const g=A?s:"";E(l?g+"  ".repeat(C):g)}function S(C=!0){const A=++f.indentLevel;C&&L(A)}function O(C=!0){const A=--f.indentLevel;C&&L(A)}function T(){L(f.indentLevel)}return{context:d,push:E,indent:S,deindent:O,newline:T,helper:C=>`_${C}`,needIndent:()=>f.needIndent}}function _n(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),le(e,t.key),t.modifier?(e.push(", "),le(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function dn(e,t){const{helper:n,needIndent:s}=e;e.push(`${n("normalize")}([`),e.indent(s());const l=t.items.length;for(let c=0;c<l&&(le(e,t.items[c]),c!==l-1);c++)e.push(", ");e.deindent(s()),e.push("])")}function En(e,t){const{helper:n,needIndent:s}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(s());const l=t.cases.length;for(let c=0;c<l&&(le(e,t.cases[c]),c!==l-1);c++)e.push(", ");e.deindent(s()),e.push("])")}}function mn(e,t){t.body?le(e,t.body):e.push("null")}function le(e,t){const{helper:n}=e;switch(t.type){case 0:mn(e,t);break;case 1:En(e,t);break;case 2:dn(e,t);break;case 6:_n(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break;default:throw _e(I.UNHANDLED_CODEGEN_NODE_TYPE,null,{domain:un,args:[t.type]})}}const Nn=(e,t={})=>{const n=Ye(t.mode)?t.mode:"normal",s=Ye(t.filename)?t.filename:"message.intl";t.sourceMap;const l=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":`
`,c=t.needIndent?t.needIndent:n!=="arrow",f=e.helpers||[],d=fn(e,{filename:s,breakLineCode:l,needIndent:c});d.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),d.indent(c),f.length>0&&(d.push(`const { ${nt(f.map(S=>`${S}: _${S}`),", ")} } = ctx`),d.newline()),d.push("return "),le(d,e),d.deindent(c),d.push("}"),delete e.helpers;const{code:E,map:L}=d.context();return{ast:e,code:E,map:L?L.toJSON():void 0}};function Ln(e,t={}){const n=tt({},t),s=!!n.jit,l=!!n.minify,c=n.optimize==null?!0:n.optimize,d=sn(n).parse(e);return s?(c&&ln(d),l&&ae(d),{ast:d,code:""}):(cn(d,n),Nn(d,n))}/*!
  * core-base v9.14.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function Tn(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(he().__INTLIFY_PROD_DEVTOOLS__=!1),typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(he().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(he().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}const z=[];z[0]={w:[0],i:[3,0],"[":[4],o:[7]};z[1]={w:[1],".":[2],"[":[4],o:[7]};z[2]={w:[2],i:[3,0],0:[3,0]};z[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};z[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};z[5]={"'":[4,0],o:8,l:[5,0]};z[6]={'"':[4,0],o:8,l:[6,0]};const pn=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function hn(e){return pn.test(e)}function On(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function In(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function An(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:hn(t)?On(t):"*"+t}function Cn(e){const t=[];let n=-1,s=0,l=0,c,f,d,E,L,S,O;const T=[];T[0]=()=>{f===void 0?f=d:f+=d},T[1]=()=>{f!==void 0&&(t.push(f),f=void 0)},T[2]=()=>{T[0](),l++},T[3]=()=>{if(l>0)l--,s=4,T[0]();else{if(l=0,f===void 0||(f=An(f),f===!1))return!1;T[1]()}};function y(){const b=e[n+1];if(s===5&&b==="'"||s===6&&b==='"')return n++,d="\\"+b,T[0](),!0}for(;s!==null;)if(n++,c=e[n],!(c==="\\"&&y())){if(E=In(c),O=z[s],L=O[E]||O.l||8,L===8||(s=L[0],L[1]!==void 0&&(S=T[L[1]],S&&(d=c,S()===!1))))return;if(s===7)return t}}const Ge=new Map;function Sn(e,t){return F(e)?e[t]:null}function pr(e,t){if(!F(e))return null;let n=Ge.get(t);if(n||(n=Cn(t),n&&Ge.set(t,n)),!n)return null;const s=n.length;let l=e,c=0;for(;c<s;){const f=l[n[c]];if(f===void 0||v(l))return null;l=f,c++}return l}const gn=e=>e,Pn=e=>"",yn="text",bn=e=>e.length===0?"":Yt(e),Dn=Wt;function Ve(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function kn(e){const t=K(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(K(e.named.count)||K(e.named.n))?K(e.named.count)?e.named.count:K(e.named.n)?e.named.n:t:t}function Rn(e,t){t.count||(t.count=e),t.n||(t.n=e)}function Mn(e={}){const t=e.locale,n=kn(e),s=F(e.pluralRules)&&P(t)&&v(e.pluralRules[t])?e.pluralRules[t]:Ve,l=F(e.pluralRules)&&P(t)&&v(e.pluralRules[t])?Ve:void 0,c=g=>g[s(n,g.length,l)],f=e.list||[],d=g=>f[g],E=e.named||W();K(e.pluralIndex)&&Rn(n,E);const L=g=>E[g];function S(g){const D=v(e.messages)?e.messages(g):F(e.messages)?e.messages[g]:!1;return D||(e.parent?e.parent.message(g):Pn)}const O=g=>e.modifiers?e.modifiers[g]:gn,T=U(e.processor)&&v(e.processor.normalize)?e.processor.normalize:bn,y=U(e.processor)&&v(e.processor.interpolate)?e.processor.interpolate:Dn,b=U(e.processor)&&P(e.processor.type)?e.processor.type:yn,A={list:d,named:L,plural:c,linked:(g,...D)=>{const[i,o]=D;let m="text",_="";D.length===1?F(i)?(_=i.modifier||_,m=i.type||m):P(i)&&(_=i||_):D.length===2&&(P(i)&&(_=i||_),P(o)&&(m=o||m));const N=S(g)(A),h=m==="vnode"&&j(N)&&_?N[0]:N;return _?O(_)(h,m):h},message:S,type:b,interpolate:y,normalize:T,values:ie(W(),f,E)};return A}let oe=null;function hr(e){oe=e}function Un(e,t,n){oe&&oe.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}const wn=Fn("function:translate");function Fn(e){return t=>oe&&oe.emit(e,t)}const vn=Pe.__EXTEND_POINT__,ne=ze(vn),Or={FALLBACK_TO_TRANSLATE:ne(),CANNOT_FORMAT_NUMBER:ne(),FALLBACK_TO_NUMBER_FORMAT:ne(),CANNOT_FORMAT_DATE:ne(),FALLBACK_TO_DATE_FORMAT:ne(),EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:ne(),__EXTEND_POINT__:ne()},rt=I.__EXTEND_POINT__,re=ze(rt),B={INVALID_ARGUMENT:rt,INVALID_DATE_ARGUMENT:re(),INVALID_ISO_DATE_ARGUMENT:re(),NOT_SUPPORT_NON_STRING_MESSAGE:re(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:re(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:re(),NOT_SUPPORT_LOCALE_TYPE:re(),__EXTEND_POINT__:re()};function x(e){return _e(e,null,void 0)}function be(e,t){return t.locale!=null?He(t.locale):He(e.locale)}let Oe;function He(e){if(P(e))return e;if(v(e)){if(e.resolvedOnce&&Oe!=null)return Oe;if(e.constructor.name==="Function"){const t=e();if(vt(t))throw x(B.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return Oe=t}else throw x(B.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw x(B.NOT_SUPPORT_LOCALE_TYPE)}function Wn(e,t,n){return[...new Set([n,...j(t)?t:F(t)?Object.keys(t):P(t)?[t]:[n]])]}function Ir(e,t,n){const s=P(n)?n:Ce,l=e;l.__localeChainCache||(l.__localeChainCache=new Map);let c=l.__localeChainCache.get(s);if(!c){c=[];let f=[n];for(;j(f);)f=je(c,f,t);const d=j(t)||!U(t)?t:t.default?t.default:null;f=P(d)?[d]:d,j(f)&&je(c,f,!1),l.__localeChainCache.set(s,c)}return c}function je(e,t,n){let s=!0;for(let l=0;l<t.length&&X(s);l++){const c=t[l];P(c)&&(s=Yn(e,t[l],n))}return s}function Yn(e,t,n){let s;const l=t.split("-");do{const c=l.join("-");s=$n(e,c,n),l.splice(-1,1)}while(l.length&&s===!0);return s}function $n(e,t,n){let s=!1;if(!e.includes(t)&&(s=!0,t)){s=t[t.length-1]!=="!";const l=t.replace(/!/g,"");e.push(l),(j(n)||U(n))&&n[l]&&(s=n[l])}return s}const Kn="9.14.3",De=-1,Ce="en-US",Ar="",Be=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function Xn(){return{upper:(e,t)=>t==="text"&&P(e)?e.toUpperCase():t==="vnode"&&F(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&P(e)?e.toLowerCase():t==="vnode"&&F(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&P(e)?Be(e):t==="vnode"&&F(e)&&"__v_isVNode"in e?Be(e.children):e}}let st;function Cr(e){st=e}let at;function Sr(e){at=e}let ct;function gr(e){ct=e}let lt=null;const Pr=e=>{lt=e},Gn=()=>lt;let it=null;const yr=e=>{it=e},br=()=>it;let xe=0;function Dr(e={}){const t=v(e.onWarn)?e.onWarn:$t,n=P(e.version)?e.version:Kn,s=P(e.locale)||v(e.locale)?e.locale:Ce,l=v(s)?Ce:s,c=j(e.fallbackLocale)||U(e.fallbackLocale)||P(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:l,f=U(e.messages)?e.messages:Ie(l),d=U(e.datetimeFormats)?e.datetimeFormats:Ie(l),E=U(e.numberFormats)?e.numberFormats:Ie(l),L=ie(W(),e.modifiers,Xn()),S=e.pluralRules||W(),O=v(e.missing)?e.missing:null,T=X(e.missingWarn)||Fe(e.missingWarn)?e.missingWarn:!0,y=X(e.fallbackWarn)||Fe(e.fallbackWarn)?e.fallbackWarn:!0,b=!!e.fallbackFormat,C=!!e.unresolving,A=v(e.postTranslation)?e.postTranslation:null,g=U(e.processor)?e.processor:null,D=X(e.warnHtmlMessage)?e.warnHtmlMessage:!0,i=!!e.escapeParameter,o=v(e.messageCompiler)?e.messageCompiler:st,m=v(e.messageResolver)?e.messageResolver:at||Sn,_=v(e.localeFallbacker)?e.localeFallbacker:ct||Wn,N=F(e.fallbackContext)?e.fallbackContext:void 0,h=e,R=F(h.__datetimeFormatters)?h.__datetimeFormatters:new Map,Y=F(h.__numberFormatters)?h.__numberFormatters:new Map,J=F(h.__meta)?h.__meta:{};xe++;const k={version:n,cid:xe,locale:s,fallbackLocale:c,messages:f,modifiers:L,pluralRules:S,missing:O,missingWarn:T,fallbackWarn:y,fallbackFormat:b,unresolving:C,postTranslation:A,processor:g,warnHtmlMessage:D,escapeParameter:i,messageCompiler:o,messageResolver:m,localeFallbacker:_,fallbackContext:N,onWarn:t,__meta:J};return k.datetimeFormats=d,k.numberFormats=E,k.__datetimeFormatters=R,k.__numberFormatters=Y,__INTLIFY_PROD_DEVTOOLS__&&Un(k,n,J),k}const Ie=e=>({[e]:W()});function ke(e,t,n,s,l){const{missing:c,onWarn:f}=e;if(c!==null){const d=c(e,n,t,l);return P(d)?d:t}else return t}function kr(e,t,n){const s=e;s.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function Vn(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function Hn(e,t){const n=t.indexOf(e);if(n===-1)return!1;for(let s=n+1;s<t.length;s++)if(Vn(e,t[s]))return!0;return!1}function Ae(e){return n=>jn(n,e)}function jn(e,t){const n=xn(t);if(n==null)throw ue(0);if(Re(n)===1){const c=Qn(n);return e.plural(c.reduce((f,d)=>[...f,Je(e,d)],[]))}else return Je(e,n)}const Bn=["b","body"];function xn(e){return ee(e,Bn)}const Jn=["c","cases"];function Qn(e){return ee(e,Jn,[])}function Je(e,t){const n=Zn(t);if(n!=null)return e.type==="text"?n:e.normalize([n]);{const s=er(t).reduce((l,c)=>[...l,Se(e,c)],[]);return e.normalize(s)}}const qn=["s","static"];function Zn(e){return ee(e,qn)}const zn=["i","items"];function er(e){return ee(e,zn,[])}function Se(e,t){const n=Re(t);switch(n){case 3:return me(t,n);case 9:return me(t,n);case 4:{const s=t;if(se(s,"k")&&s.k)return e.interpolate(e.named(s.k));if(se(s,"key")&&s.key)return e.interpolate(e.named(s.key));throw ue(n)}case 5:{const s=t;if(se(s,"i")&&K(s.i))return e.interpolate(e.list(s.i));if(se(s,"index")&&K(s.index))return e.interpolate(e.list(s.index));throw ue(n)}case 6:{const s=t,l=sr(s),c=cr(s);return e.linked(Se(e,c),l?Se(e,l):void 0,e.type)}case 7:return me(t,n);case 8:return me(t,n);default:throw new Error(`unhandled node on format message part: ${n}`)}}const tr=["t","type"];function Re(e){return ee(e,tr)}const nr=["v","value"];function me(e,t){const n=ee(e,nr);if(n)return n;throw ue(t)}const rr=["m","modifier"];function sr(e){return ee(e,rr)}const ar=["k","key"];function cr(e){const t=ee(e,ar);if(t)return t;throw ue(6)}function ee(e,t,n){for(let s=0;s<t.length;s++){const l=t[s];if(se(e,l)&&e[l]!=null)return e[l]}return n}function ue(e){return new Error(`unhandled node type: ${e}`)}const ot=e=>e;let ce=W();function fe(e){return F(e)&&Re(e)===0&&(se(e,"b")||se(e,"body"))}function ut(e,t={}){let n=!1;const s=t.onError||Bt;return t.onError=l=>{n=!0,s(l)},{...Ln(e,t),detectError:n}}const Rr=(e,t)=>{if(!P(e))throw x(B.NOT_SUPPORT_NON_STRING_MESSAGE);{X(t.warnHtmlMessage)&&t.warnHtmlMessage;const s=(t.onCacheKey||ot)(e),l=ce[s];if(l)return l;const{code:c,detectError:f}=ut(e,t),d=new Function(`return ${c}`)();return f?d:ce[s]=d}};function Mr(e,t){if(__INTLIFY_JIT_COMPILATION__&&!__INTLIFY_DROP_MESSAGE_COMPILER__&&P(e)){X(t.warnHtmlMessage)&&t.warnHtmlMessage;const s=(t.onCacheKey||ot)(e),l=ce[s];if(l)return l;const{ast:c,detectError:f}=ut(e,{...t,location:!1,jit:!0}),d=Ae(c);return f?d:ce[s]=d}else{const n=e.cacheKey;if(n){const s=ce[n];return s||(ce[n]=Ae(e))}else return Ae(e)}}const Qe=()=>"",H=e=>v(e);function Ur(e,...t){const{fallbackFormat:n,postTranslation:s,unresolving:l,messageCompiler:c,fallbackLocale:f,messages:d}=e,[E,L]=or(...t),S=X(L.missingWarn)?L.missingWarn:e.missingWarn,O=X(L.fallbackWarn)?L.fallbackWarn:e.fallbackWarn,T=X(L.escapeParameter)?L.escapeParameter:e.escapeParameter,y=!!L.resolvedMessage,b=P(L.default)||X(L.default)?X(L.default)?c?E:()=>E:L.default:n?c?E:()=>E:"",C=n||b!=="",A=be(e,L);T&&lr(L);let[g,D,i]=y?[E,A,d[A]||W()]:ft(e,E,A,f,O,S),o=g,m=E;if(!y&&!(P(o)||fe(o)||H(o))&&C&&(o=b,m=o),!y&&(!(P(o)||fe(o)||H(o))||!P(D)))return l?De:E;let _=!1;const N=()=>{_=!0},h=H(o)?o:_t(e,E,D,o,m,N);if(_)return o;const R=fr(e,D,i,L),Y=Mn(R),J=ir(e,h,Y),k=s?s(J,E):J;if(__INTLIFY_PROD_DEVTOOLS__){const G={timestamp:Date.now(),key:P(E)?E:H(o)?o.key:"",locale:D||(H(o)?o.locale:""),format:P(o)?o:H(o)?o.source:"",message:k};G.meta=ie({},e.__meta,Gn()||{}),wn(G)}return k}function lr(e){j(e.list)?e.list=e.list.map(t=>P(t)?We(t):t):F(e.named)&&Object.keys(e.named).forEach(t=>{P(e.named[t])&&(e.named[t]=We(e.named[t]))})}function ft(e,t,n,s,l,c){const{messages:f,onWarn:d,messageResolver:E,localeFallbacker:L}=e,S=L(e,s,n);let O=W(),T,y=null;const b="translate";for(let C=0;C<S.length&&(T=S[C],O=f[T]||W(),(y=E(O,t))===null&&(y=O[t]),!(P(y)||fe(y)||H(y)));C++)if(!Hn(T,S)){const A=ke(e,t,T,c,b);A!==t&&(y=A)}return[y,T,O]}function _t(e,t,n,s,l,c){const{messageCompiler:f,warnHtmlMessage:d}=e;if(H(s)){const L=s;return L.locale=L.locale||n,L.key=L.key||t,L}if(f==null){const L=()=>s;return L.locale=n,L.key=t,L}const E=f(s,ur(e,n,l,s,d,c));return E.locale=n,E.key=t,E.source=s,E}function ir(e,t,n){return t(n)}function or(...e){const[t,n,s]=e,l=W();if(!P(t)&&!K(t)&&!H(t)&&!fe(t))throw x(B.INVALID_ARGUMENT);const c=K(t)?String(t):(H(t),t);return K(n)?l.plural=n:P(n)?l.default=n:U(n)&&!ge(n)?l.named=n:j(n)&&(l.list=n),K(s)?l.plural=s:P(s)?l.default=s:U(s)&&ie(l,s),[c,l]}function ur(e,t,n,s,l,c){return{locale:t,key:n,warnHtmlMessage:l,onError:f=>{throw c&&c(f),f},onCacheKey:f=>Rt(t,n,f)}}function fr(e,t,n,s){const{modifiers:l,pluralRules:c,messageResolver:f,fallbackLocale:d,fallbackWarn:E,missingWarn:L,fallbackContext:S}=e,T={locale:t,modifiers:l,pluralRules:c,messages:y=>{let b=f(n,y);if(b==null&&S){const[,,C]=ft(S,y,t,d,E,L);b=f(C,y)}if(P(b)||fe(b)){let C=!1;const g=_t(e,y,t,b,y,()=>{C=!0});return C?Qe:g}else return H(b)?b:Qe}};return e.processor&&(T.processor=e.processor),s.list&&(T.list=s.list),s.named&&(T.named=s.named),K(s.plural)&&(T.pluralIndex=s.plural),T}function wr(e,...t){const{datetimeFormats:n,unresolving:s,fallbackLocale:l,onWarn:c,localeFallbacker:f}=e,{__datetimeFormatters:d}=e,[E,L,S,O]=dr(...t),T=X(S.missingWarn)?S.missingWarn:e.missingWarn;X(S.fallbackWarn)?S.fallbackWarn:e.fallbackWarn;const y=!!S.part,b=be(e,S),C=f(e,l,b);if(!P(E)||E==="")return new Intl.DateTimeFormat(b,O).format(L);let A={},g,D=null;const i="datetime format";for(let _=0;_<C.length&&(g=C[_],A=n[g]||{},D=A[E],!U(D));_++)ke(e,E,g,T,i);if(!U(D)||!P(g))return s?De:E;let o=`${g}__${E}`;ge(O)||(o=`${o}__${JSON.stringify(O)}`);let m=d.get(o);return m||(m=new Intl.DateTimeFormat(g,ie({},D,O)),d.set(o,m)),y?m.formatToParts(L):m.format(L)}const _r=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function dr(...e){const[t,n,s,l]=e,c=W();let f=W(),d;if(P(t)){const E=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!E)throw x(B.INVALID_ISO_DATE_ARGUMENT);const L=E[3]?E[3].trim().startsWith("T")?`${E[1].trim()}${E[3].trim()}`:`${E[1].trim()}T${E[3].trim()}`:E[1].trim();d=new Date(L);try{d.toISOString()}catch{throw x(B.INVALID_ISO_DATE_ARGUMENT)}}else if(Ut(t)){if(isNaN(t.getTime()))throw x(B.INVALID_DATE_ARGUMENT);d=t}else if(K(t))d=t;else throw x(B.INVALID_ARGUMENT);return P(n)?c.key=n:U(n)&&Object.keys(n).forEach(E=>{_r.includes(E)?f[E]=n[E]:c[E]=n[E]}),P(s)?c.locale=s:U(s)&&(f=s),U(l)&&(f=l),[c.key||"",d,c,f]}function Fr(e,t,n){const s=e;for(const l in n){const c=`${t}__${l}`;s.__datetimeFormatters.has(c)&&s.__datetimeFormatters.delete(c)}}function vr(e,...t){const{numberFormats:n,unresolving:s,fallbackLocale:l,onWarn:c,localeFallbacker:f}=e,{__numberFormatters:d}=e,[E,L,S,O]=mr(...t),T=X(S.missingWarn)?S.missingWarn:e.missingWarn;X(S.fallbackWarn)?S.fallbackWarn:e.fallbackWarn;const y=!!S.part,b=be(e,S),C=f(e,l,b);if(!P(E)||E==="")return new Intl.NumberFormat(b,O).format(L);let A={},g,D=null;const i="number format";for(let _=0;_<C.length&&(g=C[_],A=n[g]||{},D=A[E],!U(D));_++)ke(e,E,g,T,i);if(!U(D)||!P(g))return s?De:E;let o=`${g}__${E}`;ge(O)||(o=`${o}__${JSON.stringify(O)}`);let m=d.get(o);return m||(m=new Intl.NumberFormat(g,ie({},D,O)),d.set(o,m)),y?m.formatToParts(L):m.format(L)}const Er=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function mr(...e){const[t,n,s,l]=e,c=W();let f=W();if(!K(t))throw x(B.INVALID_ARGUMENT);const d=t;return P(n)?c.key=n:U(n)&&Object.keys(n).forEach(E=>{Er.includes(E)?f[E]=n[E]:c[E]=n[E]}),P(s)?c.locale=s:U(s)&&(f=s),U(l)&&(f=l),[c.key||"",d,c,f]}function Wr(e,t,n){const s=e;for(const l in n){const c=`${t}__${l}`;s.__numberFormatters.has(c)&&s.__numberFormatters.delete(c)}}Tn();export{dr as A,wr as B,B as C,Ce as D,mr as E,vr as F,fe as G,H,Er as I,_r as J,ge as K,Cr as L,Ar as M,De as N,Sr as O,gr as P,he as Q,hr as R,Or as S,Mr as T,Rr as U,pr as V,U as a,P as b,j as c,Fe as d,v as e,Nr as f,_e as g,ie as h,X as i,K as j,W as k,Tr as l,Lr as m,se as n,Dr as o,F as p,Fr as q,Wr as r,Pr as s,br as t,kr as u,Ir as v,ze as w,yr as x,or as y,Ur as z};
