import{u as z}from"./vue-i18n-kWKo0idO.js";import{f as K,c as G}from"./index-DHV2tfOS.js";import{Q as J,T as W,i as y,N as I}from"./@inertiajs-Dt0-hqjZ.js";import{s as X,a as m}from"./primevue-CrCPcMFN.js";import{_ as Y}from"./AppLayout-DKZEmXIb.js";import{_ as w}from"./InputLabel-BTXevqr4.js";import{_ as M}from"./SearchInput-CdoSYJL3.js";import{_ as Z}from"./Pagination-Dmt48FUb.js";import{_ as P}from"./LoadingIcon-CesYxFkK.js";import{_ as U,a as T}from"./SecondaryButton-BoI1NwE9.js";import{_ as j}from"./RedButton-D21iPtqa.js";import{_ as k}from"./FixedSelectionBox-CkXOgkaT.js";import{s as h}from"./ziggy-js-C7EU8ifa.js";import{_ as ee}from"./GridContainer-n7ZDMxOZ.js";import{i as te,v as se,b as oe,c as g,o as d,l as r,k as v,K as C,S as i,a,a4 as o,P as u,O as S,F as D,R as re}from"./@vue-BnW70ngI.js";import"./@intlify-xvnhHnag.js";import"./moment-C5S46NFB.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./PrimaryButton-DE9sqoJj.js";import"./SelectionBox-CzAgH5wz.js";import"./@heroicons-BLousAGu.js";import"./@element-plus-CyTLADhX.js";import"./TextInput-C52bsWxF.js";import"./@headlessui-gOb5_P77.js";const le={class:"flex-1 flex items-center"},ae=["textContent"],ne={class:"p-6 sm:mx-2"},ie={class:"flex items-stretch"},ue={class:"mt-0 w-80 mr-8"},ce={class:"mt-0 w-80 mr-8"},de={class:"mt-0 w-64 mr-8"},pe={class:"mt-0 w-64 mr-8"},me={class:"mt-0 w-64 mr-8"},he=["innerHTML"],fe=["onClick"],ye=["onClick"],ve=["onClick"],Ce={key:3,class:"w-4"},be=["textContent"],we=["textContent"],ge={class:"flex items-center justify-end px-3 py-3"},$e=["textContent"],_e=["textContent"],xe=["textContent"],ke={class:"flex items-center justify-end px-3 py-3"},Se=["textContent"],_t={__name:"Index",props:{filters:Object,posts:Object},setup(c){const F=te("$toast"),$=J(),{t:b}=z(),f=c,l=W({search:f.filters.search??"",user:f.filters.user??"",reported:f.filters.reported??"",type:f.filters.type??"",topics:f.filters.topics??"",limit:f.filters.limit??10}),e=se({showSearchClearButton:(f.filters.search??"").trim().length>0,showUserSearchClearButton:(f.filters.user??"").trim().length>0,searching:!1,showModal:!1,open:!1,deleteId:null,deleting:!1,selectOptions:[{value:"yes",label:b("yes")},{value:"no",label:b("no")}],typeOptions:[{value:"answerr_q",label:b("answerrQ")},{value:"answerr_topic",label:b("answerrTopic")},{value:"other",label:b("other")}],showFeatureModal:!1,openFeature:!1,featureId:null,processing:!1,currentFeatureState:0,activePage:null,busy:oe(()=>e.activePage!==null||e.searching||l.processing)}),p=()=>{if(l.processing)return!1;l.transform(s=>G(s)).get(h("post.list"),{preserveScroll:!0,onSuccess:()=>{}})},O=()=>{e.showSearchClearButton=!0,p()},N=()=>{e.showUserSearchClearButton=!0,p()},E=()=>{l.search="",e.showSearchClearButton=!1,p()},L=()=>{l.user="",e.showUserSearchClearButton=!1,p()},q=s=>{e.deleteId=s,e.showModal=!0,setTimeout(()=>e.open=!0,150)},_=async()=>{e.open=!1,setTimeout(()=>e.showModal=!1,150)},x=async()=>{e.openFeature=!1,setTimeout(()=>e.showFeatureModal=!1,150)},A=()=>{e.deleting||I.post(h("post.delete"),{id:e.deleteId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{e.deleting=!0},onSuccess:()=>{e.deleteId=null,$.props.jetstream.flash.message&&F.success($.props.jetstream.flash.message),_()},onFinish:()=>{e.deleting=!1}})},B=(s,n)=>{e.featureId=s,e.showFeatureModal=!0,e.currentFeatureState=n,setTimeout(()=>e.openFeature=!0,150)},H=()=>{e.processing||I.post(h("post.toggleFeature"),{id:e.featureId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{e.processing=!0},onSuccess:()=>{e.featureId=null,$.props.jetstream.flash.message&&F.success($.props.jetstream.flash.message),x()},onFinish:()=>{e.processing=!1}})},Q=s=>{e.activePage=s,e.searching=!0},R=s=>{l.limit=s,l.search="",l.user="",l.reported="",l.type="",l.topics="",p()};return(s,n)=>(d(),g(D,null,[r(Y,{title:s.$t("listPost")},{header:i(()=>[a("div",le,[a("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:u(s.$t("listPost"))},null,8,ae),r(o(y),{class:"primary-button text-sm",href:o(h)("post.form"),textContent:u(s.$t("addNew"))},null,8,["href","textContent"])])]),default:i(()=>[a("div",ne,[a("div",ie,[a("div",ue,[r(w,{for:"search",value:s.$t("search")},null,8,["value"]),r(M,{id:"search",class:"mt-1 block w-full",modelValue:o(l).search,"onUpdate:modelValue":n[0]||(n[0]=t=>o(l).search=t),placeholder:s.$t("postSearch"),disabled:e.busy,"show-clear-button":e.showSearchClearButton,onInput:n[1]||(n[1]=t=>e.showSearchClearButton=!1),onClearSearch:E,onEnter:O},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),a("div",ce,[r(w,{for:"search-user",value:s.$t("createdBy")},null,8,["value"]),r(M,{id:"search-user",class:"mt-1 block w-full",modelValue:o(l).user,"onUpdate:modelValue":n[2]||(n[2]=t=>o(l).user=t),placeholder:s.$t("postUserSearch"),disabled:e.busy,"show-clear-button":e.showUserSearchClearButton,onInput:n[3]||(n[3]=t=>e.showUserSearchClearButton=!1),onClearSearch:L,onEnter:N},null,8,["modelValue","placeholder","disabled","show-clear-button"])]),a("div",de,[r(w,{for:"type-search",value:s.$t("postType"),class:"mb-1"},null,8,["value"]),r(k,{modelValue:o(l).type,"onUpdate:modelValue":n[4]||(n[4]=t=>o(l).type=t),placeholder:s.$t("all"),disabled:e.busy,options:e.typeOptions,clearable:!0,onCleared:p,onSelected:p},null,8,["modelValue","placeholder","disabled","options"])]),a("div",pe,[r(w,{for:"report-search",value:s.$t("reportStatus"),class:"mb-1"},null,8,["value"]),r(k,{modelValue:o(l).reported,"onUpdate:modelValue":n[5]||(n[5]=t=>o(l).reported=t),placeholder:s.$t("all"),disabled:e.busy,options:e.selectOptions,clearable:!0,onCleared:p,onSelected:p},null,8,["modelValue","placeholder","disabled","options"])]),a("div",me,[r(w,{for:"topics-search",value:s.$t("TOPICS"),class:"mb-1"},null,8,["value"]),r(k,{modelValue:o(l).topics,"onUpdate:modelValue":n[6]||(n[6]=t=>o(l).topics=t),placeholder:s.$t("all"),disabled:e.busy,options:e.selectOptions,clearable:!0,onCleared:p,onSelected:p},null,8,["modelValue","placeholder","disabled","options"])])]),r(ee,{loading:e.busy},{default:i(()=>[r(o(X),{value:c.posts.data},{empty:i(()=>[S(u(s.$t(c.filters.search&&c.filters.search!==""||c.filters.user&&c.filters.user!==""?"emptyResult":"emptyData")),1)]),default:i(()=>[r(o(m),{class:"number-column",header:s.$t("ID")},{body:i(({data:t})=>[r(o(y),{class:"hover:text-sky-600 hover:underline",textContent:u(t.post_id),href:o(h)("post.detail",{post:t.post_id})},null,8,["textContent","href"])]),_:1},8,["header"]),r(o(m),{class:"post-username-column",header:s.$t("createdBy")},{body:i(({data:t})=>[r(o(y),{class:"hover:text-sky-600 hover:underline",textContent:u(t.username),href:o(h)("post.list",{user:t.user_id})},null,8,["textContent","href"])]),_:1},8,["header"]),r(o(m),{class:"number-column extra",header:s.$t("createdByID")},{body:i(({data:t})=>[r(o(y),{class:"hover:text-sky-600 hover:underline",textContent:u(t.user_id),href:o(h)("post.list",{user:t.user_id})},null,8,["textContent","href"])]),_:1},8,["header"]),r(o(m),{class:"title-flex-column",header:s.$t("postContent")},{body:i(({data:t})=>[a("div",{innerHTML:t.content},null,8,he)]),_:1},8,["header"]),r(o(m),{class:"post-type-column",field:"tag",header:s.$t("postType")},null,8,["header"]),r(o(m),{class:"w-56",field:"community",header:s.$t("community")},null,8,["header"]),r(o(m),{class:"time-column",header:s.$t("postCreatedAt")},{body:i(({data:t})=>[S(u(o(K)(t.created_at)),1)]),_:1},8,["header"]),r(o(m),{class:"count-column",header:s.$t("answerCount")},{body:i(({data:t})=>[t.answer_count>0?(d(),v(o(y),{key:0,class:"hover:text-red-600 hover:underline",textContent:u(t.answer_count),href:o(h)("postAnswer.list",{post:t.post_id})},null,8,["textContent","href"])):(d(),g(D,{key:1},[S(u(t.answer_count),1)],64))]),_:1},8,["header"]),r(o(m),{class:"count-column",field:"report_count",header:s.$t("reportCount")},null,8,["header"]),r(o(m),{class:"status-column",field:"status_label",header:s.$t("status")},null,8,["header"]),r(o(m),{class:"action-column extra"},{body:i(({data:t})=>[r(o(y),{href:o(h)("post.detail",{post:t.post_id})},{default:i(()=>n[7]||(n[7]=[a("i",{class:"pi pi-info-circle text-gray-500 hover:text-sky-600"},null,-1)])),_:2},1032,["href"]),parseInt(t.status)!==0?(d(),g("i",{key:0,class:"pi pi-trash text-red-400 hover:text-red-600 hover:cursor-pointer",onClick:V=>q(t.post_id)},null,8,fe)):C("",!0),t.status===1?(d(),g("i",{key:1,class:re(["pi pi-flag hover:text-lime-700 hover:cursor-pointer",[t.featured===1?"text-lime-700":"text-gray-400"]]),onClick:V=>B(t.post_id,t.featured)},null,10,ye)):C("",!0),t.status===1&&(t.type==="answerr_topic"||t.type==="answerr_q")?(d(),v(o(y),{key:2,href:o(h)("post.form",{post:t.post_id})},{default:i(()=>[a("i",{class:"pi pi-pen-to-square text-sky-400 hover:text-sky-600 hover:cursor-pointer",onClick:V=>B(t.post_id,t.featured)},null,8,ve)]),_:2},1032,["href"])):(d(),g("i",Ce))]),_:1})]),_:1},8,["value"])]),_:1},8,["loading"]),c.posts.data.length>0?(d(),v(Z,{key:0,class:"mt-5 flex items-center justify-center mx-auto",links:c.posts.links,active:e.activePage,disabled:e.busy,limit:c.posts.per_page,total:c.posts.total,from:c.posts.from,to:c.posts.to,onProgress:Q,onChangeLimit:R},null,8,["links","active","disabled","limit","total","from","to"])):C("",!0)])]),_:1},8,["title"]),e.showModal?(d(),v(T,{key:0,show:e.open,onClose:_},{default:i(()=>[a("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:u(s.$t("confirm"))},null,8,be),a("div",{class:"border-t border-b p-4",textContent:u(s.$t("confirmDeletePostMessage"))},null,8,we),a("div",ge,[r(U,{class:"mr-3 text-sm",textContent:u(s.$t("cancel")),onClick:_},null,8,["textContent"]),r(j,{class:"text-sm overflow-hidden h-[34px]",onClick:A},{default:i(()=>[e.deleting?(d(),v(P,{key:0,class:"mr-1"})):C("",!0),a("span",{class:"text-sm text-white",textContent:u(s.$t("delete"))},null,8,$e)]),_:1})])]),_:1},8,["show"])):C("",!0),e.showFeatureModal?(d(),v(T,{key:1,show:e.openFeature,onClose:x},{default:i(()=>[a("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:u(s.$t("confirm"))},null,8,_e),a("div",{class:"border-t border-b p-4",textContent:u(s.$t("confirm"+(e.currentFeatureState===0?"EnableFeature":"DisableFeature")+"Message"))},null,8,xe),a("div",ke,[r(U,{class:"mr-3 text-sm",textContent:u(s.$t("cancel")),onClick:x},null,8,["textContent"]),r(j,{class:"text-sm overflow-hidden h-[34px]",onClick:H},{default:i(()=>[e.processing?(d(),v(P,{key:0,class:"mr-1"})):C("",!0),a("span",{class:"text-sm text-white",textContent:u(s.$t("confirm"))},null,8,Se)]),_:1})])]),_:1},8,["show"])):C("",!0)],64))}};export{_t as default};
