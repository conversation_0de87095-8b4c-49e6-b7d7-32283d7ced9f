import{g as m}from"./index-k9QJQake.js";import{Q as A,N as g,i as b}from"./@inertiajs-Dt0-hqjZ.js";import{t as N,p as V}from"./lodash-Bx_YDCCc.js";import{d as T}from"./@element-plus-CyTLADhX.js";import{_ as E}from"./AppLayout-CTb2MMqd.js";import{_ as F}from"./InputLabel-BTXevqr4.js";import{_ as z}from"./TextInput-DUNPEFms.js";import{_ as O}from"./Pagination-D56Hn3as.js";import{_ as Q}from"./LoadingIcon-CLD0VpVl.js";import{_ as M,a as R}from"./SecondaryButton-BoI1NwE9.js";import{_ as W}from"./PrimaryButton-DE9sqoJj.js";import{i as K,v as L,c as i,o as r,l as a,k as $,K as h,S as p,a as t,a4 as c,R as f,P as e,F as _,M as C}from"./@vue-BnW70ngI.js";import"./moment-C5S46NFB.js";/* empty css                                                    *//* empty css                                                                     */import"./app-65VXU7yX.js";import"./axios-t--hEgTQ.js";import"./laravel-vite-plugin-DEL3ZhID.js";import"./ziggy-js-C7EU8ifa.js";import"./pinia-Ddsh4R0D.js";import"./primevue-CrCPcMFN.js";import"./@primeuix-CKSY3gPt.js";import"./@primevue-BllOwQ3c.js";import"./@vueup-DIjuzNyW.js";import"./quill-D-mw74c0.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./quill-delta-D18WSM5Q.js";import"./fast-diff-DNDSwfiB.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./vue-i18n-kWKo0idO.js";import"./@intlify-xvnhHnag.js";import"./izitoast-CYQMso0-.js";import"./deepmerge-CxfS31y9.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./FixedSelectionBox-Bk5LSyGJ.js";import"./SelectionBox-D4JR3fGi.js";import"./@heroicons-BLousAGu.js";import"./@headlessui-gOb5_P77.js";const P=["textContent"],U={class:"py-6 px-6 sm:mx-2"},G={class:"flex items-center items-stretch"},H={class:"mt-0 w-80"},J={class:"w-full"},X={class:"text-left flex"},Y=["textContent"],Z=["textContent"],tt=["textContent"],et=["textContent"],ot=["textContent"],nt=["textContent"],st=["textContent"],lt=["textContent"],rt=["textContent"],it=["textContent"],at={class:"number-column"},ct=["textContent"],dt=["textContent"],ut=["textContent"],mt=["textContent"],ht=["textContent"],pt={class:"p-0 border-l",colspan:"2"},ft={key:0,class:"w-full h-full"},_t=["textContent"],xt=["textContent"],Ct={class:"p-0 border-l",colspan:"2"},vt={key:0,class:"w-full h-full"},yt=["textContent"],gt=["textContent"],bt={class:"border-l w-[56px]"},$t={key:1,class:"flex border-t"},kt=["textContent"],wt=["textContent"],St=["textContent"],It={class:"flex items-center justify-end px-3 py-3"},qt=["textContent"],Ve={__name:"Index",props:{filters:Object,attachedItems:Object},setup(d){const k=K("$toast"),v=A(),s=L({searched:d.filters.search??"",searching:!1,confirmDeletion:!1,open:!1,deleteId:null,deleting:!1}),w=N(()=>g.get(route("attachedSurvey.list"),V({search:s.searched}),{preserveState:!0,onBefore:()=>{s.searching=!0},onFinish:()=>{s.searching=!1}}),1e3),x=()=>{s.open=!1,setTimeout(()=>s.confirmDeletion=!1,150)},S=o=>{s.deleteId=o,s.confirmDeletion=!0,setTimeout(()=>s.open=!0,150)},I=()=>{s.deleting||g.post(route("attachedSurvey.delete"),{id:s.deleteId},{preserveScroll:!0,preserveState:!0,onBefore:()=>{s.deleting=!0},onSuccess:()=>{x(),s.deleteId=null,v.props.jetstream.flash.message&&k.success(v.props.jetstream.flash.message)},onFinish:()=>{s.deleting=!1}})},q=o=>{const l={};return o.forEach(n=>{l[n.question_id]||(l[n.question_id]={question_id:n.question_id,content:n.question_content,choices:[]}),l[n.question_id].choices.push({choice_id:n.choice_id,content:n.content})}),Object.keys(l).map(n=>l[n])};return(o,l)=>(r(),i(_,null,[a(E,{title:o.$t("listSurvey")},{header:p(()=>[t("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:e(o.$t("listAttachedSurvey"))},null,8,P),a(c(b),{class:"ml-auto flex items-center justify-center px-4 py-2 bg-sky-500 border border-transparent rounded-md font-semibold text-white uppercase transition ease-in-out duration-150 hover:bg-sky-600 focus:outline-none h-[38px]",textContent:e(o.$t("addNew")),href:o.route("attachedSurvey.create")},null,8,["textContent","href"])]),default:p(()=>[t("div",U,[t("div",G,[t("div",H,[a(F,{for:"search",value:o.$t("attachedSurveySearch")},null,8,["value"]),a(z,{class:"mt-1 block w-full",id:"search",modelValue:s.searched,"onUpdate:modelValue":l[0]||(l[0]=n=>s.searched=n),placeholder:"...",disabled:s.searching,onEnter:c(w)},null,8,["modelValue","disabled","onEnter"])])]),t("div",{class:f(["bg-white rounded-md shadow overflow-auto mt-5 flex flex-col",{"grid-loading":s.searching}])},[s.searching?(r(),$(Q,{key:0,class:"grid-loading-icon",size:24,color:"rgb(55 65 81)"})):h("",!0),t("table",J,[t("tr",X,[t("th",{class:"number-column",textContent:e(o.$t("number"))},null,8,Y),t("th",{class:"title-flex-column",textContent:e(o.$t("attachedSurveyTitle"))},null,8,Z),t("th",{class:"survey-id-column",textContent:e(o.$t("surveyID"))},null,8,tt),t("th",{class:"survey-title-column",textContent:e(o.$t("surveyTitle"))},null,8,et),t("th",{class:"attached-id-column",textContent:e(o.$t("surveyIDWasAttached"))},null,8,ot),t("th",{class:"survey-title-column",textContent:e(o.$t("surveyTitleWasAttached"))},null,8,nt),t("th",{class:"question-id-column",textContent:e(o.$t("questionID"))},null,8,st),t("th",{class:"question-content-column",textContent:e(o.$t("questionContent"))},null,8,lt),t("th",{class:"answer-id-column",textContent:e(o.$t("answerID"))},null,8,rt),t("th",{class:"answer-content-column",textContent:e(o.$t("answerContent"))},null,8,it),l[1]||(l[1]=t("th",{class:"w-[56px]"},null,-1))]),d.attachedItems.data.length>0?(r(!0),i(_,{key:0},C(d.attachedItems.data,(n,D)=>(r(),i("tr",{class:f(["flex border-t",[D%2===0?"bg-blue-50":""]])},[t("td",at,[a(c(b),{class:"hover:text-sky-600 hover:underline",textContent:e(n.attached_id),href:o.route("attachedSurvey.update",{attachedSurvey:n.attached_id})},null,8,["textContent","href"])]),t("td",{class:"title-flex-column",textContent:e(n.title)},null,8,ct),t("td",{class:"survey-id-column",textContent:e(c(m)(n.survey.survey_id,"S"))},null,8,dt),t("td",{class:"survey-title-column",textContent:e(n.survey.title)},null,8,ut),t("td",{class:"attached-id-column",textContent:e(c(m)(n.toSurvey.survey_id,"S"))},null,8,mt),t("td",{class:"survey-title-column",textContent:e(n.toSurvey.title)},null,8,ht),t("td",pt,[n.choices.length>0?(r(),i("table",ft,[(r(!0),i(_,null,C(q(n.choices),(u,j)=>(r(),i("tr",{class:f(["flex h-full",[j>0?"border-t":""]])},[t("td",{class:"question-id-column",textContent:e(c(m)(u.question_id,"Q"))},null,8,_t),t("td",{class:"question-content-column",textContent:e(u.content)},null,8,xt),t("td",Ct,[u.choices.length>0?(r(),i("table",vt,[(r(!0),i(_,null,C(u.choices,(y,B)=>(r(),i("tr",{class:f(["flex h-full",[B>0?"border-t":""]])},[t("td",{class:"answer-id-column",textContent:e(c(m)(y.choice_id,"A"))},null,8,yt),t("td",{class:"answer-content-column",textContent:e(y.content)},null,8,gt)],2))),256))])):h("",!0)])],2))),256))])):h("",!0)]),t("td",bt,[a(c(T),{class:"w-6 text-gray-500 transition ease-in-out duration-150 hover:cursor-pointer hover:text-red-500",onClick:u=>S(n.attached_id)},null,8,["onClick"])])],2))),256)):(r(),i("tr",$t,[t("td",{colspan:"11",textContent:e(o.$t(d.filters.search&&d.filters.search!==""?"emptyResult":"emptyData"))},null,8,kt)]))])],2),a(O,{class:"mt-5 flex items-center justify-center mx-auto",links:d.attachedItems.links},null,8,["links"])])]),_:1},8,["title"]),s.confirmDeletion?(r(),$(R,{key:0,show:s.open,closeable:!0,size:"lg","padding-vertical":"py-20",onClose:x},{default:p(()=>[t("div",{class:"pt-4 pb-3 px-3 font-semibold",textContent:e(o.$t("confirm"))},null,8,wt),t("div",{class:"border-t px-3 py-4 font-light",textContent:e(o.$t("attachedSurveyDeleteConfirmation"))},null,8,St),l[2]||(l[2]=t("div",{class:"border-t"},null,-1)),t("div",It,[a(M,{class:"mr-3 text-sm h-[38px]",textContent:e(o.$t("cancel")),onClick:x},null,8,["textContent"]),a(W,{class:"text-sm"},{default:p(()=>[t("span",{class:"text-sm",textContent:e(o.$t("delete")),onClick:I},null,8,qt)]),_:1})])]),_:1},8,["show"])):h("",!0)],64))}};export{Ve as default};
