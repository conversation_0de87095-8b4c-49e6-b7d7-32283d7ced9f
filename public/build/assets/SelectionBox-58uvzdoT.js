import{v as $,b as x,r as j,j as w,c,o as l,l as m,K as u,S as b,a as f,R as h,k as y,a4 as r,P as C,F as S,U as E,M as F}from"./@vue-BnW70ngI.js";import{_ as M}from"./LoadingIcon-CesYxFkK.js";import{r as D,a as H,b as I}from"./@heroicons-BLousAGu.js";import{r as N}from"./@element-plus-ccBf1-WH.js";import{t as P}from"./lodash-DBgjQQU6.js";import{_ as U}from"./TextInput-C52bsWxF.js";import{E as z,j as K,A as O,F as q,I as G}from"./@headlessui-gOb5_P77.js";const J={class:"flex items-stretch"},Q=["innerHTML"],W={class:"absolute inset-y-0 right-0 flex items-center pr-2"},X={key:0,class:"p-2 -mt-1"},Y={class:"max-h-60 overflow-auto"},Z=["textContent"],ee=["onClick"],te=["textContent"],ae={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-sky-600"},le={key:0,class:"flex flex-col ml-1.5"},se={key:0,innerHTML:" "},he={__name:"SelectionBox",props:{label:{type:String,default:""},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},showSelected:{type:Boolean,default:!0},placeholder:{type:String,default:""},modelValue:{default:"",validator(a){return a===null||typeof a=="string"||typeof a=="number"}},canRefresh:{type:Boolean,default:!0},canClear:{type:Boolean,default:!0},valueType:{type:String,default:"integer"},options:{type:Array,default:[]},enableSearch:{type:Boolean,default:!1},searchPlaceholder:{type:String,default:""}},emits:["update:modelValue","refresh","selected","cleared"],setup(a,{emit:B}){const s=a,p=B,t=$({loading:x(()=>s.loading),search:"",options:Array.isArray(s.options)?x(()=>{const e=[];return s.options.forEach(o=>{o.label.toLowerCase().includes(t.search.toLowerCase())&&e.push(o)}),e}):s.options,disabled:x(()=>s.loading||s.disabled)}),n=j({}),_=e=>s.valueType==="integer"?parseInt(e):e,k=e=>{n.value=t.options.find(o=>o.value===_(e))??{}};w(()=>s.modelValue,e=>{e?k(e):n.value={}},{immediate:!0}),w(()=>s.options,e=>{e?k(s.modelValue):n.value={}},{immediate:!0}),s.modelValue&&t.options.length&&k(s.modelValue);const A=P(()=>{if(t.disabled)return!1;p("refresh")},1500),L=e=>{if(e.preventDefault(),t.disabled)return!1;n.value=null,p("update:modelValue",null),p("cleared")},R=e=>{t.search="",p("update:modelValue",e.value),p("selected",e)},T=(e,o)=>{const i=["relative cursor-default select-none py-2 pr-4"];return i.push(e?"bg-sky-100 text-sky-900":"text-gray-900"),i.push(s.showSelected?"pl-10":"pl-4"),o>0&&i.push("border-t border-dashed"),i};return(e,o)=>(l(),c("div",J,[m(r(G),{modelValue:n.value,"onUpdate:modelValue":o[1]||(o[1]=i=>n.value=i),disabled:a.disabled},{default:b(({open:i})=>[f("div",{class:h(["relative flex-auto",[a.canRefresh?"select-box-refresh":"w-full"]])},[a.label?(l(),y(r(z),{key:0,as:"label",textContent:C(a.label)},null,8,["textContent"])):u("",!0),m(r(K),{class:h(["listbox-button",[t.disabled?"bg-gray-200":"bg-white",i&&!a.enableSearch?"border-sky-400 ring ring-sky-200":""]])},{default:b(()=>{var d,g,v;return[f("span",{class:h(["block truncate",{"text-gray-500":!((d=n.value)!=null&&d.value)}]),innerHTML:((g=n.value)==null?void 0:g.label)??a.placeholder},null,10,Q),f("span",W,[t.loading?(l(),y(M,{key:0,class:"opacity-50",color:"#000000"})):(l(),c(S,{key:1},[(v=n.value)!=null&&v.value&&a.canClear?(l(),y(r(D),{key:0,class:h(["w-4 h-4 text-gray-400 hover:text-orange-700 hover:cursor-pointer",{"pointer-events-none":t.loading}]),"aria-hidden":"true",onClick:L},null,8,["class"])):(l(),y(r(H),{key:1,class:"h-5 w-5 text-gray-400","aria-hidden":"true"}))],64))])]}),_:2},1032,["class"]),m(E,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:b(()=>[t.options.length>0||t.search!==""?(l(),y(r(O),{key:0,class:"listbox-options"},{default:b(()=>[a.enableSearch?(l(),c("div",X,[m(U,{autofocus:"",class:"w-full",modelValue:t.search,"onUpdate:modelValue":o[0]||(o[0]=d=>t.search=d),placeholder:a.searchPlaceholder},null,8,["modelValue","placeholder"])])):u("",!0),f("div",Y,[t.search!==""&&t.options.length===0?(l(),c("div",{key:0,class:"p-2 text-center",textContent:C(e.$t("emptyResult"))},null,8,Z)):u("",!0),(l(!0),c(S,null,F(t.options,(d,g)=>(l(),y(r(q),{key:d.value,value:d,as:"template"},{default:b(({active:v,selected:V})=>[f("li",{class:h(T(v,g)),onClick:oe=>R(d)},[f("span",{class:h([V?"font-semibold":"font-normal","block"]),textContent:C(d.label)},null,10,te),a.showSelected&&V?(l(),c("span",ae,[m(r(I),{class:"h-5 w-5","aria-hidden":"true"})])):u("",!0)],10,ee)]),_:2},1032,["value"]))),128))])]),_:1})):u("",!0)]),_:1})],2)]),_:1},8,["modelValue","disabled"]),a.canRefresh?(l(),c("div",le,[a.label?(l(),c("label",se)):u("",!0),f("button",{class:h(["rounded-md border border-gray-300 mt-1 p-2.5 focus:outline-none",{"bg-gray-200 cursor-default":t.disabled,"bg-white hover:bg-gray-100":!t.disabled}]),onClick:o[2]||(o[2]=()=>r(A)())},[m(r(N),{class:"w-5 h-5","aria-hidden":"true"})],2)])):u("",!0)]))}};export{he as _};
