import{b as g,c as m,o as l,l as e,a4 as o,S as i,a as r,K as y,_ as v,R as x,O as n,F as b}from"./@vue-BnW70ngI.js";import{T as h,Z as k,i as u}from"./@inertiajs-Dt0-hqjZ.js";import{A as _,a as w}from"./AuthenticationCardLogo-BnhG9BN9.js";import{_ as V}from"./PrimaryButton-DE9sqoJj.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const C={key:0,class:"mb-4 font-medium text-sm text-green-600"},E={class:"mt-4 flex items-center justify-between"},ot={__name:"VerifyEmail",props:{status:String},setup(d){const f=d,s=h({}),p=()=>{s.post(route("verification.send"))},c=g(()=>f.status==="verification-link-sent");return(a,t)=>(l(),m(b,null,[e(o(k),{title:"Email Verification"}),e(w,null,{logo:i(()=>[e(_)]),default:i(()=>[t[3]||(t[3]=r("div",{class:"mb-4 text-sm text-gray-600"}," Before continuing, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another. ",-1)),c.value?(l(),m("div",C," A new verification link has been sent to the email address you provided in your profile settings. ")):y("",!0),r("form",{onSubmit:v(p,["prevent"])},[r("div",E,[e(V,{class:x({"opacity-25":o(s).processing}),disabled:o(s).processing},{default:i(()=>t[0]||(t[0]=[n(" Resend Verification Email ")])),_:1},8,["class","disabled"]),r("div",null,[e(o(u),{href:a.route("profile.detail"),class:"underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},{default:i(()=>t[1]||(t[1]=[n(" Edit Profile")])),_:1},8,["href"]),e(o(u),{href:a.route("logout"),method:"post",as:"button",class:"underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ms-2"},{default:i(()=>t[2]||(t[2]=[n(" Log Out ")])),_:1},8,["href"])])])],32)]),_:1})],64))}};export{ot as default};
