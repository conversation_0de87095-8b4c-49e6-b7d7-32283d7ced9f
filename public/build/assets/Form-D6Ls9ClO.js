import{Q as j,T as B}from"./@inertiajs-Dt0-hqjZ.js";import{u as O}from"./vue-i18n-kWKo0idO.js";import{c as F}from"./@element-plus-CyTLADhX.js";import{_ as N}from"./AppLayout-DKZEmXIb.js";import{_ as P}from"./RedButton-D21iPtqa.js";import{_ as V}from"./LoadingIcon-CesYxFkK.js";import{_ as q}from"./InputLabel-BTXevqr4.js";import{_ as z}from"./TextInput-C52bsWxF.js";import{_ as u}from"./InputError-gQdwtcoE.js";import{_ as K}from"./SecondaryButton-BoI1NwE9.js";import{_ as C}from"./SurveySelectionBox-CYooY-yX.js";import{_ as D}from"./FixedSelectionBox-CkXOgkaT.js";import{i as I,v as L,b as R,e as W,k as p,o as n,S as _,a as c,c as m,K as f,l as i,a4 as s,F as v,M as G,R as g,P as y}from"./@vue-BnW70ngI.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./@intlify-xvnhHnag.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./PrimaryButton-DE9sqoJj.js";import"./pinia-Ddsh4R0D.js";import"./index-DHV2tfOS.js";import"./moment-C5S46NFB.js";import"./SelectionBox-CzAgH5wz.js";import"./@heroicons-BLousAGu.js";import"./@headlessui-gOb5_P77.js";const H=["textContent"],J=["textContent"],X={class:"max-w-7xl mx-auto py-6 px-6"},Y={class:"bg-white rounded-md shadow px-6 py-5 flex flex-col"},Z={class:"mt-0"},ee={class:"mt-5"},te={class:"mt-5"},se={key:0,class:"mt-5"},oe={class:"flex-1 mr-4 question-answer-select"},ae={class:"flex-1 ml-4 question-answer-select"},le={class:"ml-4 px-4 w-[56px]"},Ye={__name:"Form",props:{title:String,attachedSurvey:Object},setup(h){const{t:E}=O(),T=h,b=I("$toast"),w=j(),e=B(T.attachedSurvey),r=L({loading:!1,clearData:!1,clearToSurveyData:!1,exclude:[],questions:[],questionOptions:R(()=>{const t=[];return r.questions.forEach(o=>{t.push({label:o.content,value:o.question_id})}),t})}),U=()=>{if(e.processing)return!1;e.errors={},e.transform(t=>{const o={};return o.attached_id=t.attached_id,o.title=t.title,o.survey_id=t.survey_id,o.to_survey_id=t.to_survey_id,o.choices=[],e.choices.forEach(a=>{(a.show??!1)&&o.choices.push({question_id:a.question_id,choice_id:a.choice_id})}),o}).post(route("attachedSurvey.store"),{onSuccess:()=>{e.attached_id||(e.reset(),r.clearData=!0,r.clearToSurveyData=!0),w.props.jetstream.flash.message&&b.success(w.props.jetstream.flash.message)}})},k=()=>{e.choices.forEach(t=>{t.show=!1}),e.to_survey_id="",r.clearToSurveyData=!0,S()},$=async()=>{if(r.loading)return!1;r.loading=!0,await window.axios.post(route("survey.listQuestion"),{survey_id:e.to_survey_id}).then(t=>{r.questions=t.data}).catch(()=>{b.error(E("commonErrorMessage"))}).finally(()=>{r.loading=!1})},A=t=>{const o=[],a=r.questions.find(l=>l.question_id===t);return a&&a.choices.forEach(l=>{o.push({value:l.choice_id,label:l.content})}),o},M=t=>{e.choices[t].show=!1,e.choices[t].question_id="",e.choices[t].choice_id=""},S=()=>{e.choices.push({question_id:"",choice_id:"",show:!0})},Q=(t,o)=>{},x=t=>{let o=0;for(let a=0;a<e.choices.length;a++)if(e.choices[a].show){if(a===t)break;o++}return o};return W(()=>{e.attached_id&&$()}),(t,o)=>(n(),p(N,{title:h.title},{header:_(()=>[c("h2",{class:"text-xl text-gray-800 leading-tight mr-auto",textContent:y(h.title)},null,8,H),i(P,{class:g(["ml-auto",{"opacity-25":s(e).processing}]),disabled:s(e).processing,onClick:U},{default:_(()=>[s(e).processing?(n(),p(V,{key:0,class:"mr-2"})):f("",!0),c("span",{class:"text-sm",textContent:y(t.$t("save"))},null,8,J)]),_:1},8,["class","disabled"])]),default:_(()=>[c("div",X,[c("div",Y,[c("div",Z,[i(q,{for:"survey-title",value:t.$t("attachedSurveyTitle")},null,8,["value"]),i(z,{class:"mt-1 block w-full",id:"survey-title",modelValue:s(e).title,"onUpdate:modelValue":o[0]||(o[0]=a=>s(e).title=a),disabled:s(e).processing||r.loading,type:"text"},null,8,["modelValue","disabled"]),i(u,{message:s(e).errors.title},null,8,["message"])]),c("div",ee,[i(C,{class:"w-full",modelValue:s(e).survey_id,"onUpdate:modelValue":o[1]||(o[1]=a=>s(e).survey_id=a),label:t.$t("survey"),placeholder:t.$t("selectSurvey"),"clear-data":r.clearData,disabled:s(e).processing||r.loading,"enable-search":!0,"search-placeholder":t.$t("searchSurveyPlaceholder"),onSelected:k,onDataCleared:o[2]||(o[2]=a=>r.clearData=!1)},null,8,["modelValue","label","placeholder","clear-data","disabled","search-placeholder"]),i(u,{message:s(e).errors.survey_id},null,8,["message"])]),c("div",te,[i(C,{class:"w-full",modelValue:s(e).to_survey_id,"onUpdate:modelValue":o[3]||(o[3]=a=>s(e).to_survey_id=a),label:t.$t("surveyWasAttached"),placeholder:t.$t("selectSurvey"),"clear-data":r.clearToSurveyData,disabled:s(e).processing||r.loading||!s(e).survey_id,"enable-search":!0,"load-data":!1,exclude:[s(e).survey_id],"search-placeholder":t.$t("searchSurveyPlaceholder"),onSelected:$,onDataCleared:o[4]||(o[4]=a=>r.clearToSurveyData=!1)},null,8,["modelValue","label","placeholder","clear-data","disabled","exclude","search-placeholder"]),i(u,{message:s(e).errors.to_survey_id},null,8,["message"])]),s(e).to_survey_id?(n(),m("div",se,[r.loading?(n(),p(V,{key:0,color:"#000000",size:20})):(n(),m(v,{key:1},[i(q,{for:"survey-title",value:t.$t("answer")},null,8,["value"]),(n(!0),m(v,null,G(s(e).choices,(a,l)=>(n(),m(v,null,[a.show?(n(),m("div",{class:g(["flex w-full items-stretch",{"mt-3 pt-2 border-t border-dashed":l>0}]),key:"choice_"+l},[c("div",oe,[i(D,{modelValue:s(e).choices[l].question_id,"onUpdate:modelValue":d=>s(e).choices[l].question_id=d,placeholder:t.$t("selectQuestion"),disabled:s(e).processing,options:r.questionOptions,clearable:!1,onSelected:d=>Q(l,d)},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","options","onSelected"]),i(u,{message:s(e).errors["choices."+x(l)+".question_id"]},null,8,["message"])]),c("div",ae,[(n(),p(D,{key:"answer_choice_"+l,modelValue:s(e).choices[l].choice_id,"onUpdate:modelValue":d=>s(e).choices[l].choice_id=d,placeholder:t.$t("selectAnswer"),disabled:s(e).processing||!s(e).choices[l].question_id,options:A(s(e).choices[l].question_id),clearable:!1},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","options"])),i(u,{message:s(e).errors["choices."+x(l)+".choice_id"]},null,8,["message"])]),c("div",le,[i(s(F),{class:g(["w-5 text-gray-300 transition ease-in-out duration-150 mt-4",[s(e).choices.length>1?"hover:cursor-pointer hover:text-red-500":""]]),onClick:d=>M(l)},null,8,["class","onClick"])])],2)):f("",!0)],64))),256)),i(K,{class:"mt-3 text-sm h-[38px]",textContent:y(t.$t("addMoreAnswer")),onClick:S},null,8,["textContent"])],64))])):f("",!0)])])]),_:1},8,["title"]))}};export{Ye as default};
