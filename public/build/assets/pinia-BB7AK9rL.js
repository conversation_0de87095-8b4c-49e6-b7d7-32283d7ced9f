import{B,r as D,q as M,i as U,C as X,v as Y,D as O,E as V,t as Z,G as T,H as tt,j as et,n as st,I as nt,b as ot}from"./@vue-BnW70ngI.js";/*!
 * pinia v2.3.1
 * (c) 2025 <PERSON>
 * @license MIT
 */let q;const x=t=>q=t,H=Symbol();function N(t){return t&&typeof t=="object"&&Object.prototype.toString.call(t)==="[object Object]"&&typeof t.toJSON!="function"}var E;(function(t){t.direct="direct",t.patchObject="patch object",t.patchFunction="patch function"})(E||(E={}));function ht(){const t=B(!0),c=t.run(()=>D({}));let s=[],e=[];const r=M({install(a){x(r),r._a=a,a.provide(H,r),a.config.globalProperties.$pinia=r,e.forEach(u=>s.push(u)),e=[]},use(a){return this._a?s.push(a):e.push(a),this},_p:s,_a:null,_e:t,_s:new Map,state:c});return r}const ct=()=>{};function F(t,c,s,e=ct){t.push(c);const r=()=>{const a=t.indexOf(c);a>-1&&(t.splice(a,1),e())};return!s&&T()&&tt(r),r}function m(t,...c){t.slice().forEach(s=>{s(...c)})}const rt=t=>t(),A=Symbol(),I=Symbol();function J(t,c){t instanceof Map&&c instanceof Map?c.forEach((s,e)=>t.set(e,s)):t instanceof Set&&c instanceof Set&&c.forEach(t.add,t);for(const s in c){if(!c.hasOwnProperty(s))continue;const e=c[s],r=t[s];N(r)&&N(e)&&t.hasOwnProperty(s)&&!O(e)&&!V(e)?t[s]=J(r,e):t[s]=e}return t}const{assign:h}=Object;function ut(t){return!!(O(t)&&t.effect)}function at(t,c,s,e){const{state:r,actions:a,getters:u}=c,p=s.state.value[t];let S;function b(){p||(s.state.value[t]=r?r():{});const v=nt(s.state.value[t]);return h(v,a,Object.keys(u||{}).reduce((y,_)=>(y[_]=M(ot(()=>{x(s);const d=s._s.get(t);return u[_].call(d,d)})),y),{}))}return S=it(t,b,c,s,e,!0),S}function it(t,c,s={},e,r,a){let u;const p=h({actions:{}},s),S={deep:!0};let b,v,y=[],_=[],d;const R=e.state.value[t];D({});let k;function L(n){let o;b=v=!1,typeof n=="function"?(n(e.state.value[t]),o={type:E.patchFunction,storeId:t,events:d}):(J(e.state.value[t],n),o={type:E.patchObject,payload:n,storeId:t,events:d});const i=k=Symbol();st().then(()=>{k===i&&(b=!0)}),v=!0,m(y,o,e.state.value[t])}const K=function(){const{state:o}=s,i=o?o():{};this.$patch(j=>{h(j,i)})};function $(){u.stop(),y=[],_=[],e._s.delete(t)}const W=(n,o="")=>{if(A in n)return n[I]=o,n;const i=function(){x(e);const j=Array.from(arguments),P=[],w=[];function G(f){P.push(f)}function Q(f){w.push(f)}m(_,{args:j,name:i[I],store:l,after:G,onError:Q});let g;try{g=n.apply(this&&this.$id===t?this:l,j)}catch(f){throw m(w,f),f}return g instanceof Promise?g.then(f=>(m(P,f),f)).catch(f=>(m(w,f),Promise.reject(f))):(m(P,g),g)};return i[A]=!0,i[I]=o,i},z={_p:e,$id:t,$onAction:F.bind(null,_),$patch:L,$reset:K,$subscribe(n,o={}){const i=F(y,n,o.detached,()=>j()),j=u.run(()=>et(()=>e.state.value[t],P=>{(o.flush==="sync"?v:b)&&n({storeId:t,type:E.direct,events:d},P)},h({},S,o)));return i},$dispose:$},l=Y(z);e._s.set(t,l);const C=(e._a&&e._a.runWithContext||rt)(()=>e._e.run(()=>(u=B()).run(()=>c({action:W}))));for(const n in C){const o=C[n];if(!(O(o)&&!ut(o)||V(o))){if(typeof o=="function"){const i=W(o,n);C[n]=i,p.actions[n]=o}}}return h(l,C),h(Z(l),C),Object.defineProperty(l,"$state",{get:()=>e.state.value[t],set:n=>{L(o=>{h(o,n)})}}),e._p.forEach(n=>{h(l,u.run(()=>n({store:l,app:e._a,pinia:e,options:p})))}),R&&a&&s.hydrate&&s.hydrate(l.$state,R),b=!0,v=!0,l}/*! #__NO_SIDE_EFFECTS__ */function bt(t,c,s){let e,r;typeof t=="string"?(e=t,r=c):(r=t,e=t.id);function a(u,p){const S=X();return u=u||(S?U(H,null):null),u&&x(u),u=q,u._s.has(e)||at(e,r,u),u._s.get(e)}return a.$id=e,a}export{ht as c,bt as d};
