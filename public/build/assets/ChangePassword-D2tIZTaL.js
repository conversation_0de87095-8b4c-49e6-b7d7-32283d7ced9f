import{_ as e}from"./AppLayout-m_I9gnvX.js";import a from"./UpdatePasswordForm-ucHuJcn3.js";import{k as s,o as i,S as o,a as r,l as p,P as m}from"./@vue-BnW70ngI.js";import"./@inertiajs-BhKdJayA.js";import"./axios-t--hEgTQ.js";import"./deepmerge-4BhuujTU.js";import"./qs-CbAGxgEG.js";import"./nprogress-R_QVVDqq.js";import"./lodash.clonedeep-DCKevjwB.js";import"./lodash.isequal-BCJU-oNO.js";import"./vue-i18n-DNS8h1FH.js";import"./@intlify-TnaUIxGf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./SecondaryButton-BWHXZF7Q.js";import"./PrimaryButton-DE9sqoJj.js";import"./InputError-gQdwtcoE.js";import"./InputLabel-BTXevqr4.js";import"./LoadingIcon-CesYxFkK.js";import"./TextInput-C52bsWxF.js";const n=["textContent"],l={class:"max-w-7xl mx-auto py-10 px-6"},j={__name:"ChangePassword",setup(c){return(t,_)=>(i(),s(e,{title:t.$t("changePassword")},{header:o(()=>[r("h2",{class:"text-xl text-gray-800 leading-tight",textContent:m(t.$t("changePassword"))},null,8,n)]),default:o(()=>[r("div",l,[p(a,{user:t.$page.props.auth.user},null,8,["user"])])]),_:1},8,["title"]))}};export{j as default};
