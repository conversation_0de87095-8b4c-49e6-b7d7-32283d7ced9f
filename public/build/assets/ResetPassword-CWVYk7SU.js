import{c,o as f,l as s,a4 as e,S as m,a as t,_ as w,R as _,O as g,F as V}from"./@vue-BnW70ngI.js";import{T as k,Z as v}from"./@inertiajs-Dt0-hqjZ.js";import{A as b,a as x}from"./AuthenticationCardLogo-BnhG9BN9.js";import{_ as i}from"./InputError-gQdwtcoE.js";import{_ as l}from"./InputLabel-BTXevqr4.js";import{_ as y}from"./PrimaryButton-DE9sqoJj.js";import{_ as p}from"./TextInput-C52bsWxF.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const C={class:"mt-4"},P={class:"mt-4"},R={class:"flex items-center justify-end mt-4"},mo={__name:"ResetPassword",props:{email:String,token:String},setup(d){const n=d,o=k({token:n.token,email:n.email,password:"",password_confirmation:""}),u=()=>{o.post(route("password.update"),{onFinish:()=>o.reset("password","password_confirmation")})};return(S,r)=>(f(),c(V,null,[s(e(v),{title:"Reset Password"}),s(x,null,{logo:m(()=>[s(b)]),default:m(()=>[t("form",{onSubmit:w(u,["prevent"])},[t("div",null,[s(l,{for:"email",value:"Email"}),s(p,{id:"email",modelValue:e(o).email,"onUpdate:modelValue":r[0]||(r[0]=a=>e(o).email=a),type:"email",class:"mt-1 block w-full",required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),s(i,{class:"mt-2",message:e(o).errors.email},null,8,["message"])]),t("div",C,[s(l,{for:"password",value:"Password"}),s(p,{id:"password",modelValue:e(o).password,"onUpdate:modelValue":r[1]||(r[1]=a=>e(o).password=a),type:"password",class:"mt-1 block w-full",required:"",autocomplete:"new-password"},null,8,["modelValue"]),s(i,{class:"mt-2",message:e(o).errors.password},null,8,["message"])]),t("div",P,[s(l,{for:"password_confirmation",value:"Confirm Password"}),s(p,{id:"password_confirmation",modelValue:e(o).password_confirmation,"onUpdate:modelValue":r[2]||(r[2]=a=>e(o).password_confirmation=a),type:"password",class:"mt-1 block w-full",required:"",autocomplete:"new-password"},null,8,["modelValue"]),s(i,{class:"mt-2",message:e(o).errors.password_confirmation},null,8,["message"])]),t("div",R,[s(y,{class:_({"opacity-25":e(o).processing}),disabled:e(o).processing},{default:m(()=>r[3]||(r[3]=[g(" Reset Password ")])),_:1},8,["class","disabled"])])],32)]),_:1})],64))}};export{mo as default};
