import{r as v,v as H,c as a,o,a as l,l as r,J as Q,S as s,O as n,R as b,P as C,$ as B,n as Y,b as D,j as I,k as _,K as m,a4 as V,F as O,M as U}from"./@vue-BnW70ngI.js";import{Q as j,T as z,N as R}from"./@inertiajs-Dt0-hqjZ.js";import{_ as G}from"./ActionSection-d716unDa.js";import{_ as J}from"./DialogModal-LfgJQ09a.js";import{_ as M}from"./InputError-gQdwtcoE.js";import{_ as P}from"./PrimaryButton-DE9sqoJj.js";import{_ as S}from"./SecondaryButton-BoI1NwE9.js";import{_ as N}from"./TextInput-C52bsWxF.js";import{_ as W}from"./DangerButton-C49GvHso.js";import{_ as X}from"./InputLabel-BTXevqr4.js";import"./axios-t--hEgTQ.js";import"./deepmerge-CxfS31y9.js";import"./call-bind-apply-helpers-B4ICrQ1R.js";import"./function-bind-CHqF18-c.js";import"./es-errors-CFxpeikN.js";import"./qs-puzarlXf.js";import"./side-channel-DG-5PZt1.js";import"./object-inspect-Cfg_CA0t.js";import"./side-channel-list-BvdnDMxL.js";import"./side-channel-map-ru-_NPG8.js";import"./get-intrinsic-BFhK1_aj.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-D3FFaEao.js";import"./dunder-proto-Cj7W6A2l.js";import"./hasown-DwiY0sux.js";import"./call-bound-C_1-0vVo.js";import"./side-channel-weakmap-CGy7gfKF.js";import"./nprogress-CVH3SeWI.js";import"./lodash.clonedeep-DcBkkazC.js";import"./lodash.isequal-SGFeuw-r.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const Z={class:"mt-4"},g={__name:"ConfirmsPassword",props:{title:{type:String,default:"Confirm Password"},content:{type:String,default:"For your security, please confirm your password to continue."},button:{type:String,default:"Confirm"}},emits:["confirmed"],setup(h,{emit:T}){const x=T,u=v(!1),e=H({password:"",error:"",processing:!1}),d=v(null),w=()=>{axios.get(route("password.confirmation")).then(i=>{i.data.confirmed?x("confirmed"):(u.value=!0,setTimeout(()=>d.value.focus(),250))})},y=()=>{e.processing=!0,axios.post(route("password.confirm"),{password:e.password}).then(()=>{e.processing=!1,p(),Y().then(()=>x("confirmed"))}).catch(i=>{e.processing=!1,e.error=i.response.data.errors.password[0],d.value.focus()})},p=()=>{u.value=!1,e.password="",e.error=""};return(i,c)=>(o(),a("span",null,[l("span",{onClick:w},[Q(i.$slots,"default")]),r(J,{show:u.value,onClose:p},{title:s(()=>[n(C(h.title),1)]),content:s(()=>[n(C(h.content)+" ",1),l("div",Z,[r(N,{ref_key:"passwordInput",ref:d,modelValue:e.password,"onUpdate:modelValue":c[0]||(c[0]=F=>e.password=F),type:"password",class:"mt-1 block w-3/4",placeholder:"Password",autocomplete:"current-password",onKeyup:B(y,["enter"])},null,8,["modelValue"]),r(M,{message:e.error,class:"mt-2"},null,8,["message"])])]),footer:s(()=>[r(S,{onClick:p},{default:s(()=>c[1]||(c[1]=[n(" Cancel ")])),_:1}),r(P,{class:b(["ms-3",{"opacity-25":e.processing}]),disabled:e.processing,onClick:y},{default:s(()=>[n(C(h.button),1)]),_:1},8,["class","disabled"])]),_:1},8,["show"])]))}},ee={key:0,class:"text-lg font-medium text-gray-900"},te={key:1,class:"text-lg font-medium text-gray-900"},oe={key:2,class:"text-lg font-medium text-gray-900"},se={key:3},ae={key:0},re={class:"mt-4 max-w-xl text-sm text-gray-600"},ne={key:0,class:"font-semibold"},ie={key:1},le=["innerHTML"],ue={key:0,class:"mt-4 max-w-xl text-sm text-gray-600"},ce={class:"font-semibold"},de=["innerHTML"],me={key:1,class:"mt-4"},pe={key:1},fe={class:"grid gap-1 max-w-xl mt-4 px-4 py-4 font-mono text-sm bg-gray-100 rounded-lg"},ve={class:"mt-5"},ye={key:0},we={key:1},Ze={__name:"TwoFactorAuthenticationForm",props:{requiresConfirmation:Boolean},setup(h){const T=h,x=j(),u=v(!1),e=v(!1),d=v(!1),w=v(null),y=v(null),p=v([]),i=z({code:""}),c=D(()=>{var f;return!u.value&&((f=x.props.auth.user)==null?void 0:f.two_factor_enabled)});I(c,()=>{c.value||(i.reset(),i.clearErrors())});const F=()=>{u.value=!0,R.post(route("two-factor.enable"),{},{preserveScroll:!0,onSuccess:()=>Promise.all([L(),q(),$()]),onFinish:()=>{u.value=!1,e.value=T.requiresConfirmation}})},L=()=>axios.get(route("two-factor.qr-code")).then(f=>{w.value=f.data.svg}),q=()=>axios.get(route("two-factor.secret-key")).then(f=>{y.value=f.data.secretKey}),$=()=>axios.get(route("two-factor.recovery-codes")).then(f=>{p.value=f.data}),A=()=>{i.post(route("two-factor.confirm"),{errorBag:"confirmTwoFactorAuthentication",preserveScroll:!0,preserveState:!0,onSuccess:()=>{e.value=!1,w.value=null,y.value=null}})},E=()=>{axios.post(route("two-factor.recovery-codes")).then(()=>$())},K=()=>{d.value=!0,R.delete(route("two-factor.disable"),{preserveScroll:!0,onSuccess:()=>{d.value=!1,e.value=!1}})};return(f,t)=>(o(),_(G,null,{title:s(()=>t[1]||(t[1]=[n(" Two Factor Authentication ")])),description:s(()=>t[2]||(t[2]=[n(" Add additional security to your account using two factor authentication. ")])),content:s(()=>[c.value&&!e.value?(o(),a("h3",ee," You have enabled two factor authentication. ")):c.value&&e.value?(o(),a("h3",te," Finish enabling two factor authentication. ")):(o(),a("h3",oe," You have not enabled two factor authentication. ")),t[11]||(t[11]=l("div",{class:"mt-3 max-w-xl text-sm text-gray-600"},[l("p",null," When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application. ")],-1)),c.value?(o(),a("div",se,[w.value?(o(),a("div",ae,[l("div",re,[e.value?(o(),a("p",ne," To finish enabling two factor authentication, scan the following QR code using your phone's authenticator application or enter the setup key and provide the generated OTP code. ")):(o(),a("p",ie," Two factor authentication is now enabled. Scan the following QR code using your phone's authenticator application or enter the setup key. "))]),l("div",{class:"mt-4 p-2 inline-block bg-white",innerHTML:w.value},null,8,le),y.value?(o(),a("div",ue,[l("p",ce,[t[3]||(t[3]=n(" Setup Key: ")),l("span",{innerHTML:y.value},null,8,de)])])):m("",!0),e.value?(o(),a("div",me,[r(X,{for:"code",value:"Code"}),r(N,{id:"code",modelValue:V(i).code,"onUpdate:modelValue":t[0]||(t[0]=k=>V(i).code=k),type:"text",name:"code",class:"block mt-1 w-1/2",inputmode:"numeric",autofocus:"",autocomplete:"one-time-code",onKeyup:B(A,["enter"])},null,8,["modelValue"]),r(M,{message:V(i).errors.code,class:"mt-2"},null,8,["message"])])):m("",!0)])):m("",!0),p.value.length>0&&!e.value?(o(),a("div",pe,[t[4]||(t[4]=l("div",{class:"mt-4 max-w-xl text-sm text-gray-600"},[l("p",{class:"font-semibold"}," Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost. ")],-1)),l("div",fe,[(o(!0),a(O,null,U(p.value,k=>(o(),a("div",{key:k},C(k),1))),128))])])):m("",!0)])):m("",!0),l("div",ve,[c.value?(o(),a("div",we,[r(g,{onConfirmed:A},{default:s(()=>[e.value?(o(),_(P,{key:0,type:"button",class:b(["me-3",{"opacity-25":u.value}]),disabled:u.value},{default:s(()=>t[6]||(t[6]=[n(" Confirm ")])),_:1},8,["class","disabled"])):m("",!0)]),_:1}),r(g,{onConfirmed:E},{default:s(()=>[p.value.length>0&&!e.value?(o(),_(S,{key:0,class:"me-3"},{default:s(()=>t[7]||(t[7]=[n(" Regenerate Recovery Codes ")])),_:1})):m("",!0)]),_:1}),r(g,{onConfirmed:$},{default:s(()=>[p.value.length===0&&!e.value?(o(),_(S,{key:0,class:"me-3"},{default:s(()=>t[8]||(t[8]=[n(" Show Recovery Codes ")])),_:1})):m("",!0)]),_:1}),r(g,{onConfirmed:K},{default:s(()=>[e.value?(o(),_(S,{key:0,class:b({"opacity-25":d.value}),disabled:d.value},{default:s(()=>t[9]||(t[9]=[n(" Cancel ")])),_:1},8,["class","disabled"])):m("",!0)]),_:1}),r(g,{onConfirmed:K},{default:s(()=>[e.value?m("",!0):(o(),_(W,{key:0,class:b({"opacity-25":d.value}),disabled:d.value},{default:s(()=>t[10]||(t[10]=[n(" Disable ")])),_:1},8,["class","disabled"]))]),_:1})])):(o(),a("div",ye,[r(g,{onConfirmed:F},{default:s(()=>[r(P,{type:"button",class:b({"opacity-25":u.value}),disabled:u.value},{default:s(()=>t[5]||(t[5]=[n(" Enable ")])),_:1},8,["class","disabled"])]),_:1})]))])]),_:1}))}};export{Ze as default};
