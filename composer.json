{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "ext-gd": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-pdo": "*", "inertiajs/inertia-laravel": "^1.3", "intervention/image": "^3.8", "kreait/laravel-firebase": "^6.0", "laravel/framework": "^11.0", "laravel/horizon": "^5.30", "laravel/jetstream": "^5.1", "laravel/octane": "^2.5", "laravel/sanctum": "^4.0", "laravel/scout": "^10.11", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1", "tightenco/ziggy": "^2.0", "typesense/typesense-php": "^4.9"}, "require-dev": {"fakerphp/faker": "^1.23", "itsgoingd/clockwork": "^5.2", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^2.0", "pestphp/pest-plugin-laravel": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["helpers/helpers.php", "helpers/repositories.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}