APP_NAME=Laravel
APP_ENV=testing
APP_KEY=base64:O6dhxhGOwa3Mz915Fz18nLKVwCdSPaqWXb4ahRAiYy0=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=https://honne-dev.test

APP_LOCALE=vi
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# mysql test
#DB_CONNECTION=mysql
#DB_PORT=3306
#DB_DATABASE=homestead
#DB_USERNAME=homestead
#DB_PASSWORD=secret

# sqlite test
DB_CONNECTION=sqlite
DB_DATABASE=:memory:

# disable debug db
DB_LOG_QUERY=false

SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis

CACHE_STORE=file
CACHE_PREFIX=demo

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

OCTANE_SERVER=swoole
OCTANE_HTTPS=true

CHATWORK_ROOM_ID=369606297
CHATWORK_API_TOKEN=72f9f44731d86764ab3b5cbfcca49602
