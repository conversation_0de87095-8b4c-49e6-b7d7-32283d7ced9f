@use 'izitoast/dist/css/iziToast.css';
@use 'primeicons/primeicons.css';

.pi {
    font-size: 18px;
    height: 18px;
}

.iziToast {
    &-message {
        padding: 5px 0 !important;
        @apply font-sans text-base;
    }

    &-body {
        .iziToast-buttons {
            > button {
                font-size: 13px !important;
            }
        }
    }

    &-buttons {
        button:last-child {
            margin-left: 10px !important;
        }
    }

    &-color {
        &-green {
            background: rgba(166, 239, 184, 1) !important;
            border-color: rgba(166, 239, 184, 1) !important;
        }

        &-red {
            background: rgba(255, 175, 180, 1) !important;
            border-color: rgba(255, 175, 180, 1) !important;
        }

        &-orange {
            background: rgba(255, 211, 175, 1) !important;
            border-color: rgba(255, 211, 175, 1) !important;
        }
    }
}

main {
    height: calc(100vh - 70px - 65px - 2px); // 70 header + 65 top nav + 2px margin top
    overflow-y: auto;

    &.without-header {
        height: calc(100vh - 65px - 2px);
    }
}

.grid-loading {
    @apply relative;

    &:before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 98;
        @apply bg-black/15;
    }

    .grid-loading-icon {
        @apply absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 99;
    }
}

.pagination-limit-wrapper {
    .listbox-button {
        padding: 5px !important;
    }

    span.items-center.pr-2 {
        padding-right: 0 !important;
    }

    ul {
        @apply bottom-full mb-1 z-50;

        li {
            padding: 5px !important;

            &[aria-selected=true] {
                span.absolute {
                    @apply hidden;
                }
            }
        }
    }
}

th, td {
    @apply flex;
    @apply items-center;
    @apply px-4;
    @apply py-3;
}

.select-box-refresh {
    width: calc(100% - 48px);
}

.question-answer-select {
    width: calc(50% - 52px);
}

.modal-show {
    //transform: translateY(15px);
}

.input-clear-icon {
    width: 16px;
    height: 16px;
    top: 50%;
    right: 10px;
    font-size: 16px;
    transform: translateY(-50%);
}

td[class$="-column"] {
    @apply truncate;
}

.locale-vi {
    .number-column {
        @apply w-24;

        &.small {
            @apply w-20;
        }
    }

    .title-flex-column {
        @apply flex-1;
        @apply min-w-96;
        white-space: normal;
    }

    .survey-id-column,
    .answer-id-column {
        @apply w-28;
    }

    .attached-id-column,
    .question-id-column,
    .current-point-column {
        @apply w-32;
    }

    .survey-title-column {
        width: 390px;
    }

    .question-content-column {
        @apply w-96;
    }

    .post-type-column,
    .question-type-column,
    .phone-column,
    .role-column {
        @apply w-32;
    }

    .question-public-column,
    .status-column {
        @apply w-40;
    }

    .count-column,
    .point-column {
        width: 8rem;
    }

    .last-logged-time-column {
        @apply w-60;
    }

    .answer-content-column {
        @apply w-52;
    }

    .survey-table {
        .survey-title-column {
            @apply w-72;
        }
    }

    .time-column {
        @apply w-44;
    }

    .username-column {
        @apply flex-1;
        @apply w-60;
        max-width: 50rem;
        white-space: normal;
    }

    .post-username-column,
    .point-column {
        @apply w-44;
    }

    .action-column {
        @apply w-32;
        @apply flex;
        @apply items-center;
        @apply justify-between;

        a {
            display: flex;
            width: 18px;
            height: 18px;
        }

        &.small {
            @apply w-20;
        }

        &.extra {
            @apply w-40;
        }
    }
}

.locale-ja {
    th,
    .font-medium,
    .font-semibold {
        @apply font-normal;
    }

    .number-column {
        @apply w-28;

        &.extra {
            @apply w-32;
        }
    }

    .answer-id-column,
    .question-id-column {
        @apply w-24;

        &.user-id {
            @apply w-28;
        }
    }

    .title-flex-column {
        @apply flex-1;
        @apply w-96;
        white-space: normal;
    }

    .survey-id-column {
        @apply w-32;
    }

    .attached-id-column {
        @apply w-40;
    }

    .current-point-column {
        @apply w-32;
    }

    .survey-title-column {
        width: 390px;
    }

    .question-content-column {
        @apply w-96;
    }

    .post-username-column,
    .question-type-column {
        @apply w-40;
    }

    .role-column {
        @apply w-28;
    }

    .post-type-column {
        @apply w-52;
    }

    .question-public-column {
        @apply w-32;
    }

    .phone-column  {
        @apply w-40;
    }

    .status-column {
        @apply w-32;
    }

    .question-point-column {
        @apply w-44;
    }

    .answer-content-column {
        @apply w-52;
    }

    .survey-table {
        .survey-title-column {
            @apply w-72;
        }
    }

    .last-logged-time-column,
    .time-column {
        @apply w-44;
    }

    .username-column {
        @apply flex-1;
        @apply w-60;
        max-width: 50rem;
        white-space: normal;
    }

    .count-column,
    .point-column {
        @apply w-32;
    }

    .action-column {
        @apply w-32;
        @apply min-w-32;
        @apply flex;
        @apply items-center;
        @apply justify-between;

        a {
            display: flex;
            width: 18px;
            height: 18px;
        }

        &.small {
            @apply w-20;
        }

        &.extra {
            @apply w-40;
        }
    }
}

.normal-case {
    text-transform: none !important;
}

.p-tabpanels {
    &.p-0 {
        padding: 0 !important;
    }
}

.p-datatable-table-container {
    @apply w-full;
}

.p-datepicker-day-view,
.p-datatable-table {
    tr {
        @apply flex;

        td {
            @apply border-none !important;
        }
    }

    &.w-border {
        tr, td, th {
            border-style: solid !important;
        }
    }
}

.p-datatable-table {
    tbody {
        tr {
            @apply border-t odd:bg-blue-50 even:bg-white;
        }
    }

    .history-username-column {
        @apply w-64;
    }
}

.p-datepicker {
    @apply w-full !important;
}

.p-inputtext {
    @apply px-3 py-0 h-[42px] text-base border-gray-300 rounded-md shadow-sm placeholder-gray-400 font-light transition !important;
    @apply w-full focus:outline-none focus:border-sky-400 focus:ring focus:ring-sky-200 disabled:bg-gray-200 !important;
}

.action-column {
    .p-datatable-column-header-content {
        @apply w-full;
        display: flex;
        justify-content: space-between !important;
    }
}

button, a {
    &.primary-button {
        @apply flex items-center justify-center px-4 py-2 bg-sky-500 border border-transparent rounded-md;
        @apply text-white uppercase transition ease-in-out duration-150 hover:bg-sky-600 focus:outline-none;
    }

    &.red-button {
        @apply flex items-center justify-center px-4 py-2 bg-red-400 border border-transparent rounded-md;
        @apply font-semibold text-white uppercase transition ease-in-out duration-150 hover:bg-red-600 focus:outline-none;
    }

    &.secondary-button {
        @apply inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700;
        @apply uppercase shadow-sm hover:bg-gray-100 focus:outline-none disabled:opacity-25 transition ease-in-out duration-150;
    }
}

.link {
    &-active {
        @apply inline-flex items-center px-1 pt-1 border-b-2 border-indigo-400 leading-5 text-gray-900 focus:outline-none focus:border-indigo-700 transition duration-150 ease-in-out;
    }

    &-normal {
        @apply inline-flex items-center px-1 pt-1 border-b-2 border-transparent leading-5 text-gray-500 hover:text-gray-700 hover:border-gray-300 focus:outline-none focus:text-gray-700 focus:border-gray-300 transition duration-150 ease-in-out;
    }

    &-responsive {
        @apply block w-full ps-3 pe-4 py-2 border-l-4 border-transparent text-start text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-300 focus:outline-none focus:text-gray-800 focus:bg-gray-50 focus:border-gray-300 transition duration-150 ease-in-out;

        &-active {
            @apply block w-full ps-3 pe-4 py-2 border-l-4 border-indigo-400 text-start text-base font-medium text-indigo-700 bg-indigo-50 focus:outline-none focus:text-indigo-800 focus:bg-indigo-100 focus:border-indigo-700 transition duration-150 ease-in-out;
        }
    }
}

.input {
    &-text {
        @apply w-full border-gray-300 rounded-md shadow-sm placeholder-gray-400 font-light transition;
        @apply focus:outline-none focus:border-sky-400 focus:ring focus:ring-sky-200 disabled:bg-gray-200;
    }

    &-area {
        @apply border-gray-300 rounded-md shadow-sm placeholder-gray-400 font-light transition;
        @apply focus:outline-none focus:border-sky-400 focus:ring focus:ring-sky-200 disabled:bg-gray-200;
    }
}

.listbox {
    &-button {
        @apply relative w-full cursor-default rounded-md py-2 pl-3 pr-10 text-left border border-gray-300 mt-0 transition focus:outline-none;
    }

    &-options {
        @apply absolute mt-1.5 w-full overflow-hidden rounded-md bg-white py-1 text-base shadow-md focus:outline-none divide-y border border-gray-200 z-50;
    }
}

.ql {
    &-toolbar {
        @apply rounded-t-md;
    }

    &-container {
        @apply bg-white rounded-b-md;;
    }

    &-editor {
        @apply h-full flex-1 font-sans text-base;
    }
}

.news {
    &-editor-container {
        height: calc(100vh - 311px);
    }

    &-title-column {
        @apply w-80;
    }
}

.button {
    &-select-file {
        @apply secondary-button text-sm;
        @apply py-1 normal-case !important;
    }
}

.community-form {
    &-modal {
        > .h-screen {
            @apply items-center;
        }

        .modal-content {
            @apply -translate-y-36;
        }
    }

    &-container {
        > div {
            @apply border-none !important;
        }
    }
}
