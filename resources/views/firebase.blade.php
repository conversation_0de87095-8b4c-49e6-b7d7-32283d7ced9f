<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Firebase Cloud Message</title>

    <script type="module">
        // Import the functions you need from the SDKs you need
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.0.1/firebase-app.js";
        import { getAuth, RecaptchaVerifier, signInWithPhoneNumber } from "https://www.gstatic.com/firebasejs/11.0.1/firebase-auth.js";
        // https://firebase.google.com/docs/web/setup#available-libraries

        // Your web app's Firebase configuration
        // For Firebase JS SDK v7.20.0 and later, measurementId is optional
        const firebaseConfig = {
            apiKey: "AIzaSyDVGoyjhOFzPt9GPQHXINe25AyborNnS9A",
            authDomain: "honne-app-dev.firebaseapp.com",
            projectId: "honne-app-dev",
            storageBucket: "honne-app-dev.firebasestorage.app",
            messagingSenderId: "175592235078",
            appId: "1:175592235078:web:1f34bd7cb6f110ee24aa47",
            measurementId: "G-5HJ3219JN9"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        auth.languageCode = 'jp';
        /*const messaging = getMessaging(app);

        getToken(messaging, {
            vapidKey: "BK7LeWvY0l8DxHEXDI7WCnSX1sSk_1spVTWDdxLU2jL2SU5-bQSvzuGYkwRBYVEb_a8M1MM3SKFwDFcotfB-vHA",
        }).then(currentToken => {
            console.log(currentToken);
        });*/

        window.recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
            'size': 'invisible',
            'callback': (response) => {
                console.log(response);
            }
        });

        const signIn = () => {
            const phoneField = document.getElementById('phone-number');
            const appVerifier = window.recaptchaVerifier;

            if (! phoneField.value) {
                alert('Please enter your phone number.');
                return;
            }

            signInWithPhoneNumber(auth, phoneField.value, appVerifier)
                .then((confirmationResult) => {
                    window.confirmationResult = confirmationResult;

                    alert('OTP has been sent to your phone number. Please enter the code to sign in.')
                }).catch((error) => {
                    console.log(error);

                    window.recaptchaVerifier.render().then(function(widgetId) {
                        grecaptcha.reset(widgetId);
                    });
                });
        }

        const verifyCode = () => {
            if (typeof window.confirmationResult === 'undefined') {
                alert('Please sign in first.');
                return;
            }

            const otpField = document.getElementById('otp-number');
            if (! otpField.value) {
                alert('Please enter your OTP.');
                return;
            }

            window.confirmationResult.confirm(otpField.value).then((result) => {
                // User signed in successfully.
                const user = result.user;

                console.log(user.accessToken);
            }).catch((error) => {
                console.log(error);
            });
        }

        const sentOtpBtn = document.getElementById('sent-otp-btn');
        sentOtpBtn.addEventListener('click', signIn);

        const verifyOtpBtn = document.getElementById('verify-otp-bn');
        verifyOtpBtn.addEventListener('click', verifyCode);
    </script>
</head>
<body>
    <div>
        <label>Phone number</label>
        <input id="phone-number" type="text" />
    </div>
    <div>
        <button id="sent-otp-btn">Sent OTP</button>
    </div>

    <div style="margin-top: 20px">
        <label>OTP</label>
        <input id="otp-number" type="text" />
    </div>
    <div>
        <button id="verify-otp-bn">Login</button>
    </div>
    <div id="recaptcha-container"></div>
</body>
</html>
