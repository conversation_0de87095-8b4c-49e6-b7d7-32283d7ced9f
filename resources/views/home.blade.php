@php
/** @var $isApple $bool */
$storeLink = config('app.iosAppStoreLink');
@endphp

@extends('layouts.main')

@section('content')
    <section class="hero">
        <div class="container">
            <h1>”聞く”を、もっと自然に</h1>
            <p>answerr（アンサー）は、応えることでつながる次世代のSNSアプリ。</p>
            <p>あなたの”困った”はanswerrのみんなが解決します。</p>
            <a href="{{ $storeLink }}" class="cta-button">無料で始める</a>
            <div><br>
                <img src="/assets/images/Answerr_Lock.jpg" alt="Answerr" title="Answerr" width="300" />
            </div>
        </div>
    </section>

    <section class="features" id="features">
        <div class="container">
            <h2 class="section-title">機能紹介</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>興味ベースのQ&Aマッチング</h3>
                    <p>趣味や興味に基づいて、あなたと相性の良い人々を自動でマッチング。Q&Aで自然な会話が生まれます。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <h3>画像で伝わる</h3>
                    <p>「困った」を解決すだけじゃない、画像があるからあなたの想いが伝わります。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>プライバシー重視</h3>
                    <p>業界最高水準のセキュリティとプライバシー設定で、安心して利用できる環境を提供</p>
                </div>
            </div>
        </div>
    </section>

    <section class="testimonials" id="testimonials">
        <div class="container">
            <h2 class="section-title">利用者の声</h2>
            <div class="testimonial-cards">
                <div class="testimonial-card">
                    <p class="testimonial-text">今まで色々なSNSを試しましたが、answerrが一番自分に合っていると感じました。質問に答えることで誰かの役に立てるのが嬉しいです！</p>
                    <p class="testimonial-author">30代・女性</p>
                </div>
                <div class="testimonial-card">
                    <p class="testimonial-text">趣味が合う人とすぐに繋がれて、情報交換が活発にできるのが良いですね。画像で質問できるのも便利です。</p>
                    <p class="testimonial-author">20代・男性</p>
                </div>
                <div class="testimonial-card">
                    <p class="testimonial-text">困った時に気軽に質問できるのが本当に助かります。回答も丁寧な方が多くて、安心して利用できます。</p>
                    <p class="testimonial-author">40代・女性</p>
                </div>
            </div>
        </div>
    </section>

    <section class="how-it-works" id="how-it-works">
        <div class="container">
            <h2 class="section-title">使い方</h2>
            <div class="steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h3>質問を投稿</h3>
                    <p>知りたいこと、困っていることを気軽に投稿してみましょう。画像も追加できます。</p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h3>回答をチェック</h3>
                    <p>他のユーザーからの回答をチェックしましょう。役に立った回答には感謝を伝えましょう。</p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h3>誰かの質問に答える</h3>
                    <p>あなたの知識や経験を活かして、他のユーザーの質問に答えてみましょう。新しい繋がりが生まれるかも。</p>
                </div>
            </div>
        </div>
    </section>

    <section class="download" id="download">
        <div class="container">
            <h2>今すぐanswerrをダウンロード！</h2>
            <p>あなたの”聞きたい”と”応えたい”を繋げます。</p>
            <div class="app-stores">
                <a href="{{ $storeLink }}" class="store-badge">
                    <i class="fab fa-apple"></i>
                    <div class="store-text">
                        <small>App Store から</small>
                        <span>ダウンロード</span>
                    </div>
                </a>
            </div>
        </div>
    </section>
@endsection
