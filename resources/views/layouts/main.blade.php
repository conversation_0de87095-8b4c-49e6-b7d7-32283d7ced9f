@php
/** @var $isApple $bool */
$storeLink = config($isApple ? 'app.iosAppStoreLink' : 'app.androidAppStoreLink');
@endphp
<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>answerr - 応えることで始まるSNS</title>
    <style>
        /* 省略: 既存の CSS スタイル */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Helvetica Neue', Arial, sans-serif;
        }

        body {
            padding-top: 80px;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px; /* 利用規約向けに少し狭く調整 */
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: linear-gradient(135deg, #6e8efb, #a777e3);
            color: white;
            padding: 20px 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 28px;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .logo a {
            color: white;
            text-decoration: none;
        }

        .logo a:hover {
            text-decoration: underline;
        }

        .logo span {
            color: #ffcc00;
        }

        .cta-button {
            background-color: #ffcc00;
            color: #333;
            padding: 12px 24px;
            border-radius: 30px;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .cta-button:hover {
            background-color: #ffdd33;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .hero {
            background: linear-gradient(135deg, #a777e3, #6e8efb);
            color: white;
            padding: 80px 0 100px;
            text-align: center;
        }

        .hero h1 {
            font-size: 48px;
            margin-bottom: 20px;
            font-weight: 800;
        }

        .hero p {
            font-size: 20px;
            max-width: 700px;
            margin: 0 auto 40px;
        }

        .app-screenshot {
            width: 300px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin-top: 40px;
            border: 10px solid #fff;
        }

        .features {
            padding: 100px 0;
            background-color: #f9f9f9;
        }

        .section-title {
            text-align: center;
            font-size: 36px;
            margin-bottom: 60px;
            color: #333;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .feature-card {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            text-align: center;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        .feature-icon {
            font-size: 40px;
            margin-bottom: 20px;
            color: #6e8efb;
        }

        .feature-card h3 {
            font-size: 22px;
            margin-bottom: 15px;
            color: #333;
        }

        .testimonials {
            padding: 100px 0;
            background-color: #fff;
        }

        .testimonial-cards {
            display: flex;
            overflow-x: auto;
            gap: 20px;
            padding: 20px 0;
        }

        .testimonial-card {
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 30px;
            min-width: 300px;
            max-width: 400px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .testimonial-text {
            font-style: italic;
            margin-bottom: 20px;
        }

        .testimonial-author {
            font-weight: bold;
            color: #6e8efb;
        }

        .how-it-works {
            padding: 100px 0;
            background-color: #f9f9f9;
            text-align: center;
        }

        .steps {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            margin-top: 50px;
        }

        .step {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            width: 250px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            position: relative;
        }

        .step-number {
            background-color: #6e8efb;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
        }

        .step h3 {
            margin: 20px 0 15px;
        }

        .download {
            padding: 100px 0;
            background: linear-gradient(135deg, #6e8efb, #a777e3);
            color: white;
            text-align: center;
        }

        .app-stores {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
        }

        .store-badge {
            background-color: black;
            color: white;
            display: flex;
            align-items: center;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            gap: 10px;
        }

        .store-badge i {
            font-size: 30px;
        }

        .store-text {
            text-align: left;
        }

        .store-text small {
            display: block;
            font-size: 12px;
        }

        .store-text span {
            font-size: 18px;
            font-weight: bold;
        }

        .policy-content,
        .terms-content {
            padding: 60px 0; /* コンテンツの上下余白 */
        }

        .policy-content h1,
        .terms-content h1 {
            font-size: 36px;
            margin-bottom: 30px;
            text-align: center;
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 15px;
        }

        .policy-content h2,
        .terms-content h2 {
            font-size: 24px;
            margin-top: 40px;
            margin-bottom: 15px;
            color: #444;
            border-left: 5px solid #6e8efb;
            padding-left: 10px;
        }

        .policy-content h3 {
            font-size: 18px;
            margin-top: 30px;
            margin-bottom: 10px;
            color: #555;
        }

        .policy-content div, .policy-content ul, .policy-content ol,
        .terms-content div, .terms-content ul {
            margin-bottom: 15px;
            color: #555;
        }

        .policy-content ul, .policy-content ol,
        .terms-content ul, .terms-content ol {
            margin-left: 25px;
        }

        .policy-content li,
        .terms-content li {
            margin-bottom: 8px;
        }

        ul li {
            list-style-type: number;
        }

        ol li {
            list-style-type: circle;
        }

        .update-date {
            text-align: right;
            margin-bottom: 30px;
            color: #777;
            font-size: 14px;
        }

        footer {
            background-color: #333;
            color: white;
            padding: 50px 0;
            text-align: center;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .social-links a {
            color: white;
            font-size: 24px;
            transition: color 0.3s ease;
        }

        .social-links a:hover {
            color: #ffcc00;
        }

        .footer-links {
            max-width: 600px;
            margin: 0 auto 30px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
        }

        .footer-links a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: white;
        }

        .copyright {
            color: #999;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .hero h1 {
                font-size: 36px;
            }
            .hero p {
                font-size: 18px;
            }
            .app-screenshot {
                width: 250px;
            }
            .features-grid {
                grid-template-columns: 1fr;
            }
            .steps {
                flex-direction: column;
                align-items: center;
            }
            .step {
                width: 100%;
                max-width: 300px;
            }

            .terms-content h1,
            .policy-content h1 {
                font-size: 28px;
            }

            .terms-content h2,
            .policy-content h2 {
                font-size: 20px;
            }

            .container {
                padding: 0 15px;
            }
        }
    </style>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
</head>
<body>
    @include('layouts.partials.header')

    @yield('content')

    @include('layouts.partials.footer')
</body>
</html>
