<script setup>
import {computed, inject, reactive} from "vue";
import {clearEmptyData, formatDate} from "@/Utils/index.js";
import { Link as InertiaLink, router, useForm, usePage } from '@inertiajs/vue3';

import DataTable from 'primevue/datatable';
import Column from 'primevue/column';

import AppLayout from '@/Layouts/AppLayout.vue';
import InputLabel from "@/Components/InputLabel.vue";
import SearchInput from "@/Components/SearchInput.vue";
import Pagination from "@/Components/Pagination.vue";
import LoadingIcon from "@/Components/LoadingIcon.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import RedButton from "@/Components/RedButton.vue";
import Modal from "@/Components/Modal.vue";
import FixedSelectionBox from "@/Components/FixedSelectionBox.vue";
import { route } from "ziggy-js";
import GridContainer from "@/Components/GridContainer.vue";

const $toast = inject('$toast');
const page = usePage();

const props = defineProps({
    filters: Object,
    news: Object,
});

const form = useForm({
    search: props.filters['search'] ?? '',
    status: props.filters['status'] ?? '',
    limit: props.filters['limit'] ?? 10,
});

const config = reactive({
    showSearchClearButton: (props.filters.search ?? '').trim().length > 0,
    searching: false,
    showModal: false,
    open: false,
    deleteId: null,
    deleting: false,
    selectOptions: [
        {
            value: 'enable',
            label: 'アクティブ',
        },
        {
            value: 'disable',
            label: '削除',
        },
    ],
    activePage: null,
    busy: computed(() => config.activePage !== null || config.searching || form.processing),
});

const _search = () => {
    if (form.processing) {
        return false;
    }

    form.transform((data) => clearEmptyData(data)).get(route('news.list'), {
        preserveScroll: true,
        onSuccess: () => {},
    });
}

const search = () => {
    config.showSearchClearButton = true;
    _search();
}

const clearSearch = () => {
    form.search = '';
    config.showSearchClearButton = false;
    _search();
}

const confirmDeleteNews = (id) => {
    config.deleteId = id;
    config.showModal = true;
    setTimeout(() => config.open = true, 150);
}

const closeModal = async () => {
    config.open = false;
    setTimeout(() => config.showModal = false, 150);
}

const deleteNews = () => {
    if (config.deleting) {
        return;
    }

    router.post(route('news.delete'), {
        id: config.deleteId,
    }, {
        preserveScroll: true,
        preserveState: true,
        onBefore: () => {
            config.deleting = true;
        },
        onSuccess: () => {
            config.deleteId = null;

            if (page.props.jetstream.flash.message) {
                $toast.success(page.props.jetstream.flash.message);
            }

            closeModal();
        },
        onFinish: () => {
            config.deleting = false;
        },
    });
}

const updateProgress = (progress) => {
    config.activePage = progress;
    config.searching = true;
};

const changeLimit = (limit) => {
    form.limit = limit;
    form.search = '';
    form.status = '';

    _search();
}
</script>

<template>
    <app-layout :title="$t('listNews')">
        <template v-slot:header>
            <div class="flex-1 flex items-center">
                <h2
                    class="text-xl text-gray-800 leading-tight mr-auto"
                    v-text="$t('listNews')"
                />

                <inertia-link class="primary-button text-sm" :href="route('news.form', { news: '' })" v-text="$t('addNew')" />
            </div>
        </template>

        <div class="p-6 sm:mx-2">
            <div class="flex items-stretch">
                <div class="mt-0 w-80 mr-8">
                    <input-label for="search" :value="$t('search')" />

                    <search-input
                        id="search"
                        class="mt-1 block w-full"
                        v-model="form.search"
                        :placeholder="$t('newsSearch')"
                        :disabled="config.busy"
                        :show-clear-button="config.showSearchClearButton"
                        @input="config.showSearchClearButton = false"
                        @clear-search="clearSearch"
                        @enter="search"
                    />
                </div>

                <div class="mt-0 w-64 mr-8">
                    <input-label for="status-search" :value="$t('status')" class="mb-1" />

                    <fixed-selection-box
                        v-model="form.status"
                        :placeholder="$t('all')"
                        :disabled="config.busy"
                        :options="config.selectOptions"
                        :clearable="true"
                        @cleared="_search"
                        @selected="_search"
                    />
                </div>
            </div>

            <grid-container :loading="config.busy">
                <data-table :value="news['data']">
                    <column class="number-column small" field="news_id" :header="$t('ID')" />

                    <column class="time-column" :header="$t('newsCreatedAt')">
                        <template #body="{ data }">
                            {{ formatDate(data['created_at']) }}
                        </template>
                    </column>

                    <column class="title-flex-column" field="title" :header="$t('newsTitle')" />

                    <column class="number-column" :header="$t('url')">
                        <template #body="{ data }">
                            <a class="text-blue-600 hover:text-blue-700 hover:underline" :href="route('news.detail', { news: data['news_id'] })" target="_blank" v-text="$t('view')" />
                        </template>
                    </column>

                    <column class="status-column" field="status_label" :header="$t('status')" />
                    <column class="action-column small">
                        <template #body="{ data }">
                            <template v-if="data['status'] !== 0">
                                <inertia-link
                                    :href="route('news.form', { news: data['news_id'] })"
                                >
                                    <i class="pi pi-pen-to-square text-blue-400 hover:text-blue-600" />
                                </inertia-link>

                                <i
                                    class="pi pi-trash text-red-400 hover:text-red-600 hover:cursor-pointer"
                                    @click="confirmDeleteNews(data['news_id'])"
                                />
                            </template>
                        </template>
                    </column>

                    <template #empty>{{ $t((filters.search && filters.search !== '') || (filters.status && filters.status !== '') ? 'emptyResult' : 'emptyData') }}</template>
                </data-table>
            </grid-container>

            <pagination
                v-if="news['data'].length > 0"
                class="mt-5 flex items-center justify-center mx-auto"
                :links="news['links']"
                :active="config.activePage"
                :disabled="config.busy"
                :limit="news['per_page']"
                :total="news['total']"
                :from="news['from']"
                :to="news['to']"
                @progress="updateProgress"
                @changeLimit="changeLimit"
            />
        </div>
    </app-layout>

    <modal v-if="config.showModal" :show="config.open" @close="closeModal">
        <div class="pt-4 pb-3 px-3 font-semibold" v-text="$t('confirm')" />
        <div class="border-t border-b p-4" v-text="$t('confirmDeleteNewsMessage')" />
        <div class="flex items-center justify-end px-3 py-3">
            <secondary-button class="mr-3 text-sm" v-text="$t('cancel')" @click="closeModal" />

            <red-button class="text-sm overflow-hidden h-[34px]" @click="deleteNews">
                <loading-icon v-if="config.deleting" class="mr-1" />

                <span class="text-sm text-white" v-text="$t('delete')" />
            </red-button>
        </div>
    </modal>
</template>
