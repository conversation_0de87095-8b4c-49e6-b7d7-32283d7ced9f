<script setup>
import { inject } from "vue";
import { useForm, Link as InertiaLink } from "@inertiajs/vue3";

import AppLayout from '@/Layouts/AppLayout.vue';
import RedButton from '@/Components/RedButton.vue';
import LoadingIcon from '@/Components/LoadingIcon.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';

import { QuillEditor } from '@vueup/vue-quill'

const props = defineProps({
    news: Object,
    action: String,
});

const $toast = inject('$toast');
const newsForm = useForm(props.news);

const save = () => {
    if (newsForm.processing) {
        return false;
    }

    newsForm.errors = {};

    newsForm.transform(data => {
        return data;
    }).post(route('news.store'), {
        preserveScroll: true,
        preserveState: true,
        onSuccess: (data) => {
            if (data.props.jetstream.flash.message) {
                $toast.success(data.props.jetstream.flash.message);
            }

            if (props.action === 'create') {
                window.location = route('news.list');
            }
        },
    });
}
</script>

<template>
    <app-layout :title="$t(props.action + 'News')">
        <template v-slot:header>
            <div class="flex-1 flex items-center">
                <h2
                    class="text-xl text-gray-800 leading-tight flex-1 mr-6"
                    v-text="$t(props.action + 'News')"
                />

                <inertia-link class="primary-button text-sm mr-3" :href="route('news.list')" v-text="$t('list')" />

                <red-button class="normal-case" :disabled="newsForm.processing" @click="save">
                    <loading-icon v-if="newsForm.processing" class="mr-2" />
                    <span class="text-sm" v-text="$t('save')" />
                </red-button>
            </div>
        </template>

        <div class="max-w-7xl mx-auto py-6 px-6">
            <div class="bg-white rounded-md shadow flex flex-col overflow-auto">
                <div class="px-5 pt-3 pb-4 flex flex-col">
                    <label v-text="$t('newsTitle')" class="w-full" />
                    <text-input
                        class="block w-full mt-1"
                        type="text"
                        v-model="newsForm['title']"
                        :disabled="newsForm.processing"
                    />

                    <input-error class="w-full mt-1" :message="newsForm.errors['title']" />
                </div>

                <div class="border-t px-5 pt-3 pb-4 flex flex-col news-editor-container">
                    <label v-text="$t('newsContent')" class="w-full mb-1" />

                    <div class="flex-1 flex flex-col">
                        <quill-editor
                            theme="snow"
                            class="h-full flex-1 flex flex-col"
                            content-type="html"
                            :content="newsForm.content"
                            :v-model="newsForm.content"
                            @update:content="newsForm.content = $event"
                        />
                    </div>

                    <input-error class="w-full mt-1" :message="newsForm.errors['content']" />
                </div>
            </div>
        </div>
    </app-layout>
</template>
