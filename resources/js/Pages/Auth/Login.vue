<template>
    <inertia-head :title="$t('login')" />

    <authentication-card>
        <template #logo>
            <authentication-card-logo />
        </template>

        <form
            @submit.prevent="submit"
            autocomplete="off"
        >
            <div class="mt-0">
                <input-label for="email" :value="$t('email')" />

                <text-input
                    id="email"
                    class="mt-1"
                    v-model="form.email"
                    autofocus
                    autocomplete="off"
                />

                <input-error class="mt-2" :message="form.errors.email" />
            </div>

            <div class="mt-4">
                <input-label for="password" :value="$t('password')" />

                <text-input
                    id="password"
                    class="mt-1"
                    v-model="form.password"
                    type="password"
                    autocomplete="off"
                />

                <input-error class="mt-2" :message="form.errors.password" />
            </div>

            <div class="block mt-4">
                <label class="flex items-center">
                    <checkbox v-model:checked="form.remember" name="remember" />
                    <span class="ms-2 text-sm text-gray-600" v-text="$t('rememberMe')" />
                </label>
            </div>

            <div class="flex items-center justify-end mt-4">
                <primary-button
                    class="ms-4"
                    :class="{ 'opacity-25': form.processing }"
                    :disabled="form.processing"
                >
                    <loading-icon v-if="form.processing" class="mr-2" color="#ffffff" />

                    <span v-text="$t('login')" />
                </primary-button>
            </div>
        </form>
    </authentication-card>
</template>

<script setup>
import { Head as InertiaHead, useForm, usePage } from '@inertiajs/vue3';
import { inject, watchEffect } from "vue";

import AuthenticationCard from '@/Layouts/AuthenticationCard.vue';
import AuthenticationCardLogo from '@/Components/AuthenticationCardLogo.vue';
import Checkbox from '@/Components/Checkbox.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import LoadingIcon from "@/Components/LoadingIcon.vue";
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

defineProps({
    canResetPassword: Boolean,
});

let errors = {};
const $toast = inject('$toast');
const page = usePage();

watchEffect(async () => {
    if (page.props.jetstream.flash.throttle) {
        form.setError(errors);
        $toast.error(page.props.jetstream.flash.throttle);
    }
});

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

const submit = () => {
    if (form.processing) {
        return false;
    }

    form.transform(data => ({
        ...data,
        remember: form.remember ? 'on' : '',
    })).post(route('login'), {
        onBefore: () => {
            errors = form.errors;
            form.clearErrors();
        },
        onError: (errors) => {
            if (errors.unauthorized) {
                $toast.error(errors.unauthorized);
            }
        },
    });
};
</script>
