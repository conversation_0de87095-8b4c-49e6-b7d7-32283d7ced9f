<script setup>
import { useForm, usePage } from '@inertiajs/vue3';
import { computed, inject, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { getId } from "@/Utils/index.js";
import { Close } from "@element-plus/icons-vue";

import AppLayout from '@/Layouts/AppLayout.vue';
import RedButton from "@/Components/RedButton.vue";
import LoadingIcon from "@/Components/LoadingIcon.vue";
import InputLabel from "@/Components/InputLabel.vue";
import TextInput from "@/Components/TextInput.vue";
import SurveySelectionBox from "@/Components/SurveySelectionBox.vue";

const { t } = useI18n();

const props = defineProps({
    formData: Object,
});

const $toast = inject('$toast');
const page = usePage();
const form = useForm(props.formData);

const config = reactive({
    total: computed(() => form.surveys.length + 1),
    currentSurveyId: '',
    clearSurvey: false,
    exclude: computed(() => {
        const excludeIds = [];
        form.surveys.forEach(survey => {
            excludeIds.push(survey.survey_id);
        });

        return excludeIds;
    }),
});

const storeSurveySort = () => {
    if (form.processing) {
        return false;
    }

    form.errors = {};

    form.transform(data => {
        const ids = [];
        data.surveys.forEach(survey => {
            ids.push(survey.survey_id);
        })

        return {
            ids: ids,
        };
    }).post(route('surveySort'), {
        onSuccess: () => {
            if (page.props.jetstream.flash.message) {
                $toast.success(page.props.jetstream.flash.message);
            }
        },
        onError: (errors) => {
            if (errors.ids) {
                $toast.error(errors.ids);
            }
        },
    });
}

const removeSurvey = (index) => {
    form.surveys.splice(index, 1);
}

const onSurveySelected = (data) => {
    form.surveys.push({
        survey_id: data.value,
        title: data.title,
    });

    setTimeout(() => {
        config.currentSurveyId = '';
        config.clearSurvey = true;
    }, 0);
}
</script>

<template lang="pug">
AppLayout(
    :title="$t('surveySort')",
)
    template(
        v-slot:header,
    )
        h2.text-xl.text-gray-800.leading-tight.mr-auto(
            v-text="$t('surveySort')",
        )

        RedButton.ml-auto(
            :class="{ 'opacity-25': form.processing }",
            :disabled="form.processing",
            @click="storeSurveySort",
        )
            LoadingIcon.mr-2(
                v-if="form.processing",
            )

            span.text-sm(
                v-text="$t('save')",
            )

    .max-w-7xl.mx-auto.py-6.px-6
        .bg-white.rounded-md.shadow
            table.w-full
                tr.flex
                    th.w-32(
                        v-text="$t('number')",
                    )

                    th.w-32(
                        v-text="$t('surveyID')",
                    )

                    th.flex-1(
                        v-text="$t('surveyTitle')",
                    )

                    th.w-10
                tr.flex.border-t(
                    v-for="(survey, index) in form.surveys",
                    :class="[index % 2 === 0 ? 'bg-blue-50' : '']",
                )
                    td.w-32(
                        v-text="index + 1",
                    )

                    td.w-32(
                        v-text="getId(survey.survey_id, 'S')",
                    )

                    td.flex-1(
                        v-text="survey.title",
                    )

                    td.flex.items-center.justify-end
                        Close.w-5.text-gray-300.transition.ease-in-out.duration-150(
                            class="hover:cursor-pointer hover:text-red-500",
                            @click="removeSurvey(index)",
                        )
                tr.flex.border-t
                    td.w-32(
                        v-text="config.total",
                    )

                    td.flex-1(
                        colspan="3",
                    )
                        SurveySelectionBox(
                            class="w-[500px]",
                            v-model="config.currentSurveyId",
                            :placeholder="$t('selectSurvey')",
                            :clear-data="config.clearSurvey",
                            :disabled="form.processing",
                            :show-selected="false",
                            :exclude="config.exclude",
                            :enable-search="true",
                            :search-placeholder="$t('searchSurveyPlaceholder')",
                            @selected="onSurveySelected",
                            @dataCleared="config.clearSurvey = false",
                        )
</template>
