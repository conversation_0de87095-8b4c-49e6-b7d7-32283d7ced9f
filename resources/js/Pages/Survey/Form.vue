<script setup>
import { Link, useForm, usePage } from '@inertiajs/vue3';
import { inject, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { CirclePlusFilled, Delete, Close } from "@element-plus/icons-vue";

import AppLayout from '@/Layouts/AppLayout.vue';
import RedButton from "@/Components/RedButton.vue";
import LoadingIcon from "@/Components/LoadingIcon.vue";
import InputLabel from "@/Components/InputLabel.vue";
import TextInput from "@/Components/TextInput.vue";
import InputError from "@/Components/InputError.vue";
import FixedSelectionBox from "@/Components/FixedSelectionBox.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";

const { t } = useI18n();

const props = defineProps({
    title: String,
    survey: Object,
});

const $toast = inject('$toast');
const page = usePage();
const surveyForm = useForm(props.survey);

const config = reactive({
    clearQuestionType: false,
    clearQuestionPublicTarget: false,
});

const questionTypeOptions = [{
    value: 'checkbox',
    label: t('checkbox'),
}, {
    value: 'select',
    label: t('selectBox'),
}, {
    value: 'text',
    label: t('textBox'),
}, {
    value: 'text_with_star',
    label: t('textBoxWithStar'),
}];

const questionPublicTargetOptions = [{
    value: 'none',
    label: t('publicNone'),
}, {
    value: 'platform',
    label: t('publicPlatform'),
}, {
    value: 'app',
    label: t('publicApp'),
}, {
    value: 'platform_app',
    label: t('publicPlatformApp'),
}];

const storeSurvey = () => {
    if (surveyForm.processing) {
        return false;
    }

    surveyForm.errors = {};

    surveyForm.transform(data => {
        return data;
    }).post(route('survey.store'), {
        onSuccess: () => {
            if (! surveyForm.survey_id) {
                surveyForm.reset();

                config.clearQuestionType = true;
                config.clearQuestionPublicTarget = true;
            }

            if (page.props.jetstream.flash.message) {
                $toast.success(page.props.jetstream.flash.message);
            }
        },
    });
}

const addMoreQuestion = () => {
    surveyForm.questions.push({
        'question_id': '',
        'type': '',
        'content': '',
        'point': '',
        'public': '',
        'choices': [{
            'choice_id' : '',
            'content' : '',
        }],
    });

    setTimeout(() => {
        const mainEl = document.getElementsByTagName('main')[0];
        mainEl.scrollTo({ left: 0, top: mainEl.scrollHeight, behavior: "smooth", });
    }, 0);
}

const addMoreAnswer = (questionIndex) => {
    surveyForm.questions[questionIndex].choices.push({
        'choice_id' : '',
        'content' : '',
    });
}

const removeQuestion = (index) => {
    if (surveyForm.questions.length > 1) {
        surveyForm.questions.splice(index, 1);
    }
}

const removeAnswerChoice = (key, index) => {
    if (surveyForm.questions[key].choices.length > 1) {
        surveyForm.questions[key].choices.splice(index, 1);
    }
}

const onQuestionTypeSelected = (option, key) => {
    if (['checkbox', 'select'].includes(option.value) && surveyForm.questions[key].choices.length === 0) {
        addMoreAnswer(key);
    }
}
</script>

<template lang="pug">
AppLayout(
    :title="title",
)
    template(
        v-slot:header,
    )
        h2.text-xl.text-gray-800.leading-tight.mr-auto(
            v-text="title",
        )

        RedButton.ml-auto(
            :class="{ 'opacity-25': surveyForm.processing }",
            :disabled="surveyForm.processing",
            @click="storeSurvey",
        )
            LoadingIcon.mr-2(
                v-if="surveyForm.processing",
            )

            span.text-sm(
                v-text="$t('save')",
            )

    .max-w-7xl.mx-auto.py-6.px-6
        .bg-white.rounded-md.shadow.px-6.py-5.flex.flex-col.mb-6
            InputLabel(
                for="survey-title",
                :value="$t('surveyTitle')",
            )

            TextInput#survey-title.mt-1.block.w-full(
                v-model="surveyForm.title",
                :disabled="surveyForm.processing",
                type="text",
            )

            InputError(
                :message="surveyForm.errors.title",
            )

        .flex.items-stretch(
            v-for="(question, key) in surveyForm.questions",
            :class="[key === 0 ? 'mt-2' : 'mt-6']",
        )
            .flex-1.bg-white.rounded-md.shadow.border.border-gray-200.px-6.pt-4.pb-6.transition
                .mt-0.flex.items-stretch
                    .flex-1.mr-3
                        FixedSelectionBox(
                            v-model="surveyForm.questions[key].type",
                            :label="$t('questionType')",
                            :disabled="surveyForm.processing",
                            :clear-data="config.clearQuestionType",
                            :options="questionTypeOptions",
                            :clearable="false",
                            @dataCleared="config.clearQuestionType = false",
                            @selected="(option) => onQuestionTypeSelected(option, key)",
                        )

                        InputError(
                            :message="surveyForm.errors['questions.' + key + '.type']",
                        )
                    .flex-1.ml-3.mr-3
                        FixedSelectionBox(
                            v-model="surveyForm.questions[key].public",
                            :label="$t('questionPublic')",
                            :disabled="surveyForm.processing",
                            :clear-data="config.clearQuestionPublicTarget",
                            :options="questionPublicTargetOptions",
                            :clearable="false",
                            @dataCleared="config.clearQuestionPublicTarget = false",
                        )

                        InputError(
                            :message="surveyForm.errors['questions.' + key + '.public']",
                        )
                    .flex-1.ml-3
                        InputLabel(
                            :for="'question_point_' + key",
                            :value="$t('questionPoint')",
                        )

                        TextInput.mt-1.block.w-full(
                            :id="'question_point_' + key",
                            v-model="surveyForm.questions[key].point",
                            :disabled="surveyForm.processing",
                            type="text",
                        )

                        InputError(
                            :message="surveyForm.errors['questions.' + key + '.point']",
                        )

                .mt-3
                    InputLabel(
                        :for="'question_' + key",
                        :value="$t('questionContent')",
                    )

                    TextInput.mt-1.block.w-full(
                        :id="'question_' + key",
                        v-model="surveyForm.questions[key].content",
                        :disabled="surveyForm.processing",
                        type="text",
                    )

                    InputError(
                        :message="surveyForm.errors['questions.' + key + '.content']",
                    )

                .mt-3.transition(
                    v-if="['checkbox', 'select'].includes(surveyForm.questions[key].type)",
                )
                    .mt-0(
                        v-text="$t('answerOptions')",
                    )

                    .mt-1.flex.items-start(
                        v-for="(choice, choiceIndex) in surveyForm.questions[key].choices",
                        :key="'choice_' + key + '_' + choiceIndex",
                        :class="{ 'mt-3 pt-2 border-t border-dashed': choiceIndex > 0 }",
                    )
                        .flex-1
                            TextInput.mt-1.block.w-full(
                                v-model="surveyForm.questions[key].choices[choiceIndex].content",
                                :placeholder="$t('answerChoice', { index: choiceIndex + 1 })",
                                :disabled="surveyForm.processing",
                                type="text",
                            )

                            InputError(
                                :message="surveyForm.errors['questions.' + key + '.choices.' + choiceIndex + '.content']",
                            )
                        Close.ml-6.w-5.text-gray-300.transition.ease-in-out.duration-150.mt-4(
                            :class="[surveyForm.questions[key].choices.length > 1 ? 'hover:cursor-pointer hover:text-red-500' : '']",
                            @click="removeAnswerChoice(key, choiceIndex)",
                        )

                    SecondaryButton.mt-3.text-sm(
                        class="h-[38px]",
                        v-text="$t('addMoreAnswer')",
                        @click="addMoreAnswer(key)",
                    )

            .ml-6.flex.items-center
                Delete.w-6.text-gray-500.transition.ease-in-out.duration-150(
                    :class="[surveyForm.questions.length > 1 ? 'hover:cursor-pointer hover:text-red-500' : '']",
                    @click="removeQuestion(key)",
                )
        .flex.items-center.mt-6
            .ml-auto.mr-2(
                v-text="$t('addNewQuestion')"
            )

            CirclePlusFilled.w-8.text-sky-500.transition.ease-in-out.duration-150(
                class="hover:cursor-pointer hover:text-sky-600",
                @click="addMoreQuestion",
            )
</template>
