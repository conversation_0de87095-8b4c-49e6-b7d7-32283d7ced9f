<script setup>
import { inject, reactive } from "vue";
import { getId } from "@/Utils/index.js";
import { Link, router, usePage } from '@inertiajs/vue3';
import throttle from "lodash/throttle.js";
import pickBy from "lodash/pickBy.js";
import { Delete } from "@element-plus/icons-vue";

import AppLayout from '@/Layouts/AppLayout.vue';
import InputLabel from "@/Components/InputLabel.vue";
import TextInput from "@/Components/TextInput.vue";
import Pagination from "@/Components/Pagination.vue";
import LoadingIcon from "@/Components/LoadingIcon.vue";
import Modal from "@/Components/Modal.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";

const $toast = inject('$toast');
const page = usePage();

const props = defineProps({
    filters: Object,
    surveys: Object,
});

const config = reactive({
    searched: props.filters.search ?? '',
    searching: false,
    confirmDeletion: false,
    open: false,
    deleteId: null,
    deleting: false,
});

const search = throttle(() => router.get(route('survey.list'), pickBy({
    search: config.searched,
}), {
    preserveState: true,
    onBefore: () => {
        config.searching = true;
    },
    onFinish: () => {
        config.searching = false;
    },
}), 1000);


const closeModal = () => {
    config.open = false;
    setTimeout(() => config.confirmDeletion = false, 150);
}

const removeSurveyConfirmation = (id) => {
    config.deleteId = id;
    config.confirmDeletion = true;
    setTimeout(() => config.open = true, 150);
}

const deleteSurvey = () => {
    if (config.deleting) {
        return;
    }

    router.post(route('survey.delete'), {
        id: config.deleteId,
    }, {
        preserveScroll: true,
        preserveState: true,
        onBefore: () => {
            config.deleting = true;
        },
        onSuccess: () => {
            closeModal();
            config.deleteId = null;

            if (page.props.jetstream.flash.message) {
                $toast.success(page.props.jetstream.flash.message);
            }
        },
        onFinish: () => {
            config.deleting = false;
        },
    });
}
</script>

<template lang="pug">
AppLayout(
    :title="$t('listSurvey')",
)
    template(
        v-slot:header,
    )
        h2.text-xl.text-gray-800.leading-tight.mr-auto(
            v-text="$t('listSurvey')",
        )

        Link.ml-auto.flex.items-center.justify-center.px-4.py-2.bg-sky-500.border.border-transparent.rounded-md.font-semibold.text-white.uppercase.transition.ease-in-out.duration-150(
            class="hover:bg-sky-600 focus:outline-none h-[38px]",
            v-text="$t('addNew')",
            :href="route('survey.create')",
        )

    .py-6.px-6(
        class="sm:mx-2",
    )
        .flex.items-center.items-stretch
            .mt-0.w-80
                InputLabel(
                    for="search",
                    :value="$t('surveySearch')",
                )

                TextInput#search.mt-1.block.w-full(
                    v-model="config.searched",
                    :placeholder="$t('enterSurveySearch')",
                    :disabled="config.searching",
                    @enter="search",
                )
        .bg-white.rounded-md.shadow.overflow-auto.mt-5.flex.flex-col(
            :class="{ 'grid-loading': config.searching }",
        )
            LoadingIcon.grid-loading-icon(
                v-if="config.searching",
                :size="24",
                color="rgb(55 65 81)",
            )

            table.w-full.survey-table
                tr.text-left.flex
                    th.number-column(
                        v-text="$t('ID')",
                    )

                    th.survey-title-column(
                        v-text="$t('surveyTitle')",
                    )

                    th.question-id-column(
                        v-text="$t('questionID')",
                    )

                    th.title-flex-column(
                        v-text="$t('questionContent')",
                    )

                    th.question-type-column(
                        v-text="$t('questionType')",
                    )

                    th.question-public-column(
                        v-text="$t('questionPublic')",
                    )

                    th.question-point-column(
                        v-text="$t('questionPoint')",
                    )

                    th.answer-id-column(
                        v-text="$t('answerID')",
                    )

                    th.answer-content-column(
                        v-text="$t('answerContent')",
                    )

                    th(
                        class="w-[56px]",
                    )
                tr.flex.border-t(
                    v-if="surveys.data.length > 0",
                    v-for="(survey, sIndex) in surveys.data",
                    :class="[sIndex % 2 ? 'bg-blue-50' : '']",
                )
                    td.number-column
                        Link(
                            class="hover:text-sky-600 hover:underline",
                            v-text="getId(survey.survey_id, 'S')",
                            :href="route('survey.update', {survey: survey.survey_id})",
                        )

                    td.survey-title-column(
                        v-text="survey.title",
                    )

                    td.border-l.p-0.flex-1(
                        colspan="7",
                    )
                        table.w-full.h-full
                            tr.flex.h-full(
                                v-for="(question, qKey) in survey.questions",
                                :class="[qKey > 0 ? 'border-t' : '']",
                            )
                                td.question-id-column(
                                    v-text="getId(question.question_id, 'Q')",
                                )

                                td.title-flex-column(
                                    v-text="question.content",
                                )

                                td.question-type-column(
                                    v-text="question.typeLabel",
                                )

                                td.question-public-column(
                                    v-text="question.publicLabel",
                                )

                                td.question-point-column(
                                    v-text="question.point",
                                )

                                td.p-0.border-l(
                                    colspan="2",
                                )
                                    table.w-full.h-full
                                        tr.flex.h-full(
                                            v-if="question.choices.length > 0",
                                            v-for="(choice, cKey) in question.choices",
                                            :class="[cKey > 0 ? 'border-t' : '']",
                                        )
                                            td.answer-id-column(
                                                v-text="getId(choice.choice_id, 'A')",
                                            )

                                            td.answer-content-column(
                                                v-text="choice.content",
                                            )

                                        tr.flex.h-full(
                                            v-else
                                        )
                                            td.answer-id-column
                                            td.answer-content-column
                    td.border-l(
                        class="w-[56px]",
                    )
                        Delete.w-6.text-gray-500.transition.ease-in-out.duration-150(
                            class="hover:cursor-pointer hover:text-red-500",
                            @click="removeSurveyConfirmation(survey.survey_id)",
                        )
                tr.flex.border-t(
                    v-else,
                )
                    td(
                        colspan="10",
                        v-text="$t(filters.search && filters.search !== '' ? 'emptyResult' : 'emptyData')",
                    )
        Pagination.mt-5.flex.items-center.justify-center.mx-auto(
            :links="surveys.links",
        )
Modal(
    v-if="config.confirmDeletion",
    :show="config.open",
    :closeable="true",
    size="lg",
    padding-vertical="py-20",
    @close="closeModal",
)
    .pt-4.pb-3.px-3.font-semibold(
        v-text="$t('confirm')",
    )

    .border-t.px-3.py-4.font-light(
        v-text="$t('surveyDeleteConfirmation')",
    )

    .border-t
    .flex.items-center.justify-end.px-3.py-3
        SecondaryButton.mr-3.text-sm(
            class="h-[38px]",
            v-text="$t('cancel')",
            @click="closeModal",
        )

        PrimaryButton.text-sm
            span.text-sm(
                v-text="$t('delete')",
                @click="deleteSurvey",
            )
</template>
