<script setup>
import { computed, inject, reactive } from "vue";
import { clearEmptyData, formatDate } from "@/Utils/index.js";
import { Link as InertiaLink, router, usePage, useForm } from '@inertiajs/vue3';
import { route } from "ziggy-js";

import DataTable from 'primevue/datatable';
import Column from 'primevue/column';

import AppLayout from '@/Layouts/AppLayout.vue';
import InputLabel from "@/Components/InputLabel.vue";
import SearchInput from "@/Components/SearchInput.vue";
import Pagination from "@/Components/Pagination.vue";
import LoadingIcon from "@/Components/LoadingIcon.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import RedButton from "@/Components/RedButton.vue";
import Modal from "@/Components/Modal.vue";
import GridContainer from "@/Components/GridContainer.vue";

const $toast = inject('$toast');
const page = usePage();

const props = defineProps({
    filters: Object,
    users: Object,
});

const form = useForm({
    id: props.filters.id ?? '',
    search: props.filters.search ?? '',
    limit: props.filters.limit ?? 10,
});

const config = reactive({
    showSearchClearButton: (props.filters.search ?? '').trim().length > 0,
    showSearchIDClearButton: (props.filters.id ?? '').trim().length > 0,
    searching: false,
    showModal: false,
    open: false,
    deleteId: null,
    deleting: false,
    activePage: null,
    busy: computed(() => config.activePage !== null || config.searching || form.processing),
});

const _search = () => {
    if (form.processing) {
        return false;
    }

    form.transform(data => clearEmptyData(data)).get(route('user.list'), {
        preserveScroll: true,
        onSuccess: () => {},
    });
}

const search = () => {
    config.showSearchClearButton = true;
    _search();
}

const clearSearch = () => {
    form.search = '';
    config.showSearchClearButton = false;
    _search();
}

const searchByID = () => {
    config.showSearchIDClearButton = true;
    _search();
}

const clearSearchById = () => {
    form.id = '';
    config.showSearchIDClearButton = false;
    _search();
}

const confirmDeleteUser = (userId) => {
    config.deleteId = userId;
    config.showModal = true;
    setTimeout(() => config.open = true, 150);
}

const closeModal = () => {
    config.open = false;
    setTimeout(() => config.showModal = false, 150);
}

const deleteUser = () => {
    if (config.deleting) {
        return;
    }

    router.post(route('user.delete'), {
        id: config.deleteId,
    }, {
        preserveScroll: true,
        preserveState: true,
        onBefore: () => {
            config.deleting = true;
        },
        onSuccess: () => {
            config.deleteId = null;

            if (page.props.jetstream.flash.message) {
                $toast.success(page.props.jetstream.flash.message);
            }

            closeModal();
        },
        onFinish: () => {
            config.deleting = false;
        },
    });
}


const updateProgress = (progress) => {
    config.activePage = progress;
    config.searching = true;
};

const changeLimit = (limit) => {
    form.limit = limit;
    form.id = '';
    form.search = '';

    _search();
}
</script>

<template>
    <app-layout :title="$t('listUser')">
        <template v-slot:header>
            <h2
                class="text-xl text-gray-800 leading-tight mr-auto"
                v-text="$t('listUser')"
            />
        </template>

        <div class="p-6 sm:mx-2">
            <div class="flex items-stretch">
                <div class="mt-0 w-80 mr-8">
                    <input-label for="user-search" :value="$t('userSearch')" />

                    <search-input
                        id="user-search"
                        class="mt-1 block w-full"
                        v-model="form.search"
                        :placeholder="$t('searchByNameOrPhone')"
                        :disabled="config.busy"
                        :show-clear-button="config.showSearchClearButton"
                        @input="config.showSearchClearButton = false"
                        @clear-search="clearSearch"
                        @enter="search"
                    />
                </div>

                <div class="mt-0 w-80 mr-8">
                    <input-label for="id-search" :value="$t('ID')" />

                    <search-input
                        id="id-search"
                        class="mt-1 block w-full"
                        v-model="form.id"
                        :placeholder="$t('searchByID')"
                        :disabled="config.busy"
                        :show-clear-button="config.showSearchIDClearButton"
                        @input="config.showSearchIDClearButton = false"
                        @clear-search="clearSearchById"
                        @enter="searchByID"
                    />
                </div>
            </div>

            <grid-container :loading="config.busy">
                <data-table :value="users['data']">
                    <column class="number-column" :header="$t('ID')">
                        <template #body="{ data }">
                            <inertia-link
                                class="hover:text-sky-600 hover:underline"
                                v-text="data['user_id']"
                                :href="route('user.detail', {user: data['user_id']})"
                            />
                        </template>
                    </column>

                    <column class="time-column" :header="$t('registerAt')">
                        <template #body="{ data }">
                            {{ formatDate(data['created_at']) }}
                        </template>
                    </column>

                    <column class="phone-column" field="phone" :header="$t('phone')" />
                    <column class="username-column" field="name" :header="$t('username')" />

                    <column class="role-column" :header="$t('role')">
                        <template #body="{ data }">
                            {{ $t(data['role']) }}
                        </template>
                    </column>

                    <column class="count-column" :header="$t('postCount')">
                        <template #body="{ data }">
                            <inertia-link
                                v-if="data['post_count']"
                                class="hover:text-red-600 hover:underline"
                                v-text="data['post_count']"
                                :href="route('post.list', { user: data['user_id'] })"
                            />
                            <span v-else v-text="data['post_count']" />
                        </template>
                    </column>

                    <column class="count-column" :header="$t('answerCount')">
                        <template #body="{ data }">
                            <inertia-link
                                v-if="data['answer_count']"
                                class="hover:text-red-600 hover:underline"
                                v-text="data['answer_count']"
                                :href="route('postAnswer.list', { user: data['user_id'] })"
                            />
                            <span v-else v-text="data['answer_count']" />
                        </template>
                    </column>

                    <column class="count-column" field="comment_count" :header="$t('commentCount')" />
                    <column class="count-column" field="best_answer_count" :header="$t('bestAnswerCount')" />
                    <column class="status-column" field="status_label" :header="$t('status')" />

                    <column class="last-logged-time-column" :header="$t('lastLoggedInTime')">
                        <template #body="{ data }">
                            {{ formatDate(data['last_logged_in_at']) }}
                        </template>
                    </column>

                    <column class="action-column extra">
                        <template #header>
                            <i class="pi pi-info-circle text-gray-500" v-tooltip.top="$t('userInfo')" />
                            <i class="pi pi-pen-to-square text-gray-300" v-tooltip.top="$t('edit')" />
                            <i class="pi pi-chart-bar text-sky-400" v-tooltip.top="$t('attribute')" />
                            <i class="pi pi-database text-amber-400" v-tooltip.top="$t('feed')" />
                            <i class="pi pi-trash text-red-400" v-tooltip.top="$t('delete')" />
                        </template>

                        <template #body="{ data }">
                            <template v-if="parseInt(data['status']) !== 0">
                                <inertia-link :href="route('user.detail', { user: data['user_id'] })">
                                    <i class="pi pi-info-circle text-gray-500 hover:text-sky-600" />
                                </inertia-link>

                                <i v-if="!data['is_super_admin']" class="pi pi-pen-to-square text-gray-300" />

                                <inertia-link :href="route('user.attribute', { user: data['user_id'] })">
                                    <i class="pi pi-chart-bar text-sky-400 hover:text-sky-600 hover:cursor-pointer" />
                                </inertia-link>

                                <inertia-link :href="route('user.feed', { user: data['user_id'] })">
                                    <i class="pi pi-database text-amber-400 hover:text-amber-600 hover:cursor-pointer" />
                                </inertia-link>

                                <i
                                    v-if="!data['is_super_admin']"
                                    class="pi pi-trash text-red-400 hover:text-red-600 hover:cursor-pointer"
                                    @click="confirmDeleteUser(data['user_id'])"
                                />
                            </template>
                        </template>
                    </column>
                    <template #empty>{{ $t((filters.search && filters.search !== '') || (filters.id && filters.id !== '') ? 'emptyResult' : 'emptyData') }}</template>
                </data-table>
            </grid-container>

            <pagination
                v-if="users['data'].length > 0"
                class="mt-5 flex items-center justify-center mx-auto"
                :links="users['links']"
                :active="config.activePage"
                :disabled="config.busy"
                :limit="users['per_page']"
                :total="users['total']"
                :from="users['from']"
                :to="users['to']"
                @progress="updateProgress"
                @changeLimit="changeLimit"
            />
        </div>
    </app-layout>

    <modal v-if="config.showModal" :show="config.open" @close="closeModal">
        <div class="pt-4 pb-3 px-3 font-semibold" v-text="$t('confirm')" />
        <div class="border-t border-b p-4" v-text="$t('confirmDeleteUserMessage')" />
        <div class="flex items-center justify-end px-3 py-3">
            <secondary-button class="mr-3 text-sm" v-text="$t('cancel')" @click="closeModal" />

            <red-button class="text-sm overflow-hidden h-[34px]" @click="deleteUser">
                <loading-icon v-if="config.deleting" class="mr-1" />

                <span class="text-sm text-white" v-text="$t('delete')" />
            </red-button>
        </div>
    </modal>
</template>
