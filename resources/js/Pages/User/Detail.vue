<script setup>
import { useI18n } from "vue-i18n";
import { formatDate } from "@/Utils/index.js";
import { Link as InertiaLink } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
    user: Object,
});

const { t } = useI18n();

let attributes = [
    {
        label: 'registerAt',
        attribute: 'created_at',
    },
    {
        label: 'userID',
        attribute: 'user_id',
    },
    {
        label: 'username',
        attribute: 'name',
    },
    {
        label: 'phone',
        attribute: 'phone',
    },
    {
        label: 'birthday',
        attribute: 'birthday',
    },
    {
        label: 'age',
        attribute: 'age',
    },
    {
        label: 'position',
        attribute: 'position',
    },
    {
        label: 'job',
        attribute: 'profile_label',
    },
];

if (props.user.profile_type === 'student') {
    attributes.push({
        label: 'schoolName',
        attribute: 'school_name',
    });
} else if (props.user.profile_type === 'employee') {
    attributes.push({
        label: 'work',
        attribute: 'work',
    });

    attributes.push({
        label: 'expert',
        attribute: 'expert',
    });

    attributes.push({
        label: 'service_in_charge',
        attribute: 'service_in_charge',
    });
}

if (props.user.profile_type) {
    attributes.push({
        label: 'brief',
        attribute: 'brief',
    });
}

attributes = [
    ...attributes,
    {
        label: 'postCount',
        attribute: 'post_count',
    },
    {
        label: 'answerCount',
        attribute: 'answer_count',
    },
    {
        label: 'commentCount',
        attribute: 'comment_count',
    },
    {
        label: 'role',
        attribute: 'role',
    },
    {
        label: 'status',
        attribute: 'status_label',
    },
    {
        label: 'lastLoggedInTime',
        attribute: 'last_logged_in_at',
    },
];

const getCellValue = (user, column) => {
    const value = user[column.attribute];

    return ['registerAt', 'lastLoggedInTime'].includes(column.label) ? formatDate(value) : (
        ['role'].includes(column.label) ? t(value) : value
    );
}
</script>

<template>
    <app-layout :title="$t('userInfo')">
        <template v-slot:header>
            <h2
                class="text-xl text-gray-800 leading-tight mr-auto"
                v-text="$t('userInfo')"
            />
        </template>

        <div class="max-w-7xl mx-auto py-6 px-6">
            <div class="bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden">
                <table class="p-datatable-table w-full w-border">
                    <thead>
                        <tr class="text-left flex">
                            <th colspan="2" v-text="$t('generalInfo')" />
                        </tr>
                    </thead>
                    <tbody>
                        <template v-for="column in attributes">
                            <tr class="text-left flex border-t">
                                <td class="w-1/3" v-text="$t(column.label)" />
                                <td class="border-l border-l-gray-200 flex-1">
                                    <inertia-link
                                        v-if="column.label === 'postCount' && user.post_count > 0"
                                        class="hover:text-red-600 hover:underline"
                                        v-text="user.post_count"
                                        :href="route('post.list', {user: user.user_id})"
                                    />

                                    <inertia-link
                                        v-else-if="column.label === 'answerCount' && user.answer_count > 0"
                                        class="hover:text-red-600 hover:underline"
                                        v-text="user.answer_count"
                                        :href="route('postAnswer.list', {user: user.user_id})"
                                    />

                                    <template v-else>
                                        {{ getCellValue(user, column) }}
                                    </template>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>

            <div
                v-if="user.surveys.length > 0"
                class="bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden"
                v-for="survey in user.surveys"
            >
                <table class="p-datatable-table w-full">
                    <thead>
                        <tr class="text-left flex">
                            <th colspan="3" v-text="survey.title" />
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="border-t flex">
                            <th class="flex-1" v-text="$t('question')" />
                            <th class="w-[550px]" v-text="$t('answer')" />
                            <th class="w-[220px]" v-text="$t('questionPublic')" />
                        </tr>
                        <tr
                            class="border-t flex"
                            v-for="(question, key) in survey.questions"
                            :key="key"
                        >
                            <td class="flex-1" v-text="question.content" />
                            <td class="w-[550px]" v-text="question.answer" />
                            <td class="w-[220px]" v-text="$t(question.public)" />
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </app-layout>
</template>
