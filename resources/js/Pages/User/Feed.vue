<script setup>
import { reactive } from "vue";
import { formatDate } from "@/Utils/index.js";

import AppLayout from '@/Layouts/AppLayout.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import LoadingIcon from '@/Components/LoadingIcon.vue';

const props = defineProps({
    posts: Array,
    hasNextPage: <PERSON><PERSON><PERSON>,
    user: Object,
});

const config = reactive({
    refreshing: false,
    loading: false,
    hasNextPage: props.hasNextPage,
    posts: [],
    next: 0,
});

props.posts.forEach(post => config.posts.push(post));

const loadData = async () => {
    if (config.loading) {
        return false;
    }

    config.loading = true;
    await window.axios.get(route('user.feed', {user: props.user.user_id}), {
        params: {
            next: config.next,
            axios: true,
        },
    })
        .then(response => {
            config.hasNextPage = response.data.hasNextPage;
            response.data.posts.forEach(post => config.posts.push(post));
        })
        .catch((e) => {})
        .finally(() => {
            config.loading = false;
            config.refreshing = false;
        });
}

const refresh = async () => {
    if (config.loading) {
        return false;
    }

    config.posts = [];
    config.refreshing = true;
    config.next = 0;

    await loadData();
}

const loadMore = async () => {
    if (config.loading) {
        return false;
    }

    config.refreshing = false;
    config.next = 1;

    await loadData();
}
</script>

<template>
    <app-layout :title="$t('userFeed')">
        <template v-slot:header>
            <div class="flex-1 flex items-center">
                <h2
                    class="text-xl text-gray-800 leading-tight flex-1 mr-6"
                    v-text="$t('userFeedExtra', { name: user.name.length ? user.name : 'N/A', id: user.user_id })"
                />

                <primary-button class="normal-case" :disabled="config.loading" @click="refresh">
                    <loading-icon v-if="config.refreshing" class="mr-2" />
                    <span class="text-sm" v-text="$t('refresh')" />
                </primary-button>
            </div>
        </template>

        <div class="mx-auto py-6 px-6">
            <div
                class="bg-white rounded-md shadow flex flex-col overflow-auto"
                :class="{ 'grid-loading': config.loading }"
            >
                <loading-icon class="grid-loading-icon" :size="24" color="rgb(55 65 81)" v-if="config.loading" />

                <table class="w-full feed-table">
                    <tbody>
                        <tr class="text-left flex">
                            <th class="number-column extra" v-text="$t('STT')" />
                            <th class="number-column" v-text="$t('postID')" />
                            <th class="title-flex-column" v-html="$t('postContent')" />
                            <th class="post-type-column" v-text="$t('postType')" />
                            <th class="time-column" v-text="$t('postCreatedAt')" />
                            <th class="post-type-column" v-text="$t('qaType')" />
                        </tr>
                        <tr
                            v-if="config.posts.length > 0"
                            class="flex border-t"
                            v-for="(post, index) in config.posts"
                        >
                            <td class="number-column extra" v-text="index + 1" />
                            <td class="number-column" v-text="post.post_id" />
                            <td class="title-flex-column" v-html="post.content" />
                            <td class="post-type-column" v-text="post.tag" />
                            <td class="time-column" v-text="formatDate(post.created_at)" />
                            <td class="post-type-column" v-text="$t('qaType.' + post.qa_type)" />
                        </tr>
                        <tr
                            v-else
                            class="flex border-t"
                        >
                            <td colspan="6" v-text="$t(config.loading ? 'loading' : 'emptyData')" />
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-6" v-if="config.hasNextPage">
                <primary-button class="normal-case mx-auto" :disabled="config.loading" @click="loadMore">
                    <loading-icon v-if="config.loading && !config.refreshing" class="mr-2" />
                    <span class="text-sm" v-text="$t('loadMore')" />
                </primary-button>
            </div>
        </div>
    </app-layout>
</template>
