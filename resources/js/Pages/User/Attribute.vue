<script setup>
import { computed, reactive } from "vue";
import AppLayout from '@/Layouts/AppLayout.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';

import Tabs from 'primevue/tabs';
import TabList from 'primevue/tablist';
import Tab from 'primevue/tab';
import TabPanels from 'primevue/tabpanels';
import TabPanel from 'primevue/tabpanel';

const props = defineProps({
    showToIndex: Number,
    attributes: Object,
    user: Object,
});

const config = reactive({
    showToIndex: props.showToIndex,
    showLikeDates: computed(() => {
        const dates = [];
        const allDate = Object.keys(props.attributes.like.data);
        for (let i = 0; i < allDate.length; i++) {
            if (i < config.showToIndex) {
                dates.push(allDate[i]);
            }
        }

        return dates;
    }),
    showSimilarDates: computed(() => {
        const dates = [];
        const allDate = Object.keys(props.attributes.similar.data);
        for (let i = 0; i < allDate.length; i++) {
            if (i < config.showToIndex) {
                dates.push(allDate[i]);
            }
        }

        return dates;
    }),
});
</script>

<template>
    <app-layout :title="$t('userAttribute')">
        <template v-slot:header>
            <h2
                class="text-xl text-gray-800 leading-tight mr-auto"
                v-text="$t('userAttribute')"
            />
        </template>

        <div class="max-w-7xl mx-auto py-6 px-6">
            <div class="bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden">
                <table class="w-full">
                    <thead>
                        <tr class="text-left flex">
                            <th colspan="2" v-text="$t('userInfo')" />
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="text-left flex border-t odd:bg-blue-50 even:bg-white">
                            <td class="w-1/3" v-text="$t('ID')" />
                            <td class="border-l flex-1" v-text="user.user_id" />
                        </tr>
                        <tr class="text-left flex border-t odd:bg-blue-50 even:bg-white">
                            <td class="w-1/3" v-text="$t('username')" />
                            <td class="border-l flex-1" v-text="user.name" />
                        </tr>
                    </tbody>
                </table>
            </div>

            <tabs value="0" class="bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden">
                <tab-list>
                    <tab value="0" v-text="$t('tabSimilar')" />
                    <tab value="1" v-text="$t('tabLike')" />
                </tab-list>

                <tab-panels class="p-0">
                    <tab-panel value="0">
                        <template v-if="attributes.similar.ids.length > 0">
                            <table class="w-full">
                                <thead>
                                    <tr class="text-left flex border-b">
                                        <th colspan="2" v-text="$t('topSimilarByDay')" />
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="text-left flex border-b odd:bg-blue-50 even:bg-white">
                                        <td class="w-1/3" v-text="$t('date')" />
                                        <td class="border-l flex-1" v-text="$t('ID')" />
                                    </tr>
                                    <tr
                                        class="text-left flex border-b odd:bg-blue-50 even:bg-white"
                                        v-for="(ids, date) in attributes.similar.data"
                                        :class="config.showSimilarDates.includes(date) ? '' : 'hidden'"
                                    >
                                        <td class="w-1/3" v-text="date" />
                                        <td class="border-l flex-1" v-text="ids.join(', ')" />
                                    </tr>
                                </tbody>
                            </table>

                            <div class="mb-6 w-full flex justify-center" v-if="Object.keys(attributes.similar.data).length > config.showToIndex">
                                <primary-button class="normal-case" @click="config.showToIndex = Object.keys(attributes.similar.data).length">
                                    <span class="text-sm" v-text="$t('displayAll')" />
                                </primary-button>
                            </div>

                            <table class="w-full">
                                <thead>
                                    <tr class="text-left flex">
                                        <th colspan="2" v-text="$t('topSimilar')" />
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="text-left flex border-t odd:bg-blue-50 even:bg-white">
                                        <td class="w-1/3" v-text="$t('top50')" />
                                        <td class="border-l flex-1" v-text="attributes.similar.ids.join(', ')" />
                                    </tr>
                                    <tr class="text-left flex border-t odd:bg-blue-50 even:bg-white">
                                        <td class="w-1/3" v-text="$t('viewedData')" />
                                        <td class="border-l flex-1" v-text="attributes.similar.posts.join(', ')" />
                                    </tr>
                                </tbody>
                            </table>
                        </template>

                        <div v-else class="p-3" v-text="$t('emptySimilarData')" />
                    </tab-panel>

                    <tab-panel value="1">
                        <template v-if="attributes.like.ids.length > 0">
                            <table class="w-full">
                                <thead>
                                    <tr class="text-left flex border-b">
                                        <th colspan="2" v-text="$t('topLikeByDay')" />
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="text-left flex border-b odd:bg-blue-50 even:bg-white">
                                        <td class="w-1/3" v-text="$t('date')" />
                                        <td class="border-l flex-1" v-text="$t('ID')" />
                                    </tr>
                                    <tr
                                        class="text-left flex border-b odd:bg-blue-50 even:bg-white"
                                        v-for="(ids, date) in attributes.like.data"
                                        :class="config.showLikeDates.includes(date) ? '' : 'hidden'"
                                    >
                                        <td class="w-1/3" v-text="date" />
                                        <td class="border-l flex-1" v-text="ids.join(', ')" />
                                    </tr>
                                </tbody>
                            </table>

                            <div class="mb-6 w-full flex justify-center" v-if="Object.keys(attributes.like.data).length > config.showToIndex">
                                <primary-button class="normal-case" @click="config.showToIndex = Object.keys(attributes.like.data).length">
                                    <span class="text-sm" v-text="$t('displayAll')" />
                                </primary-button>
                            </div>

                            <table class="w-full">
                                <thead>
                                    <tr class="text-left flex">
                                        <th colspan="2" v-text="$t('topLike')" />
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="text-left flex border-t odd:bg-blue-50 even:bg-white">
                                        <td class="w-1/3" v-text="$t('top15')" />
                                        <td class="border-l flex-1" v-text="attributes.like.ids.join(', ')" />
                                    </tr>
                                    <tr class="text-left flex border-t odd:bg-blue-50 even:bg-white">
                                        <td class="w-1/3" v-text="$t('relatedData')" />
                                        <td class="border-l flex-1" v-text="attributes.like.posts.join(', ')" />
                                    </tr>
                                </tbody>
                            </table>
                        </template>

                        <div v-else class="p-3" v-text="$t('emptyLikedData')" />
                    </tab-panel>
                </tab-panels>
            </tabs>
        </div>
    </app-layout>
</template>
