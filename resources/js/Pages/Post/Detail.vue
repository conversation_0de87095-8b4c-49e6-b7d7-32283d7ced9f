<script setup>
import { formatDate } from "@/Utils/index.js";
import { Link as InertiaLink } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
    post: Object,
});

const attributes = [
    {
        label: 'ID',
        attribute: 'post_id',
    },
    {
        label: 'createdBy',
        attribute: 'username',
    },
    {
        label: 'createdByID',
        attribute: 'user_id',
    },
    {
        label: 'postContent',
        attribute: 'content',
    },
    {
        label: 'postCreatedAt',
        attribute: 'created_at',
    },
    {
        label: 'postType',
        attribute: 'tag',
    },
    {
        label: 'answerCount',
        attribute: 'answer_count',
    },
    {
        label: 'postStatus',
        attribute: 'status_label',
    },
];

const getCellValue = (post, column) => {
    const value = post[column.attribute];

    return ['postCreatedAt'].includes(column.label) ? formatDate(value) : value;
}
</script>

<template>
    <app-layout :title="$t('postInfo')">
        <template v-slot:header>
            <h2
                class="text-xl text-gray-800 leading-tight mr-auto"
                v-text="$t('postInfo')"
            />
        </template>

        <div class="max-w-7xl mx-auto p-6">
            <div class="bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden">
                <table class="p-datatable-table w-full w-border">
                    <thead>
                        <tr class="text-left flex">
                            <th colspan="2" v-text="$t('generalInfo')" />
                        </tr>
                    </thead>
                    <tbody>
                        <tr
                            v-for="column in attributes"
                            class="text-left flex border-t"
                        >
                            <td class="w-1/4" v-text="$t(column.label)" />
                            <td class="border-l flex-1">
                                <inertia-link
                                    v-if="column.label === 'answerCount'"
                                    class="hover:text-red-600 hover:underline"
                                    v-text="post.answer_count"
                                    :href="route('postAnswer.list', {post: post['post_id']})"
                                />

                                <div v-else-if="column.label === 'postContent'" v-html="post['content']" />

                                <div v-else>
                                    {{ getCellValue(post, column) }}
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </app-layout>
</template>
