<script setup>
import { computed, reactive } from "vue";
import { clearEmptyData, formatDate, toDateString } from "@/Utils/index.js";
import { useForm } from "@inertiajs/vue3";
import { route } from "ziggy-js";

import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import DatePicker from 'primevue/datepicker';

import AppLayout from '@/Layouts/AppLayout.vue';
import Pagination from '@/Components/Pagination.vue';
import InputLabel from '@/Components/InputLabel.vue';
import SearchInput from '@/Components/SearchInput.vue';
import GridContainer from "@/Components/GridContainer.vue";

const props = defineProps({
    filters: Object,
    logs: Object,
});

const form = useForm({
    user: props.filters['user'] ?? '',
    postSearch: props.filters['post'] ?? '',
    date: props.filters['date'] ?? '',
    limit: props.filters['limit'] ?? 10,
});

const config = reactive({
    searching: false,
    showUserSearchClearButton: (props.filters.user ?? '').trim().length > 0,
    showPostSearchClearButton: (props.filters.post ?? '').trim().length > 0,
    activePage: null,
    busy: computed(() => config.activePage !== null || config.searching || form.processing),
});

const _search = () => {
    if (form.processing) {
        return false;
    }

    form.transform((data) => {
        data['post'] = data.postSearch;
        delete data['postSearch'];

        if (data.date) {
            data.date = toDateString(data.date);
        }

        return clearEmptyData(data);
    }).get(route('postViewHistory'), {
        preserveScroll: true,
        onSuccess: () => {},
    });
}

const searchPost = () => {
    config.showPostSearchClearButton = true;
    _search();
}

const searchUser = () => {
    config.showUserSearchClearButton = true;
    _search();
}

const clearUserSearch = () => {
    form.user = '';
    config.showSearchClearButton = false;
    _search();
}

const clearPostSearch = () => {
    form.postSearch = '';
    config.showPostSearchClearButton = false;
    _search();
}

const clearDateSearch = () => {
    form.date = '';
    _search();
}

const updateProgress = (progress) => {
    config.activePage = progress;
    config.searching = true;
};

const changeLimit = (limit) => {
    form.limit = limit;
    form.user = '';
    form.postSearch = '';
    form.date = '';

    _search();
}
</script>

<template>
    <app-layout :title="$t('postViewHistory')">
        <template v-slot:header>
            <h2
                class="text-xl text-gray-800 leading-tight flex-1 mr-6"
                v-text="$t('postViewHistory')"
            />
        </template>

        <div class="p-6 sm:mx-2">
            <div class="flex items-stretch">
                <div class="mt-0 w-80 mr-8">
                    <input-label for="user-search" :value="$t('searchByViewedUser')" />

                    <search-input
                        id="user-search"
                        class="mt-1 block w-full"
                        v-model="form.user"
                        :placeholder="$t('searchByViewedUserPlaceholder')"
                        :disabled="config.busy"
                        :show-clear-button="config.showUserSearchClearButton"
                        @input="config.showUserSearchClearButton = false"
                        @clear-search="clearUserSearch"
                        @enter="searchUser"
                    />
                </div>

                <div class="mt-0 w-80 mr-8">
                    <input-label for="user-search" :value="$t('post')" />

                    <search-input
                        id="user-search"
                        class="mt-1 block w-full"
                        v-model="form.postSearch"
                        :placeholder="$t('postSearch')"
                        :disabled="config.busy"
                        :show-clear-button="config.showPostSearchClearButton"
                        @input="config.showPostSearchClearButton = false"
                        @clear-search="clearPostSearch"
                        @enter="searchPost"
                    />
                </div>

                <div class="mt-0 w-80 mr-8">
                    <input-label for="user-search" :value="$t('viewedDate')" />

                    <div class="relative mt-1">
                        <date-picker
                            v-model="form.date"
                            size="small"
                            :disabled="config.busy"
                            :placeholder="$t('viewedDate')"
                            :hide-on-date-time-select="true"
                            :max-date="new Date()"
                            @date-select="_search"
                        />

                        <i
                            v-if="form.date"
                            class="absolute pi pi-times input-clear-icon text-gray-400 hover:text-red-400 hover:cursor-pointer"
                            @click="clearDateSearch"
                        />
                    </div>
                </div>
            </div>

            <grid-container :loading="config.busy">
                <data-table :value="logs['data']">
                    <column class="number-column" field="id" :header="$t('ID')" />
                    <column class="number-column" field="post_id" :header="$t('postID')" />
                    <column class="title-flex-column" field="content" :header="$t('postContent')" />
                    <column class="count-column" field="answer_count" :header="$t('answerCount')" />
                    <column class="number-column" field="user_id" :header="$t('viewedByID')" />
                    <column class="history-username-column" field="username" :header="$t('viewedBy')" />

                    <column class="time-column" :header="$t('viewedAt')">
                        <template #body="{ data }">
                            {{ formatDate(data['viewed_at']) }}
                        </template>
                    </column>

                    <column class="px-5" />
                    <template #empty>{{ $t((filters.user && filters.user !== '') || (filters.post && filters.post !== '') || (filters.date && filters.date !== '') ? 'emptyResult' : 'emptyData') }}</template>
                </data-table>
            </grid-container>

            <pagination
                v-if="logs['data'].length > 0"
                class="mt-5 flex items-center justify-center mx-auto"
                :links="logs['links']"
                :active="config.activePage"
                :disabled="config.busy"
                :limit="logs['per_page']"
                :total="logs['total']"
                :from="logs['from']"
                :to="logs['to']"
                @progress="updateProgress"
                @changeLimit="changeLimit"
            />
        </div>
    </app-layout>
</template>
