<script setup>
import { reactive, watch, ref } from "vue";
import { useI18n } from "vue-i18n";

import Modal from '@/Components/Modal.vue';
import RedButton from '@/Components/RedButton.vue';
import LoadingIcon from '@/Components/LoadingIcon.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import CommunityFormContent from "@/Pages/Community/CommunityFormContent.vue";

const { t } = useI18n();

const emit = defineEmits(['closeModal', 'communityCreated']);

const props = defineProps({
    showModal: Boolean,
});

const submit = ref(false);
const processing = ref(false);
const community = {
    name: '',
    description: '',
    image: '',
};

const config = reactive({
    showModal: false,
    open: false,
});

watch(() => props.showModal, () => {
    config.showModal = props.showModal;
    if (props.showModal) {
        setTimeout(() => config.open = true, 150);
    }
});

const closeModal = () => {
    if (processing.value) {
        return;
    }

    config.open = false;
    setTimeout(() => {
        config.showModal = false;
        emit('closeModal');
    }, 150);
}

const process = () => {
    if (processing.value) {
        return;
    }

    submit.value = true;
}

const onCommunitySaved = (community) => {
    emit('communityCreated', community);
    processing.value = false;
    closeModal();
}

const updateProcessing = (process) => {
    processing.value = process;
    if (! process) {
        submit.value = false;
    }
}
</script>

<template>
    <modal v-if="config.showModal" :show="config.open" @close="closeModal" class="community-form-modal">
        <div class="pt-4 pb-3 px-3 font-semibold" v-text="t('addNewCommunity')" />

        <community-form-content
            :community="community"
            :submit="submit"
            action="create"
            @community-saved="onCommunitySaved"
            @processing="updateProcessing"
        />

        <div class="flex items-center justify-end px-3 py-3">
            <secondary-button class="mr-3 text-sm" v-text="t('cancel')" @click="closeModal" />

            <red-button class="text-sm overflow-hidden h-[34px]" @click="process">
                <loading-icon v-if="processing" class="mr-1" />

                <span class="text-sm text-white" v-text="t('save')" />
            </red-button>
        </div>
    </modal>
</template>
