<script setup>
import { inject, reactive, watch } from "vue";
import { useForm } from "@inertiajs/vue3";
import { useI18n } from "vue-i18n";
import { route } from "ziggy-js";

import Modal from '@/Components/Modal.vue';
import RedButton from '@/Components/RedButton.vue';
import LoadingIcon from '@/Components/LoadingIcon.vue';
import TextAreaInput from '@/Components/TextAreaInput.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import ImageInput from "@/Components/ImageInput.vue";

const { t } = useI18n();

const emit = defineEmits(['closeModal', 'communityCreated']);

const props = defineProps({
    showModal: Boolean,
});

const $toast = inject('$toast');
const communityForm = useForm({
    name: '',
    description: '',
    image: '',
});

const config = reactive({
    showModal: false,
    open: false,
});

watch(() => props.showModal, () => {
    config.showModal = props.showModal;
    if (props.showModal) {
        setTimeout(() => config.open = true, 150);
    }
});

const closeModal = () => {
    config.open = false;
    setTimeout(() => {
        config.showModal = false;
        emit('closeModal');
    }, 150);
}

const addNewCommunity = () => {
    if (communityForm.processing) {
        return false;
    }

    communityForm.errors = {};
    communityForm.post(route('community.store'), {
        preserveScroll: true,
        preserveState: true,
        onSuccess: (data) => {
            if (data.props.jetstream.flash.message) {
                $toast.success(data.props.jetstream.flash.message);
            }

            if (data.props.jetstream.flash.community) {
                emit('communityCreated', data.props.jetstream.flash.community);
            }

            closeModal();
        },
    });
}
</script>

<template>
    <modal v-if="config.showModal" :show="config.open" @close="closeModal" class="community-form-modal">
        <div class="pt-4 pb-3 px-3 font-semibold" v-text="t('addNewCommunity')" />

        <div class="border-t border-b px-6 pt-4 pb-6 space-y-4">
            <div>
                <label v-text="t('communityName')" class="w-full" />
                <text-input
                    class="block w-full mt-1"
                    v-model="communityForm['name']"
                    :disabled="communityForm.processing"
                    rows="4"
                />
                <input-error class="w-full mt-1" :message="communityForm.errors['name']" />
            </div>

            <div>
                <label v-text="t('communityDescription')" class="w-full" />
                <text-area-input
                    class="block w-full mt-1"
                    v-model="communityForm['description']"
                    :disabled="communityForm.processing"
                    rows="4"
                />
                <input-error class="w-full mt-1" :message="communityForm.errors['description']" />
            </div>

            <div>
                <image-input
                    class="flex-1"
                    :label="t('image')"
                    :error="communityForm.errors['image']"
                    :disabled="communityForm.processing"
                    v-model="communityForm.image"
                    @update:modelValue="communityForm.image = $event"
                />
            </div>
        </div>

        <div class="flex items-center justify-end px-3 py-3">
            <secondary-button class="mr-3 text-sm" v-text="t('cancel')" @click="closeModal" />

            <red-button class="text-sm overflow-hidden h-[34px]" @click="addNewCommunity">
                <loading-icon v-if="communityForm.processing" class="mr-1" />

                <span class="text-sm text-white" v-text="t('save')" />
            </red-button>
        </div>
    </modal>
</template>
