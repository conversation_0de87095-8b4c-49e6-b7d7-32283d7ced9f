<script setup>
import { inject, ref } from "vue";
import { useForm, Link as InertiaLink } from "@inertiajs/vue3";
import { useI18n } from "vue-i18n";
import { route } from "ziggy-js";

import AppLayout from '@/Layouts/AppLayout.vue';
import RedButton from '@/Components/RedButton.vue';
import LoadingIcon from '@/Components/LoadingIcon.vue';
import TextAreaInput from '@/Components/TextAreaInput.vue';
import InputError from '@/Components/InputError.vue';
import FixedSelectionBox from "@/Components/FixedSelectionBox.vue";
import ImageInput from "@/Components/ImageInput.vue";
import CommunitySelectionBox from "@/Components/CommunitySelectionBox.vue";
import CommunityFormModal from "./CommunityFormModal.vue";
import Switcher from '@/Components/Switcher.vue';
import { Plus } from '@element-plus/icons-vue';
import { useCommunityStore } from "@/Stores/community.js";

const props = defineProps({
    post: Object,
    action: String,
});

const { t } = useI18n();
const communityStore = useCommunityStore();

const typeOptions = [
    {
        value: 'answerr_q',
        label: t('answerrQ'),
    },
    {
        value: 'answerr_topic',
        label: t('answerrTopic'),
    },
];

const $toast = inject('$toast');
const postForm = useForm(props.post);

const showModal = ref(false);
const onCommunityCreated = async (community) => {
    await communityStore.prepend(community);
    postForm.community_id = community.community_id;
}

const openCommunityFormModal = () => {
    if (props.action === 'update') {
        return false;
    }

    showModal.value = true;
}

const save = () => {
    if (postForm.processing) {
        return false;
    }

    postForm.errors = {};

    postForm.transform(data => {
        if (typeof data.image === 'string') {
            delete data.image;
        }

        return data;
    }).post(route('post.store'), {
        preserveScroll: true,
        preserveState: true,
        onSuccess: (data) => {
            if (data.props.jetstream.flash.message) {
                $toast.success(data.props.jetstream.flash.message);
            }

            if (props.action === 'create') {
                setTimeout(() => {
                    window.location = route('post.list');
                }, 1500);
            }
        },
    });
}
</script>

<template>
    <app-layout :title="$t(props.action + 'Post')">
        <template v-slot:header>
            <div class="flex-1 flex items-center">
                <h2
                    class="text-xl text-gray-800 leading-tight flex-1 mr-6"
                    v-text="$t(props.action + 'Post')"
                />

                <inertia-link class="primary-button text-sm" :href="route('post.list')" v-text="$t('list')" />
            </div>
        </template>

        <div class="max-w-7xl mx-auto py-6 px-6">
            <div class="bg-white rounded-md shadow px-5 py-4 space-y-4">
                <div class="flex flex-col">
                    <label v-text="$t('postContent')" class="w-full" />
                    <text-area-input
                        class="block w-full mt-1"
                        v-model="postForm['content']"
                        :disabled="postForm.processing"
                        rows="4"
                    />
                    <input-error class="w-full mt-1" :message="postForm.errors['content']" />
                </div>

                <div class="flex space-x-6">
                    <div class="flex-1 flex flex-col">
                        <label v-text="$t('postType')" class="w-full mb-1" />
                        <fixed-selection-box
                            v-model="postForm.type"
                            :placeholder="$t('postType')"
                            :disabled="postForm.processing"
                            :options="typeOptions"
                            :clearable="false"
                        />
                        <input-error class="w-full mt-1" :message="postForm.errors['type']" />
                    </div>

                    <div class="flex-1 flex flex-col">
                        <image-input
                            class="flex-1"
                            :label="$t('image')"
                            :error="postForm.errors['image']"
                            :disabled="postForm.processing"
                            v-model="postForm.image"
                            @update:modelValue="postForm.image = $event"
                        />
                    </div>
                </div>

                <div class="flex space-x-6">
                    <div class="flex-1 flex flex-col">
                        <div class="flex items-end">
                            <div class="flex flex-col flex-1">
                                <label v-text="$t('community')" class="w-full" />
                                <community-selection-box
                                    v-model="postForm.community_id"
                                    :placeholder="$t('community')"
                                    :disabled="postForm.processing || action === 'update'"
                                    :clearable="false"
                                />
                            </div>

                            <div class="flex flex-col ml-1.5">
                                <button
                                    class="rounded-md border transition-colors duration-200 border-sky-400 p-2.5 focus:outline-none text-white"
                                    :class="{ 'bg-gray-200 cursor-default': postForm.processing, 'bg-sky-400 disabled:hover:bg-sky-400 disabled:hover:border-sky-400 hover:bg-sky-500 hover:border-sky-500': ! postForm.processing }"
                                    :disabled="postForm.processing || action === 'update'"
                                    @click="openCommunityFormModal"
                                >
                                    <plus class="w-5 h-5" aria-hidden="true" />
                                </button>
                            </div>
                        </div>

                        <input-error class="w-full mt-1" :message="postForm.errors['community_id']" />
                    </div>

                    <div class="flex-1 flex flex-col">
                        <Switcher v-model="postForm.camera_roll" :label="t('cameraRoll')" />
                    </div>
                </div>
            </div>

            <div class="mt-4 flex">
                <red-button class="normal-case ml-auto" :disabled="postForm.processing" @click="save">
                    <loading-icon v-if="postForm.processing" class="mr-2" />
                    <span class="text-sm" v-text="$t('save')" />
                </red-button>
            </div>
        </div>
    </app-layout>

    <community-form-modal
        :show-modal="showModal"
        @closeModal="showModal = false"
        @community-created="onCommunityCreated"
    />
</template>
