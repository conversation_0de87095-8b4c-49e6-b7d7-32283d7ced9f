<template>
    <app-layout :title="$t('postAnswerInfo')">
        <template #header>
            <h2
                class="text-xl text-gray-800 leading-tight mr-auto"
                v-text="$t('postAnswerInfo')"
            />
        </template>

        <div class="max-w-7xl mx-auto p-6">
            <div class="bg-white rounded-md shadow flex flex-col mb-6 overflow-hidden">
                <table class="w-full">
                    <thead>
                        <tr class="text-left flex">
                            <th colspan="2" v-text="$t('generalInfo')" />
                        </tr>
                    </thead>

                    <tbody>
                        <tr
                            v-for="column in attributes"
                            class="text-left flex border-t even:bg-blue-50 odd:bg-white"
                        >
                            <td class="w-1/4" v-text="$t(column.label)" />
                            <td class="border-l flex-1">
                                <inertia-link
                                    v-if="column.label === 'postID'"
                                    class="hover:text-red-600 hover:underline"
                                    v-text="answer.post_id"
                                    :href="route('post.detail', {post: answer.post_id})"
                                />

                                <template v-else>{{ getCellValue(answer, column) }}</template>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </app-layout>
</template>

<script setup>
import { useI18n } from "vue-i18n";
import { formatDate } from "@/Utils/index.js";
import { Link as InertiaLink } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
    answer: Object,
});

const { t } = useI18n();

const attributes = [
    {
        label: 'ID',
        attribute: 'answer_id',
    },
    {
        label: 'postID',
        attribute: 'post_id',
    },
    {
        label: 'userPostID',
        attribute: 'post_userID',
    },
    {
        label: 'postCreatedBy',
        attribute: 'post_username',
    },
    {
        label: 'postContent',
        attribute: 'post_content',
    },
    {
        label: 'userAnswerID',
        attribute: 'user_id',
    },
    {
        label: 'answeredBy',
        attribute: 'username',
    },
    {
        label: 'postAnswerContent',
        attribute: 'content',
    },
    {
        label: 'answerCreatedAt',
        attribute: 'created_at',
    },
    {
        label: 'status',
        attribute: 'status_label',
    },
];

const getCellValue = (answer, column) => {
    const value = answer[column.attribute];

    return ['postCreatedAt'].includes(column.label) ? formatDate(value) : value;
}
</script>
