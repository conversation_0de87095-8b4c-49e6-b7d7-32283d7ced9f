<template>
    <app-layout :title="$t('listPostAnswer')">
        <template #header>
            <h2
                class="text-xl text-gray-800 leading-tight mr-auto"
                v-text="$t('listPostAnswer')"
            />
        </template>

        <div class="p-6 sm:mx-2">
            <div class="flex items-stretch">
                <div class="mt-0 w-80 mr-8">
                    <input-label for="search" :value="$t('search')" />

                    <search-input
                        id="search"
                        class="mt-1 block w-full"
                        v-model="form.search"
                        :placeholder="$t('answerSearch')"
                        :disabled="config.busy"
                        :show-clear-button="config.showSearchClearButton"
                        @input="config.showSearchClearButton = false"
                        @clear-search="clearSearch"
                        @enter="search"
                    />
                </div>

                <div class="mt-0 w-80 mr-8">
                    <input-label for="search-post" :value="$t('post')" />

                    <search-input
                        id="search-post"
                        class="mt-1 block w-full"
                        v-model="form.postSearch"
                        :placeholder="$t('postSearch')"
                        :disabled="config.busy"
                        :show-clear-button="config.showPostSearchClearButton"
                        @input="config.showPostSearchClearButton = false"
                        @clear-search="clearPostSearch"
                        @enter="searchPost"
                    />
                </div>

                <div class="mt-0 w-96 mr-8">
                    <input-label for="search-user" :value="$t('answeredBy')" />

                    <search-input
                        id="search-user"
                        class="mt-1 block w-full"
                        v-model="form.user"
                        :placeholder="$t('postUserSearch')"
                        :disabled="config.busy"
                        :show-clear-button="config.showUserSearchClearButton"
                        @input="config.showUserSearchClearButton = false"
                        @clear-search="clearUserSearch"
                        @enter="searchUser"
                    />
                </div>

                <div class="mt-0 w-80 mr-8">
                    <input-label for="report-search" :value="$t('reportStatus')" class="mb-1" />

                    <fixed-selection-box
                        v-model="form.reported"
                        :placeholder="$t('all')"
                        :disabled="config.busy"
                        :options="config.reportOptions"
                        :clearable="true"
                        @cleared="_search"
                        @selected="_search"
                    />
                </div>
            </div>

            <grid-container :loading="config.searching || form.processing">
                <data-table :value="answers['data']">
                    <column class="number-column" :header="$t('ID')">
                        <template #body="{ data }">
                            <inertia-link
                                class="hover:text-sky-600 hover:underline"
                                v-text="data['answer_id']"
                                :href="route('postAnswer.detail', { postAnswer: data['answer_id'] })"
                            />
                        </template>
                    </column>

                    <column class="post-username-column" :header="$t('answeredBy')">
                        <template #body="{ data }">
                            <inertia-link
                                class="hover:text-red-600 hover:underline"
                                v-text="data['username']"
                                :href="route('postAnswer.list', { user: data['user_id'] })"
                            />
                        </template>
                    </column>

                    <column class="number-column extra" :header="$t('createdByID')">
                        <template #body="{ data }">
                            <inertia-link
                                class="hover:text-red-600 hover:underline"
                                v-text="data['user_id']"
                                :href="route('postAnswer.list', { user: data['user_id'] })"
                            />
                        </template>
                    </column>

                    <column class="title-flex-column" field="content" :header="$t('postAnswerContent')" />
                    <column class="time-column" field="tag" :header="$t('answerCreatedAt')">
                        <template #body="{ data }">{{ formatDate(data['created_at']) }}</template>
                    </column>

                    <column class="number-column" field="tag" :header="$t('postID')">
                        <template #body="{ data }">
                            <inertia-link
                                class="hover:text-sky-600 hover:underline"
                                v-text="data['post_id']"
                                :href="route('post.detail', { post: data['post_id'] })"
                            />
                        </template>
                    </column>

                    <column class="post-username-column" field="post_username" :header="$t('postCreatedBy')" />
                    <column class="title-flex-column" field="post_content" :header="$t('postContent')" />
                    <column class="count-column" field="report_count" :header="$t('reportCount')" />
                    <column class="status-column" field="type" :header="$t('postAnswerType')" />
                    <column class="status-column" field="status_label" :header="$t('status')" />
                    <column class="action-column small">
                        <template #body="{ data }">
                            <inertia-link
                                :href="route('postAnswer.detail', { postAnswer: data['answer_id'] })"
                            >
                                <i class="pi pi-info-circle text-gray-500 hover:text-sky-600" />
                            </inertia-link>

                            <i
                                v-if="parseInt(data['status']) !== 0"
                                class="pi pi-trash text-red-400 hover:text-red-600 hover:cursor-pointer"
                                @click="confirmDeletePostAnswer(data['answer_id'])"
                            />
                        </template>
                    </column>

                    <template #empty>{{ $t((filters.search && filters.search !== '') || (filters.post && filters.post !== '') || (filters.user && filters.user !== '') ? 'emptyResult' : 'emptyData') }}</template>
                </data-table>
            </grid-container>

            <pagination
                v-if="answers['data'].length > 0"
                class="mt-5 flex items-center justify-center mx-auto"
                :links="answers['links']"
                :active="config.activePage"
                :disabled="config.busy"
                :limit="answers['per_page']"
                :total="answers['total']"
                :from="answers['from']"
                :to="answers['to']"
                @progress="updateProgress"
                @changeLimit="changeLimit"
            />
        </div>
    </app-layout>

    <modal
        v-if="config.showModal"
        :show="config.open"
        @close="closeModal"
    >
        <div class="pt-4 pb-3 px-4 font-semibold" v-text="$t('confirm')" />
        <div class="border-t border-b p-4" v-text="$t('confirmDeletePostAnswerMessage')" />
        <div class="flex items-center justify-end p-3">
            <secondary-button
                class="mr-3 text-sm"
                v-text="$t('cancel')"
                @click="closeModal"
            />

            <red-button
                class="text-sm overflow-hidden h-[34px]"
                @click="deletePostAnswer"
            >
                <loading-icon class="mr-2" v-if="config.deleting" />
                <span class="text-sm text-white" v-text="$t('delete')" />
            </red-button>
        </div>
    </modal>
</template>

<script setup>
import { computed, inject, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { clearEmptyData, formatDate } from "@/Utils/index.js";
import { Link as InertiaLink, router, useForm, usePage } from '@inertiajs/vue3';

import DataTable from 'primevue/datatable';
import Column from 'primevue/column';

import AppLayout from '@/Layouts/AppLayout.vue';
import InputLabel from "@/Components/InputLabel.vue";
import SearchInput from "@/Components/SearchInput.vue";
import Pagination from "@/Components/Pagination.vue";
import LoadingIcon from "@/Components/LoadingIcon.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import RedButton from "@/Components/RedButton.vue";
import Modal from "@/Components/Modal.vue";
import FixedSelectionBox from "@/Components/FixedSelectionBox.vue";
import GridContainer from "@/Components/GridContainer.vue";
import { route } from "ziggy-js";

const $toast = inject('$toast');
const page = usePage();
const { t } = useI18n();

const props = defineProps({
    filters: Object,
    answers: Object,
});

const form = useForm({
    search: props.filters['search'] ?? '',
    user: props.filters['user'] ?? '',
    postSearch: props.filters['post'] ?? '',
    reported: props.filters['reported'] ?? '',
    limit: props.filters['limit'] ?? 10,
});

const config = reactive({
    showSearchClearButton: (props.filters.search ?? '').trim().length > 0,
    showPostSearchClearButton: (props.filters.post ?? '').trim().length > 0,
    showUserSearchClearButton: (props.filters.user ?? '').trim().length > 0,
    searching: false,
    showModal: false,
    open: false,
    deleteId: null,
    deleting: false,
    reportOptions: [
        {
            value: 'yes',
            label: t('yes'),
        },
        {
            value: 'no',
            label: t('no'),
        },
    ],
    activePage: null,
    busy: computed(() => config.activePage !== null || config.searching || form.processing),
});

const _search = () => {
    if (form.processing) {
        return false;
    }

    form.transform((data) => {
        data['post'] = data.postSearch;
        delete data['postSearch'];

        return clearEmptyData(data);
    }).get(route('postAnswer.list'), {
        preserveScroll: true,
        onSuccess: () => {},
    });
}

const search = () => {
    config.showSearchClearButton = true;
    _search();
}

const searchPost = () => {
    config.showPostSearchClearButton = true;
    _search();
}

const searchUser = () => {
    config.showUserSearchClearButton = true;
    _search();
}

const clearSearch = () => {
    form.search = '';
    config.showSearchClearButton = false;
    _search();
}

const clearPostSearch = () => {
    form.postSearch = '';
    config.showPostSearchClearButton = false;
    _search();
}

const clearUserSearch = () => {
    form.user = '';
    config.showUserSearchClearButton = false;
    _search();
}

const confirmDeletePostAnswer = (userId) => {
    config.deleteId = userId;
    config.showModal = true;
    setTimeout(() => config.open = true, 150);
}

const closeModal = async () => {
    config.open = false;
    setTimeout(() => config.showModal = false, 150);
}

const deletePostAnswer = () => {
    if (config.deleting) {
        return;
    }

    router.post(route('postAnswer.delete'), {
        id: config.deleteId,
    }, {
        preserveScroll: true,
        preserveState: true,
        onBefore: () => {
            config.deleting = true;
        },
        onSuccess: () => {
            config.deleteId = null;

            if (page.props.jetstream.flash.message) {
                $toast.success(page.props.jetstream.flash.message);
            }

            closeModal();
        },
        onFinish: () => {
            config.deleting = false;
        },
    });
}

const updateProgress = (progress) => {
    config.activePage = progress;
    config.searching = true;
};

const changeLimit = (limit) => {
    form.limit = limit;
    form.search = '';
    form.user = '';
    form.postSearch = '';
    form.reported = '';

    _search();
}
</script>
