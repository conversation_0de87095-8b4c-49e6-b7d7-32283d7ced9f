<script setup>
import { computed } from 'vue';
import { Head as InertialHead } from '@inertiajs/vue3';

const props = defineProps({
    status: Number,
});

const title = computed(() => {
    return {
        503: '503: Service Unavailable',
        500: '500: Server Error',
        404: '404: Page Not Found',
        403: '403: Forbidden',
    }[props.status];
});
</script>

<template>
    <inertial-head :title="title ?? $t('errorTitle')" />

    <div class="min-h-screen bg-gray-100 font-light flex items-center justify-center">
        <div class="text-lg" v-text="$t(status ? 'error.' + status : 'errorMessage')" />
    </div>
</template>
