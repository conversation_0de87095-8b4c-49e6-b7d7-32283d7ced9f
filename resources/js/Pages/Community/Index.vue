<script setup>
import { inject, ref } from "vue";
import { Link as InertiaLink, router, usePage } from '@inertiajs/vue3';

import DataTable from 'primevue/datatable';
import Column from 'primevue/column';

import AppLayout from '@/Layouts/AppLayout.vue';
import { route } from "ziggy-js";
import GridContainer from "@/Components/GridContainer.vue";
import { confirmDelete } from "@/Utils/index.js";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const $toast = inject('$toast');
const page = usePage();

defineProps({
    communities: Object,
});

const deleting = ref(false);
const deleteCommunity = async (id) => {
    if (deleting.value) {
        return;
    }

    await confirmDelete(t('Are you sure you want to delete this community?'), async (resolve) => {
        router.post(route('community.delete'), {
            community_id: id,
        }, {
            preserveScroll: true,
            preserveState: true,
            onBefore: () => {
                deleting.value = true;
            },
            onSuccess: () => {
                if (page.props.jetstream.flash.message) {
                    $toast.success(page.props.jetstream.flash.message);
                }
            },
            onFinish: () => {
                deleting.value = false;
                resolve(true);
            },
        });
    });
}
</script>

<template>
    <app-layout :title="$t('communityList')">
        <template v-slot:header>
            <div class="flex-1 flex items-center">
                <h2
                    class="text-xl text-gray-800 leading-tight mr-auto"
                    v-text="$t('communityList')"
                />

                <inertia-link class="primary-button text-sm" :href="route('community.form', { community: '' })" v-text="$t('addNew')" />
            </div>
        </template>

        <div class="p-6 sm:mx-2">
            <grid-container style="margin-top: 0;">
                <data-table :value="communities['data']">
                    <column class="number-column small" field="community_id" :header="$t('ID')" />

                    <column class="w-[400px]" field="name" :header="$t('communityName')" />
                    <column class="title-flex-column" :header="$t('communityDescription')">
                        <template #body="{ data }">
                            <div v-html="data['description'].replace(/(\r\n|\n|\r)/g, '<br />')" />
                        </template>
                    </column>

                    <column class="status-column" :header="$t('image')">
                        <template #body="{ data }">
                            <img class="max-w-full max-h-full" :src="data['image']"  :alt="data['name']" />
                        </template>
                    </column>

                    <column class="action-column small">
                        <template #body="{ data }">
                            <inertia-link :href="route('community.form', { community: data['community_id'] })">
                                <i class="pi pi-pen-to-square text-blue-400 hover:text-blue-600" />
                            </inertia-link>

                            <i
                                class="pi pi-trash text-red-400 hover:text-red-600 hover:cursor-pointer"
                                @click="deleteCommunity(data['community_id'])"
                            />
                        </template>
                    </column>

                    <template #empty>{{ $t('emptyData') }}</template>
                </data-table>
            </grid-container>
        </div>
    </app-layout>
</template>
