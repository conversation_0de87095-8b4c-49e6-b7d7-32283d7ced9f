<template>
    <div class="border-t border-b px-6 pt-4 pb-6 space-y-4">
        <div>
            <label v-text="t('communityName')" class="w-full" />
            <text-input
                class="block w-full mt-1"
                v-model="communityForm['name']"
                :disabled="communityForm.processing"
                rows="4"
            />
            <input-error class="w-full mt-1" :message="communityForm.errors['name']" />
        </div>

        <div>
            <label v-text="t('communityDescription')" class="w-full" />
            <text-area-input
                class="block w-full mt-1"
                v-model="communityForm['description']"
                :disabled="communityForm.processing"
                rows="4"
            />
            <input-error class="w-full mt-1" :message="communityForm.errors['description']" />
        </div>

        <div>
            <image-input
                class="flex-1"
                :label="t('image')"
                :error="communityForm.errors['image']"
                :disabled="communityForm.processing"
                v-model="communityForm.image"
                @update:modelValue="communityForm.image = $event"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { inject, watch } from "vue";
import { useI18n } from "vue-i18n";

import ImageInput from "@/Components/ImageInput.vue";
import TextAreaInput from "@/Components/TextAreaInput.vue";
import InputError from "@/Components/InputError.vue";
import TextInput from "@/Components/TextInput.vue";
import { useForm } from "@inertiajs/vue3";
import { route } from "ziggy-js";

const emit = defineEmits(['communitySaved', 'processing']);

const props = defineProps({
    community: Object,
    submit: Boolean,
});

const communityForm = useForm(props.community);

const { t } = useI18n();
const $toast = inject('$toast');

const saveCommunity = async () => {
    if (communityForm.processing) {
        return false;
    }

    emit('processing', true);
    communityForm.errors = {};
    communityForm.transform(data => {
        if (typeof data.image === 'string') {
            delete data.image;
        }

        return data;
    }).post(route('community.store'), {
        preserveScroll: true,
        preserveState: true,
        onSuccess: (data) => {
            if (data.props.jetstream.flash.message) {
                $toast.success(data.props.jetstream.flash.message);
            }

            if (data.props.jetstream.flash.community) {
                emit('communitySaved', data.props.jetstream.flash.community);
            }
        },
        onFinish: () => {
            emit('processing', false);
        }
    });
}

watch(() => props.submit, (val) => {
    if (val) {
        saveCommunity();
    }
});
</script>
