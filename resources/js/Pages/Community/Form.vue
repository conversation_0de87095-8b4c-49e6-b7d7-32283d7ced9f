<script setup>
import { ref } from "vue";
import { Link as InertiaLink } from "@inertiajs/vue3";

import AppLayout from '@/Layouts/AppLayout.vue';
import RedButton from '@/Components/RedButton.vue';
import LoadingIcon from '@/Components/LoadingIcon.vue';
import CommunityFormContent from "@/Pages/Community/CommunityFormContent.vue";

const props = defineProps({
    community: Object,
    action: String,
});

const submit = ref(false);
const processing = ref(false);

const process = () => {
    if (processing.value) {
        return;
    }

    submit.value = true;
}

const onCommunitySaved = () => {
    processing.value = false;
}

const updateProcessing = (process) => {
    processing.value = process;
    if (! process) {
        submit.value = false;
    }
}
</script>

<template>
    <app-layout :title="$t(props.action + 'Community')">
        <template v-slot:header>
            <div class="flex-1 flex items-center">
                <h2
                    class="text-xl text-gray-800 leading-tight flex-1 mr-6"
                    v-text="$t(props.action + 'Community')"
                />

                <inertia-link class="primary-button text-sm mr-3" :href="route('community.list')" v-text="$t('list')" />

                <red-button class="normal-case" :disabled="processing" @click="process">
                    <loading-icon v-if="processing" class="mr-2" />
                    <span class="text-sm" v-text="$t('save')" />
                </red-button>
            </div>
        </template>

        <div class="max-w-7xl mx-auto py-6 px-6">
            <div class="bg-white rounded-md shadow flex flex-col overflow-auto community-form-container">
                <community-form-content
                    :community="community"
                    :submit="submit"
                    action="create"
                    @community-saved="onCommunitySaved"
                    @processing="updateProcessing"
                />
            </div>
        </div>
    </app-layout>
</template>
