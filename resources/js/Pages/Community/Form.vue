<script setup>
import { inject, ref } from "vue";
import { Link as InertiaLink, router, usePage } from "@inertiajs/vue3";

import AppLayout from '@/Layouts/AppLayout.vue';
import RedButton from '@/Components/RedButton.vue';
import LoadingIcon from '@/Components/LoadingIcon.vue';
import CommunityFormContent from "@/Pages/Community/CommunityFormContent.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import { confirmDelete } from "@/Utils/index.js";
import { route } from "ziggy-js";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const $toast = inject('$toast');
const page = usePage();

const props = defineProps({
    community: Object,
    action: String,
});

const submit = ref(false);
const processing = ref(false);
const deleting = ref(false);

const process = () => {
    if (processing.value) {
        return;
    }

    submit.value = true;
}

const onCommunitySaved = () => {
    processing.value = false;
}

const updateProcessing = (process) => {
    processing.value = process;
    if (! process) {
        submit.value = false;
    }
}

const deleteCommunity = async (id) => {
    if (deleting.value) {
        return;
    }

    await confirmDelete(t('Are you sure you want to delete this community?'), async (resolve) => {
        router.post(route('community.delete'), {
            community_id: id,
            redirect: true,
        }, {
            preserveScroll: true,
            preserveState: true,
            onBefore: () => {
                deleting.value = true;
            },
            onSuccess: () => {
                if (page.props.jetstream.flash.message) {
                    $toast.success(page.props.jetstream.flash.message);
                }
            },
            onFinish: () => {
                deleting.value = false;
                resolve(true);
            },
        });
    });
}
</script>

<template>
    <app-layout :title="$t(props.action + 'Community')">
        <template v-slot:header>
            <div class="flex-1 flex items-center">
                <h2
                    class="text-xl text-gray-800 leading-tight flex-1 mr-6"
                    v-text="$t(props.action + 'Community')"
                />

                <inertia-link class="primary-button text-sm" :href="route('community.list')" v-text="$t('list')" />
            </div>
        </template>

        <div class="max-w-7xl mx-auto py-6 px-6">
            <div class="bg-white rounded-md shadow flex flex-col overflow-auto community-form-container">
                <community-form-content
                    :community="community"
                    :submit="submit"
                    action="create"
                    @community-saved="onCommunitySaved"
                    @processing="updateProcessing"
                />
            </div>

            <div class="flex mt-4">
                <div class="ml-auto flex items-center">
                    <red-button v-if="action === 'update'" class="normal-case ml-auto" :disabled="deleting" @click="deleteCommunity(community['community_id'])">
                        <span class="text-sm" v-text="$t('delete')" />
                    </red-button>

                    <primary-button class="normal-case ml-3" :disabled="processing" @click="process">
                        <loading-icon v-if="processing" class="mr-2" />
                        <span class="text-sm" v-text="$t('save')" />
                    </primary-button>
                </div>
            </div>
        </div>
    </app-layout>
</template>
