<script setup>
import { computed, reactive } from "vue";
import { Link as InertiaLink, useForm } from '@inertiajs/vue3';

import DataTable from 'primevue/datatable';
import Column from 'primevue/column';

import AppLayout from '@/Layouts/AppLayout.vue';
import InputLabel from "@/Components/InputLabel.vue";
import SearchInput from "@/Components/SearchInput.vue";
import Pagination from "@/Components/Pagination.vue";
import { clearEmptyData } from "@/Utils/index.js";
import { route } from "ziggy-js";
import GridContainer from "@/Components/GridContainer.vue";

const props = defineProps({
    filters: Object,
    features: Object,
});

const form = useForm({
    search: props.filters['search'] ?? '',
    limit: props.filters['limit'] ?? 10,
});

const config = reactive({
    showSearchClearButton: (props.filters.search ?? '').trim().length > 0,
    searching: false,
    activePage: null,
    busy: computed(() => config.activePage !== null || config.searching || form.processing),
});

const _search = () => {
    if (form.processing) {
        return false;
    }

    form.transform((data) => clearEmptyData(data)).get(route('premiumFeature.list'), {
        preserveScroll: true,
        onSuccess: () => {},
    });
}

const search = () => {
    config.showSearchClearButton = true;
    _search();
}

const clearSearch = () => {
    form.search = '';
    config.showSearchClearButton = false;
    _search();
}


const updateProgress = (progress) => {
    config.activePage = progress;
    config.searching = true;
};

const changeLimit = (limit) => {
    form.limit = limit;
    form.search = '';

    _search();
}
</script>

<template>
    <app-layout :title="$t('premiumFeature.list')">
        <template v-slot:header>
            <div class="flex-1 flex items-center">
                <h2
                    class="text-xl text-gray-800 leading-tight mr-auto"
                    v-text="$t('premiumFeature.list')"
                />

                <inertia-link class="primary-button text-sm" :href="route('premiumFeature.form', { feature: '' })" v-text="$t('addNew')" />
            </div>
        </template>

        <div class="p-6 sm:mx-2">
            <div class="flex items-stretch">
                <div class="mt-0 w-80 mr-8">
                    <input-label for="search" :value="$t('search')" />

                    <search-input
                        id="search"
                        class="block w-full"
                        v-model="form.search"
                        :placeholder="$t('premiumFeature.search')"
                        :disabled="config.busy"
                        :show-clear-button="config.showSearchClearButton"
                        @input="config.showSearchClearButton = false"
                        @clear-search="clearSearch"
                        @enter="search"
                    />
                </div>
            </div>

            <grid-container :loading="config.busy">
                <data-table :value="features['data']">
                    <column class="number-column small" field="premium_id" :header="$t('ID')" />

                    <column class="w-96" field="name" :header="$t('premiumFeature.name')" />
                    <column class="flex-1" :header="$t('premiumFeature.description')">
                        <template #body="{ data }">
                            <div v-html="data['description'].replace(/(\r\n|\n|\r)/g, '<br />')" />
                        </template>
                    </column>

                    <column class="status-column" field="price" :header="$t('coin')" />

                    <column class="status-column" :header="$t('image')">
                        <template #body="{ data }">
                            <img class="max-w-full max-h-full" :src="data['image']" />
                        </template>
                    </column>

                    <column class="action-column small">
                        <template #body="{ data }">
                            <inertia-link
                                v-if="data['status'] !== 0"
                                :href="route('premiumFeature.form', { feature: data['premium_id'] })"
                            >
                                <i class="pi pi-pen-to-square text-blue-400 hover:text-blue-600" />
                            </inertia-link>
                        </template>
                    </column>

                    <template #empty>{{ $t((filters.search && filters.search !== '') || (filters.status && filters.status !== '') ? 'emptyResult' : 'emptyData') }}</template>
                </data-table>
            </grid-container>

            <pagination
                v-if="features['data'].length > 0"
                class="mt-5 flex items-center justify-center mx-auto"
                :links="features['links']"
                :active="config.activePage"
                :disabled="config.busy"
                :limit="features['per_page']"
                :total="features['total']"
                :from="features['from']"
                :to="features['to']"
                @progress="updateProgress"
                @changeLimit="changeLimit"
            />
        </div>
    </app-layout>
</template>
