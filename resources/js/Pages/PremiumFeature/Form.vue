<script setup>
import { inject } from "vue";
import { useForm, Link as InertiaLink } from "@inertiajs/vue3";

import AppLayout from '@/Layouts/AppLayout.vue';
import RedButton from '@/Components/RedButton.vue';
import LoadingIcon from '@/Components/LoadingIcon.vue';
import TextInput from '@/Components/TextInput.vue';
import TextAreaInput from '@/Components/TextAreaInput.vue';
import InputError from '@/Components/InputError.vue';
import FixedSelectionBox from "@/Components/FixedSelectionBox.vue";
import ImageInput from "@/Components/ImageInput.vue";

const props = defineProps({
    feature: Object,
    types: Object,
    action: String,
});

const $toast = inject('$toast');
const featureForm = useForm(props.feature);

const save = () => {
    if (featureForm.processing) {
        return false;
    }

    featureForm.errors = {};

    featureForm.transform(data => {
        if (typeof data['image'] === 'string') {
            delete data['image'];
        }

        return data;
    }).post(route('premiumFeature.store'), {
        forceFormData: true,
        preserveScroll: true,
        preserveState: true,
        onSuccess: (data) => {
            if (data.props.jetstream.flash.message) {
                $toast.success(data.props.jetstream.flash.message);
            }

            if (props.action === 'create') {
                window.location = route('premiumFeature.list');
            }
        },
    });
}
</script>

<template>
    <app-layout :title="$t('premiumFeature.' + props.action)">
        <template v-slot:header>
            <div class="flex-1 flex items-center">
                <h2
                    class="text-xl text-gray-800 leading-tight flex-1 mr-6"
                    v-text="$t('premiumFeature.' + props.action)"
                />

                <inertia-link class="primary-button text-sm mr-3" :href="route('premiumFeature.list')" v-text="$t('list')" />

                <red-button class="normal-case" :disabled="featureForm.processing" @click="save">
                    <loading-icon v-if="featureForm.processing" class="mr-2" />
                    <span class="text-sm" v-text="$t('save')" />
                </red-button>
            </div>
        </template>

        <div class="max-w-7xl mx-auto py-6 px-6">
            <div class="bg-white rounded-md shadow flex flex-col overflow-auto px-5 pt-3 pb-4">
                <div class="flex flex-col pb-3">
                    <label v-text="$t('premiumFeature.name')" class="w-full" />
                    <text-input
                        class="block w-full"
                        type="text"
                        v-model="featureForm['name']"
                        :disabled="featureForm.processing"
                    />

                    <input-error class="w-full mt-1" :message="featureForm.errors['name']" />
                </div>

                <div class="flex pb-3">
                    <div class="flex flex-col flex-1 mr-3">
                        <label v-text="$t('premiumFeature.type')" class="w-full mb-1" />
                        <fixed-selection-box
                            v-model="featureForm.type"
                            :placeholder="$t('premiumFeature.type')"
                            :disabled="props.action === 'update' || featureForm.processing"
                            :options="props.types"
                        />

                        <input-error class="w-full mt-1" :message="featureForm.errors['type']" />
                    </div>

                    <div class="flex flex-col flex-1 mx-3">
                        <label v-text="$t('premiumFeature.price')" class="w-full mb-1" />
                        <text-input
                            class="block w-full"
                            type="text"
                            v-model="featureForm['price']"
                            :disabled="featureForm.processing"
                        />

                        <input-error class="w-full mt-1" :message="featureForm.errors['price']" />
                    </div>

                    <image-input
                        class="flex-1 ml-3"
                        :label="$t('image')"
                        :error="featureForm.errors['image']"
                        :disabled="featureForm.processing"
                        v-model="featureForm.image"
                        @update:modelValue="featureForm.image = $event"
                    />
                </div>

                <div class="flex flex-col">
                    <label v-text="$t('premiumFeature.description')" class="w-full mb-1" />
                    <text-area-input
                        class="block w-full resize-none h-[150px]"
                        type="text"
                        v-model="featureForm['description']"
                        :disabled="featureForm.processing"
                    />

                    <input-error class="w-full mt-1" :message="featureForm.errors['description']" />
                </div>
            </div>
        </div>
    </app-layout>
</template>
