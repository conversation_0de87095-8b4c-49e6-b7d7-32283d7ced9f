<script setup>
import { Link, router, useForm, usePage } from '@inertiajs/vue3';
import { computed, inject, reactive, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { Close } from "@element-plus/icons-vue";

import AppLayout from '@/Layouts/AppLayout.vue';
import RedButton from "@/Components/RedButton.vue";
import LoadingIcon from "@/Components/LoadingIcon.vue";
import InputLabel from "@/Components/InputLabel.vue";
import TextInput from "@/Components/TextInput.vue";
import InputError from "@/Components/InputError.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import SurveySelectionBox from "@/Components/SurveySelectionBox.vue";
import FixedSelectionBox from "@/Components/FixedSelectionBox.vue";

const { t } = useI18n();

const props = defineProps({
    title: String,
    attachedSurvey: Object,
});

const $toast = inject('$toast');
const page = usePage();
const form = useForm(props.attachedSurvey);

const config = reactive({
    loading: false,
    clearData: false,
    clearToSurveyData: false,
    exclude: [],
    questions: [],
    questionOptions: computed(() => {
        const options = [];
        config.questions.forEach(question => {
            options.push({
                label: question.content,
                value: question.question_id,
            });
        });

        return options;
    }),
});

const storeAttachedSurvey = () => {
    if (form.processing) {
        return false;
    }

    form.errors = {};
    form.transform(data => {
        const formData = {};
        formData.attached_id = data.attached_id;
        formData.title = data.title;
        formData.survey_id = data.survey_id;
        formData.to_survey_id = data.to_survey_id;
        formData.choices = [];

        form.choices.forEach(choice => {
            if (choice.show ?? false) {
                formData.choices.push({
                    question_id: choice.question_id,
                    choice_id: choice.choice_id,
                });
            }
        });

        return formData;
    }).post(route('attachedSurvey.store'), {
        onSuccess: () => {
            if (! form.attached_id) {
                form.reset();

                config.clearData = true;
                config.clearToSurveyData = true;
            }

            if (page.props.jetstream.flash.message) {
                $toast.success(page.props.jetstream.flash.message);
            }
        },
    });
}

const onSurveySelected = () => {
    form.choices.forEach(choice => {
        choice.show = false;
    });

    form.to_survey_id = '';
    config.clearToSurveyData = true;
    addMoreAnswer();
}

const loadQuestions = async () => {
    if (config.loading) {
        return false;
    }

    config.loading = true;
    await window.axios.post(route('survey.listQuestion'), { survey_id: form.to_survey_id })
        .then(response => {
            config.questions = response.data;
        })
        .catch(() => {
            $toast.error(t('commonErrorMessage'));
        })
        .finally(() => {
            config.loading = false;
        });
}

const getAnswerOptions = (questionId) => {
    const options = [];

    const question = config.questions.find((question) => question.question_id === questionId);
    if (question) {
        question.choices.forEach(choice => {
            options.push({
                value: choice.choice_id,
                label: choice.content,
            });
        });
    }

    return options;
}

const removeChoice = (index) => {
    form.choices[index].show = false;
    form.choices[index].question_id = '';
    form.choices[index].choice_id = '';
}

const addMoreAnswer = () => {
    form.choices.push({
        question_id: '',
        choice_id: '',
        show: true,
    });
}

const onQuestionSelected = (key, option) => {
    /*for (let i = 0; i < form.choices.length; i++) {
        if (i !== key) {
            const choice = form.choices[i];
            if (choice.question_id === option.value && choice.show) {
                form.choices[key].show = false;
                form.choices[key].question_id = '';
                form.choices[key].choice_id = '';
                break;
            }
        }
    }*/
}

const getErrorKey = (index) => {
    let key = 0;
    for (let i = 0; i < form.choices.length; i++) {
        const choice = form.choices[i];
        if (choice.show) {
            if (i === index) {
                break;
            }

            key++;
        }
    }

    return key;
}

/**
 * load question selection
 */
onMounted(() => {
    if (form.attached_id) {
        loadQuestions();
    }
});
</script>

<template lang="pug">
AppLayout(
    :title="title",
)
    template(
        v-slot:header,
    )
        h2.text-xl.text-gray-800.leading-tight.mr-auto(
            v-text="title",
        )

        RedButton.ml-auto(
            :class="{ 'opacity-25': form.processing }",
            :disabled="form.processing",
            @click="storeAttachedSurvey",
        )
            LoadingIcon.mr-2(
                v-if="form.processing",
            )

            span.text-sm(
                v-text="$t('save')",
            )

    .max-w-7xl.mx-auto.py-6.px-6
        .bg-white.rounded-md.shadow.px-6.py-5.flex.flex-col
            .mt-0
                InputLabel(
                    for="survey-title",
                    :value="$t('attachedSurveyTitle')",
                )

                TextInput#survey-title.mt-1.block.w-full(
                    v-model="form.title",
                    :disabled="form.processing || config.loading",
                    type="text",
                )

                InputError(
                    :message="form.errors.title",
                )
            .mt-5
                SurveySelectionBox.w-full(
                    v-model="form.survey_id",
                    :label="$t('survey')",
                    :placeholder="$t('selectSurvey')",
                    :clear-data="config.clearData",
                    :disabled="form.processing || config.loading",
                    :enable-search="true",
                    :search-placeholder="$t('searchSurveyPlaceholder')",
                    @selected="onSurveySelected",
                    @dataCleared="config.clearData = false",
                )

                InputError(
                    :message="form.errors['survey_id']",
                )
            .mt-5
                SurveySelectionBox.w-full(
                    v-model="form.to_survey_id",
                    :label="$t('surveyWasAttached')",
                    :placeholder="$t('selectSurvey')",
                    :clear-data="config.clearToSurveyData",
                    :disabled="form.processing || config.loading || !form.survey_id",
                    :enable-search="true",
                    :load-data="false",
                    :exclude="[form.survey_id]",
                    :search-placeholder="$t('searchSurveyPlaceholder')",
                    @selected="loadQuestions",
                    @dataCleared="config.clearToSurveyData = false",
                )

                InputError(
                    :message="form.errors['to_survey_id']",
                )
            .mt-5(
                v-if="form.to_survey_id",
            )
                LoadingIcon(
                    v-if="config.loading",
                    color="#000000",
                    :size="20",
                )

                template(
                    v-else,
                )
                    InputLabel(
                        for="survey-title",
                        :value="$t('answer')",
                    )

                    template(
                        v-for="(item, key) in form.choices",
                    )
                        .flex.w-full.items-stretch(
                            v-if="item.show",
                            :key="'choice_'+ key",
                            :class="{ 'mt-3 pt-2 border-t border-dashed': key > 0 }",
                        )
                            .flex-1.mr-4.question-answer-select
                                FixedSelectionBox(
                                    v-model="form.choices[key].question_id",
                                    :placeholder="$t('selectQuestion')",
                                    :disabled="form.processing",
                                    :options="config.questionOptions",
                                    :clearable="false",
                                    @selected="($event) => onQuestionSelected(key, $event)",
                                )

                                InputError(
                                    :message="form.errors['choices.' + getErrorKey(key) + '.question_id']",
                                )
                            .flex-1.ml-4.question-answer-select
                                FixedSelectionBox(
                                    :key="'answer_choice_'+ key",
                                    v-model="form.choices[key].choice_id",
                                    :placeholder="$t('selectAnswer')",
                                    :disabled="form.processing || !form.choices[key].question_id",
                                    :options="getAnswerOptions(form.choices[key].question_id)",
                                    :clearable="false",
                                )

                                InputError(
                                    :message="form.errors['choices.' + getErrorKey(key) + '.choice_id']",
                                )
                            .ml-4.px-4(
                                class="w-[56px]",
                            )
                                Close.w-5.text-gray-300.transition.ease-in-out.duration-150.mt-4(
                                    :class="[form.choices.length > 1 ? 'hover:cursor-pointer hover:text-red-500' : '']",
                                    @click="removeChoice(key)",
                                )
                    SecondaryButton.mt-3.text-sm(
                        class="h-[38px]",
                        v-text="$t('addMoreAnswer')",
                        @click="addMoreAnswer",
                    )
</template>
