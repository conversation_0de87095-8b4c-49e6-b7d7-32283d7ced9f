<script setup>
import { inject, reactive } from "vue";
import { getId } from "@/Utils/index.js";
import { Link, router, usePage } from '@inertiajs/vue3';
import throttle from "lodash/throttle.js";
import pickBy from "lodash/pickBy.js";
import { Delete } from "@element-plus/icons-vue";

import AppLayout from '@/Layouts/AppLayout.vue';
import InputLabel from "@/Components/InputLabel.vue";
import TextInput from "@/Components/TextInput.vue";
import Pagination from "@/Components/Pagination.vue";
import LoadingIcon from "@/Components/LoadingIcon.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import Modal from "@/Components/Modal.vue";

const $toast = inject('$toast');
const page = usePage();

const props = defineProps({
    filters: Object,
    attachedItems: Object,
});

const config = reactive({
    searched: props.filters.search ?? '',
    searching: false,
    confirmDeletion: false,
    open: false,
    deleteId: null,
    deleting: false,
});

const search = throttle(() => router.get(route('attachedSurvey.list'), pickBy({
    search: config.searched,
}), {
    preserveState: true,
    onBefore: () => {
        config.searching = true;
    },
    onFinish: () => {
        config.searching = false;
    },
}), 1000);

const closeModal = () => {
    config.open = false;
    setTimeout(() => config.confirmDeletion = false, 150);
}

const removeAttachedConfirmation = (id) => {
    config.deleteId = id;
    config.confirmDeletion = true;
    setTimeout(() => config.open = true, 150);
}

const deleteAttached = () => {
    if (config.deleting) {
        return;
    }

    router.post(route('attachedSurvey.delete'), {
        id: config.deleteId,
    }, {
        preserveScroll: true,
        preserveState: true,
        onBefore: () => {
            config.deleting = true;
        },
        onSuccess: () => {
            closeModal();
            config.deleteId = null;

            if (page.props.jetstream.flash.message) {
                $toast.success(page.props.jetstream.flash.message);
            }
        },
        onFinish: () => {
            config.deleting = false;
        },
    });
}

const getQuestions = (choices) => {
    const questions = {};
    choices.forEach(choice => {
        if (! questions[choice.question_id]) {
            questions[choice.question_id] = {
                question_id: choice.question_id,
                content: choice.question_content,
                choices: [],
            }
        }

        questions[choice.question_id]['choices'].push({
            choice_id: choice.choice_id,
            content: choice.content,
        });
    });

    return Object.keys(questions).map((key) => questions[key]);
}
</script>

<template lang="pug">
AppLayout(
    :title="$t('listSurvey')",
)
    template(
        v-slot:header,
    )
        h2.text-xl.text-gray-800.leading-tight.mr-auto(
            v-text="$t('listAttachedSurvey')",
        )

        Link.ml-auto.flex.items-center.justify-center.px-4.py-2.bg-sky-500.border.border-transparent.rounded-md.font-semibold.text-white.uppercase.transition.ease-in-out.duration-150(
            class="hover:bg-sky-600 focus:outline-none h-[38px]",
            v-text="$t('addNew')",
            :href="route('attachedSurvey.create')",
        )

    .py-6.px-6(
        class="sm:mx-2",
    )
        .flex.items-center.items-stretch
            .mt-0.w-80
                InputLabel(
                    for="search",
                    :value="$t('attachedSurveySearch')",
                )

                TextInput#search.mt-1.block.w-full(
                    v-model="config.searched",
                    placeholder="...",
                    :disabled="config.searching",
                    @enter="search",
                )
        .bg-white.rounded-md.shadow.overflow-auto.mt-5.flex.flex-col(
            :class="{ 'grid-loading': config.searching }",
        )
            LoadingIcon.grid-loading-icon(
                v-if="config.searching",
                :size="24",
                color="rgb(55 65 81)",
            )

            table.w-full
                tr.text-left.flex
                    th.number-column(
                        v-text="$t('number')",
                    )

                    th.title-flex-column(
                        v-text="$t('attachedSurveyTitle')",
                    )

                    th.survey-id-column(
                        v-text="$t('surveyID')",
                    )

                    th.survey-title-column(
                        v-text="$t('surveyTitle')",
                    )

                    th.attached-id-column(
                        v-text="$t('surveyIDWasAttached')",
                    )

                    th.survey-title-column(
                        v-text="$t('surveyTitleWasAttached')",
                    )

                    th.question-id-column(
                        v-text="$t('questionID')",
                    )

                    th.question-content-column(
                        v-text="$t('questionContent')",
                    )

                    th.answer-id-column(
                        v-text="$t('answerID')",
                    )

                    th.answer-content-column(
                        v-text="$t('answerContent')",
                    )

                    th(
                        class="w-[56px]",
                    )
                tr.flex.border-t(
                    v-if="attachedItems.data.length > 0",
                    v-for="(item, index) in attachedItems.data",
                    :class="[index % 2 === 0 ? 'bg-blue-50' : '']",
                )
                    td.number-column
                        Link(
                            class="hover:text-sky-600 hover:underline",
                            v-text="item.attached_id",
                            :href="route('attachedSurvey.update', {attachedSurvey: item.attached_id})",
                        )

                    td.title-flex-column(
                        v-text="item.title",
                    )

                    td.survey-id-column(
                        v-text="getId(item.survey.survey_id, 'S')",
                    )

                    td.survey-title-column(
                        v-text="item.survey.title",
                    )

                    td.attached-id-column(
                        v-text="getId(item.toSurvey.survey_id, 'S')",
                    )

                    td.survey-title-column(
                        v-text="item.toSurvey.title",
                    )

                    td.p-0.border-l(
                        colspan="2",
                    )
                        table.w-full.h-full(
                            v-if="item.choices.length > 0",
                        )
                            tr.flex.h-full(
                                v-for="(question, qKey) in getQuestions(item.choices)",
                                :class="[qKey > 0 ? 'border-t' : '']",
                            )
                                td.question-id-column(
                                    v-text="getId(question.question_id, 'Q')",
                                )

                                td.question-content-column(
                                    v-text="question.content",
                                )

                                td.p-0.border-l(
                                    colspan="2",
                                )
                                    table.w-full.h-full(
                                        v-if="question.choices.length > 0",
                                    )
                                        tr.flex.h-full(
                                            v-for="(choice, cKey) in question.choices",
                                            :class="[cKey > 0 ? 'border-t' : '']",
                                        )
                                            td.answer-id-column(
                                                v-text="getId(choice.choice_id, 'A')",
                                            )

                                            td.answer-content-column(
                                                v-text="choice.content",
                                            )
                    td.border-l(
                        class="w-[56px]",
                    )
                        Delete.w-6.text-gray-500.transition.ease-in-out.duration-150(
                            class="hover:cursor-pointer hover:text-red-500",
                            @click="removeAttachedConfirmation(item.attached_id)",
                        )
                tr.flex.border-t(
                    v-else,
                )
                    td(
                        colspan="11",
                        v-text="$t(filters.search && filters.search !== '' ? 'emptyResult' : 'emptyData')",
                    )
        Pagination.mt-5.flex.items-center.justify-center.mx-auto(
            :links="attachedItems.links",
        )
Modal(
    v-if="config.confirmDeletion",
    :show="config.open",
    :closeable="true",
    size="lg",
    padding-vertical="py-20",
    @close="closeModal",
)
    .pt-4.pb-3.px-3.font-semibold(
        v-text="$t('confirm')",
    )

    .border-t.px-3.py-4.font-light(
        v-text="$t('attachedSurveyDeleteConfirmation')",
    )

    .border-t
    .flex.items-center.justify-end.px-3.py-3
        SecondaryButton.mr-3.text-sm(
            class="h-[38px]",
            v-text="$t('cancel')",
            @click="closeModal",
        )

        PrimaryButton.text-sm
            span.text-sm(
                v-text="$t('delete')",
                @click="deleteAttached",
            )
</template>
