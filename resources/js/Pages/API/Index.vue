<template>
    <app-layout :title="$t('API-Tokens')">
        <template #header>
            <h2
                class="font-semibold text-xl text-gray-800 leading-tight mr-auto"
                v-text="$t('API-Tokens')"
            />

            <primary-button
                class="text-sm py-1"
                v-text="$t('addNew')"
                @click="onCreateApiToken"
            />
        </template>

        <div class="max-w-7xl mx-auto p-6">
            <div class="bg-white rounded-md shadow overflow-x-auto">
                <table class="w-full whitespace-nowrap">
                    <thead>
                        <tr class="text-left">
                            <th class="pt-4 pb-3 px-4" v-text="$t('tokenName')" />
                            <th class="pt-4 pb-3 px-4 w-60" v-text="$t('lastUsed')" />
                            <th class="w-40" />
                        </tr>
                    </thead>

                    <tbody>
                        <tr
                            v-if="tokens.length > 0"
                            v-for="token in tokens"
                            :key="token.id"
                            class="border-t hover:bg-gray-100 focus-within:bg-gray-100"
                        >
                            <td class="px-4 py-3 text-left" v-text="token.name" />
                            <td class="px-4 py-3 text-left" v-text="token.last_used_ago" />
                            <td class="px-4 py-3 flex items-center justify-end">
                                <edit
                                    v-if="availablePermissions.length > 0"
                                    class="mr-3 w-5 mt-1 text-sky-400 transition hover:text-sky-700 hover:cursor-pointer"
                                    @click="manageApiTokenPermissions(token)"
                                />

                                <delete
                                    class="w-5 mt-1 text-red-400 transition hover:text-red-700 hover:cursor-pointer"
                                    @click="confirmApiTokenDeletion(token)"
                                />
                            </td>
                        </tr>
                        <tr v-else>
                            <td colspan="3" class="border-t px-4 py-5 text-center text-md" v-text="$t('emptyData')" />
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </app-layout>

    <dialog-modal :show="createApiTokenFor" @close="createApiTokenFor = false">
        <template #title>
            {{ $t('createToken') }}
        </template>

        <template #content>
            <div class="mt-0">
                <input-label for="name" :value="$t('tokenName')" />

                <text-input
                    id="name"
                    class="mt-1 block w-full"
                    v-model="createApiTokenForm.name"
                    type="text"
                    autofocus
                />

                <input-error
                    class="mt-2"
                    :message="createApiTokenForm.errors.name"
                />
            </div>

            <div v-if="availablePermissions.length > 0" class="mt-4">
                <input-label for="permissions" :value="$t('permissions')" />

                <div class="grid grid-cols-2 gap-4 mt-4">
                    <div
                        v-for="permission in availablePermissions"
                        :key="permission"
                    >
                        <label class="flex items-center">
                            <checkbox
                                v-model:checked="createApiTokenForm.permissions"
                                :value="permission"
                            />

                            <span
                                class="ms-2 text-gray-600"
                                v-text="$t(permission)"
                            />
                        </label>
                    </div>
                </div>
            </div>
        </template>

        <template #footer>
            <secondary-button
                @click="createApiTokenFor = false"
                v-text="$t('cancel')"
            />

            <primary-button
                class="ms-3"
                :class="{ 'opacity-25': createApiTokenForm.processing }"
                :disabled="createApiTokenForm.processing"
                @click="createApiToken"
            >
                <loading-icon v-if="createApiTokenForm.processing" class="mr-2" />
                <span class="text-sm" v-text="$t('create')" />
            </primary-button>
        </template>
    </dialog-modal>

    <dialog-modal
        :show="managingPermissionsFor != null"
        @close="managingPermissionsFor = null"
    >
        <template #title>{{ $t('API-TokenPermissions') }}</template>

        <template #content>
            <div class="grid grid-cols-2 gap-4">
                <div
                    v-for="permission in availablePermissions"
                    :key="permission"
                >
                    <label class="flex items-center">
                        <checkbox
                            v-model:checked="updateApiTokenForm.permissions"
                            :value="permission"
                        />

                        <span class="ms-2 text-gray-600" v-text="$t(permission)" />
                    </label>
                </div>
            </div>
        </template>

        <template #footer>
            <secondary-button
                @click="managingPermissionsFor = null"
                v-text="$t('cancel')"
            />

            <primary-button
                class="ms-3"
                :class="{ 'opacity-25': updateApiTokenForm.processing }"
                :disabled="updateApiTokenForm.processing"
                @click="updateApiToken"
            >
                <loading-icon v-if="updateApiTokenForm.processing" class="mr-2" />
                <span class="text-sm" v-text="$t('update')" />
            </primary-button>
        </template>
    </dialog-modal>

    <confirmation-modal
        :show="apiTokenBeingDeleted != null"
        @close="apiTokenBeingDeleted = null"
    >
        <template #title>{{ $t('deleteToken') }}</template>

        <template #content>{{ $t('deleteTokenMessage') }}</template>

        <template #footer>
            <secondary-button
                @click="apiTokenBeingDeleted = null"
                v-text="$t('cancel')"
            />

            <primary-button
                class="ms-3"
                :class="{ 'opacity-25': deleteApiTokenForm.processing }"
                :disabled="deleteApiTokenForm.processing"
                @click="deleteApiToken"
            >
                <loading-icon v-if="deleteApiTokenForm.processing" class="mr-2" />
                <span class="text-sm" v-text="$t('delete')" />
            </primary-button>
        </template>
    </confirmation-modal>

    <dialog-modal
        :show="displayingToken"
        @close="displayingToken = false"
    >
        <template #title>{{ $t('API-Tokens') }}</template>

        <template #content>
            <div class="mt-0" v-text="$t('tokenCopyMessage')" />

            <div
                v-if="$page.props.jetstream.flash.token"
                class="mt-4 bg-gray-100 px-4 py-3 rounded-md font-mono text-sm text-gray-500 break-all"
                v-text="$page.props.jetstream.flash.token"
            />
        </template>

        <template #footer>
            <secondary-button
                @click="displayingToken = false"
                v-text="$t('close')"
            />
        </template>
    </dialog-modal>
</template>

<script setup>
import { inject, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useForm } from "@inertiajs/vue3";
import { Delete, Edit } from "@element-plus/icons-vue";

import AppLayout from '@/Layouts/AppLayout.vue';
import Checkbox from "@/Components/Checkbox.vue";
import ConfirmationModal from "@/Components/ConfirmationModal.vue";
import DialogModal from "@/Components/DialogModal.vue";
import LoadingIcon from "@/Components/LoadingIcon.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import InputLabel from "@/Components/InputLabel.vue";
import TextInput from "@/Components/TextInput.vue";
import InputError from "@/Components/InputError.vue";

const $toast = inject('$toast');
const { t } = useI18n();

const props = defineProps({
    tokens: Array,
    availablePermissions: Array,
    defaultPermissions: Array,
});

/**
 * create
 */
const createApiTokenForm = useForm({
    name: '',
    permissions: props.defaultPermissions,
});

const displayingToken = ref(false);
const createApiTokenFor = ref(false);

const onCreateApiToken = () => {
    createApiTokenFor.value = true;
};

const createApiToken = () => {
    createApiTokenForm.post(route('api-tokens.store'), {
        preserveScroll: true,
        onSuccess: () => {
            displayingToken.value = true;
            createApiTokenFor.value = false;
            createApiTokenForm.reset();
        },
    });
};

/**
 * update
 */
const managingPermissionsFor = ref(null);
const updateApiTokenForm = useForm({
    permissions: [],
});

const manageApiTokenPermissions = (token) => {
    updateApiTokenForm.permissions = token.abilities;
    managingPermissionsFor.value = token;
};

const updateApiToken = () => {
    updateApiTokenForm.put(route('api-tokens.update', managingPermissionsFor.value), {
        preserveScroll: true,
        preserveState: true,
        onSuccess: () => {
            managingPermissionsFor.value = null;
            $toast.success(t('tokenPermissionUpdatedSuccessfully'))
        },
    });
};

/**
 * delete
 */
const deleteApiTokenForm = useForm({});
const apiTokenBeingDeleted = ref(null);

const confirmApiTokenDeletion = (token) => {
    apiTokenBeingDeleted.value = token;
};

const deleteApiToken = () => {
    deleteApiTokenForm.delete(route('api-tokens.destroy', apiTokenBeingDeleted.value), {
        preserveScroll: true,
        preserveState: true,
        onSuccess: () => {
            apiTokenBeingDeleted.value = null;
            $toast.success(t('tokenDeletedSuccessfully'));
        },
    });
};
</script>
