<template>
    <form
        class="px-6 py-5 bg-white shadow rounded-md mx-auto sm:max-w-xl"
        @submit.prevent="updatePassword"
        autocomplete="off"
    >
        <div class="mt-0">
            <input-label for="current_password" :value="$t('currentPassword')" />

            <text-input
                id="current_password"
                class="mt-1 block w-full"
                ref="currentPasswordInput"
                v-model="form.current_password"
                type="password"
                autocomplete="off"
            />

            <input-error class="mt-2" :message="form.errors.current_password" />
        </div>

        <div class="mt-4">
            <input-label for="password" :value="$t('newPassword')" />

            <text-input
                id="password"
                ref="passwordInput"
                v-model="form.password"
                type="password"
                class="mt-1 block w-full"
                autocomplete="off"
            />

            <input-error class="mt-2" :message="form.errors.password" />
        </div>

        <div class="mt-4">
            <input-label for="password_confirmation" :value="$t('confirmPassword')" />

            <text-input
                id="password_confirmation"
                v-model="form.password_confirmation"
                type="password"
                class="mt-1 block w-full"
                autocomplete="off"
            />

            <input-error class="mt-2" :message="form.errors.password_confirmation" />
        </div>

        <div class="flex items-center justify-end text-end mt-5">
            <primary-button
                :class="{ 'opacity-25': form.processing }"
                :disabled="form.processing"
            >
                <loading-icon class="mr-2" v-if="form.processing" />
                <span v-text="$t('save')" />
            </primary-button>
        </div>
    </form>
</template>

<script setup>
import { inject, ref } from 'vue';
import { useI18n } from "vue-i18n";
import { useForm } from '@inertiajs/vue3';

import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import LoadingIcon from "@/Components/LoadingIcon.vue";
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

const $toast = inject('$toast');
const { t } = useI18n();

const passwordInput = ref(null);
const currentPasswordInput = ref(null);

const form = useForm({
    current_password: '',
    password: '',
    password_confirmation: '',
});

const updatePassword = () => {
    form.put(route('user-password.update'), {
        errorBag: 'updatePassword',
        preserveScroll: true,
        onSuccess: () => {
            form.reset();
            $toast.success(t('passwordUpdatedSuccessfully'));
        },
        onError: () => {
            if (form.errors.password) {
                form.reset('password', 'password_confirmation');
                passwordInput.value.focus();
            }

            if (form.errors.current_password) {
                form.reset('current_password');
                currentPasswordInput.value.focus();
            }
        },
    });
};
</script>
