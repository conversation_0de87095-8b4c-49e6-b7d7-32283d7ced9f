<script setup>
import { inject } from 'vue';
import { useI18n } from 'vue-i18n';
import { useForm, usePage } from '@inertiajs/vue3';

import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import LoadingIcon from "@/Components/LoadingIcon.vue";
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

const $toast = inject('$toast');
const { t } = useI18n();

const page = usePage();

const props = defineProps({
    user: Object,
});

const form = useForm({
    _method: 'PUT',
    name: props.user.name,
});

const updateProfileInformation = () => {
    form.post(route('user-profile-information.update'), {
        errorBag: 'updateProfileInformation',
        preserveScroll: true,
        onSuccess: () => {
            $toast.success(t('updateProfileSuccessfully'));
        },
    });
};
</script>

<template>
    <form
        class="px-6 py-5 bg-white shadow rounded-md mx-auto sm:max-w-xl"
        @submit.prevent="updateProfileInformation"
        autocomplete="off"
    >
        <div class="mt-0">
            <input-label for="name" :value="$t('profileName')" />

            <text-input
                id="name"
                v-model="form.name"
                type="text"
                class="mt-1 block w-full"
                autocomplete="off"
            />

            <input-error class="mt-2" :message="form.errors.name" />
        </div>

        <div class="mt-4">
            <input-label for="email" :value="$t('email')" />

            <text-input
                id="email"
                v-model="$page.props.auth.user.email"
                type="text"
                class="mt-1 block w-full"
                autocomplete="off"
                disabled
            />
        </div>

        <div class="flex items-center justify-end text-end mt-5">
            <primary-button
                :class="{ 'opacity-25': form.processing }"
                :disabled="form.processing"
            >
                <loading-icon class="mr-2" v-if="form.processing" />
                <span v-text="$t('save')" />
            </primary-button>
        </div>
    </form>
</template>
