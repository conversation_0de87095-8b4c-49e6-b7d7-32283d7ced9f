<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import UpdatePasswordForm from '@/Pages/Profile/Partials/UpdatePasswordForm.vue';
</script>

<template>
    <app-layout :title="$t('changePassword')">
        <template #header>
            <h2 class="text-xl text-gray-800 leading-tight" v-text="$t('changePassword')" />
        </template>

        <div class="max-w-7xl mx-auto py-10 px-6">
            <update-password-form :user="$page.props.auth.user" />
        </div>
    </app-layout>
</template>
