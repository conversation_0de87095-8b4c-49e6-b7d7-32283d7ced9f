<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import UpdateProfileInformationForm from '@/Pages/Profile/Partials/UpdateProfileInformationForm.vue';

/*defineProps({
    confirmsTwoFactorAuthentication: <PERSON><PERSON><PERSON>,
    sessions: Array,
});*/
</script>

<template>
    <app-layout :title="$t('profile')">
        <template #header>
            <h2
                class="text-xl text-gray-800 leading-tight"
                v-text="$t('updateProfile')"
            />
        </template>

        <div class="max-w-7xl mx-auto py-10 px-6">
            <update-profile-information-form :user="$page.props.auth.user" />
        </div>
    </app-layout>
</template>
