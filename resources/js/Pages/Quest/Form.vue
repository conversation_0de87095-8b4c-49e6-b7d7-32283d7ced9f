<script setup>
import { inject } from "vue";
import { useForm, Link as InertiaLink } from "@inertiajs/vue3";

import AppLayout from '@/Layouts/AppLayout.vue';
import RedButton from '@/Components/RedButton.vue';
import LoadingIcon from '@/Components/LoadingIcon.vue';
import TextInput from '@/Components/TextInput.vue';
import TextAreaInput from '@/Components/TextAreaInput.vue';
import InputError from '@/Components/InputError.vue';
import ImageInput from '@/Components/ImageInput.vue';
import FixedSelectionBox from "@/Components/FixedSelectionBox.vue";

const props = defineProps({
    quest: Object,
    types: Object,
    action: String,
});

const $toast = inject('$toast');
const questForm = useForm(props.quest);

const save = () => {
    if (questForm.processing) {
        return false;
    }

    questForm.errors = {};

    questForm.transform(data => {
        if (typeof data['image'] === 'string') {
            delete data['image'];
        }

        return data;
    }).post(route('quest.store'), {
        forceFormData: true,
        preserveScroll: true,
        preserveState: true,
        onSuccess: (data) => {
            if (data.props.jetstream.flash.message) {
                $toast.success(data.props.jetstream.flash.message);
            }

            if (props.action === 'create') {
                window.location = route('quest.list');
            }
        },
    });
}
</script>

<template>
    <app-layout :title="$t(props.action + 'Quest')">
        <template v-slot:header>
            <div class="flex-1 flex items-center">
                <h2
                    class="text-xl text-gray-800 leading-tight flex-1 mr-6"
                    v-text="$t(props.action + 'Quest')"
                />

                <inertia-link class="primary-button text-sm mr-3" :href="route('quest.list')" v-text="$t('list')" />

                <red-button class="normal-case" :disabled="questForm.processing" @click="save">
                    <loading-icon v-if="questForm.processing" class="mr-2" />
                    <span class="text-sm" v-text="$t('save')" />
                </red-button>
            </div>
        </template>

        <div class="max-w-7xl mx-auto py-6 px-6">
            <div class="bg-white rounded-md shadow flex flex-col overflow-auto px-5 pt-3 pb-4">
                <div class="flex flex-col pb-2">
                    <label v-text="$t('title')" class="w-full" />
                    <text-input
                        class="block w-full"
                        type="text"
                        v-model="questForm['title']"
                        :disabled="questForm.processing"
                    />

                    <input-error class="w-full mt-1" :message="questForm.errors['title']" />
                </div>

                <div class="flex pb-2">
                    <div class="flex flex-col flex-1 mr-3">
                        <label v-text="$t('questType')" class="w-full mb-1" />
                        <fixed-selection-box
                            v-model="questForm.type"
                            :placeholder="$t('questType')"
                            :disabled="props.action === 'update' || questForm.processing"
                            :options="props.types"
                        />

                        <input-error class="w-full mt-1" :message="questForm.errors['type']" />
                    </div>

                    <image-input
                        class="flex-1 ml-3"
                        :label="$t('image')"
                        :error="questForm.errors['image']"
                        :disabled="questForm.processing"
                        v-model="questForm.image"
                        @update:modelValue="questForm.image = $event"
                    />
                </div>

                <div class="flex pb-2">
                    <div class="flex flex-col flex-1 mr-3">
                        <label v-text="$t('questAmount')" class="w-full mb-1" />

                        <div class="flex items-center">
                            <text-input
                                class="block w-full"
                                type="text"
                                v-model="questForm['amount']"
                                :disabled="questForm.processing"
                            />

                            <div class="ml-2 w-[100px]" v-text="$t('quest.unit.coin')" />
                        </div>

                        <input-error class="w-full mt-1" :message="questForm.errors['amount']" />
                    </div>

                    <div class="flex flex-col flex-1 ml-3">
                        <label v-text="$t('sort')" class="w-full mb-1" />
                        <text-input
                            class="block w-full"
                            type="text"
                            v-model="questForm['sort']"
                            :disabled="questForm.processing"
                        />

                        <input-error class="w-full mt-1" :message="questForm.errors['sort']" />
                    </div>
                </div>

                <div class="flex flex-col">
                    <label v-text="$t('questDescription')" class="w-full mb-1" />
                    <text-area-input
                        class="block w-full resize-none h-[150px]"
                        type="text"
                        v-model="questForm['description']"
                        :disabled="questForm.processing"
                    />

                    <input-error class="w-full mt-1" :message="questForm.errors['description']" />
                </div>
            </div>
        </div>
    </app-layout>
</template>
