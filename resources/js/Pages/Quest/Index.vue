<script setup>
import { inject, reactive, computed } from "vue";
import { Link as InertiaLink, router, usePage, useForm } from '@inertiajs/vue3';

import DataTable from 'primevue/datatable';
import Column from 'primevue/column';

import AppLayout from '@/Layouts/AppLayout.vue';
import InputLabel from "@/Components/InputLabel.vue";
import SearchInput from "@/Components/SearchInput.vue";
import Pagination from "@/Components/Pagination.vue";
import LoadingIcon from "@/Components/LoadingIcon.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import RedButton from "@/Components/RedButton.vue";
import Modal from "@/Components/Modal.vue";
import FixedSelectionBox from "@/Components/FixedSelectionBox.vue";
import { clearEmptyData } from "@/Utils/index.js";
import { route } from "ziggy-js";
import GridContainer from "@/Components/GridContainer.vue";

const $toast = inject('$toast');
const page = usePage();

const props = defineProps({
    filters: Object,
    quests: Object,
});

const form = useForm({
    search: props.filters['search'] ?? '',
    status: props.filters['status'] ?? '',
    limit: props.filters['limit'] ?? 10,
});

const config = reactive({
    showSearchClearButton: (props.filters.search ?? '').trim().length > 0,
    searching: false,
    showModal: false,
    open: false,
    toggleId: null,
    toggling: false,
    selectOptions: [
        {
            value: 'enable',
            label: 'アクティブ',
        },
        {
            value: 'disable',
            label: '削除',
        },
    ],
    activePage: null,
    busy: computed(() => config.activePage !== null || config.searching || form.processing),
});

const _search = () => {
    if (form.processing) {
        return false;
    }

    form.transform((data) => clearEmptyData(data)).get(route('quest.list'), {
        preserveScroll: true,
        onSuccess: () => {},
    });
}

const search = () => {
    config.showSearchClearButton = true;
    _search();
}

const clearSearch = () => {
    form.search = '';
    config.showSearchClearButton = false;
    _search();
}

const confirmToggleQuest = (id) => {
    config.toggleId = id;
    config.showModal = true;
    setTimeout(() => config.open = true, 150);
}

const closeModal = async () => {
    config.open = false;
    setTimeout(() => config.showModal = false, 150);
}

const toggleQuest = () => {
    if (config.toggling) {
        return;
    }

    router.post(route('quest.toggle'), {
        id: config.toggleId,
    }, {
        preserveScroll: true,
        preserveState: true,
        onBefore: () => {
            config.toggling = true;
        },
        onSuccess: () => {
            config.toggleId = null;

            if (page.props.jetstream.flash.message) {
                $toast.success(page.props.jetstream.flash.message);
            }

            closeModal();
        },
        onFinish: () => {
            config.toggling = false;
        },
    });
}

const updateProgress = (progress) => {
    config.activePage = progress;
    config.searching = true;
};

const changeLimit = (limit) => {
    form.limit = limit;
    form.search = '';
    form.status = '';

    _search();
}
</script>

<template>
    <app-layout :title="$t('questList')">
        <template v-slot:header>
            <div class="flex-1 flex items-center">
                <h2
                    class="text-xl text-gray-800 leading-tight mr-auto"
                    v-text="$t('questList')"
                />

                <inertia-link class="primary-button text-sm" :href="route('quest.form', { quest: '' })" v-text="$t('addNew')" />
            </div>
        </template>

        <div class="p-6 sm:mx-2">
            <div class="flex items-stretch">
                <div class="mt-0 w-80 mr-8">
                    <input-label for="search" :value="$t('search')" />

                    <search-input
                        id="search"
                        class="block w-full"
                        v-model="form.search"
                        :placeholder="$t('questSearch')"
                        :disabled="config.busy"
                        :show-clear-button="config.showSearchClearButton"
                        @input="config.showSearchClearButton = false"
                        @clear-search="clearSearch"
                        @enter="search"
                    />
                </div>

                <div class="mt-0 w-64 mr-8">
                    <input-label for="status-search" :value="$t('status')" />

                    <fixed-selection-box
                        v-model="form.status"
                        :placeholder="$t('all')"
                        :disabled="config.busy"
                        :options="config.selectOptions"
                        :clearable="true"
                        @cleared="_search"
                        @selected="_search"
                    />
                </div>
            </div>

            <grid-container :loading="config.busy">
                <data-table :value="quests['data']">
                    <column class="number-column small" field="id" :header="$t('ID')" />

                    <column class="title-flex-column" field="title" :header="$t('title')" />
                    <column class="title-flex-column" :header="$t('questDescription')">
                        <template #body="{ data }">
                            <div v-html="data['description'].replace(/(\r\n|\n|\r)/g, '<br />')" />
                        </template>
                    </column>

                    <column class="status-column" :header="$t('questAmount')">
                        <template #body="{ data }">
                            <div v-html="data['amount_label']" />
                        </template>
                    </column>

                    <column class="status-column" :header="$t('image')">
                        <template #body="{ data }">
                            <img class="max-w-full max-h-full" :src="data['image']" />
                        </template>
                    </column>

                    <column class="status-column" field="status_label" :header="$t('status')" />
                    <column class="action-column small">
                        <template #body="{ data }">
                            <inertia-link
                                v-if="data['status'] !== 0"
                                :href="route('quest.form', { quest: data['id'] })"
                            >
                                <i class="pi pi-pen-to-square text-blue-400 hover:text-blue-600" />
                            </inertia-link>

                            <i
                                class="pi hover:cursor-pointer"
                                :class="{ 'pi-eye text-sky-400 hover:text-sky-600': data['status'], 'pi-eye-slash text-red-400 hover:text-red-600': !data['status'] }"
                                @click="confirmToggleQuest(data['id'])"
                            />
                        </template>
                    </column>

                    <template #empty>{{ $t((filters.search && filters.search !== '') || (filters.status && filters.status !== '') ? 'emptyResult' : 'emptyData') }}</template>
                </data-table>
            </grid-container>

            <pagination
                v-if="quests['data'].length > 0"
                class="mt-5 flex items-center justify-center mx-auto"
                :links="quests['links']"
                :active="config.activePage"
                :disabled="config.busy"
                :limit="quests['per_page']"
                :total="quests['total']"
                :from="quests['from']"
                :to="quests['to']"
                @progress="updateProgress"
                @changeLimit="changeLimit"
            />
        </div>
    </app-layout>

    <modal v-if="config.showModal" :show="config.open" @close="closeModal">
        <div class="pt-4 pb-3 px-3 font-semibold" v-text="$t('confirm')" />
        <div class="border-t border-b p-4" v-text="$t('confirmToggleQuestMessage')" />
        <div class="flex items-center justify-end px-3 py-3">
            <secondary-button class="mr-3 text-sm" v-text="$t('cancel')" @click="closeModal" />

            <red-button class="text-sm overflow-hidden h-[34px]" @click="toggleQuest">
                <loading-icon v-if="config.toggling" class="mr-1" />

                <span class="text-sm text-white" v-text="$t('confirm')" />
            </red-button>
        </div>
    </modal>
</template>
