<script setup>
import { inject, ref, reactive, onMounted } from "vue";
import { useForm } from "@inertiajs/vue3";
import { useI18n } from "vue-i18n";
import { route } from "ziggy-js";
import { confirmDelete } from "@/Utils";

import AppLayout from '@/Layouts/AppLayout.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import LoadingIcon from '@/Components/LoadingIcon.vue';
import TextInput from '@/Components/TextInput.vue';
import FixedSelectionBox from "@/Components/FixedSelectionBox.vue";
import { Plus } from "@element-plus/icons-vue";

const { t } = useI18n();

const props = defineProps({
    posts: Object,
});

const $toast = inject('$toast');
const settingForm = useForm(props.posts);

// Drag & Drop state
const dragState = reactive({
    draggedIndex: null,
    draggedItem: null,
    isDragging: false,
    dropIndicatorIndex: null,
    isInputFocused: false, // Track if any input is focused
});

// Convert posts object to array for easier manipulation
const postsList = ref([]);
const loading = ref(false);

// Initialize posts list
const initializePostsList = () => {
    postsList.value = Object.keys(props.posts).map((key, index) => ({
        id: key,
        order: index + 1,
        postId: props.posts[key]['post_id'] || key,
        content: props.posts[key].content || '',
        type: props.posts[key].type || '',
        unlockTime: props.posts[key].unlock_time || '',
        size: props.posts[key].size || 'small',
        originalData: props.posts[key]
    }));
};

onMounted(() => {
    initializePostsList();
});

// Input focus handlers
const handleInputFocus = () => {
    dragState.isInputFocused = true;
};

const handleInputBlur = (post) => {
    dragState.isInputFocused = false;
    fetchPostContent(post);
};

// Drag & Drop functions
const handleDragStart = (event, index) => {
    // Prevent drag if any input is focused
    if (dragState.isDragging || settingForm.processing || loading.value || dragState.isInputFocused) {
        event.preventDefault();
        return false;
    }

    dragState.draggedIndex = index;
    dragState.draggedItem = postsList.value[index];
    dragState.isDragging = true;

    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/html', event.target);

    // Add dragging class immediately for better visual feedback
    event.target.classList.add('dragging');

    // Set drag image to be the same as the element (prevents default ghost image)
    const dragImage = event.target.cloneNode(true);
    dragImage.style.transform = 'none';
    dragImage.style.opacity = '0.8';
    document.body.appendChild(dragImage);
    event.dataTransfer.setDragImage(dragImage, 0, 0);

    // Remove the temporary drag image after a short delay
    setTimeout(() => {
        document.body.removeChild(dragImage);
    }, 0);
};

const handleDragEnd = (event) => {
    dragState.isDragging = false;
    dragState.dropIndicatorIndex = null;
    dragState.draggedIndex = null;
    dragState.draggedItem = null;
    event.target.classList.remove('dragging');
};

const handleDragOver = (event, index) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';

    if (dragState.draggedIndex !== index) {
        dragState.dropIndicatorIndex = index;
    }
};

const handleDragLeave = () => {
    dragState.dropIndicatorIndex = null;
};

const handleDrop = (event, dropIndex) => {
    event.preventDefault();

    const dragIndex = dragState.draggedIndex;
    if (dragIndex === null || dragIndex === dropIndex) {
        return;
    }

    // Reorder the array
    const draggedItem = postsList.value[dragIndex];
    postsList.value.splice(dragIndex, 1);
    postsList.value.splice(dropIndex, 0, draggedItem);

    // Update order numbers
    postsList.value.forEach((item, index) => {
        item.order = index + 1;
    });

    // Reset drag state
    dragState.draggedIndex = null;
    dragState.draggedItem = null;
    dragState.dropIndicatorIndex = null;
};

// Remove post function
const removePost = async (index) => {
    const confirmed = await confirmDelete(t('Are you sure you want to remove this post?'));
    if (confirmed) {
        postsList.value.splice(index, 1);

        // Update order numbers
        postsList.value.forEach((item, idx) => {
            item.order = idx + 1;
        });
    }
};

const addPost = () => {
    if (dragState.isDragging || settingForm.processing || loading.value) {
        return;
    }

    if (postsList.value.length === 10) {
        $toast.warning(t('You can only add up to 10 posts.'));
        return;
    }

    const newPost = {
        id: `new_${Date.now()}`,
        order: postsList.value.length + 1,
        postId: '',
        content: '',
        type: '',
        unlockTime: 1,
        size: 'small',
        originalData: {}
    };

    postsList.value.push(newPost);
};

const save = () => {
    if (settingForm.processing) {
        return false;
    }

    settingForm.errors = {};

    settingForm.transform(data => {
        const orderedPosts = [];
        postsList.value.forEach((item, index) => {
            if (! item.postId) {
                return;
            }

            orderedPosts.push({
                post_id: item.postId,
                unlock_time: item.unlockTime,
                size: item.size,
            });
        });

        return {
            data: orderedPosts,
        };
    }).post(route('setting.storeUnlockPosts'), {
        preserveScroll: true,
        preserveState: true,
        onSuccess: (data) => {
            if (data.props.jetstream.flash.message) {
                $toast.success(data.props.jetstream.flash.message);
            }

            /**
             * sync form data
             */
            for (const [key, value] of Object.entries(data.props.settings || {})) {
                settingForm[key] = value;
            }
        },
    });
};

const sizeOptions = [
    {
        value: 'small',
        label: t('Small'),
    },
    {
        value: 'large',
        label: t('Large'),
    },
];

const unlockTimeOptions = [
    {
        value: 1,
        label: '1',
    },
    {
        value: 2,
        label: '2',
    },
];

const fetchPostContent = async (post) => {
    // Reset previous content if postId changed
    if (!post.postId || post.postId.toString().trim() === '') {
        post.content = '';
        post.type = '';
        return;
    }

    // Skip if already loading or content already exists for this postId
    if (loading.value || (post.content && post.lastFetchedId === post.postId)) {
        return;
    }

    loading.value = true;

    try {
        const response = await window.axios.get(route('post.detailJson', { post: post.postId }));
        const postData = response.data.post;

        post.content = postData.content;
        post.type = postData.type;
        post.lastFetchedId = post.postId;
    } catch (error) {
        clearContent(post);
        post.postId = '';
        $toast.error(t('Invalid post ID.'));
    } finally {
        loading.value = false;
    }
};

const clearContent = (post) => {
    post.content = '';
    post.type = '';
    post.lastFetchedId = null;
};
</script>

<template>
    <app-layout :title="$t('unlockPosts')">
        <template v-slot:header>
            <div class="flex-1 flex items-center">
                <h2
                    class="text-xl text-gray-800 leading-tight flex-1 mr-6"
                    v-text="$t('unlockPosts')"
                />

                <div class="flex items-center space-x-3">
                    <primary-button class="normal-case" :disabled="settingForm.processing" @click="save">
                        <loading-icon v-if="settingForm.processing" class="mr-2" />
                        <span class="text-sm" v-text="$t('save')" />
                    </primary-button>
                </div>
            </div>
        </template>

        <div class="max-w-7xl mx-auto py-6 px-6 select-none">
            <div class="bg-white rounded-md shadow flex flex-col">
                <table class="w-full rounded-md">
                    <tbody>
                        <tr class="text-left flex border-t even:bg-blue-50 odd:bg-white rounded-t-md">
                            <th class="border-l w-20" v-text="$t('order')" />
                            <th class="border-l w-36" v-text="$t('postID')" />
                            <th class="border-l flex-1" v-text="$t('postContent')" />
                            <th class="border-l w-32" v-text="$t('postType')" />
                            <th class="border-l w-32" v-text="$t('unlockTime')" />
                            <th class="border-l w-32" v-text="$t('size')" />
                            <th class="border-l w-[49px]">
                                <Plus class="w-4 h-4 cursor-pointer text-sky-500 hover:text-sky-600" @click="addPost" />
                            </th>
                        </tr>

                        <tr
                            v-for="(post, index) in postsList"
                            :key="post.id"
                            class="text-left flex border-t transition-all duration-200"
                            :class="{
                                'cursor-move': !dragState.isInputFocused,
                                'cursor-default': dragState.isInputFocused,
                                'even:bg-blue-50 odd:bg-white hover:bg-blue-100': !dragState.isDragging || dragState.draggedIndex !== index,
                                'bg-blue-50 hover:bg-blue-100 shadow-lg border radius-sm border-blue-300': dragState.draggedIndex === index && dragState.isDragging,
                                'border-t-4 border-blue-500': dragState.dropIndicatorIndex === index,
                                'hover:shadow-md': dragState.draggedIndex !== index && !dragState.isInputFocused
                            }"
                            :draggable="!dragState.isInputFocused"
                            @dragstart="handleDragStart($event, index)"
                            @dragend="handleDragEnd"
                            @dragover="handleDragOver($event, index)"
                            @dragleave="handleDragLeave"
                            @drop="handleDrop($event, index)"
                        >
                            <!-- Order Column -->
                            <td class="border-l w-20 p-3 flex items-center">
                                <div class="flex items-center space-x-2">
                                    <!-- Drag Handle -->
                                    <svg
                                        class="w-4 h-4 transition-colors duration-200"
                                        :class="{
                                            'text-gray-400 cursor-move': !dragState.isInputFocused,
                                            'text-gray-300 cursor-not-allowed': dragState.isInputFocused
                                        }"
                                        fill="currentColor"
                                        viewBox="0 0 20 20"
                                    >
                                        <path d="M7 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM7 8a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM7 14a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 8a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 14a2 2 0 1 0 0 4 2 2 0 0 0 0-4z"/>
                                    </svg>
                                    <span class="font-medium text-gray-700">{{ post.order }}</span>
                                </div>
                            </td>

                            <!-- Post ID Column -->
                            <td class="border-l w-36 p-3">
                                <div class="relative">
                                    <text-input
                                        v-model="post.postId"
                                        type="number"
                                        min="1"
                                        :placeholder="$t('postID')"
                                        :disabled="settingForm.processing || loading"
                                        :tabindex="index + 1"
                                        @focus="handleInputFocus"
                                        @blur="handleInputBlur(post)"
                                        @input="clearContent(post)"
                                    />

                                    <!-- Loading indicator -->
                                    <div
                                        v-if="loading && post.postId"
                                        class="absolute w-4 h-4 right-2 top-1/2 transform -translate-y-1/2 -mt-0.5"
                                    >
                                        <loading-icon class="w-4 h-4" color="#9ca3af" />
                                    </div>
                                </div>
                            </td>

                            <!-- Post Content Column -->
                            <td class="border-l flex-1 p-3">
                                <div class="max-w-md">
                                    <p class="text-sm text-gray-800 line-clamp-2">{{ post.content }}</p>
                                </div>
                            </td>

                            <!-- Post Type Column -->
                            <td class="border-l w-32 p-3">
                                {{ post.type }}
                            </td>

                            <!-- Unlock Time Column -->
                            <td class="border-l w-32 p-3">
                                <fixed-selection-box
                                    class="w-full"
                                    v-model="post.unlockTime"
                                    :placeholder="$t('unlockTime')"
                                    :options="unlockTimeOptions"
                                    :disabled="settingForm.processing"
                                    :clearable="false"
                                />
                            </td>

                            <!-- Size Column -->
                            <td class="border-l w-32 p-3">
                                <fixed-selection-box
                                    class="w-full"
                                    v-model="post.size"
                                    :placeholder="$t('size')"
                                    :options="sizeOptions"
                                    :disabled="settingForm.processing"
                                    :clearable="false"
                                />
                            </td>

                            <!-- Actions Column -->
                            <td class="border-l w-[49px]">
                                <button
                                    @click="removePost(index)"
                                    class="text-red-400 hover:text-red-600 transition-colors duration-200"
                                >
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            </td>
                        </tr>

                        <!-- Empty State -->
                        <tr v-if="postsList.length === 0" class="w-full border-t">
                            <td colspan="7" class="p-4">
                                <div class="w-full text-center" v-text="$t('emptyData')" />
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </app-layout>
</template>

<style scoped>
/* Drag and Drop Styles */
.dragging {
    opacity: 0.8;
    background-color: #dbeafe !important; /* bg-blue-50 */
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    border: 2px solid #3b82f6 !important; /* border-blue-500 */
    border-radius: 4px;
}

/* Line clamp utility */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Smooth transitions */
tr {
    transition: all 0.2s ease-in-out;
}

/* Dragged item maintains stable appearance */
tr.dragging {
    background-color: #dbeafe !important; /* Keep hover-like background */
}

tr:last-child {
    @apply rounded-b-md;
}

/* Drop indicator */
.border-t-4 {
    border-top-width: 4px !important;
}

/* Custom scrollbar for table */
.overflow-auto::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading state */
.processing {
    pointer-events: none;
    opacity: 0.7;
}

/* Success animation */
@keyframes success-pulse {
    0% { background-color: #dcfce7; }
    50% { background-color: #bbf7d0; }
    100% { background-color: #dcfce7; }
}

.success-animation {
    animation: success-pulse 0.6s ease-in-out;
}
</style>
