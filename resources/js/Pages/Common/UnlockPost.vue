<script setup>
import { inject } from "vue";
import { useForm } from "@inertiajs/vue3";

import AppLayout from '@/Layouts/AppLayout.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import LoadingIcon from '@/Components/LoadingIcon.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';

const props = defineProps({
    posts: Object,
});

const $toast = inject('$toast');
const settingForm = useForm(props.posts);

const save = () => {
    if (settingForm.processing) {
        return false;
    }

    settingForm.errors = {};

    settingForm.transform(data => {
        return data;
    }).post(route('setting.storeUnlockPosts'), {
        preserveScroll: true,
        preserveState: true,
        onSuccess: (data) => {
            if (data.props.jetstream.flash.message) {
                $toast.success(data.props.jetstream.flash.message);
            }

            /**
             * sync form data
             */
            for (const [key, value] of Object.entries(data.props.settings)) {
                settingForm[key] = value;
            }
        },
    });
}
</script>

<template>
    <app-layout :title="$t('unlockPosts')">
        <template v-slot:header>
            <div class="flex-1 flex items-center">
                <h2
                    class="text-xl text-gray-800 leading-tight flex-1 mr-6"
                    v-text="$t('unlockPosts')"
                />

                <primary-button class="normal-case" :disabled="settingForm.processing" @click="save">
                    <loading-icon v-if="settingForm.processing" class="mr-2" />
                    <span class="text-sm" v-text="$t('save')" />
                </primary-button>
            </div>
        </template>

        <div class="max-w-7xl mx-auto py-6 px-6">
            <div class="bg-white rounded-md shadow flex flex-col overflow-auto">
                <table class="w-full">
                    <tbody>
                        <tr class="text-left flex border-t even:bg-blue-50 odd:bg-white">
                            <th class="border-l w-32" v-text="$t('order')" />
                            <th class="border-l w-32" v-text="$t('postID')" />
                            <th class="border-l flex-1" v-text="$t('postContent')" />
                            <th class="border-l w-32" v-text="$t('postType')" />
                            <th class="border-l w-32" v-text="$t('unlockTime')" />
                            <th class="border-l w-32" v-text="$t('size')" />
                            <th class="border-l w-10" />
                        </tr>

                        <template v-for="(_, qaType) in props.settings" :key="qaType">
                            <tr class="text-left flex border-t even:bg-blue-50 odd:bg-white">
                                <td class="w-64" v-text="$t('qaType.' + qaType)" />
                                <td class="border-l flex-1 flex flex-col">
                                    <text-input
                                        class="block w-full"
                                        type="text"
                                        v-model="settingForm[qaType]['index']"
                                        :disabled="settingForm.processing"
                                    />

                                    <input-error class="w-full" :message="settingForm.errors[qaType + '.index']" />
                                </td>
                                <td class="border-l flex-1 flex flex-col">
                                    <text-input
                                        class="block w-full"
                                        type="text"
                                        v-model="settingForm[qaType]['limit']"
                                        :disabled="settingForm.processing"
                                    />

                                    <input-error class="w-full" :message="settingForm.errors[qaType + '.limit']" />
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>
    </app-layout>
</template>
