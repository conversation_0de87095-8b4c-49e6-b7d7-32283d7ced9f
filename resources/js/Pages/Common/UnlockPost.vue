<script setup>
import { inject, ref, reactive, onMounted, nextTick } from "vue";
import { useForm } from "@inertiajs/vue3";

import AppLayout from '@/Layouts/AppLayout.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import LoadingIcon from '@/Components/LoadingIcon.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';

const props = defineProps({
    posts: Object,
});

const $toast = inject('$toast');
const settingForm = useForm(props.posts);

// Drag & Drop state
const dragState = reactive({
    draggedIndex: null,
    draggedItem: null,
    isDragging: false,
    dropIndicatorIndex: null,
});

// Convert posts object to array for easier manipulation
const postsList = ref([]);

// Initialize posts list
const initializePostsList = () => {
    postsList.value = Object.keys(props.posts).map((key, index) => ({
        id: key,
        order: index + 1,
        postId: props.posts[key].post_id || key,
        content: props.posts[key].content || 'No content',
        type: props.posts[key].type || 'Unknown',
        unlockTime: props.posts[key].unlock_time || '',
        size: props.posts[key].size || 'Medium',
        originalData: props.posts[key]
    }));
};

onMounted(() => {
    initializePostsList();
});

// Drag & Drop functions
const handleDragStart = (event, index) => {
    dragState.draggedIndex = index;
    dragState.draggedItem = postsList.value[index];
    dragState.isDragging = true;

    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/html', event.target);

    // Add dragging class after a small delay to avoid flickering
    nextTick(() => {
        event.target.classList.add('dragging');
    });
};

const handleDragEnd = (event) => {
    dragState.isDragging = false;
    dragState.dropIndicatorIndex = null;
    event.target.classList.remove('dragging');
};

const handleDragOver = (event, index) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';

    if (dragState.draggedIndex !== index) {
        dragState.dropIndicatorIndex = index;
    }
};

const handleDragLeave = () => {
    dragState.dropIndicatorIndex = null;
};

const handleDrop = (event, dropIndex) => {
    event.preventDefault();

    const dragIndex = dragState.draggedIndex;
    if (dragIndex === null || dragIndex === dropIndex) {
        return;
    }

    // Reorder the array
    const draggedItem = postsList.value[dragIndex];
    postsList.value.splice(dragIndex, 1);
    postsList.value.splice(dropIndex, 0, draggedItem);

    // Update order numbers
    postsList.value.forEach((item, index) => {
        item.order = index + 1;
    });

    // Reset drag state
    dragState.draggedIndex = null;
    dragState.draggedItem = null;
    dragState.dropIndicatorIndex = null;

    $toast.success('Order updated successfully');
};

// Remove post function
const removePost = (index) => {
    if (confirm('Are you sure you want to remove this post?')) {
        postsList.value.splice(index, 1);

        // Update order numbers
        postsList.value.forEach((item, idx) => {
            item.order = idx + 1;
        });

        $toast.success('Post removed successfully');
    }
};

// Add new post function (optional)
const addPost = () => {
    const newPost = {
        id: `new_${Date.now()}`,
        order: postsList.value.length + 1,
        postId: 'NEW',
        content: 'New post content',
        type: 'question',
        unlockTime: '0',
        size: 'Medium',
        originalData: {}
    };

    postsList.value.push(newPost);
    $toast.success('New post added');
};

const save = () => {
    if (settingForm.processing) {
        return false;
    }

    settingForm.errors = {};

    // Convert postsList back to the format expected by backend
    const orderedPosts = {};
    postsList.value.forEach((item, index) => {
        orderedPosts[item.id] = {
            ...item.originalData,
            order: index + 1,
            unlock_time: item.unlockTime,
            size: item.size
        };
    });

    settingForm.transform(data => {
        return orderedPosts;
    }).post(route('setting.storeUnlockPosts'), {
        preserveScroll: true,
        preserveState: true,
        onSuccess: (data) => {
            if (data.props.jetstream.flash.message) {
                $toast.success(data.props.jetstream.flash.message);
            }

            /**
             * sync form data
             */
            for (const [key, value] of Object.entries(data.props.settings || {})) {
                settingForm[key] = value;
            }
        },
    });
};
</script>

<template>
    <app-layout :title="$t('unlockPosts')">
        <template v-slot:header>
            <div class="flex-1 flex items-center">
                <h2
                    class="text-xl text-gray-800 leading-tight flex-1 mr-6"
                    v-text="$t('unlockPosts')"
                />

                <div class="flex items-center space-x-3">
                    <!-- Add Post Button -->
                    <button
                        @click="addPost"
                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                    >
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        {{ $t('addPost') }}
                    </button>

                    <!-- Save Button -->
                    <primary-button class="normal-case" :disabled="settingForm.processing" @click="save">
                        <loading-icon v-if="settingForm.processing" class="mr-2" />
                        <span class="text-sm" v-text="$t('save')" />
                    </primary-button>
                </div>
            </div>
        </template>

        <div class="max-w-7xl mx-auto py-6 px-6">
            <div class="bg-white rounded-md shadow flex flex-col overflow-auto">
                <table class="w-full">
                    <tbody>
                        <tr class="text-left flex border-t even:bg-blue-50 odd:bg-white">
                            <th class="border-l w-32" v-text="$t('order')" />
                            <th class="border-l w-32" v-text="$t('postID')" />
                            <th class="border-l flex-1" v-text="$t('postContent')" />
                            <th class="border-l w-32" v-text="$t('postType')" />
                            <th class="border-l w-32" v-text="$t('unlockTime')" />
                            <th class="border-l w-32" v-text="$t('size')" />
                            <th class="border-l w-10" />
                        </tr>

                        <tr
                            v-for="(post, index) in postsList"
                            :key="post.id"
                            class="text-left flex border-t transition-all duration-200 cursor-move hover:bg-blue-50"
                            :class="{
                                'even:bg-blue-50 odd:bg-white': !dragState.isDragging,
                                'bg-blue-100 shadow-lg': dragState.draggedIndex === index,
                                'border-t-4 border-blue-500': dragState.dropIndicatorIndex === index,
                                'opacity-50': dragState.isDragging && dragState.draggedIndex === index
                            }"
                            draggable="true"
                            @dragstart="handleDragStart($event, index)"
                            @dragend="handleDragEnd"
                            @dragover="handleDragOver($event, index)"
                            @dragleave="handleDragLeave"
                            @drop="handleDrop($event, index)"
                        >
                            <!-- Order Column -->
                            <td class="border-l w-32 p-3 flex items-center">
                                <div class="flex items-center space-x-2">
                                    <!-- Drag Handle -->
                                    <svg class="w-4 h-4 text-gray-400 cursor-move" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M7 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM7 8a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM7 14a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 8a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 14a2 2 0 1 0 0 4 2 2 0 0 0 0-4z"/>
                                    </svg>
                                    <span class="font-medium text-gray-700">{{ post.order }}</span>
                                </div>
                            </td>

                            <!-- Post ID Column -->
                            <td class="border-l w-32 p-3">
                                <span class="text-sm text-gray-600">#{{ post.postId }}</span>
                            </td>

                            <!-- Post Content Column -->
                            <td class="border-l flex-1 p-3">
                                <div class="max-w-md">
                                    <p class="text-sm text-gray-800 line-clamp-2">{{ post.content }}</p>
                                </div>
                            </td>

                            <!-- Post Type Column -->
                            <td class="border-l w-32 p-3">
                                <span
                                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                    :class="{
                                        'bg-blue-100 text-blue-800': post.type === 'question',
                                        'bg-green-100 text-green-800': post.type === 'answer',
                                        'bg-purple-100 text-purple-800': post.type === 'topic',
                                        'bg-gray-100 text-gray-800': !['question', 'answer', 'topic'].includes(post.type)
                                    }"
                                >
                                    {{ post.type }}
                                </span>
                            </td>

                            <!-- Unlock Time Column -->
                            <td class="border-l w-32 p-3">
                                <text-input
                                    v-model="post.unlockTime"
                                    type="number"
                                    class="w-full text-sm"
                                    placeholder="0"
                                    min="0"
                                />
                            </td>

                            <!-- Size Column -->
                            <td class="border-l w-32 p-3">
                                <select
                                    v-model="post.size"
                                    class="w-full text-sm border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                >
                                    <option value="Small">Small</option>
                                    <option value="Medium">Medium</option>
                                    <option value="Large">Large</option>
                                </select>
                            </td>

                            <!-- Actions Column -->
                            <td class="border-l w-10 p-3">
                                <button
                                    @click="removePost(index)"
                                    class="text-red-500 hover:text-red-700 transition-colors duration-200"
                                    title="Remove post"
                                >
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            </td>
                        </tr>

                        <!-- Empty State -->
                        <tr v-if="postsList.length === 0" class="text-center">
                            <td colspan="7" class="p-8 text-gray-500">
                                <div class="flex flex-col items-center space-y-2">
                                    <svg class="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                    <p>{{ $t('noPostsFound') }}</p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </app-layout>
</template>

<style scoped>
/* Drag and Drop Styles */
.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    z-index: 1000;
}

/* Line clamp utility */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Smooth transitions */
tr {
    transition: all 0.2s ease-in-out;
}

/* Hover effects */
tr:hover {
    @apply bg-blue-100;
}

/* Drop indicator */
.border-t-4 {
    border-top-width: 4px !important;
}

/* Custom scrollbar for table */
.overflow-auto::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading state */
.processing {
    pointer-events: none;
    opacity: 0.7;
}

/* Success animation */
@keyframes success-pulse {
    0% { background-color: #dcfce7; }
    50% { background-color: #bbf7d0; }
    100% { background-color: #dcfce7; }
}

.success-animation {
    animation: success-pulse 0.6s ease-in-out;
}
</style>
