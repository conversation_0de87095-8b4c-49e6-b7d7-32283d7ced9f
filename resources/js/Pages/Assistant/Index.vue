<script setup>
import { inject, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { router, useForm } from '@inertiajs/vue3';
import throttle from "lodash/throttle.js";
import { route } from "ziggy-js";

import DataTable from 'primevue/datatable';
import Column from 'primevue/column';

import AppLayout from '@/Layouts/AppLayout.vue';
import InputLabel from "@/Components/InputLabel.vue";
import SearchInput from "@/Components/SearchInput.vue";
import Pagination from "@/Components/Pagination.vue";
import LoadingIcon from "@/Components/LoadingIcon.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import Modal from "@/Components/Modal.vue";
import FixedSelectionBox from "@/Components/FixedSelectionBox.vue";
import TextInput from "@/Components/TextInput.vue";
import TextAreaInput from "@/Components/TextAreaInput.vue";
import InputError from "@/Components/InputError.vue";

const { t } = useI18n();
const $toast = inject('$toast');

const props = defineProps({
    filters: Object,
    assistants: Object,
});

const config = reactive({
    searched: props.filters.search ?? '',
    model: props.filters.model ?? 'multilingual-e5-large-instruct',
    showSearchClearButton: (props.filters.search ?? '').trim().length > 0,
    searchedLanguage: props.filters.language ?? 'vi',
    searching: false,
    showModal: false,
    open: false,
    languages: [
        {
            value: 'vi',
            label: t('vietnamese'),
        },
        {
            value: 'ja',
            label: t('japanese'),
        },
    ],
    models: [
        {
            value: 'multilingual-e5-large-instruct',
            label: 'multilingual-e5-large-instruct',
        },
    ],
});

const _search = throttle(() => router.get(route('assistant'), {
    search: config.searched,
    model: config.model ? config.model : '',
    language: config.searchedLanguage,
}, {
    preserveState: true,
    onBefore: () => {
        config.searching = true;
    },
    onFinish: () => {
        config.searching = false;
    },
}), 1000);

const search = () => {
    config.showSearchClearButton = true;
    _search();
}

const clearSearch = () => {
    config.searched = '';
    config.showSearchClearButton = false;
    _search();
}

const searchByModel = () => {
    _search();
}

const searchByLanguage = () => {
    _search();
}

let form = useForm({
    name: '',
    work: '',
    expertise: '',
    description: '',
    language: 'vi',
});

const closeModal = () => {
    form.language = config.searchedLanguage;
    config.open = false;
    setTimeout(() => {
        config.showModal = false;
        form.clearErrors();
    }, 150);
}

const openModal = () => {
    config.showModal = true;
    setTimeout(() => config.open = true, 150);
}

const save = () => {
    if (form.processing) {
        return false;
    }

    form.errors = {};

    form.transform(data => {
        data.language = config.searchedLanguage ?? data.language;

        return data;
    }).post(route('assistant.store'), {
        preserveScroll: true,
        preserveState: true,
        onSuccess: (data) => {
            if (data.props.jetstream.flash.message) {
                $toast.success(data.props.jetstream.flash.message);
            }

            closeModal();

            /**
             * reset form
             */
            form = useForm({
                name: '',
                work: '',
                expertise: '',
                description: '',
                language: config.searchedLanguage,
            })
        },
    });
}

</script>

<template>
    <app-layout :title="$t('assistantList')">
        <template v-slot:header>
            <div class="flex-1 flex items-center mr-auto">
                <h2
                    class="text-xl text-gray-800 leading-tight mr-auto"
                    v-text="$t('assistantList')"
                />

                <primary-button class="normal-case ml-2" :disabled="form.processing" @click="openModal">
                    <loading-icon v-if="form.processing" class="mr-2" />
                    <span class="text-sm" v-text="$t('newAssistant')" />
                </primary-button>
            </div>
        </template>

        <div class="p-6 sm:mx-2">
            <div class="flex items-stretch">
                <div class="mt-0 w-80 mr-8">
                    <input-label for="user-search" :value="$t('assistantSearch')" />

                    <search-input
                        id="user-search"
                        class="mt-1 block w-full"
                        v-model="config.searched"
                        placeholder="..."
                        :disabled="config.searching"
                        :show-clear-button="config.showSearchClearButton"
                        @input="config.showSearchClearButton = false"
                        @clear-search="clearSearch"
                        @enter="search"
                    />
                </div>

                <div class="mt-0 w-80 mr-8">
                    <input-label for="user-search" :value="$t('assistantModel')" />

                    <fixed-selection-box
                        v-model="config.model"
                        :disabled="config.searching"
                        :options="config.models"
                        :clearable="false"
                        @selected="searchByModel"
                    />
                </div>

                <div class="mt-0 w-80 mr-8">
                    <input-label for="language-search" :value="$t('language')" />

                    <fixed-selection-box
                        v-model="config.searchedLanguage"
                        :disabled="config.searching"
                        :options="config.languages"
                        :clearable="false"
                        @selected="searchByLanguage"
                    />
                </div>
            </div>

            <div
                class="bg-white rounded-md shadow overflow-auto mt-5 flex flex-col"
                :class="{ 'grid-loading': config.searching }"
            >
                <loading-icon v-if="config.searching" class="grid-loading-icon z-10" :size="36" color="rgb(55 65 81)" />

                <data-table :value="assistants['data']">
                    <column class="number-column" field="assistant_id" :header="$t('ID')" />
                    <column class="post-username-column" field="name" :header="$t('assistantName')" />
                    <column class="w-[250px]" field="work" :header="$t('assistantWork')" />
                    <column class="w-[320px]" field="expertise" :header="$t('assistantExpertise')" />
                    <column class="title-flex-column" field="description" :header="$t('assistantDescription')" />
                    <template #empty>{{ $t((filters.search && filters.search !== '') || (filters.language && filters.language !== '') ? 'emptyResult' : 'emptyData') }}</template>
                </data-table>
            </div>

            <pagination
                v-if="assistants['data'].length > 0"
                class="mt-5 flex items-center justify-center mx-auto"
                :links="assistants['links']"
            />
        </div>
    </app-layout>

    <modal v-if="config.showModal" :show="config.open" @close="closeModal">
        <div class="pt-4 pb-3 px-3 font-semibold" v-text="$t('addNewAssistant')" />

        <div class="border-t border-b p-4">
            <div class="flex items-stretch">
                <div class="flex-1 mr-3">
                    <input-label for="input-name" :value="$t('assistantName')" />

                    <text-input
                        id="input-name"
                        class="mt-1"
                        v-model="form.name"
                    />

                    <input-error :message="form.errors.name" />
                </div>
                <div class="flex-1 ml-3">
                    <input-label for="input-work" :value="$t('assistantWork')" />

                    <text-input
                        id="input-work"
                        class="mt-1"
                        v-model="form.work"
                    />

                    <input-error :message="form.errors.work" />
                </div>
            </div>

            <div class="mt-3">
                <input-label for="input-expertise" :value="$t('assistantExpertise')" />

                <text-input
                    id="input-expertise"
                    class="mt-1"
                    v-model="form.expertise"
                />

                <input-error :message="form.errors.expertise" />
            </div>

            <div class="mt-3">
                <input-label for="input-description" :value="$t('assistantDescription')" />

                <text-area-input
                    id="input-expertise"
                    class="mt-1 w-full"
                    v-model="form.description"
                />

                <input-error :message="form.errors.description" />
            </div>
        </div>

        <div class="flex items-center justify-end px-3 py-3">
            <secondary-button class="mr-3 text-sm" v-text="$t('cancel')" @click="closeModal" />

            <primary-button class="text-sm overflow-hidden h-[34px]" @click="save">
                <loading-icon v-if="form.processing" class="mr-1" />

                <span class="text-sm text-white" v-text="$t('save')" />
            </primary-button>
        </div>
    </modal>
</template>
