<template>
    <selection-box
        :label="label"
        :disabled="disabled"
        :loading="config.loading"
        :placeholder="placeholder"
        :options="config.options"
        :can-clear="clearable"
        :show-selected="showSelected"
        :enable-search="enableSearch"
        :search-placeholder="searchPlaceholder"
        v-model="config.modelValue"
        @update:modelValue="updateModelValue"
        @refresh="loadData"
        @selected="onSelected"
    />
</template>

<script setup>
import { computed, reactive, watch } from "vue";
import { useSurveyStore } from "@/Stores/survey";
import { getId } from "@/Utils/index.js";

import SelectionBox from "@/Components/SelectionBox.vue";

const emit = defineEmits([
    'update:modelValue',
    'update:loading',
    'selected',
    'dataCleared',
]);

const props = defineProps({
    label: {
        type: String,
        default: '',
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: '',
    },
    modelValue: {
        default: '',
        validator(value) {
            return value === null || typeof value === 'string' || typeof value === 'number';
        },
    },
    clearData: {
        type: Boolean,
        default: false,
    },
    clearable: {
        type: Boolean,
        default: false,
    },
    showSelected: {
        type: Boolean,
        default: true,
    },
    enableSearch: {
        type: Boolean,
        default: false,
    },
    loadData: {
        type: Boolean,
        default: true,
    },
    searchPlaceholder: {
        type: String,
        default: '',
    },
    exclude: {
        type: Array,
        default: [],
    },
});

const surveyStore = useSurveyStore();

const config = reactive({
    loading: false,
    modelValue: props.modelValue ?? '',
    exclude: computed(() => props.exclude),
    options: computed(() => {
        const options = [];
        if (surveyStore.hasData) {
            surveyStore.data.forEach(survey => {
                if (!config.exclude.includes(survey.survey_id)) {
                    options.push({
                        label: getId(survey['survey_id'], 'S') + ' - ' + survey['title'],
                        title: survey['title'],
                        value: survey['survey_id'],
                    });
                }
            });
        }

        return options;
    }),
});

watch(() => props.clearData, (val) => {
    if (val === true) {
        config.modelValue = '';
        emit('dataCleared');
    }
});

const loadData = async () => {
    emit('update:loading', true);
    config.loading = true;

    await surveyStore.loadData().then(() => {
        config.loading = false;
        config.modelValue = props.modelValue;
        emit('update:loading', false);
    });
}

if (! surveyStore.hasData && props.loadData) {
    loadData();
}

const updateModelValue = (value) => {
    emit('update:modelValue', value);
}

const onSelected = (option) => {
    emit('selected', option);
}
</script>
