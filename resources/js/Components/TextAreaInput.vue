<template>
    <textarea
        class="input-area"
        v-model="config.text"
        :disabled="disabled"
        :placeholder="placeholder"
        @input="$emit('update:modelValue', $event.target.value)"
        autocomplete="off"
    />
</template>

<script setup>
import { reactive, watch } from "vue";

const props = defineProps({
    modelValue: {
        default: '',
        validator(value) {
            return value === null || typeof value === 'string' || typeof value === 'number';
        },
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: '',
    },
    clearData: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['update:modelValue', 'dataCleared']);

const config = reactive({
    text: props.modelValue,
});

watch(() => props.clearData, (val) => {
    if (true === val) {
        config.text = '';
        emit('dataCleared');
    }
})
</script>
