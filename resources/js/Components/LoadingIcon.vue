<template>
    <i class="loading-icon" :style="style" />
</template>

<script setup>
import { computed } from 'vue';
const defaultSize = 16;
const defaultBackgroundColor = '#ffffff';

const props = defineProps({
    size: {
        type: Number,
        default: null,
    },

    color: {
        type: String,
        default: null,
    },
});

const style = computed(() => {
    const params = {}

    if (props.size && props.size !== defaultSize) {
        params['--icon-size'] = `${props.size}px`
    }

    if (props.color && props.color !== defaultBackgroundColor) {
        params['--background-color'] = `${props.color}`
    }

    return params
});
</script>

<style lang='scss'>
.loading-icon {
    --icon-size: 16px;
    --background-color: #ffffff;

    display: inline-block;
    width: var(--icon-size);
    height: var(--icon-size);

    mask-image: url('/assets/images/loading.svg');
    mask-position: center;
    mask-size: cover;
    background-color: var(--background-color);
}
</style>
