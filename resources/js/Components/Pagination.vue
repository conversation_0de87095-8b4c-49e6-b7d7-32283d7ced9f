<template>
    <div class="w-full flex items-center">
        <div class="mr-auto flex items-center text-sm">
            <span v-text="t('display')" class="mr-1.5" />
            <fixed-selection-box
                class="w-[54px] pagination-limit-wrapper"
                v-model="config.limit"
                :disabled="disabled"
                :options="config.limitOptions"
                :clearable="false"
                @selected="changeLimit"
            />
            <span v-text="t('itemPerPage', { total: total, from: from, to: to })" class="ml-1.5" />
        </div>

        <div class="flex flex-wrap items-end -mb-1 ml-2" v-if="links.length > 3">
            <template v-for="(link, key) in links">
                <div
                    v-if="link.url === null"
                    :key="key"
                    class="mr-1 px-3.5 py-2.5 text-gray-400 text-sm leading-4 transition"
                    :class="{ 'border rounded': link.label !== '...' }"
                    v-html="link.label"
                />

                <div
                    v-else-if="link.active"
                    :key="`active-${key}`"
                    class="mr-1 px-3.5 py-2.5 text-sm leading-4 border focus:border-sky-500 rounded transition"
                    :class="{ 'bg-sky-500 text-white font-semibold': !active, 'bg-gray-100/90': active }"
                    v-html="link.label"
                />

                <template v-else>
                    <div
                        v-if="disabled"
                        :key="`pagination-disabled-${key}`"
                        class="mr-1 px-3.5 py-2.5 text-gray-400 text-sm leading-4 transition border rounded"
                        :class="{ 'bg-sky-500 text-white font-semibold': active === key, 'bg-gray-100/90': active !== key }"
                        v-html="link.label"
                    />

                    <inertia-link
                        v-else
                        :key="`link-${key}`"
                        class="mr-1 px-3.5 py-2.5 focus:text-sky-500 text-sm leading-4 hover:bg-sky-100 border focus:border-sky-500 rounded bg-white transition"
                        :class="{ 'bg-white': link.active }"
                        :href="link.url"
                        v-html="link.label"
                        @click.prevent="$emit('progress', key)"
                    />
                </template>
            </template>
        </div>
    </div>
</template>

<script setup>
import { Link as InertiaLink } from "@inertiajs/vue3";
import FixedSelectionBox from "@/Components/FixedSelectionBox.vue";
import { reactive } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps({
    links: Array,
    active: Number | null,
    disabled: Boolean,
    limit: Number | null,
    total: Number | 0,
    from: Number | 0,
    to: Number | 0,
});

const config = reactive({
    limit: props.limit || 10,
    limitOptions: [
        {
            value: 10,
            label: t('10'),
        },
        {
            value: 20,
            label: t('20'),
        },
        {
            value: 50,
            label: t('50'),
        },
        {
            value: 100,
            label: t('100'),
        },
        {
            value: 200,
            label: t('200'),
        },
    ],
});

const emit = defineEmits([
    'progress',
    'changeLimit',
]);

const changeLimit = () => {
    emit('changeLimit', config.limit);
}
</script>
