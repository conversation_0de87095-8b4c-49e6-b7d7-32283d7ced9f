<template>
    <label v-if="label" class="w-full" v-text="label" />

    <button
        type="button"
        :class="switchClasses"
        :disabled="disabled"
        @click="toggle"
        role="switch"
        :aria-checked="isOn"
        :aria-labelledby="labelId"
    >
        <span
            :class="backgroundClasses"
            aria-hidden="true"
        />

        <span
            :class="toggleClasses"
            aria-hidden="true"
        />
    </button>
</template>

<script setup>
import { computed, ref } from 'vue';

const props = defineProps({
    modelValue: {
        type: [Number, Boolean],
        default: 0,
        validator(value) {
            return [0, 1, true, false].includes(value);
        }
    },
    label: {
        type: String,
        default: ''
    },
    disabled: {
        type: Boolean,
        default: false
    },
    size: {
        type: String,
        default: 'lg',
        validator(value) {
            return ['sm', 'md', 'lg'].includes(value);
        }
    },
    color: {
        type: String,
        default: 'sky',
        validator(value) {
            return ['sky', 'blue', 'green', 'red', 'purple', 'indigo', 'pink'].includes(value);
        }
    },
});

const emit = defineEmits(['update:modelValue', 'change']);

// Convert modelValue to boolean for internal use
const isOn = computed(() => {
    return props.modelValue === 1 || props.modelValue === true;
});

// Generate unique ID for accessibility
const labelId = ref(`switcher-${Math.random().toString(36).substr(2, 9)}`);

// Size configurations
const sizeConfig = {
    sm: {
        switch: 'h-5 w-9',
        toggle: 'h-4 w-4',
        translate: 'translate-x-[18px]'
    },
    md: {
        switch: 'h-6 w-11',
        toggle: 'h-5 w-5',
        translate: 'translate-x-[22px]'
    },
    lg: {
        switch: 'h-7 w-14',
        toggle: 'h-6 w-6',
        translate: 'translate-x-[30px]'
    }
};

// Color configurations
const colorConfig = {
    sky: 'bg-sky-400',
    blue: 'bg-blue-400',
    green: 'bg-green-400',
    red: 'bg-red-400',
    purple: 'bg-purple-400',
    indigo: 'bg-indigo-400',
    pink: 'bg-pink-400'
};

// Computed classes
const switchClasses = computed(() => [
    'mt-[6px] relative inline-flex flex-shrink-0 cursor-pointer rounded-full transition-colors duration-200 ease-in-out focus:outline-none',
    sizeConfig[props.size].switch,
    props.disabled
        ? 'opacity-50 cursor-not-allowed'
        : '',
    isOn.value
        ? colorConfig[props.color]
        : 'bg-gray-200'
]);

const backgroundClasses = computed(() => [
    'pointer-events-none absolute inset-0 rounded-full transition-colors duration-200 ease-in-out',
    isOn.value
        ? colorConfig[props.color]
        : 'bg-gray-200'
]);

const toggleClasses = computed(() => [
    'pointer-events-none inline-block rounded-full bg-white shadow transform transition duration-200 ease-in-out flex items-center justify-center translate-y-0.5',
    sizeConfig[props.size].toggle,
    isOn.value
        ? sizeConfig[props.size].translate
        : 'translate-x-[2px]'
]);

// Toggle function
const toggle = () => {
    if (props.disabled) return;

    const newValue = isOn.value ? 0 : 1;
    emit('update:modelValue', newValue);
    emit('change', newValue);
};
</script>

<style scoped>
.transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}
</style>
