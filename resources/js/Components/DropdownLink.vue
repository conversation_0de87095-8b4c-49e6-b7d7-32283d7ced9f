<script setup>
import { Link } from '@inertiajs/vue3';

defineProps({
    href: String,
    type: String,
});
</script>

<template>
    <button v-if="type === 'button'" type="submit" class="block w-full px-4 py-2 text-start leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out">
        <slot />
    </button>

    <a v-else-if="type === 'a'" :href="href" class="block px-4 py-2 leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out">
        <slot />
    </a>

    <Link v-else :href="href" class="block px-4 py-2 leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out">
        <slot />
    </Link>
</template>
