<template>
    <div class="flex flex-col">
        <div class="flex w-full">
            <div class="flex-1 flex flex-col mr-3">
                <label v-if="label" v-text="$t('image')" class="w-full mb-1" />
                <div
                    class="flex flex-1 h-[42px] border border-gray-300 rounded-md shadow-sm px-3 flex items-center"
                    :class="{ 'bg-gray-200': disabled, 'text-gray-400': !selectedFile }"
                >
                    <div v-text="selected ?? $t('chooseFile')" class="flex-1 max-w-[380px] mr-3 truncate" />

                    <button
                        type="button"
                        class="button-select-file ml-auto"
                        :class="{ 'bg-gray-200': disabled }"
                        v-text="$t('choose')"
                        @click="chooseFile"
                    />
                </div>
            </div>

            <div class="w-[70px] h-[70px] flex items-center justify-center">
                <img class="max-w-full max-h-full" :src="selectedFile" />
            </div>
        </div>

        <input-error class="w-full mt-1" :message="errorMessage" />
    </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

import InputError from '@/Components/InputError.vue';

const { t } = useI18n();

const emit = defineEmits(['update:modelValue']);

const props = defineProps({
    label: {
        type: String,
        default: '',
    },
    error: {
        type: String,
        default: '',
    },
    modelValue: {
        default: '',
        validator(value) {
            return value === null || typeof value === 'string' || typeof value === 'number' || typeof value === 'object';
        },
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    defaultImage: {
        type: String,
        default: '/assets/images/no-image-img.svg',
    },
});

const errorMessage = ref(props.error);
watch(() => props.error, (val) => {
    errorMessage.value = val;
});


const selectedFile = ref(props.modelValue || '/assets/images/no-image-img.svg');
const selected = ref();

const uploadFile = document.createElement('input');
uploadFile.type = 'file';
uploadFile.accept = 'image/png,image/jpeg,image/webp';

uploadFile.onchange = e => {
    let file = e.target['files'][0];
    errorMessage.value = '';

    if (file.size > (15 * 1024 * 1024)) {
        errorMessage.value = t('uploadMaxSize');
        return;
    }

    selected.value = file.name;

    /**
     * clear old value
     */
    uploadFile.value = '';

    const reader = new FileReader();

    /**
     * update image src
     */
    reader.onload = async() => {
        const image = reader.result;
        selectedFile.value = image;
    };

    reader.readAsDataURL(file);

    emit('update:modelValue', file);
}

const chooseFile = () => {
    if (props.disabled) {
        return;
    }

    uploadFile.click();
};
</script>
