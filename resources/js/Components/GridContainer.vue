<template>
    <div
        class="bg-white rounded-md shadow overflow-auto mt-5 flex flex-col"
        :class="{ 'grid-loading': loading }"
    >
        <loading-icon v-if="loading" class="grid-loading-icon z-10" :size="36" color="rgb(2 132 199)" />

        <slot />
    </div>
</template>

<script setup>
import LoadingIcon from "@/Components/LoadingIcon.vue";

const props = defineProps({
    loading: Boolean,
});
</script>
