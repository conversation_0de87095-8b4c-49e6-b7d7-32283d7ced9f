<template>
    <div class="flex items-stretch">
        <listbox
            v-model="selectedObject"
            :disabled="disabled"
            v-slot="{ open }"
        >
            <div
                class="relative flex-auto"
                :class="[canRefresh ? 'select-box-refresh' : 'w-full']"
            >
                <listbox-label v-if="label" as="label" v-text="label" />

                <listbox-button
                    class="listbox-button"
                    :class="[config.disabled ? 'bg-gray-200': 'bg-white', open && !enableSearch ? 'border-sky-400 ring ring-sky-200' : '']"
                >
                    <span
                        class="block truncate"
                        :class="{ 'text-gray-500': ! selectedObject?.value }"
                        v-html="selectedObject?.label ?? placeholder"
                    />

                    <span class="absolute inset-y-0 right-0 flex items-center pr-2">
                        <loading-icon v-if="config.loading" class="opacity-50" color="#000000" />

                        <template v-else>
                            <x-icon
                                v-if="selectedObject?.value && canClear"
                                class="w-4 h-4 text-gray-400 hover:text-orange-700 hover:cursor-pointer"
                                :class="{ 'pointer-events-none': config.loading }"
                                aria-hidden="true"
                                @click="clearSelection"
                            />

                            <selector-icon
                                v-else
                                class="h-5 w-5 text-gray-400"
                                aria-hidden="true"
                            />
                        </template>
                    </span>
                </listbox-button>

                <transition
                    leave-active-class="transition duration-100 ease-in"
                    leave-from-class="opacity-100"
                    leave-to-class="opacity-0"
                >
                    <listbox-options
                        v-if="config.options.length > 0 || config.search !== ''"
                        class="listbox-options"
                    >
                        <div v-if="enableSearch" class="p-2 -mt-1">
                            <text-input
                                autofocus
                                class="w-full"
                                v-model="config.search"
                                :placeholder="searchPlaceholder"
                            />
                        </div>

                        <div class="max-h-60 overflow-auto">
                            <div
                                v-if="config.search !== '' && config.options.length === 0"
                                class="p-2 text-center"
                                v-text="$t('emptyResult')"
                            />

                            <listbox-option
                                v-slot="{ active, selected }"
                                v-for="(option, index) in config.options"
                                :key="option.value"
                                :value="option"
                                as="template"
                            >
                                <li
                                    :class="getRowClasses(active, index)"
                                    @click="select(option)"
                                >
                                    <span
                                        :class="[selected ? 'font-semibold' : 'font-normal', 'block']"
                                        v-text="option.label"
                                    />

                                    <span
                                        v-if="showSelected && selected"
                                        class="absolute inset-y-0 left-0 flex items-center pl-3 text-sky-600"
                                    >
                                        <check-icon class="h-5 w-5" aria-hidden="true" />
                                    </span>
                                </li>
                            </listbox-option>
                        </div>
                    </listbox-options>
                </transition>
            </div>
        </listbox>

        <div v-if="canRefresh" class="flex flex-col ml-1.5">
            <button
                class="rounded-md border border-gray-300 p-2.5 focus:outline-none"
                :class="{ 'bg-gray-200 cursor-default': config.disabled, 'bg-white hover:bg-gray-100': ! config.disabled }"
                @click="() => refreshData()"
            >
                <refresh class="w-5 h-5" aria-hidden="true" />
            </button>
        </div>
    </div>
</template>

<script setup>
import LoadingIcon from "@/Components/LoadingIcon.vue";
import { computed, ref, reactive, watch } from "vue";
import {
    Listbox,
    ListboxLabel,
    ListboxButton,
    ListboxOptions,
    ListboxOption,
} from '@headlessui/vue';
import { CheckIcon, SelectorIcon, XIcon } from '@heroicons/vue/outline';
import { Refresh } from '@element-plus/icons-vue';
import TextInput from "@/Components/TextInput.vue";

const props = defineProps({
    label: {
        type: String,
        default: '',
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    loading: {
        type: Boolean,
        default: false,
    },
    showSelected: {
        type: Boolean,
        default: true,
    },
    placeholder: {
        type: String,
        default: '',
    },
    modelValue: {
        default: '',
        validator(value) {
            return value === null || typeof value === 'string' || typeof value === 'number';
        },
    },
    canRefresh: {
        type: Boolean,
        default: true,
    },
    canClear: {
        type: Boolean,
        default: true,
    },
    valueType: {
        type: String,
        default: 'integer',
    },
    options: {
        type: Array,
        default: [],
    },
    enableSearch: {
        type: Boolean,
        default: false,
    },
    searchPlaceholder: {
        type: String,
        default: '',
    },
});

const emit = defineEmits([
    'update:modelValue',
    'refresh',
    'selected',
    'cleared',
]);

const config = reactive({
    loading: computed(() => props.loading),
    search: '',
    options: !Array.isArray(props.options) ? props.options : computed(() => {
        const options = [];
        props.options.forEach(option => {
            if (option.label.toLowerCase().includes(config.search.toLowerCase())) {
                options.push(option);
            }
        });

        return options;
    }),
    disabled: computed(() => props.loading || props.disabled),
});

const selectedObject = ref({});

const getValue = (value) => {
    return props.valueType === 'integer' ? parseInt(value) : value;
}

const setActive = (value) => {
    selectedObject.value = config.options['find'](option => option.value === getValue(value)) ?? {};
}

watch(() => props.modelValue, (val) => {
    val ? setActive(val) : selectedObject.value = {};
}, {
    immediate: true,
});

watch(() => props.options, (val) => {
    val ? setActive(props.modelValue) : selectedObject.value = {};
}, {
    immediate: true,
});

if (props.modelValue && config.options.length) {
    setActive(props.modelValue);
}

const refreshData = () => {
    if (config.disabled) {
        return false;
    }

    emit('refresh');
}

const clearSelection = (e) => {
    e.preventDefault();

    if (config.disabled) {
        return false;
    }

    selectedObject.value = null;
    emit('update:modelValue', null);
    emit('cleared');
}

const select = (option) => {
    config.search = '';
    emit('update:modelValue', option.value);
    emit('selected', option);
}

/*const onSearch = (val) => {
    config.search = val;
}*/

const getRowClasses = (active, index) => {
    const classes = [
        'relative cursor-default select-none py-2 pr-4'
    ];

    classes.push(active ? 'bg-sky-100 text-sky-900' : 'text-gray-900');
    classes.push(props.showSelected ? 'pl-10' : 'pl-4');

    if (index > 0) {
        classes.push('border-t border-dashed');
    }

    return classes;
}
</script>
