<template>
    <div class="relative">
        <input
            class="input-text"
            ref="input"
            :class="showClearButton && modelValue ? 'pr-[36px]' : ''"
            :value="modelValue"
            :disabled="disabled"
            :placeholder="placeholder"
            @input="onInput"
            @keyup.enter="$emit('enter')"
            @keydown="onKeyDown"
            autocomplete="off"
        />

        <i
            v-if="showClearButton && modelValue"
            class="absolute pi pi-times input-clear-icon text-gray-400 hover:text-red-400 hover:cursor-pointer"
            @click="$emit('clearSearch')"
        />
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';

defineProps({
    modelValue: {
        default: '',
        validator(value) {
            return value === null || typeof value === 'string' || typeof value === 'number' || typeof value === 'object';
        },
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: '',
    },
    showClearButton: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['update:modelValue', 'enter', 'clearSearch', 'input']);

const input = ref(null);

const onInput = ($event) => {
    emit('update:modelValue', $event.target.value);
    emit('input');
}

onMounted(() => {
    if (input.value.hasAttribute('autofocus')) {
        setTimeout(() => input.value.focus(), 50);
    }
});

defineExpose({focus: () => input.value.focus()});

const onKeyDown = (e) => {
    const allowedHeadlessUIKeys = [
        "ArrowUp",
        "ArrowDown",
        "Enter",
        " ", // Space Key
        "Home",
        "End",
        "Escape",
    ];

    if (allowedHeadlessUIKeys.includes(e.key)) {
        e.stopPropagation();
    }
}
</script>
