<script setup>
import Modal from './Modal.vue';

const emit = defineEmits(['close']);

defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    maxWidth: {
        type: String,
        default: '2xl',
    },
    closeable: {
        type: Boolean,
        default: true,
    },
});

const close = () => {
    emit('close');
};
</script>

<template>
    <Modal
        :show="show"
        :max-width="maxWidth"
        :closeable="closeable"
        @close="close"
    >
        <div v-if="$slots.title" class="text-lg font-semibold text-gray-900 border-b py-4 px-6">
            <slot name="title" />
        </div>

        <div class="px-6 py-4 text-gray-600">
            <slot name="content" />
        </div>

        <div v-if="$slots.footer" class="flex flex-row justify-end px-6 py-4 border-t text-end">
            <slot name="footer" />
        </div>
    </Modal>
</template>
