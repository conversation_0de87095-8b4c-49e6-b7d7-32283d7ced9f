<template>
    <selection-box
        :label="label"
        :disabled="disabled"
        :placeholder="placeholder"
        :options="options"
        :can-refresh="false"
        :can-clear="clearable"
        value-type="string"
        v-model="config.modelValue"
        @update:modelValue="updateModelValue"
        @selected="onSelected"
        @cleared="onCleared"
    />
</template>

<script setup>
import { reactive, watch } from "vue";
import SelectionBox from "@/Components/SelectionBox.vue";

const emit = defineEmits([
    'update:modelValue',
    'selected',
    'dataCleared',
    'cleared',
]);

const props = defineProps({
    label: {
        type: String,
        default: '',
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: '',
    },
    modelValue: {
        default: '',
        validator(value) {
            return value === null || typeof value === 'string' || typeof value === 'number';
        },
    },
    options: {
        type: Array,
        default: [],
    },
    clearData: {
        type: Boolean,
        default: false,
    },
    clearable: {
        type: Boolean,
        default: true,
    },
});

const config = reactive({
    modelValue: props.modelValue ?? '',
});

watch(() => props.clearData, (val) => {
    if (val === true) {
        config.modelValue = '';
        emit('dataCleared');
    }
});

const updateModelValue = (value) => {
    emit('update:modelValue', value);
}

const onSelected = (option) => {
    emit('selected', option);
}

const onCleared = () => {
    emit('cleared');
}
</script>
