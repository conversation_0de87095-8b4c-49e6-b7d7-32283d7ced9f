<script setup>
import { computed } from 'vue';
import { Link as InertiaLink } from '@inertiajs/vue3';

const props = defineProps({
    active: Boolean,
    href: String,
    as: String,
});

const classes = computed(() => {
    return props.active ? 'link-responsive-active' : 'link-responsive';
});
</script>

<template>
    <div>
        <button v-if="as === 'button'" :class="classes" class="w-full text-start">
            <slot />
        </button>

        <inertia-link v-else :href="href" :class="classes">
            <slot />
        </inertia-link>
    </div>
</template>
