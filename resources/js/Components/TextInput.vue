<template>
    <input
        class="input-text"
        ref="input"
        :value="modelValue"
        :disabled="disabled"
        :placeholder="placeholder"
        @input="$emit('update:modelValue', $event.target.value)"
        @keyup.enter="$emit('enter')"
        @keydown="onKeyDown"
        @blur="$emit('blur', $event)"
        @focus="$emit('focus', $event)"
        autocomplete="off"
    />
</template>

<script setup>
import { onMounted, ref } from 'vue';

defineProps({
    modelValue: {
        default: '',
        validator(value) {
            return value === null || typeof value === 'string' || typeof value === 'number' || typeof value === 'object';
        },
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: '',
    },
});

defineEmits(['update:modelValue', 'enter', 'blur', 'focus']);

const input = ref(null);

onMounted(() => {
    if (input.value.hasAttribute('autofocus')) {
        setTimeout(() => input.value.focus(), 50);
    }
});

defineExpose({focus: () => input.value.focus()});

const onKeyDown = (e) => {
    const allowedHeadlessUIKeys = [
        "ArrowUp",
        "ArrowDown",
        "Enter",
        " ", // Space Key
        "Home",
        "End",
        "Escape",
    ];

    if (allowedHeadlessUIKeys.includes(e.key)) {
        e.stopPropagation();
    }
}
</script>
