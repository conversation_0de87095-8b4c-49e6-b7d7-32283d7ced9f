import moment from 'moment';

export const nl2br = (str, is_xhtml = true) => {
    const breakTag = (is_xhtml || typeof is_xhtml === 'undefined') ? '<br />' : '<br>';
    return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1'+ breakTag +'$2');
}

export const getId = (id, type = '') => {
    return type + id.toString().padStart(6, '0');
}

const convertDate = (value, format) => {
    if (! value) {
        return '';
    }

    return moment.utc(value).local().format(format);
}

export const formatDate = (value) => {
    return convertDate(value, 'YYYY/MM/DD HH:mm:ss');
}

export const toDateString = (value) => {
    return moment.utc(value).local().format('YYYY-MM-DD');
}

export const clearEmptyData = (data) => {
    return Object.fromEntries(
        Object.entries(data).filter(([_, v]) => v !== null && v !== '' && v !== undefined)
    );
}

