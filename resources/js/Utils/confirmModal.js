import { createApp, h } from 'vue';
import ConfirmModal from '@/Components/ConfirmModal.vue';
import i18n from '@/Plugins/i18n';

// Get global translation function
const getTranslation = (key, fallback = key) => {
    try {
        return i18n.global.t(key);
    } catch (error) {
        return fallback;
    }
};

/**
 * Show confirmation modal
 * @param {Object} options - Configuration options
 * @param {string} options.title - Modal title
 * @param {string} options.message - Modal message
 * @param {string} options.confirmText - Confirm button text
 * @param {string} options.cancelText - Cancel button text
 * @param {string} options.type - Modal type: 'danger', 'warning', 'info', 'success'
 * @param {boolean} options.showIcon - Show icon or not
 * @param {Function} options.onConfirm - Callback when confirmed
 * @param {Function} options.onCancel - Callback when cancelled
 * @returns {Promise} Promise that resolves with true/false
 */
export const showConfirmModal = (options = {}) => {
    return new Promise((resolve) => {
        const {
            title,
            message,
            confirmText,
            cancelText,
            type = 'warning',
            showIcon = true,
            onConfirm = null,
            onCancel = null,
        } = options;

        // Create a container for the modal
        const container = document.createElement('div');
        document.body.appendChild(container);

        // Create Vue app instance
        const app = createApp({
            render() {
                return h(ConfirmModal, {
                    show: true,
                    title,
                    message,
                    confirmText,
                    cancelText,
                    type,
                    showIcon,
                    onConfirm: async () => {
                        if (onConfirm) await new Promise(async (onConfirmResolve) => {
                            await onConfirm(onConfirmResolve);
                        })

                        cleanup();
                        resolve(true);
                    },
                    onCancel: () => {
                        cleanup();
                        if (onCancel) onCancel();
                        resolve(false);
                    },
                    onClose: () => {
                        cleanup();
                        if (onCancel) onCancel();
                        resolve(false);
                    }
                });
            }
        });

        // Cleanup function
        const cleanup = () => {
            app.unmount();
            document.body.removeChild(container);
        };

        // Mount the app
        app.mount(container);
    });
};

/**
 * Quick confirmation for delete actions
 * @param {string} message - Name of item to delete
 * @param {Function} onConfirm - Callback when confirmed
 * @param {Function} t - Translation function (optional)
 * @returns {Promise}
 */
export const confirmDelete = (message, onConfirm = null, t = null) => {
    const translateFn = t || getTranslation;

    return showConfirmModal({
        title: translateFn('deleteConfirmation'),
        message,
        confirmText: translateFn('delete'),
        cancelText: translateFn('cancel'),
        type: 'danger',
        onConfirm,
    });
};

/**
 * Quick confirmation for save actions
 * @param {string} message - Custom message
 * @param {Function} onConfirm - Callback when confirmed
 * @param {Function} t - Translation function (optional)
 * @returns {Promise}
 */
export const confirmSave = (message, onConfirm = null, t = null) => {
    const translateFn = t || getTranslation;

    return showConfirmModal({
        title: translateFn('saveChanges'),
        message,
        confirmText: translateFn('save'),
        cancelText: translateFn('cancel'),
        type: 'info',
        onConfirm,
    });
};

/**
 * Quick confirmation for dangerous actions
 * @param {string} message - Action description
 * @param {Function} onConfirm - Callback when confirmed
 * @param {Function} t - Translation function (optional)
 * @returns {Promise}
 */
export const confirmDangerousAction = (message, onConfirm = null, t = null) => {
    const translateFn = t || getTranslation;

    return showConfirmModal({
        title: translateFn('warning'),
        message,
        confirmText: translateFn('proceed'),
        cancelText: translateFn('cancel'),
        type: 'danger',
        onConfirm,
        translate: false // We handle translation manually for the message
    });
};

/**
 * Simple yes/no confirmation
 * @param {string} message - Question to ask
 * @param {Function} onConfirm - Callback when confirmed
 * @param {Function} t - Translation function (optional)
 * @returns {Promise}
 */
export const confirmYesNo = (message, onConfirm = null, t = null) => {
    const translateFn = t || getTranslation;

    return showConfirmModal({
        title: translateFn('confirmation'),
        message,
        confirmText: translateFn('yes'),
        cancelText: translateFn('no'),
        type: 'info',
        onConfirm,
    });
};
