import { createApp, h } from 'vue';
import { useI18n } from 'vue-i18n';
import ConfirmModal from '@/Components/ConfirmModal.vue';

const i18n = useI18n();

/**
 * Show confirmation modal
 * @param {Object} options - Configuration options
 * @param {string} options.title - Modal title
 * @param {string} options.message - Modal message
 * @param {string} options.confirmText - Confirm button text
 * @param {string} options.cancelText - Cancel button text
 * @param {string} options.type - Modal type: 'danger', 'warning', 'info', 'success'
 * @param {boolean} options.showIcon - Show icon or not
 * @param {Function} options.onConfirm - Callback when confirmed
 * @param {Function} options.onCancel - Callback when cancelled
 * @returns {Promise} Promise that resolves with true/false
 */
export const showConfirmModal = (options = {}) => {
    return new Promise((resolve) => {
        const {
            title = 'Confirm Action',
            message = 'Are you sure you want to proceed?',
            confirmText = 'Confirm',
            cancelText = 'Cancel',
            type = 'warning',
            showIcon = true,
            onConfirm = null,
            onCancel = null
        } = options;

        // Create a container for the modal
        const container = document.createElemeni18n.t('div');
        document.body.appendChild(container);

        // Create Vue app instance
        const app = createApp({
            render() {
                return h(ConfirmModal, {
                    show: true,
                    title,
                    message,
                    confirmText,
                    cancelText,
                    type,
                    showIcon,
                    onConfirm: () => {
                        cleanup();
                        if (onConfirm) onConfirm();
                        resolve(true);
                    },
                    onCancel: () => {
                        cleanup();
                        if (onCancel) onCancel();
                        resolve(false);
                    },
                    onClose: () => {
                        cleanup();
                        if (onCancel) onCancel();
                        resolve(false);
                    }
                });
            }
        });

        // Cleanup function
        const cleanup = () => {
            app.unmouni18n.t();
            document.body.removeChild(container);
        };

        // Mount the app
        app.mouni18n.t(container);
    });
};

/**
 * Quick confirmation for delete actions
 * @param {string} message
 * @param {Function} onConfirm - Callback when confirmed
 * @returns {Promise}
 */
export const confirmDelete = (message, onConfirm = null) => {
    return showConfirmModal({
        title: i18n.t('Delete Confirmation'),
        message: message,
        confirmText: i18n.t('Delete'),
        cancelText: i18n.t('Cancel'),
        type: 'danger',
        onConfirm
    });
};

/**
 * Quick confirmation for save actions
 * @param {string} message
 * @param {Function} onConfirm - Callback when confirmed
 * @returns {Promise}
 */
export const confirmSave = (message, onConfirm = null) => {
    return showConfirmModal({
        title: i18n.t('Save Changes'),
        message,
        confirmText: i18n.t('Save'),
        cancelText: i18n.t('Cancel'),
        type: 'info',
        onConfirm
    });
};

/**
 * Quick confirmation for dangerous actions
 * @param {string} message
 * @param {Function} onConfirm - Callback when confirmed
 * @returns {Promise}
 */
export const confirmDangerousAction = (message, onConfirm = null) => {
    return showConfirmModal({
        title: i18n.t('Warning'),
        message,
        confirmText: i18n.t('Proceed'),
        cancelText: i18n.t('Cancel'),
        type: 'danger',
        onConfirm,
    });
};

/**
 * Simple yes/no confirmation
 * @param {string} message
 * @param {Function} onConfirm - Callback when confirmed
 * @returns {Promise}
 */
export const confirmYesNo = (message, onConfirm = null) => {
    return showConfirmModal({
        title: i18n.t('Confirmation'),
        message,
        confirmText: i18n.t('Yes'),
        cancelText: i18n.t('No'),
        type: 'info',
        onConfirm
    });
};
