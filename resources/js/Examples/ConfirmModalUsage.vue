<template>
    <div class="p-6 space-y-4">
        <h2 class="text-2xl font-bold mb-6">Confirm Modal Examples</h2>
        
        <!-- Basic Usage -->
        <div class="space-y-2">
            <h3 class="text-lg font-semibold">Basic Usage</h3>
            <div class="flex space-x-3">
                <button @click="showBasicConfirm" class="btn-primary">
                    Basic Confirm
                </button>
                
                <button @click="showDeleteConfirm" class="btn-danger">
                    Delete Item
                </button>
                
                <button @click="showSaveConfirm" class="btn-success">
                    Save Changes
                </button>
                
                <button @click="showDangerousAction" class="btn-warning">
                    Dangerous Action
                </button>
            </div>
        </div>

        <!-- Advanced Usage -->
        <div class="space-y-2">
            <h3 class="text-lg font-semibold">Advanced Usage</h3>
            <div class="flex space-x-3">
                <button @click="showCustomModal" class="btn-info">
                    Custom Modal
                </button>
                
                <button @click="showAsyncAction" class="btn-secondary">
                    Async Action
                </button>
            </div>
        </div>

        <!-- Results -->
        <div v-if="lastResult" class="mt-6 p-4 bg-gray-100 rounded-lg">
            <h4 class="font-semibold">Last Result:</h4>
            <p>{{ lastResult }}</p>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import { 
    showConfirmModal, 
    confirmDelete, 
    confirmSave, 
    confirmDangerousAction,
    confirmYesNo 
} from '@/Utils';

const lastResult = ref('');

// Basic confirm
const showBasicConfirm = async () => {
    const result = await showConfirmModal({
        title: 'Basic Confirmation',
        message: 'Do you want to proceed with this action?',
        type: 'info'
    });
    
    lastResult.value = `Basic confirm: ${result ? 'Confirmed' : 'Cancelled'}`;
};

// Delete confirmation
const showDeleteConfirm = async () => {
    const result = await confirmDelete('this post');
    
    if (result) {
        lastResult.value = 'Delete confirmed - Item would be deleted';
        // Perform delete action here
        // await deletePost(postId);
    } else {
        lastResult.value = 'Delete cancelled';
    }
};

// Save confirmation
const showSaveConfirm = async () => {
    const result = await confirmSave('Do you want to save your changes? Unsaved changes will be lost.');
    
    if (result) {
        lastResult.value = 'Save confirmed - Changes would be saved';
        // Perform save action here
        // await saveChanges();
    } else {
        lastResult.value = 'Save cancelled';
    }
};

// Dangerous action
const showDangerousAction = async () => {
    const result = await confirmDangerousAction('reset all user data');
    
    if (result) {
        lastResult.value = 'Dangerous action confirmed - Action would be performed';
        // Perform dangerous action here
    } else {
        lastResult.value = 'Dangerous action cancelled';
    }
};

// Custom modal
const showCustomModal = async () => {
    const result = await showConfirmModal({
        title: 'Custom Modal',
        message: 'This is a custom modal with specific styling and callbacks.',
        confirmText: 'Yes, Do It!',
        cancelText: 'No, Cancel',
        type: 'success',
        showIcon: true,
        onConfirm: () => {
            console.log('Custom confirm callback executed');
        },
        onCancel: () => {
            console.log('Custom cancel callback executed');
        }
    });
    
    lastResult.value = `Custom modal: ${result ? 'Confirmed' : 'Cancelled'}`;
};

// Async action with loading
const showAsyncAction = async () => {
    const result = await confirmYesNo('Do you want to start the async process?');
    
    if (result) {
        lastResult.value = 'Starting async process...';
        
        // Simulate async operation
        setTimeout(async () => {
            const continueResult = await showConfirmModal({
                title: 'Process Complete',
                message: 'The async process has completed successfully. Do you want to view the results?',
                confirmText: 'View Results',
                cancelText: 'Close',
                type: 'success'
            });
            
            lastResult.value = continueResult 
                ? 'Async process completed - Results would be shown'
                : 'Async process completed - Results not viewed';
        }, 2000);
    } else {
        lastResult.value = 'Async process cancelled';
    }
};
</script>

<style scoped>
.btn-primary {
    @apply bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors;
}

.btn-danger {
    @apply bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded transition-colors;
}

.btn-success {
    @apply bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded transition-colors;
}

.btn-warning {
    @apply bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded transition-colors;
}

.btn-info {
    @apply bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded transition-colors;
}

.btn-secondary {
    @apply bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors;
}
</style>
