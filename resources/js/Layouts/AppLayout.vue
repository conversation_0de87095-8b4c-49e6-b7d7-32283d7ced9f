<template>
    <inertia-head :title="title" />

    <div
        class="min-h-screen bg-gray-100 font-light text-gray-700"
        :class="'locale-' + i18n.locale.value"
    >
        <nav class="bg-white" :class="[$slots.header ? 'border-b border-gray-100' : 'shadow']">
            <div class="mx-auto px-4">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <div class="shrink-0 flex items-center">
                            <application-mark />
                        </div>

                        <div class="flex space-x-4 -my-px ms-10">
                            <nav-link
                                :href="route('user.list')"
                                :active="route().current('user.*')"
                                v-text="$t('nav.user')"
                            />

                            <nav-link
                                :href="route('post.list')"
                                :active="route().current('post.*')"
                                v-text="$t('nav.post')"
                            />

                            <nav-link
                                :href="route('postAnswer.list')"
                                :active="route().current('postAnswer.*')"
                                v-text="$t('nav.answer')"
                            />

                            <nav-link
                                :href="route('postViewHistory')"
                                :active="route().current('postViewHistory')"
                                v-text="$t('nav.history')"
                            />

                            <nav-link
                                :href="route('news.list')"
                                :active="route().current('news.*')"
                                v-text="$t('nav.news')"
                            />

                            <nav-link
                                :href="route('quest.list')"
                                :active="route().current('quest.*')"
                                v-text="$t('nav.quest')"
                            />

                            <nav-link
                                :href="route('premiumFeature.list')"
                                :active="route().current('premiumFeature.*')"
                                v-text="$t('premiumFeature.label')"
                            />

                            <nav-link
                                :href="route('system.settings')"
                                :active="route().current('system.settings')"
                                v-text="$t('nav.setting')"
                            />

                            <nav-link
                                :href="route('setting.unlockPost')"
                                :active="route().current('setting.unlockPost')"
                                v-text="$t('unlockPosts')"
                            />

                            <nav-link
                                :href="route('community.list')"
                                :active="route().current('community.*')"
                                v-text="$t('nav.adminCommunity')"
                            />
                        </div>
                    </div>

                    <div class="flex items-center ms-6">
                        <div class="ms-3 relative">
                            <dropdown align="right" width="48">
                                <template v-slot:trigger>
                                    <span class="inline-flex.rounded-md">
                                        <button
                                            type="button"
                                            class="inline-flex items-center px-3 py-2 border border-transparent leading-4 rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none focus:bg-gray-50 active:bg-gray-50 transition ease-in-out duration-150"
                                        >
                                            {{ $page.props.auth.user.name }}

                                            <svg
                                                class="ms-2 -me-0.5 h-4 w-4"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M19.5 8.25l-7.5 7.5-7.5-7.5"
                                                />
                                            </svg>
                                        </button>
                                    </span>
                                </template>

                                <template v-slot:content>
                                    <dropdown-link
                                        v-if="$page.props.jetstream.canUpdateProfileInformation"
                                        :href="route('profile.detail')"
                                        v-text="$t('profile')"
                                    />

                                    <dropdown-link
                                        v-if="$page.props.jetstream.canUpdatePassword"
                                        :href="route('profile.changePassword')"
                                        v-text="$t('changePassword')"
                                    />

                                    <div class="border-t border-gray-200" />

                                    <dropdown-link
                                        type="button"
                                        v-text="$t('logout')"
                                        @click="logoutConfirmation"
                                    />
                                </template>
                            </dropdown>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <header v-if="$slots.header" class="bg-white shadow">
            <div class="mx-auto px-6 flex items-center font-semibold h-[70px] sm:mx-2">
                <slot name="header" />
            </div>
        </header>

        <main class="mt-[2px]" :class="[!$slots.header ? 'without-header' : '']">
            <slot />
        </main>
    </div>

    <modal
        v-if="config.confirmLogout"
        :show="config.open"
        :closeable="true"
        size="lg"
        padding-vertical="py-20"
        @close="closeModal"
    >
        <div class="pt-4 pb-3 px-3 font-semibold" v-text="$t('confirm')" />
        <div class="border-t px-3 py-4 font-light" v-text="$t('logoutConfirmationText')" />
        <div class="border-t" />

        <div class="flex items-center justify-end p-3">
            <secondary-button class="mr-3 text-sm h-[38px]" v-text="$t('cancel')" @click="closeModal" />

            <primary-button class="text-sm" @click="logout">
                <span class="text-sm" v-text="$t('logout')" />
            </primary-button>
        </div>
    </modal>
</template>

<script setup>
import { Head as InertiaHead, router } from '@inertiajs/vue3';
import { reactive } from "vue";
import { useI18n } from "vue-i18n";

import ApplicationMark from "@/Components/ApplicationMark.vue";
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';
import Modal from "@/Components/Modal.vue";
import NavLink from '@/Components/NavLink.vue';
import PrimaryButton from "@/Components/PrimaryButton.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";

const i18n = useI18n();

defineProps({
    title: String,
});

const config = reactive({
    confirmLogout: false,
    open: false,
});

const logoutConfirmation = () => {
    config.confirmLogout = true;
    setTimeout(() => config.open = true, 150);
}

const closeModal = () => {
    config.open = false;
    setTimeout(() => config.confirmLogout = false, 150);
}

const logout = () => {
    router.post(route('logout'));
};
</script>

<style lang="scss">
@use "../../css/style";
</style>
