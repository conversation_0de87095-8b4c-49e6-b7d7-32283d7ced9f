import iziToast from 'izitoast';

class VueToast {
    constructor (options) {
        const defaults = {
            zindex: 99999,
            rtl: false,
            transitionIn: 'fadeInUp',
            transitionOut: 'fadeOut',
            transitionInMobile: 'fadeInUp',
            transitionOutMobile: 'fadeOutDown',
            buttons: {},
            inputs: {},
            balloon: false,
            close: false,
            closeOnEscape: false,
            position: 'topRight',
            timeout: 3000,
            animateInside: true,
            drag: true,
            pauseOnHover: true,
            resetOnHover: false,
            progressBar: false,
            layout: 2,
            displayMode: 2,
        };

        this.options = { ...defaults, ...options };

        this.izi = iziToast;
        this.izi.settings(this.options);
    }

    getPayload (message, title = '', options = {}) {
        return {
            ...options,
            ...{
                message,
                title,
            },
        };
    }

    success (message, title = '', options = {}) {
        this.izi.success(this.getPayload(message, title, options));
    }

    warning (message, title = '', options = {}) {
        this.izi.warning(this.getPayload(message, title, options));
    }

    error (message, title = '', options = {}) {
        this.izi.error(this.getPayload(message, title, options));
    }

    question (message, options = {}) {
        this.izi.question(this.getPayload(message, options['title'] || '', options));
    }
}

export default {
    install: (app) => {
        const toast = new VueToast();
        app.config.globalProperties.$toast = toast;

        const toastMessage = window['toastMessage'] || null;
        if (toastMessage) {
            toast.success(toastMessage);
        }

        app.provide('$toast', app.config.globalProperties.$toast);
    },
}
