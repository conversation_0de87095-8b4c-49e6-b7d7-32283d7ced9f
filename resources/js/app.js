import './bootstrap';
import '../css/app.css';

import { createApp, h } from 'vue';
import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { ZiggyVue } from 'ziggy-js';
import { createPinia } from 'pinia';

import PrimeVue from 'primevue/config';
import Tooltip from 'primevue/tooltip';
import Material from '@primevue/themes/material';

//import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css';

import i18n from './Plugins/i18n';
import toast from './Plugins/toast';

const appName = import.meta.env.VITE_APP_NAME || 'Dictionary';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./Pages/${name}.vue`, import.meta.glob('./Pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        return createApp({ render: () => h(App, props) })
            //.component('QuillEditor', QuillEditor)
            .use(createPinia())
            .use(plugin)
            .use(PrimeVue, {
                theme: {
                    preset: Material,
                    options: {
                        darkModeSelector: 'none',
                        // cssLayer: {
                        //     name: 'primevue',
                        //     order: 'primevue, tailwind-base, tailwind-utilities'
                        // }
                    },
                },
            })
            .use(ZiggyVue)
            .use(i18n)
            .use(toast)
            .directive('tooltip', Tooltip)
            .mount(el);
    },
    progress: {
        color: 'rgb(20 184 166)',
    },
}).then(() => console.log('App Initialized'));
