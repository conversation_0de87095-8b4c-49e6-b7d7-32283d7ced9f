import { defineStore, acceptHMRUpdate } from 'pinia';

export const useCommunityStore = defineStore('community-stores', {
    state: () => ({
        communities: [],
    }),

    getters: {
        hasData: state => state.communities.length > 0,
        data: state => state.communities,
    },

    actions: {
        setData (communities) {
            this.communities = communities;
        },

        clear() {
            this.communities = [];
        },

        async loadData() {
            await window.axios.post('/community/list')
                .then(response => {
                    this.setData(response.data);
                })
                .catch((e) => {
                    console.log(e);
                });
        },
    },
});

if (import.meta.hot) {
    import.meta.hot.accept(acceptHMRUpdate(useCommunityStore, import.meta.hot));
}
