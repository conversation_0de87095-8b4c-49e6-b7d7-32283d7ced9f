APP_NAME=Honne
#APP_ENV=staging
APP_KEY=base64:bwIxTITEiMiy2WDzpHH2dQrBzQlhC/RVujw1EprAGeg=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_SCHEDULE_TIMEZONE=Asia/Tokyo
APP_URL=https://staging.answerr.app
APP_ENCRYPT_PASSWD=exidea@passwd
HORIZON_DOMAIN=console-staging.answerr.app
IOS_APP_STORE_LINK=https://apps.apple.com/app/id6747106346

APP_LOCALE=ja
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=cache

BCRYPT_ROUNDS=12

LOG_CHANNEL=stderr
LOG_STACK=stderr
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_LOG_QUERY=false
#DB_HOST=127.0.0.1
DB_PORT=3306
#DB_DATABASE=honne
#DB_USERNAME=root
#DB_PASSWORD=TALanDuc

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=s3
QUEUE_CONNECTION=redis

CACHE_STORE=redis
CACHE_PREFIX=honne

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
#REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_PREFIX=honne_staging

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

#AWS_ACCESS_KEY_ID=
#AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=ap-northeast-1
AWS_BUCKET=cdn-staging.answerr.app
AWS_USE_PATH_STYLE_ENDPOINT=false
AWS_URL=https://cdn-staging.answerr.app

VITE_APP_NAME="${APP_NAME}"

OCTANE_SERVER=swoole
OCTANE_HTTPS=true

CHATWORK_ROOM_ID=369606297
CHATWORK_API_TOKEN=72f9f44731d86764ab3b5cbfcca49602

FIREBASE_CREDENTIALS=storage/keys/firebase-adminsdk-staging.json

SCOUT_DRIVER=typesense
