<?php

use App\Enums\PostType;
use App\Enums\PremiumFeatureType;
use App\Enums\QuestType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('premium_features', function (Blueprint $table) {
            $table->id('premium_id');
            $table->string('name');
            $table->string('description');
            $table->enum('type', PremiumFeatureType::toArray());
            $table->unsignedTinyInteger('price')->default(1);
            $table->string('image')->nullable();
            $table->unsignedTinyInteger('is_dark')->default(0);
            $table->timestamps();
        });

        Schema::create('user_premium_features', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('premium_id');

            $table->unique(['user_id', 'premium_id']);
        });

        Schema::table('users', function (Blueprint $table) {
            $table->unsignedTinyInteger('gender')
                ->after('birthday')
                ->default(0)
                ->comment('1 - male, 2 - female, 3 - other');
        });

        Schema::table('quests', function (Blueprint $table) {
            $table->enum('type', QuestType::toArray())->change();
        });

        Schema::table('posts', function (Blueprint $table) {
            $table->enum('type', PostType::toArray())->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
