<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('profile_type', 20)->nullable()->after('last_logged_in_at');
            $table->dropIndex('u_s_idx');
            $table->index(['status', 'profile_type'], 'u_s_pt_idx');
        });

        Schema::create('profiles', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->primary();
            $table->unsignedBigInteger('school_id')->nullable();
            $table->string('work')->nullable()->comment('field of work');
            $table->string('expert', 500)->nullable()->comment('field of expertise');
            $table->string('service_in_charge', 1000)->nullable();
            $table->text('brief')->nullable();

            $table->index(['user_id', 'school_id'], 'u_school_idx');
        });

        Schema::create('schools', function (Blueprint $table) {
            $table->id('school_id');
            $table->string('name');
            $table->unsignedTinyInteger('status')->default(1)->index('school_status_idx');
        });

        Schema::create('media', function (Blueprint $table) {
            $table->id('media_id');
            $table->unsignedBigInteger('post_id')->index();
            $table->unsignedBigInteger('user_id')->index();
            $table->string('image');
            $table->string('content', 1000)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
