<?php

use App\Enums\QuestionPublicTarget;
use App\Enums\QuestionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('surveys', function (Blueprint $table) {
            $table->id('survey_id');
            $table->string('title');
            $table->text('description')->nullable();
            $table->unsignedTinyInteger('status')
                ->default(1)
                ->index()
                ->comment('0 - disabled, 1 - enabled');
            $table->timestamps();
        });

        Schema::create('questions', function (Blueprint $table) {
            $table->id('question_id');
            $table->unsignedBigInteger('survey_id');
            $table->string('content');
            $table->decimal('point', 10, 0)->unsigned()->default(0);
            $table->enum('type', QuestionType::toArray());
            $table->enum('public', QuestionPublicTarget::toArray());
            $table->timestamps();

            $table->index(['survey_id', 'public']);
        });

        Schema::create('question_choices', function (Blueprint $table) {
            $table->id('choice_id');
            $table->unsignedBigInteger('question_id')->index();
            $table->string('content');
            $table->timestamps();
        });

        Schema::create('answers', function (Blueprint $table) {
            $table->id('answer_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('survey_id');
            $table->unsignedBigInteger('question_id');
            $table->text('content')->nullable();
            $table->decimal('star', 1, 0)->unsigned()->default(0);
            $table->decimal('point', 10, 0)->unsigned()->default(0);
            $table->unsignedTinyInteger('public')->default(0)->comment('1 - public');
            $table->timestamps();

            $table->index(['user_id', 'survey_id', 'public']);
            $table->index(['survey_id', 'user_id', 'public']);
        });

        Schema::create('answer_choices', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('answer_id');
            $table->unsignedBigInteger('choice_id')->comment('question choice_id');
        });

        Schema::create('published_surveys',function (Blueprint $table) {
            $table->unsignedBigInteger('survey_id')->primary();
            $table->unsignedBigInteger('sort');
            $table->timestamps();

            $table->index(['survey_id', 'sort']);
        });

        Schema::create('attached_surveys',function (Blueprint $table) {
            $table->id('attached_id');
            $table->unsignedBigInteger('survey_id');
            $table->unsignedBigInteger('to_survey_id');
            $table->string('title');
            $table->timestamps();

            $table->unique(['to_survey_id', 'survey_id']);
        });

        Schema::create('attached_choices', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('attached_id');
            $table->unsignedBigInteger('choice_id')->comment('question choice_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
