<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reactions', function (Blueprint $table) {
            $table->id();
            $table->string('object_type', 20);
            $table->unsignedBigInteger('object_id');
            $table->unsignedBigInteger('object_user_id');
            $table->unsignedBigInteger('user_id');
            $table->string('action', 10);
            $table->decimal('count', 5, 0)->unsigned()->default(0);
            $table->timestamps();

            $table->index(['object_id', 'object_type',  'user_id'], 'oid_type_uid_idx');
            $table->index(['user_id', 'object_type'], 'reaction_uid_type_idx');
        });

        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('reaction_user_id')->nullable();
            $table->unsignedBigInteger('reaction_id')->nullable();

            $table->foreign('reaction_id', 'reaction_foreign_key')
                ->references('id')
                ->on('reactions')
                ->onDelete('CASCADE');

            $table->string('object_type', 20)->nullable();
            $table->unsignedBigInteger('object_id')->nullable();
            $table->unsignedTinyInteger('type');
            $table->text('data')->nullable();
            $table->unsignedTinyInteger('status')->default(0);
            $table->timestamps();

            $table->index(['user_id', 'type', DB::raw('`id` DESC')], 'uid_id_idx');
        });

        Schema::create('devices', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('name')->nullable();
            $table->string('token')->index();
            $table->unsignedTinyInteger('status')->default(1);
            $table->timestamps();

            $table->index(['user_id', 'status']);
        });

        Schema::table('users', function (Blueprint $table) {
            $table->decimal('best_answer_count', 8, 0)->default(0)->after('answer_count');
            $table->dateTime('last_answered_post_at')->after('status')->nullable();
        });

        Schema::table('post_answers', function (Blueprint $table) {
            $table->decimal('coin_count', 10, 0)->default(0)->after('comment_count');
            $table->unsignedTinyInteger('voted')->default(0)->after('coin_count');
            $table->index(['user_id', 'voted'], 'u_voted_idx');
        });

        Schema::table('posts', function (Blueprint $table) {
            $table->unsignedTinyInteger('voted')->default(0)->after('latest_answered_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reactions');
        Schema::dropIfExists('notifications');
        Schema::dropIfExists('devices');

        Schema::table('users', function(Blueprint $table) {
            $table->dropColumn('best_answer_count');
            $table->dropColumn('last_answered_post_at');
        });

        Schema::table('post_answers', function(Blueprint $table) {
            $table->dropColumn('coin_count');
            $table->dropColumn('voted');
        });

        Schema::table('posts', function(Blueprint $table) {
            $table->dropColumn('voted');
        });
    }
};
