<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('qa_viewed_posts', function (Blueprint $table) {
            $table->string('device_token', 100);
            $table->unsignedBigInteger('post_id');
            $table->timestamps();

            $table->primary(['device_token', 'post_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
