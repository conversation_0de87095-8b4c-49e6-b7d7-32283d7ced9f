<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->uuid('uuid')->nullable()->after('user_id')->unique();
            $table->unsignedBigInteger('badge_count')->default(0)->after('profile_type');
            $table->unsignedTinyInteger('invited_user_count')->default(0)->after('badge_count');
        });

        Schema::table('notifications', function (Blueprint $table) {
            $table->unsignedBigInteger('badge_count')->default(1)->after('type');
            $table->index(['object_id', 'object_type', 'type'], 'object_and_type_idx');
        });

        Schema::table('posts', function (Blueprint $table) {
            $table->unsignedTinyInteger('featured')->default(0)->after('voted');
            $table->index(['status', 'featured'], 'status_and_featured_idx');
            $table->index(['status', 'created_at', 'type'], 'status_type_time_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
