<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('post_answers', function(Blueprint $table) {
            $table->unsignedBigInteger('parent_id')->default(0)->after('answer_id');
            $table->unsignedTinyInteger('level')->default(1)->after('tab_index');
            $table->unsignedBigInteger('children_count')->default(0)->after('level');
            $table->index(['parent_id', 'status', DB::raw('`answer_id` DESC')], 'pr_s_ad');
        });

        Schema::create('notification_users', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('notification_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('reaction_id')->nullable();

            $table->foreign('notification_id', 'notification_foreign_key')
                ->references('id')
                ->on('notifications')
                ->onDelete('CASCADE');

            $table->foreign('reaction_id', 'n_reaction_foreign_key')
                ->references('id')
                ->on('reactions')
                ->onDelete('CASCADE');

            $table->timestamps();

            $table->index(['notification_id', 'user_id'], 'notification_user_idx');
        });

        Schema::table('notifications', function (Blueprint $table) {
            $table->index(['user_id', 'object_type', 'object_id'], 'u_ot_id_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
