<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('communities', function (Blueprint $table) {
            $table->id('community_id');
            $table->unsignedBigInteger('user_id')->default(0)->comment('created by');
            $table->string('name');
            $table->string('description');
            $table->string('avatar')->nullable();
            $table->unsignedBigInteger('school_id')->nullable()->index();
            $table->unsignedTinyInteger('status')->default(1)->comment('0 - disabled, 1 - public, 2 - closed');
            $table->timestamps();

            $table->index(['status', 'name'], 'community_search_idx');
        });

        Schema::create('community_users', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('community_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedTinyInteger('status')->default(1)->comment('0 - left, 1 - joined, 2 - removed by admin (blocked)');
            $table->timestamps();

            $table->index(['community_id', 'status'], 'community_user_status_idx');
            $table->index(['user_id', 'status'], 'user_community_status_idx');
        });

        Schema::create('community_posts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('community_id');
            $table->unsignedBigInteger('post_id');

            $table->index(['post_id', 'community_id'], 'post_community_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
