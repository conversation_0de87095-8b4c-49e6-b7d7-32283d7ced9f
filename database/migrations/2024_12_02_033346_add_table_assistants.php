<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assistants', function (Blueprint $table) {
            $table->id('assistant_id');
            $table->string('name');
            $table->string('work');
            $table->string('expertise');
            $table->string('description');
            $table->enum('language', ['vi', 'ja']);
            $table->unsignedTinyInteger('searchable')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
