<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ogp_metadata', function (Blueprint $table) {
            $table->increments('id');
            $table->morphs('metadata');
            $table->string('url', 300);
            $table->string('title', 300);
            $table->string('description', 500)->nullable();
            $table->string('image')->nullable();
            $table->unsignedInteger('status')->default(1);
            $table->timestamps();

            $table->index(['metadata_id', 'metadata_type'], 'ogp_metadata_idx');
        });

        Schema::table('post_answers', function (Blueprint $table) {
            $table->string('image')->after('content')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
