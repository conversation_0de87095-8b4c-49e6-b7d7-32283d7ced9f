<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reactions', function (Blueprint $table) {
            $table->date('react_date')->default('2024-10-01')->after('count');
            $table->index(['object_user_id'], 'object_user_id_idx');
        });

        Schema::create('post_view_histories', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->date('view_date');
            $table->unsignedBigInteger('post_id');
            $table->unsignedBigInteger('post_user_id');
            $table->timestamps();

            $table->unique(['user_id', 'view_date', 'post_id']);
            $table->index(['post_user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
