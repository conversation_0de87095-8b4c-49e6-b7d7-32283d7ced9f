<?php

use App\Enums\PostType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function(Blueprint $table) {
            $table->string('position')->nullable()->after('role');
            $table->decimal('total_coin', 15, 0)->unsigned()->default(0)->after('used_point');
            $table->decimal('used_coin', 15, 0)->unsigned()->default(0)->after('total_coin');
        });

        Schema::create('user_followers', function(Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('user who follows others');
            $table->unsignedBigInteger('follow_id')->comment('people who followed by others');
            $table->datetime('followed_at');
            $table->unsignedTinyInteger('status')
                ->default(1)
                ->comment('0 - disabled, 1 - enabled');

            $table->index(['user_id', 'status', DB::raw('`followed_at` DESC')], 'u_s_fd');
            $table->index(['follow_id', 'status', DB::raw('`followed_at` DESC')], 'f_s_fd');
        });

        Schema::create('posts', function(Blueprint $table) {
            $table->id('post_id');
            $table->unsignedBigInteger('user_id');
            $table->text('content');
            $table->string('image');
            $table->enum('type', PostType::toArray());
            $table->decimal('like_count', 10, 0)->unsigned()->default(0);
            $table->decimal('answer_count', 10, 0)->unsigned()->default(0);
            $table->timestamp('latest_answered_at')->nullable();
            $table->unsignedTinyInteger('status')
                ->default(1)
                ->comment('0 - disabled, 1 - enabled');
            $table->timestamps();

            $table->index(['status', DB::raw('`post_id` DESC')], 's_pd');
            $table->index(['user_id', 'status', DB::raw('`post_id` DESC')], 'u_s_pd');
        });

        Schema::create('post_answers', function(Blueprint $table) {
            $table->id('answer_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('post_id');
            $table->string('content', 1000);
            $table->decimal('like_count', 10, 0)->unsigned()->default(0);
            $table->decimal('comment_count', 10, 0)->unsigned()->default(0);
            $table->unsignedTinyInteger('status')
                ->default(1)
                ->comment('0 - disabled, 1 - enabled');
            $table->timestamps();

            $table->index(['post_id', 'status', DB::raw('`answer_id` DESC')], 'p_s_ad');
            $table->index(['user_id', 'status', DB::raw('`answer_id` DESC')], 'u_s_ad');
        });

        Schema::create('comments', function(Blueprint $table) {
            $table->id('comment_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('commentable_id');
            $table->string('commentable_type', 20);
            $table->string('content', 1000);
            $table->unsignedTinyInteger('status')
                ->default(1)
                ->comment('0 - disabled, 1 - enabled');
            $table->timestamps();

            $table->index(['commentable_type', 'commentable_id', 'status', DB::raw('`comment_id` DESC')], 'ct_ci_s_cd');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
