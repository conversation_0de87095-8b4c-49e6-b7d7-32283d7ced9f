<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_relations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->index();
            $table->unsignedBigInteger('target_id')->index();
            $table->unsignedTinyInteger('is_friend')
                ->default(0)
                ->comment(join(', ', [
                    '0 => Not Friend',
                    '1 => Friend',
                    '2 => Pending',
                ]));

            $table->timestamp('friend_at')->nullable();

            $table->unsignedTinyInteger('is_follow')->default(0);
            $table->timestamp('followed_at')->nullable();

            $table->unsignedTinyInteger('is_blocked')->default(0);
            $table->timestamp('blocked_at')->nullable();

            $table->timestamps();

            $table->unique(['user_id', 'target_id'], 'user_target_unique');
        });

        DB::scalar('
            INSERT INTO `user_relations` (`user_id`, `target_id`, `is_friend`, `friend_at`, `is_follow`, `followed_at`, `is_blocked`)
            SELECT `user_id`, `follow_id` as `target_id`, 1 as `is_friend`, `followed_at` as `friend_at`, `status` as `is_follow`, `followed_at`, 0 as `is_blocked` FROM `user_followers` WHERE `status`=1
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
