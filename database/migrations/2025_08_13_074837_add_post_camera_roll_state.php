<?php

use App\Enums\PostType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->unsignedTinyInteger('camera_roll')->default(0)->after('featured');
            $table->enum('type', PostType::toArray())->change();
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dateTime('unlocked_until')->nullable()->after('last_answered_post_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
