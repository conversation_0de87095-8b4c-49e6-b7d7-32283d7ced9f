<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('devices', function (Blueprint $table) {
            $table->unsignedBigInteger('token_id')->nullable()->after('user_id');
            $table->index(['token_id', 'status'], 'tk_status_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
