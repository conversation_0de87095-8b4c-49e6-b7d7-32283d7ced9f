<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function(Blueprint $table) {
            $table->decimal('post_count', 10, 0)->unsigned()->default(0)->after('used_coin');
            $table->decimal('answer_count', 10, 0)->unsigned()->default(0)->after('post_count');
            $table->decimal('comment_count', 10, 0)->unsigned()->default(0)->after('answer_count');
            $table->dateTime('last_logged_in_at')->nullable()->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
