<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('feed_viewed_stats', function (Blueprint $table) {
            $table->id();
            $table->string('feed', 20);
            $table->unsignedBigInteger('user_id');
            $table->dateTime('viewed_at');

            $table->index(['user_id', 'viewed_at', 'feed'], 'user_viewed_feed_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
