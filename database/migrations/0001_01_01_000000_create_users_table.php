<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id('user_id');
            $table->string('name');
            $table->string('phone', 25)->unique();
            $table->string('email')->nullable()->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->enum('role', \App\Enums\UserRole::toArray())->default(\App\Enums\UserRole::CUSTOMER->value);
            $table->decimal('total_point', 15, 0)->unsigned()->default(0);
            $table->decimal('used_point', 15, 0)->unsigned()->default(0);
            $table->rememberToken();
            $table->foreignId('current_team_id')->nullable();
            $table->string('profile_photo_path', 2048)->nullable();
            $table->dateTime('answer_started_at')->nullable();
            $table->dateTime('answer_ended_at')->nullable();
            $table->unsignedTinyInteger('answer_status')->default(0)->comment('0 - uncompleted, 1 - completed');
            $table->unsignedTinyInteger('status')->default(1)->comment('0 - disabled, 1 - enabled');
            $table->timestamps();

            $table->index('status', 'u_s_idx');
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
    }
};
