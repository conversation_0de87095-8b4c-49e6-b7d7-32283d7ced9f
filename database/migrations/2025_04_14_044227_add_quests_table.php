<?php

use App\Enums\QuestType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quests', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->string('image');
            $table->unsignedInteger('amount')->default(0);
            $table->enum('unit', ['coin', 'point']);
            $table->enum('type', QuestType::toArray());
            $table->decimal('sort', 10, 0)->unsigned()->default(0);
            $table->unsignedTinyInteger('is_dark')->default(0);
            $table->unsignedTinyInteger('status')->default(1);
            $table->timestamps();

            $table->index(['status', 'sort']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
