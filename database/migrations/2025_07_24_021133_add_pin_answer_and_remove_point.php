<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('post_answers', function (Blueprint $table) {
            $table->unsignedTinyInteger('pinned')->default(0)->after('voted');
            $table->unsignedInteger('like_user_count')->default(0)->after('like_count');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('total_point');
            $table->dropColumn('used_point');

            $table->unsignedTinyInteger('first_answer_rewarded')
                ->default(0)
                ->after('referral_rewarded');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
