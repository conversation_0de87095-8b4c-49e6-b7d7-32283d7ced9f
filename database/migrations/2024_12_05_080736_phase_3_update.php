<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /*$rawType = '\'' . implode('\', \'', PostType::toArray()) . '\'';
        DB::statement(sprintf('ALTER TABLE `posts` CHANGE COLUMN `type` `type` ENUM(%s) NOT NULL', $rawType));*/

        Schema::table('post_answers', function (Blueprint $table) {
            $table->unsignedTinyInteger('tab_index')->default(0)->after('voted');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
