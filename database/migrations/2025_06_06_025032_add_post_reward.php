<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->decimal('reward_amount', 10, 0)
                ->unsigned()
                ->default(0)
                ->after('voted');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->decimal('holding_coin', 10, 0)
                ->unsigned()
                ->default(0)
                ->after('used_coin');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
