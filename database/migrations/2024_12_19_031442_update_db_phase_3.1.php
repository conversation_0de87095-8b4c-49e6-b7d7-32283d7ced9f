<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->date('birthday')->after('email_verified_at')->nullable();
        });

        Schema::table('posts', function (Blueprint $table) {
            $table->decimal('report_count', 10, 0)->after('answer_count')->default(0)->unsigned();
            $table->timestamp('first_answered_at')->after('report_count')->nullable();
        });

        Schema::table('post_answers', function (Blueprint $table) {
            $table->decimal('report_count', 10, 0)->after('coin_count')->default(0)->unsigned();
        });

        Schema::table('comments', function (Blueprint $table) {
            $table->decimal('report_count', 10, 0)->after('content')->default(0)->unsigned();
        });

        Schema::create('reports', function (Blueprint $table) {
            $table->id('report_id');
            $table->unsignedBigInteger('user_id');
            $table->morphs('reportable');
            $table->unsignedTinyInteger('reason');
            $table->text('description')->nullable();
            $table->timestamps();

            $table->index(['reportable_id', 'reportable_type', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
