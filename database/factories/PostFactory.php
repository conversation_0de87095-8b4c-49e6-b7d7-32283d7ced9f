<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<\App\Models\Post>
 */
class PostFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'content' => fake()->text(rand(100, 250)),
            'image' => 'https://honne-dev.exidea.vn/storage/2024/10/03/673176e8-13a5-4bba-b40c-99e48084e58b.webp',
        ];
    }
}
