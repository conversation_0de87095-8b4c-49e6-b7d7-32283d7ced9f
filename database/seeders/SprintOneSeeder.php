<?php

namespace Database\Seeders;

use App\Enums\PremiumFeatureType;
use App\Enums\QuestType;
use App\Models\PremiumFeature;
use App\Models\Quest;
use Illuminate\Database\Seeder;

class SprintOneSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        Quest::query()->create([
            'title' => '同性の質問Check性別を答えよう',
            'description' => '同性の質問Check性別を答えよう',
            'image' => 'https://cdn.answerr.app/assets/images/gender-quest.png',
            'amount' => 1,
            'unit' => 'coin',
            'type' => QuestType::GENDER->value,
            'sort' => 7,
            'is_dark' => 0,
            'status' => 1,
        ]);

        $now = now('UTC')->toDateTimeString();
        PremiumFeature::query()->insert([
            [
                'name' => 'ハートタップ無制限',
                'description' => "性別を答えるクエストをクリアすると、コインを獲得できます。",
                'type' => PremiumFeatureType::UNLIMIT_POINT->value,
                'price' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => '男性・女性の限定投稿',
                'description' => "男性向け、女性向けへ分けた投稿をすることができます。異性への投稿はできません。",
                'type' => PremiumFeatureType::GENDER->value,
                'price' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ]);
    }
}
