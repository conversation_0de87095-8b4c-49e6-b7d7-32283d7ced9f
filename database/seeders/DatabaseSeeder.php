<?php

namespace Database\Seeders;

use App\Enums\UserRole;
use App\Models\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        User::factory()->create([
            'phone' => '01234567890',
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'email_verified_at' => now('UTC'),
            'password' => bcrypt('123@123'),
            'role' => UserRole::ADMINISTRATOR->value,
            'status' => 1,
        ]);
    }
}
