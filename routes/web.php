<?php

use App\Models\News;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/**
 * console routes
 */
require_once __DIR__ . '/console.php';

/**
 * home landing page, privacy & terms
 */
Route::get('/{slug?}', function (Request $request, ?string $slug = null) {
    $view = match ($slug) {
        'privacy-policy' => 'privacy',
        'terms' => 'terms',
        default => 'home',
    };

    $userAgent = $request->server('HTTP_USER_AGENT');

    $iPod = stripos($userAgent,"iPod");
    $iPhone = stripos($userAgent,"iPhone");
    $iPad = stripos($userAgent,"iPad");

    return view($view, [
        'isApple' => $iPod || $iPhone || $iPad,
    ]);
})->whereIn('slug', ['privacy-policy', 'terms']);

/**
 * news routes
 */
Route::get('/news/{news}', function (News $news) {
    return view('news-detail', [
        'content' => $news->content,
    ]);
})->name('news.detail')->whereNumber('news');

Route::get('/{slug}', fn () => view('404'))->where('slug', '.*');
