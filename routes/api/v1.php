<?php

use App\Http\Controllers\Api\V1\CommonController;
use App\Http\Controllers\Api\V1\CommunityController;
use App\Http\Controllers\Api\V1\NewsController;
use App\Http\Controllers\Api\V1\PostController;
use App\Http\Controllers\Api\V1\PostAnswerController;
use App\Http\Controllers\Api\V1\PremiumFeatureController;
use App\Http\Controllers\Api\V1\SurveyController;
use App\Http\Controllers\Api\V1\UserController;
use Illuminate\Support\Facades\Route;

/**
 * guest
 */
Route::middleware(['guest'])->group(function () {
    Route::post('/user/check-phone-exist', [UserController::class, 'checkPhoneExists'])
        ->middleware('throttle:authenticate');

    Route::post('/user/verify-invited-link', [UserController::class, 'verifyInvitedLink']);
    Route::post('/user/firebase-authenticate', [UserController::class, 'authenticateWithFirebase']);

    Route::get('/common/schools', [CommonController::class, 'schools']);
});

/**
 * authenticated
 */
Route::middleware(['auth:sanctum', 'prepare.data'])->group(function () {
    /**
     * user
     */
    Route::post('/user/update-information', [UserController::class, 'updateInformation'])->name('updateInformation');
    Route::post('/user/update-career', [UserController::class, 'updateCareer']);
    Route::post('/user/update-hobby', [UserController::class, 'updateHobby']);
    Route::get('/user/profile', [UserController::class, 'getProfile']);
    Route::get('/user/posts', [UserController::class, 'listPosts']);
    Route::post('/user/add-device-token', [UserController::class, 'addDeviceToken']);
    Route::get('/user/notifications', [UserController::class, 'getNotifications']);
    Route::post('/user/mark-notification-read', [UserController::class, 'markNotificationRead']);
    Route::post('/user/terminate', [UserController::class, 'terminate']);
    Route::post('/user/logout', [UserController::class, 'logout']);
    Route::post('/user/update-phone-number', [UserController::class, 'updatePhoneNumber']);
    Route::post('/user/find-friend', [UserController::class, 'findFriend']);
    Route::post('/user/find-schoolmate', [UserController::class, 'findSchoolmate']);
    Route::post('/user/recommendation', [UserController::class, 'recommendation']);
    Route::post('/user/import', [UserController::class, 'import'])->name('importUser');
    Route::post('/user/reset-badge', [UserController::class, 'resetBadge']);
    Route::post('/user/block', [UserController::class, 'block']);
    Route::post('/user/unblock', [UserController::class, 'unblock']);
    Route::get('/user/blocked-list', [UserController::class, 'blockedList']);

    Route::get('/user/friends', [UserController::class, 'getFriends']);
    Route::post('/user/invite-friend', [UserController::class, 'inviteFriend']);
    Route::post('/user/ignore-friend-invitation', [UserController::class, 'ignoreFriendInvitation']);
    Route::post('/user/add-friend', [UserController::class, 'addFriend']);
    Route::post('/user/unfriend', [UserController::class, 'unfriend']);
    Route::get('/user/pending-friends', [UserController::class, 'pendingFriends']);
    Route::post('/user/cancel-friend-invitation', [UserController::class, 'cancelFriendInvitation']);
    Route::get('/user/friend-count', [UserController::class, 'fetchFriendCount']);
    Route::post('/user/update-gender', [UserController::class, 'updateGender']);
    Route::post('/user/unlock-with-coin', [UserController::class, 'unlockWithCoin']);

    /**
     * survey
     */
    Route::get('/survey/list', [SurveyController::class, 'listSurveys']);
    Route::post('/survey/answer', [SurveyController::class, 'answer']);

    /**
     * post
     */
    Route::post('/post/store', [PostController::class, 'store']);
    Route::get('/post/detail', [PostController::class, 'detail']);
    Route::get('/post/feeds', [PostController::class, 'feeds']);
    Route::get('/post/list', [PostController::class, 'feedPosts']);
    Route::get('/post/list-qa', [PostController::class, 'qaPosts']);
    Route::post('/post/like', [PostController::class, 'like']);
    Route::post('/post/unlike', [PostController::class, 'unlike']);
    Route::post('/post/mute-notification', [PostController::class, 'muteNotification']);
    Route::post('/post/delete', [PostController::class, 'delete']);
    Route::get('/post/reward-posts', [PostController::class, 'fetchRewardPosts']);

    /**
     * post answer
     */
    Route::post('/post-answer/store', [PostAnswerController::class, 'store']);
    Route::get('/post-answer/list', [PostAnswerController::class, 'list']);
    Route::get('/post-answer/list-by-parent', [PostAnswerController::class, 'listByParent']);
    Route::post('/post-answer/like', [PostAnswerController::class, 'like']);
    Route::post('/post-answer/unlike', [PostAnswerController::class, 'unlike']);
    Route::post('/post-answer/donate', [PostAnswerController::class, 'donate']);
    Route::post('/post-answer/cancel-donate', [PostAnswerController::class, 'cancelDonate']);
    Route::post('/post-answer/vote-best', [PostAnswerController::class, 'voteBest']);
    Route::get('/post-answer/detail', [PostAnswerController::class, 'detail']);
    Route::post('/post-answer/delete', [PostAnswerController::class, 'delete']);
    Route::post('/post-answer/mute-notification', [PostAnswerController::class, 'muteNotification']);
    Route::post('/post-answer/pin', [PostAnswerController::class, 'pin']);
    Route::post('/post-answer/unpin', [PostAnswerController::class, 'unpin']);

    /**
     * common
     */
    Route::post('/common/report-content', [CommonController::class, 'report']);
    Route::get('/common/quests', [CommonController::class, 'quests']);

    /**
     * news
     */
    Route::get('/news/list', [NewsController::class, 'list']);

    /**
     * community
     */
    Route::post('/community/store', [CommunityController::class, 'store']);
    Route::get('/community/detail', [CommunityController::class, 'detail']);
    Route::post('/community/add-member', [CommunityController::class, 'addMember']);
    Route::post('/community/remove-member', [CommunityController::class, 'removeMember']);
    Route::post('/community/join', [CommunityController::class, 'joinCommunity']);
    Route::post('/community/leave', [CommunityController::class, 'leaveCommunity']);
    Route::get('/community/posts', [CommunityController::class, 'posts']);
    Route::get('/community/members', [CommunityController::class, 'members']);
    Route::get('/community/list', [CommunityController::class, 'listCommunities']);
    Route::post('/community/terminate', [CommunityController::class, 'terminate']);

    /**
     * premium features
     */
    Route::get('/premium-feature/list', [PremiumFeatureController::class, 'list']);
    Route::post('/premium-feature/activate', [PremiumFeatureController::class, 'activeFeature']);
});
