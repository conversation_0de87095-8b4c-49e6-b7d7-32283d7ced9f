2025/02/06
```
- fix notification after logged out
```

2025/01/17
```
- add docker
```

2025/01/16
```
- [API] - update user recommendation api
```

2025/01/14
```
- [API] - post store: push notification to recommend users if existed
```

2025/01/13
```
- [API] - add recommendation api
```

2025/01/06
```
- [API] - fix user age
```

2025/01/03
```
- upgrade composer packages
```

2024/12/31
```
- [API] - return user birthday
```

2024/12/27
```
- [API] - update post store api
- [API] - fix post detail api
- fix post options
```

2024/12/26
```
- [API] - allow reusing phone number after user was deleted
```

2024/12/25
```
- [API] - fix upload avatar
- update assets url
```

2024/12/24
```
- [API] - update encrypt keys
- [API] - change phone encryption algorithm
```

2024/12/23
```
- [API] - add post caption response
```

2024/12/20
```
- [API] - add birthday when update profile
- [API] - add report api
- Fix issue 615
- [API] - update notification api
- [API] - add user find-friend api structure
```

2024/12/17
```
- [API] - fix notification response
```

2024/12/13
```
- [API] - update user apis (update information, terminate, logout)
```

2024/12/12
```
- [API] - update post answer like, donate
```

2024/12/10
```
- [API] - add post answer detail api
- [API] - update post object (add liked_at response)
```

2024/12/10
```
- [API] - update
```

2024/12/09
```
- [API] - update
```

2024/12/05
```
- [API] - update like & donate
```

2024/12/03
```
- [Console] - add typesense demo
- typesense switch to model: multilingual-e5-large-instruct
```

2024/11/28
```
- [API] - fix feeds api (missing answer)
```

2024/11/27
```
- [Console] - vue: convert pug to html
- fix test Profile update: ignore email
- fix typos
```

2024/11/26
```
- Q&A post - fix latest posts
- Q&A post - optimize SQL queries
```

2024/11/25
```
- [Console] - add post view history
- [API] - fix unlike post
- [Console] - vue: convert pug to html
```

2024/11/22
```
- [API, Console] - update Q&A list
- [Console] - user attribute (similar)
- [API] - fix bug
```

2024/11/20
```
- [API, Console] - update Q&A list
- [Console] - update css
- update JA language
```

2024/11/19
```
- [Console] - update JP language
- [API] - fix follow api
```

2024/11/18
```
- [Console] - update Q&A settings
- [Console] - fix dark mode
```

2024/11/15
```
- [Console] - update user feed & attributes
- [Console] - user feed remove load more
- [API] - update Q&A posts
```

2024/11/13
```
- [API] - update qa posts
- [API] - add Q&A posts api for test
```

2024/11/12
```
- [API] - update qa posts
```

2024/11/06
```
- optimize eloquent query
```

2024/11/05
```
- [API] - update post answer coin
- Remove debug bar
- fix - clear memory usage after each request
- update deployment command
- update push title
- [API] - update post answer coin count after cancel donate
```

2024/11/04
```
- [API] - fix notification api
- [API] - fix api
```

2024/11/01
```
- [API] - fix best answer state
- [API] - add best answer notification
- update task schedule
```

2024/10/31
```
- [API] - fix post like api
- update firebase push notification
```

2024/10/30
```
- [API] - update
```

2024/10/28
```
- [API] - fix donate api
- [API] - add user/add-device-token, user/notifications api
- [API] - add user/mark-notification-read api
```

2024/10/25
```
- [API] - update
- [API] - add user best_answer_count
- [API] - add default coin & point to new user
```

2024/10/24
```
- [API] - resize post image
```

2024/10/23
```
- [API] - update - fix load latest answer
- [Console] - Add laravel/pulse - monitor tool
```

2024/10/21
```
- [API] - fix user follow count
```

2024/10/17
```
- [Console] - fix bugs
- [Console] - post answer detail - remove user link
- [Console] - fix bugs JA text
- [Console] - user list add filter by ID
- [Console] - post answer list filter by user ID
- [API] - fix bug #393
- [API] - verify user status
- [API] - fix test case
```

2024/10/16
```
- [Console] - fix post & post answer search by user phone number
- [Console] - update link
```

2024/10/15
```
- Fix bugs console
```
