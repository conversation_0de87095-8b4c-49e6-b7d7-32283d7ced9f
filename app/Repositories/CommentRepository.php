<?php

namespace App\Repositories;

use App\Models\Comment;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class CommentRepository
 * @package App\Repositories
 */
class CommentRepository extends BaseRepository
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return Comment::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {

    }
}
