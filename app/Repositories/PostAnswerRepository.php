<?php

namespace App\Repositories;

use App\Models\PostAnswer;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;

/**
 * Class PostAnswerRepository
 * @package App\Repositories
 */
class PostAnswerRepository extends BaseRepository
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return PostAnswer::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {
        $table = $this->getTable();
        if (! $asTable) {
            $asTable = $table;
        }

        if ($asTable !== $table) {
            $query->getModel()->setTable($asTable);
        }

        $postId = (int) Arr::get($payload, 'postId');
        $parentId = null;
        if ($objectType = Arr::get($payload, 'object_type')) {
            $objectId = (int) Arr::get($payload, 'object_id');
            $objectType === 'post' ? $postId = $objectId : $parentId = $objectId;
        }

        $userId = null;
        if (! $postId && ! $parentId) {
            $userId = (int) Arr::get($payload, 'userId');
        }

        /**
         * filter by post_id
         */
        $query->when($postId, fn ($q) => $q->where('post_id', $postId));

        /**
         * parent_id
         */
        $query->when($parentId, fn ($q) => $q->where('parent_id', $parentId));

        /**
         * user_id
         */
        $query->when($userId, function ($q) use ($userId) {
            $q->where('user_id', $userId);

            /**
             * answer không nằm trong những bài post đã xóa
             */
            $q->whereHas('post', function ($q) {
                $q->where('status', 1);
            });
        });

        $query->when(! $userId, function ($q) use ($payload) {
            $all = Arr::get($payload, 'all');

            if (! $all) {
                /**
                 * owner wasn't blocked
                 */
                $userBlockedIds = blockHelper()->getBlockedIds(auth()->id());
                $q->when($userBlockedIds, fn (Builder $q) => $q->whereNotIn('user_id', $userBlockedIds));
            }

            /**
             * owner must be active
             */
            $q->whereHas('createdBy', function ($q) use ($payload, $all) {
                if (! $all) {
                    $q->where('status', 1);
                }

                if ($searchUser = Arr::get($payload, 'searchUser')) {
                    $q->where('name', 'LIKE', '%' .$searchUser. '%');
                }
            });
        });

        /**
         * filter by status
         */
        $query->when(! is_null($status = Arr::get($payload, 'status')), fn($q) => $q->where('status', (int) $status));

        /**
         * chỉ trả về answers level 1 khi filter theo user
         */
        if ($userId) {
            $query->where('level', 1);
        }

        /**
         * report status
         */
        $reported = Arr::get($payload, 'reported');
        $query->when(in_array($reported, ['yes', 'no']), function ($q) use ($reported) {
            $q->where('report_count', $reported === 'yes' ? '>' : '=', 0);
        });

        /**
         * search by post-content
         */
        $searchPost = Arr::get($payload, 'searchPost');
        $query->when($searchPost, fn($q) => $q->whereHas('post', fn($q) => $q->where('content', 'LIKE', '%' .$searchPost. '%')));

        /**
         * search by answer content
         */
        $search = Arr::get($payload, 'search');
        $query->when($search, function ($q) use ($search) {
            is_numeric($search) ? $q->where('answer_id', (int) $search) : $q->where('content', 'LIKE', '%' .$search. '%');
        });

        $query->getModel()->setTable($table);
    }
}
