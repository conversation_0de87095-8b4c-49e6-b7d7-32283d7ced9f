<?php

namespace App\Repositories;

use App\Models\News;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;

/**
 * Class NewsRepository
 * @package App\Repositories
 */
class NewsRepository extends BaseRepository
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return News::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {
        $table = $this->getTable();
        if (! $asTable) {
            $asTable = $table;
        }

        if ($asTable !== $table) {
            $query->getModel()->setTable($asTable);
        }

        /**
         * filter by status
         */
        $status = Arr::get($payload, 'status');
        $query->when(in_array($status, ['enable', 'disable']), fn($q) => $q->where('status', $status === 'enable' ? 1 : 0));

        /**
         * search by news title
         */
        $search = Arr::get($payload, 'search');
        $query->when($search, fn($q) => $q->where('title', 'LIKE', '%' .$search. '%'));

        $query->getModel()->setTable($table);
    }
}
