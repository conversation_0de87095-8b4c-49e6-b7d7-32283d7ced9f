<?php

namespace App\Repositories;

use App\Models\PostViewHistory;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;

/**
 * Class PostViewHistoryRepository
 * @package App\Repositories
 */
class PostViewHistoryRepository extends BaseRepository
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return PostViewHistory::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {
        $table = $this->getTable();
        if (! $asTable) {
            $asTable = $table;
        }

        if ($needModifyTable = ($asTable !== $table)) {
            $query->getModel()->setTable($asTable);
        }

        /**
         * search by user (ID or name)
         */
        $query->when(Arr::get($payload, 'user'), function (Builder $q, $user) {
            $q->whereHas('user', fn ($q) => $q->where('user_id', $user)->orWhere('name', 'LIKE', '%' . $user . '%'));
        });

        /**
         * search by post
         */
        $query->when(Arr::get($payload, 'post'), function (Builder $q, $post) {
            $q->whereHas('post', fn ($q) => $q->where('post_id', $post)->orWhere('content', 'LIKE', '%' . $post . '%'));
        });

        /**
         * filter by date
         */
        $query->when(Arr::get($payload, 'date'), function ($q, $date) {
            $date = Carbon::parse($date);

            $q->whereBetween('created_at', [
                $date->startOfDay()->toDateTimeString(),
                $date->endOfDay()->toDateTimeString(),
            ]);
        });

        if ($needModifyTable) {
            $query->getModel()->setTable($table);
        }
    }
}
