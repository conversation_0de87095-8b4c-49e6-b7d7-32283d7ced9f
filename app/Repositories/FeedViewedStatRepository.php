<?php

namespace App\Repositories;

use App\Models\FeedViewedStat;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

/**
 * Class FeedViewedStatRepository
 * @package App\Repositories
 */
class FeedViewedStatRepository extends BaseRepository
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return FeedViewedStat::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {

    }

    /**
     * @param User $user
     * @param int $limit
     * @return array
     */
    public function getStatByUser(User $user, int $limit = 50)
    {
        $subQuery = DB::table($this->getTable())
            ->where('user_id', $user->getKey())
            ->orderByDesc('id')
            ->limit($limit);

        return DB::table(DB::raw("({$subQuery->toSql()}) as `sub`"))
            ->mergeBindings($subQuery)
            ->selectRaw('`feed`, COUNT(*) as `viewed_count`')
            ->groupBy('feed')
            ->get()
            ->pluck('viewed_count', 'feed')
            ->toArray();
    }
}
