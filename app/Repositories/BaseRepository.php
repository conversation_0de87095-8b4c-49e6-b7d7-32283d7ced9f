<?php

namespace App\Repositories;

use App\Contracts\Repositories\RepositoryInterface;
use Illuminate\Cache\Repository;
use Illuminate\Container\Container;
use Illuminate\Contracts\Cache\Repository as CacheRepository;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Schema;

/**
 * Class BaseRepository
 * @package App\Repositories
 */
abstract class BaseRepository extends Repository implements CacheRepository, RepositoryInterface
{
    /**
     * @var Model
     */
    protected $model;

    /**
     * @var array|null
     */
    protected ?array $modelColumns = null;

    /**
     * @var string|null
     */
    protected ?string $modelTable = null;

    /**
     * @throws BindingResolutionException
     */
    public function __construct()
    {
        parent::__construct(
            cache()->getStore(),
        );

        $this->initModel();
    }

    /**
     * @return void
     * @throws BindingResolutionException
     */
    protected function initModel(): void
    {
        $this->model = $this->newModel();
    }

    /**
     * @return string
     */
    abstract protected function getModelClass(): string;

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    abstract protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void;

    /**
     * @param array $payload
     * @return string|null
     */
    protected function getSortColumn(array $payload)
    {
        if (($sortColumn = (string) Arr::get($payload, 'orderBy')) && $this->isValidColumn($sortColumn)) {
            return $sortColumn;
        }

        return null;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQuerySorts(Builder $query, array $payload, ?string $asTable = null): void
    {
        if (! $asTable) {
            $asTable = $this->getTable();
        }

        if ($sortColumn = $this->getSortColumn($payload)) {
            $sortDirection = $this->getSortDirection($payload);
            $query->orderBy($asTable . '.' . $sortColumn, $sortDirection);

            /**
             * extra sort
             */
            if ($sortColumn !== ($primaryKey = $this->getPrimaryKey())) {
                $query->orderBy($asTable . '.' . $primaryKey, 'DESC');
            }
        }
    }

    /**
     * @return Request
     */
    protected function getRequest(): Request
    {
        return request();
    }

    /**
     * @return Model
     */
    protected function getModel(): Model
    {
        return $this->model;
    }

    /**
     * @return Builder
     */
    public function query(): Builder
    {
        return $this->getModel()->query();
    }

    /**
     * @return string
     */
    public function getPrimaryKey(): string
    {
        return $this->getModel()->getKeyName();
    }

    /**
     * @return string
     */
    public function getTable(): string
    {
        if (! $this->modelTable) {
            $this->modelTable = $this->getModel()->getTable();
        }

        return $this->modelTable;
    }

    /**
     * @return array
     */
    protected function getColumns(): array
    {
        if (! $this->modelColumns) {
            $this->modelColumns = Schema::getColumnListing($this->getTable());
        }

        return $this->modelColumns;
    }

    /**
     * @inheritdoc
     */
    public function find(mixed $id, array|string $columns = ['*'])
    {
        return $this->query()->find($id, $columns);
    }

    /**
     * @inheritdoc
     */
    public function findMany(array $ids, array|string $columns = ['*'])
    {
        return $this->query()->findMany($ids, $columns);
    }

    /**
     * @inheritdoc
     */
    public function findOrFail(mixed $id, array|string $columns = ['*'])
    {
        return $this->query()->findOrFail($id, $columns);
    }

    /**
     * @inheritdoc
     */
    public function findOrNew(mixed $id, array|string $columns = ['*'])
    {
        return $this->query()->findOrNew($id, $columns);
    }

    /**
     * @inheritdoc
     */
    public function firstOrNew(array $attributes = [], array $values = [])
    {
        return $this->query()->firstOrNew($attributes, $values);
    }

    /**
     * @inheritdoc
     */
    public function firstOrCreate(array $attributes = [], array $values = [])
    {
        return $this->query()->firstOrCreate($attributes, $values);
    }

    /**
     * @inheritdoc
     */
    public function updateOrCreate(array $attributes, array $values = [])
    {
        return $this->query()->updateOrCreate($attributes, $values);
    }

    /**
     * @inheritdoc
     */
    public function findOrCreate(int $id, array $payload)
    {
        return tap($this->findOrNew($id), function ($instance) use ($payload) {
            $instance->fill($payload)->save();
        });
    }

    /**
     * @return Model
     * @throws BindingResolutionException
     */
    public function newModel()
    {
        return app()->make(
            $this->getModelClass()
        );
    }

    /**
     * @inheritdoc
     */
    public function destroy(Model $model): void
    {
        $model->delete();
    }

    /**
     * @inheritdoc
     */
    public function forceDelete(Model $model): void
    {
        $model->forceDelete();
    }

    /**
     * @param string $column
     * @return bool
     */
    public function isValidColumn(string $column): bool
    {
        return in_array($column, $this->getColumns());
    }

    /**
     * @param array $payload
     * @return Model
     * @throws BindingResolutionException
     */
    public function create(array $payload)
    {
        $model = $this->newModel();
        $model->fill($payload)->save();

        return $model;
    }

    /**
     * @param Model $model
     * @param array $payload
     * @return void
     */
    public function update(Model $model, array $payload)
    {
        $model->update($payload);
    }

    /**
     * @return string
     */
    protected function getSortDirection(array $payload)
    {
        $orderDirection = strtolower(Arr::get($payload, 'orderDirection', 'DESC'));
        if (! in_array($orderDirection, ['asc', 'desc'])) {
            $orderDirection = 'asc';
        }

        return $orderDirection;
    }

    /**
     * @param array $payload
     * @param int $perPage
     * @param int $offset
     * @return array
     */
    protected function getPaginationJoinSql(array $payload, int $perPage, int $offset = 0)
    {
        $asTable = 'j';
        $query = $this->query()->from($this->getTable(), $asTable);

        /**
         * use index
         */
        /*if ($index = Arr::get($payload, 'index')) {
            $query->useIndex($index);
        }*/

        /**
         * apply filters
         */
        $this->applyQueryFilters($query, $payload, $asTable);

        /**
         * apply sorts
         */
        $this->applyQuerySorts($query, $payload, $asTable);

        $total = 0;
        if (Arr::get($payload, 'fetchTotal', true)) {
            $total = $query->count();
        }

        return [
            $total,
            $query->addSelect($asTable . '.' . $this->getPrimaryKey())
                ->limit($perPage)
                ->offset($offset)
                ->toRawSql(),
        ];
    }

    /**
     * @param array $payload
     * @param int $perPage
     * @param int $currentPage
     * @param int $offset
     * @return LengthAwarePaginator
     * @throws BindingResolutionException
     */
    public function pagination(array $payload, int $perPage = 10, int $currentPage = 1, int $offset = 0)
    {
        $table = $this->getTable();
        $query = $this->query();

        /**
         * apply sorts
         */
        $this->applyQuerySorts($query, $payload, $table);

        [$total, $joinSql] = $this->getPaginationJoinSql($payload, $perPage, $offset);

        /**
         * pagination join
         */
        $query->joinSub($joinSql, 't', function (JoinClause $join) use ($table) {
            $primaryKey = $this->getPrimaryKey();
            $join->on("t.$primaryKey", '=', "$table.$primaryKey");
        });

        /**
         * with
         */
        if ($with = Arr::get($payload, 'with', [])) {
            $query->with($with);
        }

        /**
         * with count
         */
        if ($withCount = Arr::get($payload, 'count', [])) {
            $query->withCount($withCount);
        }

        /**
         * extra select
         */
        if ($extraSelect = Arr::get($payload, 'extraSelect')) {
            $query->select($table . '.*');
            $query->addSelect($extraSelect);
        }

        /**
         * extra orders
         */
        if ($extraOrders = Arr::get($payload, 'extraOrders')) {
            foreach ($extraOrders as $column => $direction) {
                $query->orderBy($column, $direction);
            }
        }

        /**
         * fetch results
         */
        $items = $query->get();

        $options = [
            'path' => Paginator::resolveCurrentPath(),
            'pageName' => 'page',
        ];

        return Container::getInstance()->makeWith(LengthAwarePaginator::class, compact(
            'items', 'total', 'perPage', 'currentPage', 'options',
        ))->appends('limit', $perPage);
    }
}
