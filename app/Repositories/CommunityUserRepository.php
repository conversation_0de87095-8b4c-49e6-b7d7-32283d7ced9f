<?php

namespace App\Repositories;

use App\Models\CommunityUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;

/**
 * Class CommunityUserRepository
 * @package App\Repositories
 */
class CommunityUserRepository extends BaseRepository
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return CommunityUser::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {
        $table = $this->getTable();
        if (! $asTable) {
            $asTable = $table;
        }

        if ($asTable !== $table) {
            $query->getModel()->setTable($asTable);
        }

        /**
         * filter by community
         */
        $communityId = Arr::get($payload, 'communityId');
        $query->when(! is_null($communityId), fn ($q) => $q->where('community_id', (int) $communityId));

        /**
         * filter by status
         */
        $status = Arr::get($payload, 'status');
        $query->when(! is_null($status), fn ($q) => $q->where('status', (int) $status));

        $query->whereHas('user', fn ($q) => $q->where('status', 1));

        $query->getModel()->setTable($table);
    }
}
