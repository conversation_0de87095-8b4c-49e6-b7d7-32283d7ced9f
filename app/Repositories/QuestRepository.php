<?php

namespace App\Repositories;

use App\Models\Quest;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;

/**
 * Class QuestRepository
 * @package App\Repositories
 */
class QuestRepository extends BaseRepository
{
    /**
     * @var string
     */
    protected $activeCacheKey = 'quests.active';

    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return Quest::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {
        $table = $this->getTable();
        if (! $asTable) {
            $asTable = $table;
        }

        if ($asTable !== $table) {
            $query->getModel()->setTable($asTable);
        }

        /**
         * filter by status
         */
        $status = Arr::get($payload, 'status');
        $query->when(in_array($status, ['enable', 'disable']), fn($q) => $q->where('status', $status === 'enable' ? 1 : 0));

        /**
         * search by quest title
         */
        $search = Arr::get($payload, 'search');
        $query->when($search, fn($q) => $q->where('title', 'LIKE', '%' .$search. '%'));

        $query->getModel()->setTable($table);
    }

    /**
     * @param string $type 
     * @return bool 
     */
    public function existedType(string $type, ?int $id = null)
    {
        return $this->query()
            ->where([
                'type' => $type,
            ])
            ->when($id, fn ($q) => $q->where('id', '!=', $id))
            ->exists();
    }

    /**
     * @return void 
     */
    public function clearActiveQuest()
    {
        $this->forget($this->activeCacheKey);
    }

    /**
     * Get all active quests
     */
    public function getActiveQuests()
    {
        return $this->rememberForever($this->activeCacheKey, fn () => $this->getActiveQuestsFromDB());
    }

    /**
     * @return array
     */
    protected function getActiveQuestsFromDB()
    {
        return Quest::query()
            ->where('status', 1)
            ->orderBy('sort')
            ->get()
            ->toArray();
    }
}
