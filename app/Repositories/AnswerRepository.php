<?php

namespace App\Repositories;

use App\Models\Answer;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class AnswerRepository
 * @package App\Repositories
 */
class AnswerRepository extends BaseRepository
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return Answer::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {

    }
}
