<?php

namespace App\Repositories;

use App\Enums\FeedType;
use App\Models\Setting;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;

/**
 * Class CommentRepository
 * @package App\Repositories
 */
class SettingRepository extends BaseRepository
{
    /** @noinspection PhpMissingClassConstantTypeInspection */
    const SYSTEM_SETTINGS = 'system::settings';

    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return Setting::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {

    }

    /**
     * @return array|mixed
     */
    public function getSettings(?int $userId = null)
    {
        $key = is_null($userId) ? self::SYSTEM_SETTINGS : 'user:settings:' . $userId;
        if (! $settings = $this->get($key)) {
            $settings = $this->_getSettings($userId);

            $this->updateSettings($settings, $userId);
        }

        return $settings;
    }

    /**
     * @return array
     */
    public function getHomeTimeSettings()
    {
        $systemSettings = $this->getSettings();
        if ($settings = Arr::get($systemSettings, 'home_time_settings')) {
            $settings = json_decode($settings, true);
        }

        /**
         * default settings
         */
        $defaults = [
            FeedType::RECOMMENDED_ANSWER->value => 48,
            FeedType::GENERAL->value => 24,
            FeedType::LAUGH->value => 48,
            FeedType::RAISE_HAND->value => 48,
        ];

        $data = [];
        foreach ($defaults as $key => $value) {
            $data[$key] = Arr::get($settings, $key, $value);
        }

        return $data;
    }

    /**
     * @return array
     */
    public function getQASettings()
    {
        $settings = $this->getSettings();
        if ($settings = Arr::get($settings, 'qa_setting')) {
            return json_decode($settings, true);
        }

        /**
         * default settings
         */
        return [
            /*'follow' => [
                'index' => 1,
                'limit' => 2,
            ],*/
            'friend' => [
                'index' => 1,
                'limit' => 2,
            ],
            'similar' => [
                'index' => 2,
                'limit' => 5,
            ],
            'like' => [
                'index' => 3,
                'limit' => 3,
            ],
            'general' => [
                'index' => 4,
                'limit' => 2,
            ],
            'latest' => [
                'index' => 5,
                'limit' => 1,
            ],
        ];
    }

    /**
     * @param array $settings
     * @param int|null $userId
     * @return void
     */
    public function updateSettings(array $settings, ?int $userId = null)
    {
        $this->forever(is_null($userId) ? self::SYSTEM_SETTINGS : 'user:settings:' . $userId, $settings);
    }

    /**
     * @return void
     */
    public function clearSystemSettings()
    {
        $this->forget(self::SYSTEM_SETTINGS);
    }

    /**
     * @param int $userId
     * @return void
     */
    public function clearUserSetting(int $userId)
    {
        $this->forget('user:settings:' . $userId);
    }

    /**
     * @param int|null $userId
     * @return array
     */
    protected function _getSettings(?int $userId = null)
    {
        return $this->query()
            ->where('user_id', $userId ?? 0)
            ->get()
            ->pluck('value', 'key')
            ->toArray();
    }
}
