<?php

namespace App\Repositories;

use App\Models\Community;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class CommunityRepository
 * @package App\Repositories
 */
class CommunityRepository extends BaseRepository
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return Community::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {

    }

    /**
     * @param int $userId
     * @return array
     */
    public function listPublicCommunities(int $userId)
    {
        $communities = [];
        $this->query()
            ->select('community_id', 'name')
            ->orderBy('name')
            ->orderBy('community_id')
            ->where('status', 1)
            ->whereHas('members', function (Builder $query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->chunk(100, function ($records) use (&$communities) {
                /** @var Community[] $records */
                foreach ($records as $community) {
                    $communities[] = [
                        'community_id' => $community->getKey(),
                        'name' => $community->name,
                    ];
                }
            });

        return $communities;
    }
}
