<?php

namespace App\Repositories;

use App\Models\Community;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class CommunityRepository
 * @package App\Repositories
 */
class CommunityRepository extends BaseRepository
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return Community::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {

    }

    /**
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function listAdminCommunities()
    {
        return $this->query()
            ->orderBy('name')
            ->orderBy('community_id')
            ->where([
                'status' => 1,
                'user_id' => 1,
            ])
            ->get();
    }
}
