<?php

namespace App\Repositories;

use App\Models\PremiumFeature;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;

/**
 * Class PremiumFeatureRepository
 * @package App\Repositories
 */
class PremiumFeatureRepository extends BaseRepository
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return PremiumFeature::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {
        $table = $this->getTable();
        if (! $asTable) {
            $asTable = $table;
        }

        if ($asTable !== $table) {
            $query->getModel()->setTable($asTable);
        }

        /**
         * search by feature name
         */
        $search = Arr::get($payload, 'search');
        $query->when($search, fn($q) => $q->where('name', 'LIKE', '%' .$search. '%'));

        $query->getModel()->setTable($table);
    }
}
