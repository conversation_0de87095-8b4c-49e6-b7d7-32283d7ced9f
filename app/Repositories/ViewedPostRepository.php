<?php

namespace App\Repositories;

use App\Models\QAViewedPost;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Class ViewedPostRepository
 * @package App\Repositories
 */
class ViewedPostRepository extends BaseRepository
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return QAViewedPost::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {

    }

    /**
     * @param string $token
     * @return void
     */
    public function clearViewedPosts(string $token)
    {
        DB::table($this->getTable())->where([
            'device_token' => $token,
        ])->delete();
    }

    /**
     * @param string $token
     * @param array $ids
     * @return void
     */
    public function appends(string $token, array $ids)
    {
        $items = [];
        $time = now('UTC')->toDateTimeString();
        foreach ($ids as $id) {
            $items[] = [
                'device_token' => $token,
                'post_id' => $id,
                'created_at' => $time,
            ];
        }

        try {
            DB::table($this->getTable())->insert($items);
        } catch (\Throwable $e) {
            Log::error('sync Q&A viewed posts error: ' . $e->getMessage());
        }
    }
}
