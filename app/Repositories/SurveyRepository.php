<?php

namespace App\Repositories;

use App\Enums\QuestionType;
use App\Models\PublishedSurvey;
use App\Models\Survey;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Arr;

/**
 * Class SurveyRepository
 * @package App\Repositories
 */
class SurveyRepository extends BaseRepository
{
    /** @noinspection PhpMissingClassConstantTypeInspection */
    const ALL_CACHED_SURVEYS_KEY = 'all_cached_surveys';

    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return Survey::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {
        $table = $this->getTable();
        if (! $asTable) {
            $asTable = $table;
        }

        if ($needModifyTable = ($asTable !== $table)) {
            $query->getModel()->setTable($asTable);
        }

        /**
         * filter by status
         */
        $query->where($asTable . '.status', 1);

        /**
         * search by word
         */
        $query->when(Arr::get($payload, 'search'), fn (Builder $q, $search) => $q->where($asTable . '.title', 'LIKE', '%' .$search. '%'));

        if ($needModifyTable) {
            $query->getModel()->setTable($table);
        }
    }

    /**
     * @return void
     */
    public function clearAllCachedSurveys()
    {
        $this->forget(self::ALL_CACHED_SURVEYS_KEY);
    }

    /**
     * @return mixed
     */
    public function listSurveyOptions()
    {
        return $this->rememberForever(self::ALL_CACHED_SURVEYS_KEY, function () {
            $surveys = [];
            $this->query()
                ->select('survey_id', 'title')
                ->orderBy('title')
                ->orderBy('survey_id')
                ->where('status', 1)
                ->chunk(100, function ($records) use (&$surveys) {
                    /** @var Collection|Survey[] $records */
                    foreach ($records as $survey) {
                        $surveys[] = [
                            'survey_id' => $survey->getKey(),
                            'title' => $survey->title,
                        ];
                    }
                });

            return $surveys;
        });
    }

    /**
     * @param User $user
     * @return Collection
     */
    public function listPublishedSurveys(User $user)
    {
        /** @var PublishedSurvey $publishedModel */
        $publishedModel = app(PublishedSurvey::class);

        $query = $publishedModel->newQuery()
            ->with([
                'survey' => function (HasOne $query) {
                    $query->where('status', 1);
                },
                'survey.attachedSurveys' => function (HasMany $query) {
                    $query->orderBy('created_at');
                },
                'survey.attachedSurveys.choices',
                'survey.attachedSurveys.survey' => function (HasOne $query) {
                    $query->where('status', 1);
                },
                'survey.attachedSurveys.survey.answers' => function(HasMany $query) use ($user) {
                    $query->where('user_id', $user->getKey());
                },
                'survey.attachedSurveys.survey.questions' => function (HasMany $query) {
                    $query->orderBy('question_id');
                },
                'survey.attachedSurveys.survey.questions.choices',
                'survey.questions' => function (HasMany $query) {
                    $query->orderBy('question_id');
                },
                'survey.questions.choices',

                /**
                 * load survey answers by user
                 */
                'survey.answers' => function(HasMany $query) use ($user) {
                    $query->where('user_id', $user->getKey());
                },
            ]);

        return $query->orderBy('sort')->get();
    }

    /**
     * @param int $surveyID
     * @return \App\Models\Question[]|array|Collection
     */
    public function listSurveyQuestions(int $surveyID)
    {
        /** @var Survey|null $survey */
        if (! $survey = $this->find($surveyID)) {
            return [];
        }

        $survey->load([
            'questions' => function (HasMany $query) {
                $query->whereIn('type', [QuestionType::SELECT_BOX->value, QuestionType::CHECK_BOX->value]);
            },
            'questions.choices',
        ]);

        return $survey->questions;
    }

    /**
     * @return Collection
     */
    public function listSortedSurveys()
    {
        /** @var PublishedSurvey $publishedModel */
        $publishedModel = app(PublishedSurvey::class);

        $query = $publishedModel->newQuery()
            ->with([
                'survey',
            ]);

        /**
         * filter by survey status
         */
        $query->whereHas('survey',  function (Builder $query) {
            $query->where('status', 1);
        });

        return $query->orderBy('sort')->get();
    }
}
