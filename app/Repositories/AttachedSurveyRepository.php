<?php

namespace App\Repositories;

use App\Models\AttachedSurvey;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;

/**
 * Class AttachedSurveyRepository
 * @package App\Repositories
 */
class AttachedSurveyRepository extends BaseRepository
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return AttachedSurvey::class;
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {
        $table = $this->getTable();
        if (! $asTable) {
            $asTable = $this->getTable();
        }

        if ($asTable !== $table) {
            $query->getModel()->setTable($asTable);
        }

        /**
         * filter by survey status
         */
        $query->where(function (Builder $query) {
            $query->whereHas('survey', function (Builder $query) {
                $query->select('survey_id')
                    ->where('status', 1);
            });

            $query->whereHas('toSurvey', function (Builder $query) {
                $query->select('survey_id')
                    ->where('status', 1);
            });
        });

        /**
         * search
         */
        $query->when(Arr::get($payload, 'search'), function ($q, $search) use ($asTable) {
            $q->where($asTable . '.title', 'LIKE', '%' . $search . '%');
        });

        /**
         * reset table to default
         */
        $query->getModel()->setTable($table);
    }
}
