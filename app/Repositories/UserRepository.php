<?php

namespace App\Repositories;

use App\Models\User;
use App\Models\UserRelation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

/**
 * Class UserRepository
 * @package App\Repositories
 * @method User create(array $payload)
 */
class UserRepository extends BaseRepository
{
    /**
     * @return string
     */
    protected function getModelClass(): string
    {
        return User::class;
    }

    /**
     * @param string $value
     * @param string $column
     * @return User|null
     */
    public function findBy(string $value, string $column = 'phone')
    {
        return $this->query()->where($column, $value)->first();
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string|null $asTable
     * @return void
     */
    protected function applyQueryFilters(Builder $query, array $payload, ?string $asTable = null): void
    {
        $table = $this->getTable();
        if (! $asTable) {
            $asTable = $table;
        }

        if ($needModifyTable = ($asTable !== $table)) {
            $query->getModel()->setTable($asTable);
        }

        /**
         * get relation data
         */
        $query->when(Arr::get($payload, 'relation'), fn ($q, $relation) => $this->applyQueryWithRelation($q, $payload, $relation, $asTable));

        /**
         * filter by id
         */
        $query->when(Arr::get($payload, 'id'), fn (Builder $q, $id) => $q->where($asTable . '.user_id', $id));

        /**
         * filter by status
         */
        $query->when(! is_null($status = Arr::get($payload, 'status')), fn (Builder $q) => $q->where($asTable . '.status', (int) $status));

        /**
         * chặn những user đã bị remove khỏi cộng đồng, hoặc đang tham gia cộng đồng
         * trong trường hợp lấy danh sách bạn bè để mời vào cộng đồng
         */
        $inCommunityId = (int) Arr::get($payload, 'inCommunityId');
        $query->when($inCommunityId !== 0, function (Builder $query) use ($inCommunityId, $asTable) {
            $query->whereNotExists(
                DB::table('community_users', 'cu')
                    ->whereColumn($asTable . '.user_id', 'cu.user_id')
                    ->where('cu.community_id', $inCommunityId)
                    ->whereIn('cu.status', [1, 2])
            );
        });

        /**
         * search by name or phone number
         */
        $query->when(Arr::get($payload, 'search'), fn ($q, $search) => $this->searchByNameOrPhone($q, $search, $asTable));

        if ($needModifyTable) {
            $query->getModel()->setTable($table);
        }
    }

    /**
     * @param Builder $query
     * @param array $payload
     * @param string $relation
     * @param string $asTable
     * @return void
     */
    protected function applyQueryWithRelation(Builder $query, array $payload, string $relation, string $asTable): void
    {
        $extraSelect = Arr::get($payload, 'extraSelect');
        $extraSelect = str_replace('t.', 'f.', $extraSelect);

        $query->select($extraSelect);

        $query->join(DB::raw('`user_relations` as `f`'), function (JoinClause $query) use ($payload, $asTable, $relation) {
            $joinColumn = 'target_id';
            $conditionColumn = 'user_id';

            $relationType = Arr::get($payload, 'relationType');
            if ($relationType === 'to') {
                $joinColumn = 'user_id';
                $conditionColumn = 'target_id';
            }

            $query->on('f.' . $joinColumn, $asTable . '.user_id');

            $userId = Arr::get($payload, 'userId');
            $query->when($userId, function ($query, $userId) use ($conditionColumn) {
                $query->where('f.' . $conditionColumn, $userId);
            });

            $relationColumn = match ($relation) {
                'friend' => 'f.is_friend',
                'follow' => 'f.is_follow',
                default => null,
            };

            $authId = auth()->id();
            $isNotMe = $userId != $authId;
            $query->when($relationColumn, function ($q, $column) use ($isNotMe, $authId) {
                $q->when($isNotMe, function ($q) use ($authId) {
                    $blockedIds = blockHelper()->getBlockedIds($authId);

                    $q->whereNotIn('f.target_id', $blockedIds);
                });

                $q->where($column, 1)->where('f.is_blocked', 0);
            });

            $query->when(is_null($relationColumn), fn ($q) => $q->where('f.is_blocked', 1));
        });

        /**
         * sort by followed_at
         */
        $query->orderByDesc($extraSelect);
        $query->orderByDesc($asTable . '.user_id');
    }

    /**
     * @param Builder $q
     * @param string $search
     * @param string $asTable
     * @return void
     */
    protected function searchByNameOrPhone(Builder $q, string $search, string $asTable)
    {
        $q->where(function (Builder $q) use ($asTable, $search) {
            $q->where($asTable . '.name', 'LIKE', '%' . $search . '%')
                ->orWhere($asTable . '.phone', 'LIKE', '%' . $search . '%');
        });
    }

    /**
     * @param int $userId
     * @param string $column
     * @return array
     */
    public function getBlockedUserIDs(int $userId, string $column = 'user_id'): array
    {
        return UserRelation::query()
                ->where($column, $userId)
                ->where('is_blocked', 1)
                ->pluck($column === 'user_id' ? 'target_id' : 'user_id')
                ->toArray();
    }

    /**
     * @param User $user
     * @param string $relationColumn
     * @param bool $checkGender
     * @return array
     */
    public function getRelationIds(User $user, string $relationColumn = 'is_friend', bool $checkGender = false)
    {
        return DB::table('user_relations')
            ->select('target_id')
            ->where('user_id', $user->getKey())
            ->when(blockHelper()->getBlockedIds($user->getKey()), fn ($q, $blockedIds) => $q->whereNotIn('target_id', $blockedIds))
            ->where($relationColumn, 1)
            //->where('is_blocked', 0)

            /**
             * user must be active
             */
            ->whereExists(
                DB::table('users')
                    ->whereRaw('`users`.`user_id` = `user_relations`.`target_id`')
                    ->where('users.status', 1)
                    ->where(function (\Illuminate\Database\Query\Builder $query) use ($user, $checkGender) {
                        if ($user->isMale() || $user->isFemale()) {
                            $query->whereIn('users.gender', [0, $user->gender]);
                        }

                        /**
                         * người chưa có giới tính hoặc giới tính khác
                         * thì không được xem những bài post của người có giới tính
                         */
                        elseif ($user->isOtherGender() || $user->emptyGender()) {
                            $query->whereIn('users.gender', [0, 3]);
                        }
                    }),
            )
            ->get()
            ->pluck('target_id')
            ->toArray();
    }
}
