<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @class Reaction
 * @property int $id
 * @property string $object_type
 * @property int $object_id
 * @property int $child_id
 * @property int $object_user_id
 * @property int $user_id
 * @property string $action
 * @property int $count
 * @property Carbon|string $react_date
 * @property Carbon|string $updated_at
 * @property Post|PostAnswer $object
 * @property User $user
 */
class Reaction extends Model
{
    /**
     * @var string
     */
    public $table = 'reactions';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'object_type',
        'object_id',
        'child_id',
        'object_user_id',
        'user_id',
        'action',
        'count',
        'react_date',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'object_type' => 'string',
            'object_id' => 'integer',
            'child_id' => 'integer',
            'object_user_id' => 'integer',
            'user_id' => 'integer',
            'action' => 'string',
            'count' => 'integer',
            'react_date' => 'date',
        ];
    }

    /**
     * @return MorphTo
     */
    public function object(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @param int $amount
     * @return void
     */
    public function incrementCount(int $amount = 1)
    {
        $this->incrementQuietly('count', $amount);
    }

    /**
     * @param int $amount
     * @return void
     */
    public function decrementCount(int $amount = 1)
    {
        $this->decrementQuietly('count', $amount);
    }

    /**
     * @return bool
     */
    public function canMerge(): bool
    {
        return $this->canBeCancel();
    }

    /**
     * @return bool
     */
    public function canBeCancel()
    {
        return $this->updated_at->toDateTimeString() > now('UTC')->subMinutes(5)->toDateTimeString();
    }
}
