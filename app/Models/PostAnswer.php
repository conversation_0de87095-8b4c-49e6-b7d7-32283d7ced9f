<?php

namespace App\Models;

use App\Models\Contracts\Reportable;
use App\Models\Traits\HasStatus;
use App\Models\Traits\ReportableObject;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Facades\DB;

/**
 * @class PostAnswer
 * @property integer $answer_id
 * @property integer $parent_id
 * @property integer $user_id
 * @property integer $post_id
 * @property string $content
 * @property string $image
 * @property integer $like_count
 * @property integer $like_user_count
 * @property integer $comment_count
 * @property integer $coin_count
 * @property integer $report_count
 * @property integer $voted
 * @property integer $pinned
 * @property integer $tab_index
 * @property integer $level
 * @property integer $children_count
 * @property integer $status
 * @property Carbon $created_at
 * @property User $createdBy
 * @property Post $post
 * @property PostAnswer $parent
 * @property Collection|Comment[] $comments
 * @property Collection|Reaction[] $reactions
 * @property Collection|PostAnswer[] $children
 * @property MetaData|null $metadata
 */
class PostAnswer extends Model implements Reportable
{
    use HasFactory;
    use HasStatus;
    use ReportableObject;

    /**
     * @var string
     */
    public $table = 'post_answers';

    /**
     * @var string
     */
    public $primaryKey = 'answer_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'parent_id',
        'user_id',
        'post_id',
        'content',
        'image',
        'like_count',
        'like_user_count',
        'comment_count',
        'coin_count',
        'report_count',
        'voted',
        'pinned',
        'tab_index',
        'level',
        'children_count',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'parent_id' => 'integer',
            'user_id' => 'integer',
            'post_id' => 'integer',
            'content' => 'string',
            'image' => 'string',
            'like_count' => 'integer',
            'like_user_count' => 'integer',
            'comment_count' => 'integer',
            'coin_count' => 'integer',
            'report_count' => 'integer',
            'voted' => 'integer',
            'pinned' => 'integer',
            'tab_index' => 'integer',
            'level' => 'integer',
            'children_count' => 'integer',
            'status' => 'integer',
        ];
    }

    /**
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return BelongsTo
     */
    public function post(): BelongsTo
    {
        return $this->belongsTo(Post::class, 'post_id');
    }

    /**
     * @return MorphMany
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    /**
     * @return MorphMany
     */
    public function reactions(): MorphMany
    {
        return $this->morphMany(Reaction::class, 'object')->orderByDesc('id');
    }

    /**
     * @return HasMany
     */
    public function likedReactions()
    {
        return $this->hasMany(Reaction::class, 'object_id', 'answer_id')
            ->where('object_type', 'post_answer')
            ->where('action', 'liked');
    }

    /**
     * @return MorphMany
     */
    public function reports(): MorphMany
    {
        return $this->morphMany(Report::class, 'reportable');
    }

    /**
     * @param string $column
     * @param int $amount
     * @return void
     */
    public function incrementCount(string $column = 'like_count', int $amount = 1)
    {
        $this->incrementQuietly($column, $amount);
    }

    /**
     * @param string $column
     * @param int $amount
     * @return void
     */
    public function decrementCount(string $column = 'like_count', int $amount = 1)
    {
        $this->decrementQuietly($column, $amount);
    }

    /**
     * @return Reaction|null
     */
    protected function latestReaction(string $type = 'liked')
    {
        if (! $this->relationLoaded('reactions')) {
            return null;
        }

        return $this->reactions->sortByDesc('id')->firstWhere('action', $type);
    }

    /**
     * @return Reaction|null
     */
    public function latestLiked()
    {
        return $this->latestReaction();
    }

    /**
     * @return Reaction|null
     */
    public function latestDonated()
    {
        return $this->latestReaction('donated');
    }

    /**
     * @return HasMany
     */
    public function children()
    {
        return $this->hasMany(PostAnswer::class, 'parent_id');
    }

    /**
     * @return BelongsTo
     */
    public function parent()
    {
        return $this->belongsTo(PostAnswer::class, 'parent_id');
    }

    /**
     * @return void
     */
    public function updateChildrenCount(string $action = 'increment', int $amount = 1)
    {
        $method = $action === 'increment' ? 'incrementQuietly' : 'decrementQuietly';
        $this->{ $method }('children_count', $amount);
    }

    /**
     * @return void
     */
    public function markAsDeleted()
    {
        $this->updateQuietly([
            'status' => 0,
        ]);

        /**
         * update children status
         */
        if ($this->children_count > 0) {
            DB::table('post_answers')
                ->where('parent_id', $this->getKey())
                ->update([
                    'status' => 0,
                ]);
        }
    }

    /**
     * @param $userId
     * @return bool
     */
    public function ownerBy($userId)
    {
        $userId = (int) $userId;

        return $userId === (int) $this->user_id;
    }

    /**
     * Determine if the current level is level one.
     *
     * @return bool
     */
    public function isLevelOne()
    {
        return 1 === (int) $this->level;
    }

    /**
     * Determine if the current instance is not at level one.
     *
     * @return bool
     */
    public function isNotLevelOne()
    {
        return $this->level > 1;
    }

    /**
     * Determines if the entity has been voted for.
     *
     * @return bool
     */
    public function isVoted()
    {
        return 1 === (int) $this->voted;
    }

    /**
     * Determine if the entity has not been voted on.
     *
     * @return bool
     */
    public function isNotVoted()
    {
        return 0 === (int) $this->voted;
    }

    /**
     * @return bool
     */
    public function isPinned()
    {
        return 1 === (int) $this->pinned;
    }

    /**
     * @return bool
     */
    public function isNotPinned()
    {
        return ! $this->isPinned();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\MorphOne
     */
    public function metadata()
    {
        return $this->morphOne(MetaData::class, 'metadata');
    }
}
