<?php

namespace App\Models;

use App\Models\Contracts\Reportable;
use App\Models\Traits\HasStatus;
use App\Models\Traits\ReportableObject;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @class Comment
 * @property integer $comment_id
 * @property integer $user_id
 * @property integer $commentable_id
 * @property string $commentable_type
 * @property string $content
 * @property integer $report_count
 * @property integer $status
 * @property User $createdBy
 * @property PostAnswer $commentable
 */
class Comment extends Model implements Reportable
{
    use HasStatus;
    use ReportableObject;

    /**
     * @var string
     */
    public $table = 'comments';

    /**
     * @var string
     */
    public $primaryKey = 'comment_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'commentable_id',
        'commentable_type',
        'content',
        'report_count',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'integer',
            'commentable_id' => 'integer',
            'commentable_type' => 'string',
            'content' => 'string',
            'report_count' => 'integer',
            'status' => 'integer',
        ];
    }

    /**
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return MorphTo
     */
    public function commentable(): MorphTo
    {
        return $this->morphTo();
    }
}
