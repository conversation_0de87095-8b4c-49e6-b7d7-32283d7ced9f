<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

trait HasGeneralPost
{
    /**
     * câu hỏi có số lượng người trả lời nhiều nhất trong khoảng thời gian (time) gần nhất
     * @param Builder $query
     * @param string $time
     * @param string|null $type
     * @return void
     */
    protected function applyGeneralQuery(Builder $query, string $time, ?string $type = null)
    {
        $query->select('posts.*')
            ->addSelect('total_user')
            ->joinSub(
                DB::table('post_answers', 'pa')
                    ->selectRaw('`post_id`, COUNT(DISTINCT `pa`.`user_id`) AS `total_user`')
                    ->where('pa.status', 1)
                    ->where('pa.level', 1)
                    ->whereExists(
                        DB::table('users', 'u')
                            ->whereColumn('u.user_id', 'pa.user_id')
                            ->where('u.status', 1),
                    )
                    ->groupBy('pa.post_id'),
                'pa',
                function (JoinClause $join) {
                    $join->on('pa.post_id', '=', 'posts.post_id');
                }
            )
            ->where('posts.created_at', '>=', $time)
            ->when($type, fn ($q) => $q->where('posts.type', $type))
            ->groupBy('posts.post_id');

        $this->applyGeneralSort($query);
    }

    /**
     * theo số lượng người trả lời nhiều nhất
     * @param Builder $query
     * @param string $tableAlias
     * @return void
     */
    protected function applyGeneralSort(Builder $query, string $tableAlias = 'posts')
    {
        $query->orderByDesc('total_user')
            ->orderByDesc($tableAlias . '.post_id');
    }
}
