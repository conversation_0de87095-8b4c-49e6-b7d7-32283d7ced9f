<?php

namespace App\Models\Traits;

use App\Models\Report;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * @property int $report_count
 * @property Collection|Report[] $reports
 * @mixin Model
 */
trait ReportableObject
{
    /**
     * @return MorphMany
     */
    public function reports(): MorphMany
    {
        return $this->morphMany(Report::class, 'reportable');
    }

    /**
     * @return void
     */
    public function incrementReportCount()
    {
        $this->incrementQuietly('report_count');
    }
}
