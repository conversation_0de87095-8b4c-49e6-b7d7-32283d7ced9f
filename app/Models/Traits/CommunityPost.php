<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

trait CommunityPost
{
    /**
     * lấy bài post trong nhóm community
     * @param Builder $query
     * @param int $communityId
     * @return void
     */
    protected function applyCommunityQuery(Builder $query, int $communityId = 0)
    {
        $query->when($communityId, function (Builder $query) use ($communityId) {
            $query->whereHas('communityPost', function (Builder $query) use ($communityId) {
                $query->where('community_id', $communityId);

                $query->whereExists(
                    DB::table('community_users')
                        ->whereColumn('community_posts.community_id', 'community_users.community_id')
                        ->where('community_users.status', 1)
                );
            });
        });

        $this->applyCommunitySort($query);
    }

    /**
     * theo post_id mới nhất
     * @param Builder $query
     * @param string $tableAlias
     * @return void
     */
    protected function applyCommunitySort(Builder $query, string $tableAlias = 'posts')
    {
        $query->orderByDesc($tableAlias . '.sort_answered_at');
        $query->orderByDesc($tableAlias . '.post_id');
    }
}
