<?php

namespace App\Models\Traits;

use App\Models\Post;
use App\Models\Reaction;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

trait HasFriendPost
{
    /**
     * @param User $user
     * @return array
     */
    protected function getUserFriendIds(User $user)
    {
        return userRepository()->getRelationIds($user, 'is_friend', true);
    }

    /**
     * câu hỏi của những người là bạn mình
     * @param Builder $query
     * @param User $user
     * @return void
     */
    protected function applyFriendQuery(Builder $query, User $user)
    {
        $friendIds = $this->getUserFriendIds($user);

        $query->select('post_id')
            ->selectRaw('`created_at` as `action_at`')
            ->whereIn('user_id', $friendIds)
            ->where('status', 1)
            ->union(
                Reaction::query()
                    ->selectRaw('`object_id` as `post_id`, MAX(`updated_at`) as `action_at`')
                    ->whereIn('user_id', $friendIds)
                    ->where('object_type', 'post')
                    ->whereIn('action', ['answered', 'liked'])
                    ->whereHasMorph('object', [Post::class], function (Builder $query) use ($user) {
                        $query->where('status', 1)
                            ->when(blockHelper()->getBlockedIds($user->getKey()), fn ($q, $blockedIds) => $q->whereNotIn('user_id', $blockedIds))
                            ->whereHas('createdBy', fn ($q) => $q->where('status', 1));
                    })
                    ->groupBy('object_id')
            );
    }

    /**
     * theo thứ tự mới nhất
     * @param Builder $query
     * @param string|null $tableAlias
     * @return void
     */
    protected function applyFriendSort(Builder $query, ?string $tableAlias = null)
    {
        $query->orderByDesc('action_at');
        $query->orderByDesc($tableAlias ? $tableAlias . '.post_id' : 'post_id');
    }
}
