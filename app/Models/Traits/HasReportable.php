<?php

namespace App\Models\Traits;

use App\Models\Comment;
use App\Models\Contracts\Reportable;
use App\Models\Post;
use App\Models\PostAnswer;

trait HasReportable
{
    /**
     * @param int $id
     * @param string $objectType
     * @return Reportable|null
     */
    protected function getReportObject(int $id, string $objectType)
    {
        if (! $model = $this->getObjectModel($objectType)) {
            return null;
        }

        /** @var Reportable $model */
        $model = $model->newQuery()->find($id);
        if (! $model || ! $model->isEnable()) {
            return null;
        }

        return $model;
    }

    /**
     * @param string $type
     * @return Reportable
     */
    protected function getObjectModel(string $type)
    {
        return match ($type) {
            'post' => app(Post::class),
            'post_answer' => app(PostAnswer::class),
            'comment' => app(Comment::class),
            default => null,
        };
    }
}
