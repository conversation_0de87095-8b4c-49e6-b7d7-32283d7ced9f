<?php

namespace App\Models\Traits;

use App\Enums\PostType;
use Illuminate\Database\Eloquent\Builder;

trait HasGenderPost
{
    /**
     * gender post type
     * @param Builder $query
     * @return void
     */
    protected function applyGenderQuery(Builder $query)
    {
        $query->where('posts.type', PostType::GENDER->value);

        $this->applyGenderSort($query);
    }

    /**
     * sort by post_id descending
     * @param Builder $query
     * @param string $tableAlias
     * @return void
     */
    protected function applyGenderSort(Builder $query, string $tableAlias = 'posts')
    {
        $query->orderByDesc($tableAlias . '.post_id');
    }
}
