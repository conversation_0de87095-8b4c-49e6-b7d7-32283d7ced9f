<?php

namespace App\Models\Traits\QAPosts;

use App\Models\Post;
use App\Models\Reaction;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

trait UserAnswerLikedPost
{
    /**
     * @param User $user
     * @param int $limit
     * @return Collection
     */
    protected function getUserAnswerLikedPostsForTest(User $user, int $limit = 100)
    {
        return $this->_getUserAnswerLikedPosts($user, $limit, false);
    }

    /**
     * @param User $user
     * @param array $cached
     * @param int $limit
     * @return Collection
     */
    protected function getUserAnswerLikedPosts(User $user, array &$cached, int $limit = 10)
    {
        $type = 'like';
        if (! $actualLimit = $this->getActualLimit($cached, $type)) {
            return Collection::make();
        }

        $posts = $this->_getUserAnswerLikedPosts($user, $limit);

        /**
         * mark posts gone
         */
        if ($posts->count() <= $actualLimit) {
            $cached['limit'][$type] = 0;
            $this->updateCachedData($user->currentTokenId(), $cached);
        }

        return $posts;
    }

    /**
     * @param User $user
     * @param int $limit
     * @param bool $ignore
     * @return Collection
     */
    protected function _getUserAnswerLikedPosts(User $user, int $limit = 10, bool $ignore = true)
    {
        return $this->getQueryBuilder()
            ->from('posts', 'p')
            ->select('p.*', DB::raw('\'like\' as `qa_type`'))
            ->joinSub(
                $this->getUserAnswerLikedSubQuery($user, $limit, $ignore),
                't',
                function (JoinClause $join) {
                    $join->on('t.post_id', '=', 'p.post_id');
                },
            )
            ->orderByDesc('p.post_id')
            ->get();
    }

    /**
     * lấy những câu hỏi
     * của top 5 người mình đã thích trong ngày
     * tối đa 15 người (trong vòng 5 ngày)
     * @param User $user
     * @param int $limit
     * @param bool $ignore
     * @return Builder
     */
    protected function getUserAnswerLikedSubQuery(User $user, int $limit, bool $ignore = true)
    {
        $token = $user->currentTokenId();
        $users = $this->getLikedUserIds($user)
            ->pluck('user_id')
            ->toArray();

        $blockedIds = blockHelper()->getBlockedIds($user->getKey());

        return DB::table(
            /**
             * bài post của user
             */
            Post::query()
                ->select('post_id')
                ->where(function (EloquentBuilder $query) use ($users, $token, $ignore, $blockedIds) {
                    $query->whereIn('posts.user_id', $users);

                    $query->when($blockedIds, function (EloquentBuilder $query, $blockedIds) {
                        $query->whereNotIn('posts.user_id', $blockedIds);
                    });

                    $query->whereHas('createdBy', function (EloquentBuilder $query) {
                        $query->where('status', 1);
                        $this->ignoreGenderPosts($query);
                    });

                    $query->where('posts.status', 1);

                    /**
                     * ignore viewed posts
                     */
                    if ($ignore) {
                        $this->ignoreViewedPosts($query, $token);
                    }
                })

                /**
                 * bài post user answered
                 */
                ->union(
                    Reaction::query()
                        ->selectRaw('`object_id` as `post_id`')
                        ->where(function (EloquentBuilder $query) use ($users, $token, $ignore) {
                            count($users) === 1 ? $query->where('user_id', $users[0]) : $query->whereIn('user_id', $users);
                            $query->where([
                                'object_type' => 'post',
                                'action' => 'answered',
                            ]);

                            /**
                             * ignore viewed posts
                             */
                            if ($ignore) {
                                $this->ignoreViewedPosts($query, $token, '`reactions`.`object_id`');
                            }
                        })
                        ->whereHasMorph('object', [Post::class], function (EloquentBuilder $query) use ($blockedIds) {
                            /**
                             * object status
                             */
                            $query->where('status', 1)
                                /**
                                 * ignore blocked users
                                 */
                                ->when($blockedIds, function (EloquentBuilder $query, $blockedIds) {
                                    $query->whereNotIn('user_id', $blockedIds);
                                })

                                /**
                                 * object user status
                                 */
                                ->whereHas('createdBy', function (EloquentBuilder $query) {
                                    $query->where('status', 1);
                                    $this->ignoreGenderPosts($query);
                                });
                        })
                ),
                'u',
            )
            ->select('u.post_id')
            ->orderByDesc('u.post_id')
            ->limit($limit);
    }

    /**
     * @param User $user
     * @param int $perDay
     * @param int $total
     * @return Builder
     */
    protected function getLikedUserIds(User $user, int $perDay = 5, int $total = 15)
    {
        return DB::table($this->getTopLikedUserPerDay($user, $perDay, $total), 't')
            ->select('user_id');
    }

    /**
     * @param User $user
     * @param int $perDay
     * @param int $total
     * @return Builder
     */
    protected function getTopLikedUserPerDay(User $user, int $perDay = 5, int $total = 15)
    {
        return DB::table($this->getTopUserLikedQuery($user), 'tmp')
            ->select('user_id')
            ->where('row_num', '<=', $perDay)
            ->limit($total);
    }

    /**
     * @param User $user
     * @param int $perDay
     * @return Builder
     */
    protected function getTopLikedUserPerDayForStats(User $user, int $perDay = 5)
    {
        return DB::table($this->getTopUserLikedQuery($user, true), 'tmp')
            ->where('row_num', '<=', $perDay);
    }

    /**
     * @param User $user
     * @param bool $filterDate
     * @return Builder
     */
    protected function getTopUserLikedQuery(User $user, bool $filterDate = false)
    {
        return DB::table($this->getReactionStats($user, $filterDate), 'r')
            ->select( 'like_count', 'react_date')
            ->selectRaw('`object_user_id` as `user_id`')
            ->selectRaw('ROW_NUMBER() OVER (PARTITION BY `react_date` ORDER BY `like_count` desc) AS `row_num`')
            ->orderByDesc('react_date')
            ->orderByDesc('row_num');
    }

    /**
     * @param User $user
     * @param bool $filterDate
     * @return Builder
     */
    protected function getReactionStats(User $user, bool $filterDate = false)
    {
        return Reaction::query()
            ->selectRaw('`object_user_id`, SUM(`count`) as `like_count`, `react_date`')
            ->where([
                'reactions.user_id' => $user->getKey(),
                'object_type' => 'post_answer',
                'action' => 'liked',
            ])
            /**
             * active status
             */
            ->whereHasMorph('object', '*', function (EloquentBuilder $query) {
                /**
                 * object status
                 */
                $query->where('status', 1)
                    /**
                     * object user status
                     */
                    ->whereHas('createdBy', fn ($q) => $q->where('status', 1));
            })
            ->where(function (EloquentBuilder $query) use ($user, $filterDate) {
                if ($filterDate && ($date = Cache::get('limit_liked_date_filter_for_user_' . $user->getKey()))) {
                    $query->where('created_at', '>=', $date . ' 00:00:00');
                }
            })
            ->groupBy(['object_user_id', 'react_date']);
    }
}
