<?php

namespace App\Models\Traits\QAPosts;

use App\Models\Traits\HasGeneralPost;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

trait GeneralPost
{
    use HasGeneralPost;

    /**
     * @param User $user
     * @param int $limit
     * @return Collection
     */
    protected function getGeneralPostsForTest(User $user, int $limit = 100)
    {
        return $this->_getGeneralPostQuery($user, $user->currentTokenId(), $limit, false)->get();
    }

    /**
     * câu hỏi được đăng trong vòng 6h qua
     * theo thứ tự câu trả lời nhiều nhất
     * @param User $user
     * @param array $cached
     * @param int $limit
     * @return Collection
     */
    protected function getGeneralPosts(User $user, array &$cached, int $limit = 10)
    {
        $type = 'general';
        if (! $actualLimit = $this->getActualLimit($cached, $type)) {
            return Collection::make();
        }

        $token = $user->currentTokenId();
        $posts = $this->_getGeneralPostQuery($user, $token, $limit)->get();

        /**
         * mark posts gone
         */
        if ($posts->count() <= $actualLimit) {
            $cached['limit'][$type] = 0;
            $this->updateCachedData($token, $cached);
        }

        return $posts;
    }

    /**
     * @param User $user
     * @param string $token
     * @param int $limit
     * @param bool $ignore
     * @return Builder
     */
    private function _getGeneralPostQuery(User $user, string $token, int $limit = 10, bool $ignore = true)
    {
        $query = $this->getQueryBuilder()
            ->from('posts', 'p')
            ->select('p.*', DB::raw('\'general\' as `qa_type`'))
            ->joinSub(
                $this->getGeneralSubQuery($user, $token, $limit, $ignore),
                't',
                function (JoinClause $join) {
                    $join->on('t.post_id', '=', 'p.post_id');
                },
            );

        /**
         * sort
         */

        return $query->orderByDesc('p.answer_count')
            ->orderByDesc('p.post_id');
    }

    /**
     * @param User $user
     * @param string $token
     * @param int $limit
     * @param bool $ignore
     * @return Builder
     */
    protected function getGeneralSubQuery(User $user, string $token, int $limit, bool $ignore = true)
    {
        return $this->getQueryBuilder()
            ->select('posts.post_id')
            ->join('post_answers AS a', 'a.post_id', '=', 'posts.post_id')
            ->where(function (Builder $query) use ($token, $ignore, $user) {
                $query->where('posts.status', 1);

                /**
                 * ignore blocked users
                 */
                $query->when(blockHelper()->getBlockedIds($user->getKey()), function (Builder $query, $blockedIds) {
                    $query->whereNotIn('posts.user_id', $blockedIds);
                });

                $query->whereHas('createdBy', function (Builder $query) {
                    $query->where('status', 1);
                    $this->ignoreGenderPosts($query);
                });

                /**
                 * câu hỏi được đăng trong vòng 6h qua
                 */
                $time = now('UTC')->subHours(6)->toDateTimeString();
                $query->where('posts.created_at', '>=', $time);

                /**
                 * ignore viewed posts
                 */
                if ($ignore) {
                    $this->ignoreViewedPosts($query, $token);
                }
            })
            ->orderByDesc('posts.answer_count')
            ->orderByDesc('posts.post_id')
            ->limit($limit);
    }
}
