<?php

namespace App\Models\Traits\QAPosts;

use App\Models\Post;
use App\Models\Reaction;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

trait FriendPost
{
    /**
     * @param User $user
     * @param int $limit
     * @return Collection
     */
    protected function getFriendPostsForTest(User $user, int $limit = 100)
    {
        return $this->_getFollowPosts($user, $limit, false);
    }

    /**
     * @param User $user
     * @param array $cached
     * @param int $limit
     * @return Collection
     */
    protected function getFriendPosts(User $user, array &$cached, int $limit = 10)
    {
        $type = 'friend';
        if (! $actualLimit = $this->getActualLimit($cached, $type)) {
            return Collection::make();
        }

        $posts = $this->_getFriendPosts($user, $limit);

        /**
         * mark posts gone
         */
        if ($posts->count() <= $actualLimit) {
            $cached['limit'][$type] = 0;
            $this->updateCachedData($user->currentTokenId(), $cached);
        }

        return $posts;
    }

    /**
     * @param User $user
     * @param int $limit
     * @param bool $ignore
     * @return Collection
     */
    protected function _getFriendPosts(User $user, int $limit = 10, bool $ignore = true)
    {
        return $this->getQueryBuilder()
            ->from('posts', 'p')
            ->select('p.*', DB::raw('\'friend\' as `qa_type`, `action_at`'))
            ->joinSub(
                $this->getFriendSubQuery($user, $limit, $ignore),
                't',
                function (JoinClause $join) {
                    $join->on('t.post_id', '=', 'p.post_id');
                },
            )
            ->orderByDesc('t.action_at')
            ->orderByDesc('p.post_id')
            ->get();
    }

    /**
     * @param User $user
     * @param int $limit
     * @param bool $ignore
     * @return Builder
     */
    protected function getFriendSubQuery(User $user, int $limit, bool $ignore = true)
    {
        $userFriendIds = $this->getUserFriendIds($user);
        $token = $user->currentTokenId();

        return DB::table(
            /**
             * owner post
             */
            Post::query()
                ->select('post_id')
                ->selectRaw('`created_at` as `action_at`')
                ->whereIn('user_id', $userFriendIds)
                ->whereHas('createdBy', function (EloquentBuilder $query) {
                    $this->ignoreGenderPosts($query);
                })
                ->where('status', 1)

                /**
                 * ignore viewed posts
                 */
                ->when($ignore, fn ($q) => $this->ignoreViewedPosts($q, $token))

                /**
                 * answered post & liked post
                 */
                ->union(
                    Reaction::query()
                        ->selectRaw('`object_id` as `post_id`, MAX(`updated_at`) as `action_at`')
                        ->whereIn('user_id', $userFriendIds)
                        ->where('object_type', 'post')
                        ->whereIn('action', ['answered', 'liked'])
                        ->whereHasMorph('object', [Post::class], function (EloquentBuilder $query) use ($user) {
                            $query->where('status', 1)
                                ->when(blockHelper()->getBlockedIds($user->getKey()), fn ($q, $blockedIds) => $q->whereNotIn('user_id', $blockedIds))
                                ->whereHas('createdBy', function (EloquentBuilder $query) {
                                    $query->where('status', 1);
                                    $this->ignoreGenderPosts($query);
                                });
                        })
                        ->when($ignore, fn ($q) => $this->ignoreViewedPosts($q, $token, '`reactions`.`object_id`'))
                        ->groupBy('object_id')
                ),
                'tmp',
            )
            ->selectRaw('`tmp`.`post_id`, MAX(`tmp`.`action_at`) as `action_at`')
            ->orderByDesc('action_at')
            ->orderByDesc('tmp.post_id')
            ->groupBy('tmp.post_id')
            ->limit($limit);
    }

    /**
     * @param User $user
     * @return array
     */
    protected function getUserFriendIds(User $user)
    {
        return userRepository()->getRelationIds($user);
    }
}
