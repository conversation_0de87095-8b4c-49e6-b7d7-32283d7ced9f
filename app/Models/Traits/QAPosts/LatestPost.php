<?php

namespace App\Models\Traits\QAPosts;

use App\Models\Post;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

trait LatestPost
{
    /**
     * @param User $user
     * @param int $limit
     * @return Collection
     */
    protected function getLatestPostsForTest(User $user, int $limit = 100)
    {
        return $this->_getLatestPosts($user, $user->currentTokenId(), $limit, false);
    }

    /**
     * @param User $user
     * @param array $cached
     * @param int $limit
     * @return Collection
     */
    protected function getLatestPosts(User $user, array &$cached, int $limit = 10)
    {
        $type = 'latest';
        if (! $actualLimit = $this->getActualLimit($cached, $type)) {
            return Collection::make();
        }

        $token = $user->currentTokenId();
        $posts = $this->_getLatestPosts($user, $token, $limit);

        /**
         * mark posts gone
         */
        if ($posts->count() <= $actualLimit) {
            $cached['limit'][$type] = 0;
            $this->updateCachedData($token, $cached);
        }

        return $posts;
    }

    /**
     * @param User $user
     * @param string $token
     * @param int $limit
     * @param bool $ignore
     * @return Collection
     */
    private function _getLatestPosts(User $user, string $token, int $limit = 10, bool $ignore = true)
    {
        return $this->getQueryBuilder()
            ->from('posts', 'p')
            ->select('p.*', DB::raw('\'latest\' as `qa_type`'))
            ->joinSub(
                $this->getLatestSubQuery($user, $token, $limit, $ignore),
                't',
                function (JoinClause $join) {
                    $join->on('t.post_id', '=', 'p.post_id');
                },
            )
            ->orderByDesc('p.post_id')
            ->get();
    }

    /**
     * @param User $user
     * @param string $token
     * @param int $limit
     * @param bool $ignore
     * @return Builder
     */
    private function getLatestSubQuery(User $user, string $token, int $limit, bool $ignore = true)
    {
        return Post::query()
            ->select('posts.post_id')
            ->where(function (EloquentBuilder $query) use ($token, $ignore, $user) {
                $query->where('posts.status', 1);

                $query->when(blockHelper()->getBlockedIds($user->getKey()), function (EloquentBuilder $query, $blockedIds) {
                    $query->whereNotIn('posts.user_id', $blockedIds);
                });

                $query->whereHas('createdBy', function (EloquentBuilder $query) {
                    $query->where('status', 1);
                    $this->ignoreGenderPosts($query);
                });

                /**
                 * ignore viewed posts
                 */
                if ($ignore) {
                    $this->ignoreViewedPosts($query, $token);
                }
            })
            ->orderByDesc('posts.post_id')
            ->limit($limit);
    }
}
