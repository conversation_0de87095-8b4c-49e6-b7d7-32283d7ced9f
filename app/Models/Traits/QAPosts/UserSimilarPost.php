<?php

namespace App\Models\Traits\QAPosts;

use App\Models\PostViewHistory;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

trait UserSimilarPost
{
    /**
     * @param User $user
     * @param int $limit
     * @return Collection
     */
    protected function getUserSimilarPostsForTest(User $user, int $limit = 100)
    {
        return $this->_getUserSimilarPosts($user, $limit, false);
    }

    /**
     * @param User $user
     * @param array $cached
     * @param int $limit
     * @return Collection
     */
    protected function getUserSimilarPosts(User $user, array &$cached, int $limit = 10)
    {
        $type = 'similar';
        if (! $actualLimit = $this->getActualLimit($cached, $type)) {
            return Collection::make();
        }

        $posts = $this->_getUserSimilarPosts($user, $limit);

        /**
         * mark posts gone
         */
        if ($posts->count() <= $actualLimit) {
            $cached['limit'][$type] = 0;
            $this->updateCachedData($user->currentTokenId(), $cached);
        }

        return $posts;
    }

    /**
     * @param User $user
     * @param int $limit
     * @param bool $ignore
     * @return Collection
     */
    protected function _getUserSimilarPosts(User $user, int $limit = 10, bool $ignore = true)
    {
        return $this->getQueryBuilder()
            ->from('posts', 'p')
            ->selectRaw('`p`.*, \'similar\' as `qa_type`, `t`.`sort`')
            ->joinSub(
                $this->getUserSimilarPostSubQuery($user, $limit, $ignore),
                't',
                function (JoinClause $join) {
                    $join->on('t.post_id', '=', 'p.post_id');
                },
            )
            ->orderByDesc('sort')
            ->orderByDesc('p.post_id')
            ->get();
    }

    /**
     * lấy những câu hỏi những người tương đồng đã xem
     * (trừ những bài post mình đã xem và bài post có answer count < 3)
     * của top 5 người tương đồng mỗi ngày
     * tối đa 50 người tương đồng gần nhất
     * @param User $user
     * @param int $limit
     * @param bool $ignore
     * @return Builder
     */
    protected function getUserSimilarPostSubQuery(User $user, int $limit, bool $ignore = true)
    {
        $users = $this->getSimilarUserIds($user)
            ->pluck('user_id')
            ->toArray();

        return DB::table(
            /**
             * bài post những người tương đồng đã xem
             */
            PostViewHistory::query()
                ->from('post_view_histories', 'h')
                ->selectRaw('`h`.`post_id`, MAX(`h`.`id`) as `sort`')
                ->where(fn ($q) => $q->getModel()->setTable('h'))
                ->where(function (EloquentBuilder $query) use ($users, $user, $ignore) {
                    $query->whereIn('h.user_id', $users)
                        ->whereHas('post', function (EloquentBuilder $query) use ($user) {
                            $query->where('posts.status', 1)
                                /**
                                 * ignore blocked users
                                 */
                                ->when(blockHelper()->getBlockedIds($user->getKey()), fn ($query, $blockedIds) => $query->whereNotIn('posts.user_id', $blockedIds))

                                /**
                                 * user active
                                 */
                                ->whereHas('createdBy', function (EloquentBuilder $query) {
                                    $query->where('status', 1);
                                    $this->ignoreGenderPosts($query);
                                });
                        })
                        ->whereHas('user', function (EloquentBuilder $query) {
                            $query->where('status', 1);
                            $this->ignoreGenderPosts($query);
                        });

                    /**
                     * ignore viewed posts (viewed list)
                     */
                    if ($ignore) {
                        $token = $user->currentTokenId();
                        $this->ignoreViewedPosts($query, $token, '`h`.`post_id`');
                    }

                    /**
                     * những bài mình đã xem (chi tiết))
                     */
                    $query->whereNotExists(
                        PostViewHistory::query()
                            ->from('post_view_histories', 'u_h')
                            ->select('post_id')
                            ->where(fn ($q) => $q->getModel()->setTable('u_h'))
                            ->where('user_id', $user->getKey())
                            ->whereHas('post', function (EloquentBuilder $query) {
                                $query->where('status', 1)
                                    ->whereHas('createdBy', fn ($q) => $q->where('status', 1));
                            })
                            ->whereRaw('`u_h`.`post_id` = `h`.`post_id`'),
                    );
                })

                /**
                 * answer_count >= 3
                 */
                ->join('posts', function (JoinClause $join) {
                    $join->on('posts.post_id', '=', 'h.post_id')
                        ->where('answer_count', '>', 2);
                })
                ->groupBy('h.post_id'),
                'u',
            )
            ->select('u.*')
            ->orderByDesc('u.sort')
            ->orderByDesc('u.post_id')
            ->limit($limit);
    }

    /**
     * @param User $user
     * @param int $perDay
     * @param int $total
     * @return Builder
     */
    protected function getSimilarUserIds(User $user, int $perDay = 5, int $total = 50)
    {
        return DB::table($this->getTopSimilarUserPerDay($user, $perDay, $total), 't')
            ->select('user_id');
    }

    /**
     * @param User $user
     * @param int $perDay
     * @param int $total
     * @return Builder
     */
    protected function getTopSimilarUserPerDay(User $user, int $perDay = 5, int $total = 50)
    {
        return DB::table($this->getTopUserSimilarQuery($user), 'tmp')
            ->distinct()
            ->select('user_id')
            ->where('row_num', '<=', $perDay)
            ->limit($total);
    }

    /**
     * @param User $user
     * @param int $perDay
     * @return Builder
     */
    protected function getTopSimilarUserPerDayForStats(User $user, int $perDay = 5)
    {
        return DB::table($this->getTopUserSimilarQuery($user, true), 'tmp')
            ->where('row_num', '<=', $perDay);
    }

    /**
     * @param User $user
     * @param bool $filterDate
     * @return Builder
     */
    protected function getTopUserSimilarQuery(User $user, bool $filterDate = false)
    {
        return DB::table($this->getViewedStats($user, $filterDate), 'r')
            ->select('user_id', 'view_date', 'similar_count')
            ->selectRaw('ROW_NUMBER() OVER (PARTITION BY `view_date` ORDER BY `similar_count` desc) AS `row_num`')
            ->orderByDesc('view_date')
            ->orderByDesc('row_num');
    }

    /**
     * @param User $user
     * @param bool $filterDate
     * @return Builder
     */
    protected function getViewedStats(User $user, bool $filterDate = false)
    {
        $query = PostViewHistory::query();
        $query->getModel()->setTable('similar_posts');

        return $query->from('post_view_histories', 'similar_posts')
            ->selectRaw('`similar_posts`.`user_id`, `similar_posts`.`view_date`, COUNT(*) as `similar_count`')
            ->joinSub(
                $this->userViewedPostQuery($user),
                'user_posts',
                function (JoinClause $join) {
                    $join->on('similar_posts.post_id', '=', 'user_posts.post_id')
                        ->where('similar_posts.view_date', '=', DB::raw('`user_posts`.`view_date`'));
                },
            )
            ->where('similar_posts.user_id', '!=', $user->getKey())
            ->where(function (EloquentBuilder $query) use ($user, $filterDate) {
                if ($filterDate && ($date = Cache::get('limit_similar_date_filter_for_user_' . $user->getKey()))) {
                    $query->where('view_date', '>=', $date);
                }
            })
            ->whereHas('post', function (EloquentBuilder $query) {
                $query->where('status', 1)
                    ->whereHas('createdBy', fn ($q) => $q->where('status', 1));
            })
            ->whereHas('user', fn ($q) => $q->where('status', 1))
            ->groupBy(['similar_posts.user_id', 'similar_posts.view_date']);
    }

    /**
     * @param User $user
     * @return Builder
     */
    protected function userViewedPostQuery(User $user)
    {
        return PostViewHistory::query()
            ->select('post_id', 'view_date')
            ->where('user_id', $user->getKey())
            ->whereHas('post', function (EloquentBuilder $query) {
                $query->where('status', 1)
                    ->whereHas('createdBy', fn ($q) => $q->where('status', 1));
            });
    }
}
