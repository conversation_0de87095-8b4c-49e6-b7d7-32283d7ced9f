<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Builder;

trait HasTopicsPost
{
    /**
     * câu hỏi được gắn nhãn featured
     * @param Builder $query
     * @return void
     */
    protected function applyTopicsQuery(Builder $query)
    {
        $query->where('posts.featured', 1);

        $this->applyTopicsSort($query);
    }

    /**
     * theo thứ tự câu hỏi nhất
     * @param Builder $query
     * @param string $tableAlias
     * @return void
     */
    protected function applyTopicsSort(Builder $query, string $tableAlias = 'posts')
    {
        $query->orderByDesc($tableAlias . '.post_id');
    }
}
