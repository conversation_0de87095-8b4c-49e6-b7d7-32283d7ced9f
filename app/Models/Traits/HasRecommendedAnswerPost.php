<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

trait HasRecommendedAnswerPost
{
    /**
     * Xếp hạng theo thứ tự nhiều nhất dựa trên tổng số lượng (của các chỉ số like + coint) trong 48 giờ qua
     * @param Builder $query
     * @param int $time
     * @return void
     */
    protected function applyRecommendedAnswerQuery(Builder $query, int $time = 48)
    {
        $query->select('posts.*')
            ->addSelect('rank')
            ->joinSub(
                DB::table('post_answers', 'pa')
                    ->selectRaw('`pa`.`post_id`, MAX(`rank`) as `rank`')
                    ->joinSub(
                        DB::table('reactions', 'r')
                            ->selectRaw('`object_id`, SUM(`count`) as `rank`')
                            ->where('object_type', 'post_answer')
                            ->whereIn('r.action', ['liked', 'donated'])
                            ->where('r.created_at', '>=', now('UTC')->subHours($time)->toDateTimeString())
                            ->groupBy('object_id'),
                        'r',
                        function (JoinClause $join) {
                            $join->on('pa.answer_id', '=', 'r.object_id');
                        }
                    )
                    ->where('pa.status', 1)
                    ->where('pa.level', 1)
                    ->whereExists(function (QueryBuilder $query) {
                        $query->from('users', 'u')
                            ->where('u.status', 1)
                            ->whereRaw('`u`.`user_id`=`pa`.`user_id`');
                    })
                    ->groupBy('pa.post_id'),
                'a',
                function (JoinClause $join) {
                    $join->on("a.post_id", '=', "posts.post_id");
                }
            )
            ->where('rank', '>', 0);

        $this->applyRecommendedAnswerSort($query);
    }

    /**
     * theo thứ tự mới nhất
     * @param Builder $query
     * @param string $tableAlias
     * @return void
     */
    protected function applyRecommendedAnswerSort(Builder $query, string $tableAlias = 'posts')
    {
        $query->orderByDesc('rank')
            ->orderByDesc($tableAlias . '.post_id');
    }
}
