<?php

namespace App\Models\Traits;

use App\Helpers\CommunityHelper;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Facades\DB;

trait IgnoreViewedPost
{
    /**
     * @param Builder $query
     * @param string $token
     * @param string $parentColumn
     * @return void
     */
    protected function ignoreViewedPosts(Builder $query, string $token, string $parentColumn = '`posts`.`post_id`')
    {
        /**
         * không lấy trong cộng đồng kín mà mình chưa tham gia
         * hoặc trong cộng đồng trường mà mình không ở đó
         */
        $this->ignoreCloseCommunityPosts($query, $parentColumn);

        /**
         * không lấy trong danh sách QA posts đã xem
         */
        $query->whereNotExists(
            DB::table('qa_viewed_posts', 'v')
                ->where('v.device_token', $token)
                ->whereRaw($parentColumn . ' = `v`.`post_id`'),
        );
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function ignoreGenderPosts(Builder $query)
    {
        /** @var User $user */
        $user = auth()->user();

        /**
         * người có giới tính thì lấy những bài post của người cùng giới tính
         */
        if ($user->isMale() || $user->isFemale()) {
            $query->whereIn('gender', [0, $user->gender]);
        }

        /**
         * người chưa có giới tính hoặc giới tính khác
         * thì không được xem những bài post của người có giới tính
         */
        elseif ($user->isOtherGender() || $user->emptyGender()) {
            $query->whereIn('gender', [0, 3]);
        }
    }

    /**
     * không lấy trong cộng đồng kín mà mình chưa tham gia
     * hoặc trong cộng đồng trường mà mình không ở đó
     * @param Builder|QueryBuilder $query
     * @param string $parentColumn
     * @return void
     */
    protected function ignoreCloseCommunityPosts(Builder|QueryBuilder $query, string $parentColumn = '`posts`.`post_id`')
    {
        /** @var User $user */
        $user = auth()->user();

        $closedCommunityIds = CommunityHelper::getClosedCommunityIds($user);

        $query->whereNotExists(
            DB::table('community_posts', 'cp')
                ->whereRaw('`cp`.`post_id` = ' . $parentColumn)
                ->whereNotIn('cp.community_id', $closedCommunityIds)
                ->where('cp.community_status', 2)
        );
    }
}
