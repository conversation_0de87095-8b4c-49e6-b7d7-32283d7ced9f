<?php

namespace App\Models\Traits;

use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;

trait HasOwnerPost
{
    /**
     * câu hỏi của mình và câu hỏi có câu trả lời của mình
     * @param Builder $query
     * @param User $user
     * @return void
     */
    protected function applyOwnerQuery(Builder $query, User $user)
    {
        $userId = $user->getKey();

        $query
            ->selectRaw('GREATEST(`posts`.`created_at`, COALESCE(MAX(`pa`.`created_at`), `posts`.`created_at`)) as `sorted_created_at`')
            ->leftJoin('post_answers as pa', function (JoinClause $join) use ($userId) {
                $join->on('pa.post_id', '=', 'posts.post_id')
                    ->where('pa.status', 1)
                    ->where('pa.level', 1)
                    ->where('pa.user_id', $userId);
            })
            ->where(function (Builder $query) use ($userId) {
                $query->where('posts.user_id', $userId)
                    ->orWhere('pa.user_id', $userId);
            })
            ->groupBy('posts.post_id');

        $this->applyOwnerSort($query);
    }

    /**
     * theo thời gian mới nhất
     * @param Builder $query
     * @param string $tableAlias
     * @return void
     */
    protected function applyOwnerSort(Builder $query, string $tableAlias = 'posts')
    {
        $query->orderByDesc('sorted_created_at')
            ->orderByDesc($tableAlias . '.post_id');
    }
}
