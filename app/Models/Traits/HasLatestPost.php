<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Builder;

trait HasLatestPost
{
    /**
     * các câu trả lời mới nhất
     * @param Builder $query
     * @return void
     */
    protected function applyLatestQuery(Builder $query)
    {
        // $query->where('posts.answer_count', '>', 0);

        $this->applyLatestSort($query);
    }

    /**
     * theo thứ tự lần cuối trả lời mới nhất
     * @param Builder $query
     * @param string $tableAlias
     * @return void
     */
    protected function applyLatestSort(Builder $query, string $tableAlias = 'posts')
    {
        $query->orderByDesc($tableAlias . '.post_id');
    }
}
