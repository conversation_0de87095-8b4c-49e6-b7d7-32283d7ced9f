<?php

namespace App\Models;

use App\Models\Traits\HasCompositePrimaryKey;
use Illuminate\Database\Eloquent\Model;

/**
 * @class QAViewedPost
 * @property string $device_token
 * @property integer $post_id
 */
class QAViewedPost extends Model
{
    use HasCompositePrimaryKey;

    /**
     * @var string
     */
    public $table = 'qa_viewed_posts';

    /**
     * @var string
     */
    public $primaryKey = ['device_token', 'post_id'];

    /**
     * @var bool
     */
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'device_token',
        'post_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'device_token' => 'string',
            'post_id' => 'integer',
        ];
    }
}
