<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @class AttachedSurvey
 * @property integer $attached_id
 * @property integer $survey_id
 * @property integer $to_survey_id
 * @property string $title
 * @property Survey $survey
 * @property Survey $toSurvey
 * @property AttachedChoice[]|\Illuminate\Database\Eloquent\Collection $choices
 */
class AttachedSurvey extends Model
{
    /**
     * @var string
     */
    public $table = 'attached_surveys';

    /**
     * @var string
     */
    public $primaryKey = 'attached_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'survey_id',
        'to_survey_id',
        'title',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'survey_id' => 'integer',
            'to_survey_id' => 'integer',
            'title' => 'string',
        ];
    }

    /**
     * @return HasOne
     */
    public function survey(): HasOne
    {
        return $this->hasOne(Survey::class, 'survey_id', 'survey_id');
    }

    /**
     * @return HasOne
     */
    public function toSurvey(): HasOne
    {
        return $this->hasOne(Survey::class, 'survey_id', 'to_survey_id');
    }

    /**
     * @return HasMany
     */
    public function choices(): HasMany
    {
        return $this->hasMany(AttachedChoice::class, 'attached_id');
    }
}
