<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @class School
 * @property int $school_id
 * @property string $name
 * @property int $status
 */
class School extends Model
{
    /**
     * @var string
     */
    public $table = 'schools';

    /**
     * @var string
     */
    protected $primaryKey = 'school_id';

    /**
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'school_id' => 'integer',
            'name' => 'string',
            'status' => 'integer',
        ];
    }
}
