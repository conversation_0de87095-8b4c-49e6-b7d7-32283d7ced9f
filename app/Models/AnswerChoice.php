<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @class AnswerChoice
 * @property integer $id
 * @property integer $answer_id
 * @property integer $choice_id
 * @property QuestionChoice $choice
 */
class AnswerChoice extends Model
{
    /**
     * @var string
     */
    public $table = 'answer_choices';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'answer_id',
        'choice_id',
    ];

    /**
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'answer_id' => 'integer',
            'choice_id' => 'integer',
        ];
    }

    /**
     * @return HasOne
     */
    public function choice(): HasOne
    {
        return $this->hasOne(QuestionChoice::class, 'choice_id', 'choice_id');
    }
}
