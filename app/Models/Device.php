<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @class Device
 * @property int $id
 * @property int $user_id
 * @property int $token_id
 * @property string $name
 * @property string $token
 * @property int $status
 */
class Device extends Model
{
    /**
     * @var string
     */
    public $table = 'devices';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'token_id',
        'name',
        'token',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'integer',
            'token_id' => 'integer',
            'name' => 'string',
            'token' => 'string',
            'status' => 'integer',
        ];
    }
}
