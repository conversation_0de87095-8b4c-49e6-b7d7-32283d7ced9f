<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * @class Community
 * @property int $community_id
 * @property int $user_id
 * @property string $name
 * @property string $description
 * @property string $avatar
 * @property int $school_id
 * @property int $status
 * @property User $createdBy
 * @property Collection|CommunityUser[] $members
 */
class Community extends Model
{
    /**
     * @var string
     */
    public $table = 'communities';

    /**
     * @var string
     */
    public $primaryKey = 'community_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'name',
        'description',
        'avatar',
        'school_id',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'integer',
            'name' => 'string',
            'description' => 'string',
            'avatar' => 'string',
            'school_id' => 'integer',
            'status' => 'integer',
        ];
    }

    /**
     * @param $userId
     * @return bool
     */
    public function ownerBy($userId)
    {
        $userId = (int) $userId;

        return $userId === (int) $this->user_id;
    }

    /**
     * @param $userId
     * @return bool
     */
    public function wasNotOwnedBy($userId)
    {
        return ! $this->ownerBy($userId);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function members()
    {
        return $this->hasMany(CommunityUser::class, 'community_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function posts()
    {
        return $this->hasMany(CommunityPost::class, 'community_id');
    }

    /**
     * @return bool
     */
    public function isInActive()
    {
        return 0 === (int) $this->status;
    }

    /**
     * @return bool
     */
    public function isClosed()
    {
        return 2 === (int) $this->status;
    }

    /**
     * @param $userId
     * @return bool
     */
    public function wasBlocked($userId)
    {
        $this->loadMissing([
            'members' => function ($query) use ($userId) {
                $query->where('user_id', $userId);
            }
        ]);

        /** @var CommunityUser $record */
        if ($record = $this->members->first(fn ($c) => $c->isCommunity($this->getKey()))) {
            return $record->wasBlocked();
        }

        return false;
    }

    /**
     * @param $id
     * @return bool
     */
    public function isID($id)
    {
        $id = (int) $id;

        return $id === (int) $this->getKey();
    }

    /**
     * @param $id
     * @return bool
     */
    public function isNotID($id)
    {
        return ! $this->isID($id);
    }
}
