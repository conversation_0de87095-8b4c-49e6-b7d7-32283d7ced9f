<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @class Media
 * @property integer $media_id
 * @property integer $post_id
 * @property integer $user_id
 * @property string $image
 * @property string $content
 * @property integer $is_dark
 * @property Post $post
 * @property User $user
 */
class Media extends Model
{
    /**
     * @var string
     */
    public $table = 'media';

    /**
     * @var string
     */
    public $primaryKey = 'media_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'post_id',
        'user_id',
        'image',
        'content',
        'is_dark',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'post_id' => 'integer',
            'user_id' => 'integer',
            'image' => 'string',
            'content' => 'string',
            'is_dark' => 'integer',
        ];
    }

    /**
     * @return BelongsTo
     */
    public function post()
    {
        return $this->belongsTo(Post::class, 'post_id');
    }

    /**
     * @return BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @param $userId
     * @return bool
     */
    public function ownerBy($userId)
    {
        $userId = (int) $userId;

        return $userId === (int) $this->user_id;
    }

    /**
     * @return void
     */
    protected static function booted(): void
    {
        static::deleted(function ($media) {
            /** @var static $media */
            storage()->removeMedia($media->image);
        });
    }
}
