<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @class QuestionChoice
 * @property integer $choice_id
 * @property integer $question_id
 * @property string $content
 * @property Question $question
 */
class QuestionChoice extends Model
{
    /**
     * @var string
     */
    public $table = 'question_choices';

    /**
     * @var string
     */
    public $primaryKey = 'choice_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'question_id',
        'content',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'question_id' => 'integer',
            'content' => 'string',
        ];
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function question()
    {
        return $this->belongsTo(Question::class, 'question_id');
    }
}
