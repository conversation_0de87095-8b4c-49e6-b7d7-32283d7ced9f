<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @class PremiumFeature
 * @property integer $premium_id
 * @property string $name
 * @property string $description
 * @property string $type
 * @property integer $price
 * @property string $image
 * @property integer $is_dark
 */
class PremiumFeature extends Model
{
    /**
     * @var string
     */
    public $table = 'premium_features';

    /**
     * @var string
     */
    public $primaryKey = 'premium_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'description',
        'type',
        'price',
        'image',
        'is_dark',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'name' => 'string',
            'description' => 'string',
            'type' => 'string',
            'price' => 'integer',
            'image' => 'string',
            'is_dark' => 'integer',
        ];
    }
}
