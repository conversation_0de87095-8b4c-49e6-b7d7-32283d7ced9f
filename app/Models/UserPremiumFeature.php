<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @class PremiumFeature
 * @property integer $id
 * @property integer $user_id
 * @property integer $premium_id
 */
class UserPremiumFeature extends Model
{
    /**
     * @var string
     */
    public $table = 'user_premium_features';

    /**
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'premium_id',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'integer',
            'premium_id' => 'integer',
        ];
    }
}
