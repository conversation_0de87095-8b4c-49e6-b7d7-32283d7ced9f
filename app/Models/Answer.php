<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @class Answer
 * @property integer $answer_id
 * @property integer $user_id
 * @property integer $survey_id
 * @property integer $question_id
 * @property string $content
 * @property integer $star
 * @property integer $point
 * @property string $public
 * @property Survey $survey
 * @property Question $question
 * @property Collection|AnswerChoice[] $choices
 */
class Answer extends Model
{
    /**
     * @var string
     */
    public $table = 'answers';

    /**
     * @var string
     */
    public $primaryKey = 'answer_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'survey_id',
        'question_id',
        'content',
        'star',
        'point',
        'public',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'integer',
            'survey_id' => 'integer',
            'question_id' => 'integer',
            'content' => 'string',
            'star' => 'integer',
            'point' => 'integer',
            'public' => 'string',
        ];
    }

    /**
     * @return BelongsTo
     */
    public function survey(): BelongsTo
    {
        return $this->belongsTo(Survey::class, 'survey_id');
    }

    /**
     * @return BelongsTo
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class, 'question_id');
    }

    /**
     * @return HasMany
     */
    public function choices(): HasMany
    {
        return $this->hasMany(AnswerChoice::class, 'answer_id');
    }
}
