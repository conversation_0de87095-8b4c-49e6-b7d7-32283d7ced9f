<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @class UserFollower
 * @property integer $id
 * @property integer $user_id
 * @property integer $follow_id
 * @property string|Carbon $followed_at
 * @property integer $status
 * @property User $follower
 * @property User $following
 */
class UserFollower extends Model
{
    /**
     * @var string
     */
    public $table = 'user_followers';

    /**
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'follow_id',
        'followed_at',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'status',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'integer',
            'follow_id' => 'string',
            'followed_at' => 'datetime',
            'status' => 'integer',
        ];
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function follower()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function following()
    {
        return $this->belongsTo(User::class, 'follow_id');
    }

    /**
     * @return bool
     */
    public function isActive()
    {
        return 1 === (int) $this->status;
    }
}
