<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @class PostViewHistory
 * @property int $id
 * @property int $user_id
 * @property Carbon|string $view_date
 * @property int $post_id
 * @property int $post_user_id
 * @property Carbon|string $created_at
 * @property Post $post
 * @property User $user
 */
class PostViewHistory extends Model
{
    /**
     * @var string
     */
    public $table = 'post_view_histories';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'view_date',
        'post_id',
        'post_user_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'integer',
            'view_date' => 'date',
            'post_id' => 'integer',
            'post_user_id' => 'integer',
        ];
    }

    /**
     * @return BelongsTo
     */
    public function post()
    {
        return $this->belongsTo(Post::class, 'post_id');
    }

    /**
     * @return BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
