<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @class UserBlock
 * @property integer $id
 * @property integer $user_id
 * @property integer $blocked_id
 */
class UserBlock extends Model
{
    /**
     * @var string
     */
    public $table = 'user_blocks';

    /**
     * @var string
     */
    public $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'blocked_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'integer',
            'blocked_id' => 'integer',
        ];
    }
}
