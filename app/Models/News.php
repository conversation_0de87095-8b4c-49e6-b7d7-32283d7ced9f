<?php

namespace App\Models;

use App\Models\Traits\HasStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

/**
 * @class News
 * @property integer $news_id
 * @property integer $user_id
 * @property string $title
 * @property string $content
 * @property integer $status
 * @property Carbon $created_at
 */
class News extends Model
{
    use HasStatus;

    /**
     * @var string
     */
    public $table = 'news';

    /**
     * @var string
     */
    public $primaryKey = 'news_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'title',
        'content',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'integer',
            'title' => 'string',
            'content' => 'string',
            'status' => 'integer',
        ];
    }

    /**
     * @return void
     */
    protected static function booted(): void
    {
        /**
         * increment news_badge_count for all users when a new news is created
         */
        static::created(function ($news) {
            DB::table('users')
                ->where('status', 1)
                ->increment('news_badge_count');
        });
    }
}
