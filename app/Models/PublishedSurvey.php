<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @class PublishedSurvey
 * @property integer $survey_id
 * @property integer $sort
 * @property Survey $survey
 */
class PublishedSurvey extends Model
{
    /**
     * @var string
     */
    public $table = 'published_surveys';

    /**
     * @var string
     */
    public $primaryKey = 'survey_id';

    /**
     * @var bool
     */
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'survey_id',
        'sort',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'survey_id' => 'integer',
            'sort' => 'integer',
        ];
    }

    /**
     * @return HasOne
     */
    public function survey(): HasOne
    {
        return $this->hasOne(Survey::class, 'survey_id');
    }
}
