<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @class Answer
 * @property integer $id
 * @property integer $user_id
 * @property string $key
 * @property string $value
 */
class Setting extends Model
{
    /**
     * @var string
     */
    public $table = 'settings';

    /**
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'key',
        'value',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'integer',
            'key' => 'string',
            'value' => 'string',
        ];
    }
}
