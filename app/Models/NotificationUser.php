<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @class NotificationUser
 * @property int $id
 * @property int $notification_id
 * @property int $user_id
 * @property int $reaction_id
 * @property int $status
 * @property User $user
 * @property Carbon $updated_at
 */
class NotificationUser extends Model
{
    /**
     * @var string
     */
    public $table = 'notification_users';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'notification_id',
        'user_id',
        'reaction_id',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'notification_id' => 'integer',
            'user_id' => 'integer',
            'reaction_id' => 'integer',
            'status' => 'integer',
        ];
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return bool
     */
    public function isActive()
    {
        return 1 === (int) $this->status;
    }
}
