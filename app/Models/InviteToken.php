<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @class InviteToken
 * @property int $id
 * @property string $token
 * @property int $user_id
 * @property int $status
 * @property User $user
 */
class InviteToken extends Model
{
    /**
     * @var string
     */
    public $table = 'invite_tokens';

    /**
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'token',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'integer',
            'token' => 'string',
            'status' => 'integer',
        ];
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return bool 
     */
    public function isValid(): bool
    {
        return 0 === (int) $this->status;
    }
}
