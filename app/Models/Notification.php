<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\AsArrayObject;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Arr;

/**
 * @class Notification
 * @property int $id
 * @property string|null $object_type
 * @property int|null $object_id
 * @property int $user_id
 * @property int $reaction_user_id
 * @property int $reaction_id
 * @property int $type
 * @property string $data
 * @property int $badge_count
 * @property int $status
 * @property int $active
 * @property Post|PostAnswer|null $object
 * @property Carbon|string $created_at
 * @property Carbon|string $updated_at
 * @property Collection|NotificationUser $users
 * @property Reaction|null $reaction
 * @property User $user
 * @property User $reactionUser
 */
class Notification extends Model
{
    use HasFactory;

    /**
     * @var string
     */
    public $table = 'notifications';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'object_type',
        'object_id',
        'user_id',
        'reaction_user_id',
        'reaction_id',
        'type',
        'badge_count',
        'data',
        'status',
        'active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'object_type' => 'string',
            'object_id' => 'integer',
            'user_id' => 'integer',
            'reaction_user_id' => 'integer',
            'reaction_id' => 'integer',
            'type' => 'integer',
            'data' => AsArrayObject::class,
            'status' => 'integer',
            'badge_count' => 'integer',
            'active' => 'integer',
        ];
    }

    /**
     * @return MorphTo
     */
    public function object(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * @return HasMany
     */
    public function users()
    {
        return $this->hasMany(NotificationUser::class, 'notification_id');
    }

    /**
     * @return BelongsTo
     */
    public function reaction()
    {
        return $this->belongsTo(Reaction::class, 'reaction_id');
    }

    /**
     * @return BelongsTo
     */
    public function reactionUser()
    {
        return $this->belongsTo(User::class, 'reaction_user_id');
    }

    /**
     * @return BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return bool
     */
    public function isActive()
    {
        return 1 === (int) $this->active;
    }

    /**
     * 0 - Có câu trả lời cho câu hỏi của mình.
     * 1 - Nút "Coin" đã được nhấn vào câu trả lời của mình, và mình được tặng n đồng xu.
     * 2 - Câu trả lời của mình đã nhận được "thích" nhờ đó mình đã nhận được n điểm.
     * 3 - Câu trả lời của mình đã nhận được bình luận.
     * 4 - Kết quả công việc đã thực hiện trong tab "Công việc" được phê duyệt, và mình nhận được n đồng xu.
     * 5 - Kết quả công việc đã thực hiện trong tab "Công việc" được phê duyệt, và mình nhận được n điểm.
     * 6 - Công việc đã thực hiện trong tab "Công việc" không được phê duyệt.
     * 7 - Có công việc từ quản lý gửi đến.
     * 8 - Có người theo dõi mình.
     * 9 - Câu hỏi của mình đã được người khác ủng hộ.
     * 10 - Câu hỏi của mình chưa chọn best answer trong vòng 24h có câu trả lời
     * 11 - Câu trả lời được chọn là best answer
     * 12 - User được gợi ý trả lời câu hỏi
     * 13 - User đăng bài post mới - thông báo cho những người đang follow
     * 14 - User được giới thiệu đăng ký thành công (qua link invited)
     * 15 - User mời người khác đăng ký thành công
     * 16 - Nhận được lời mời add-friend
     * 17 - Friend accepted
     * 18 - User được add vào community
     * 19 - User được cộng thêm lượt invite, khi 1 người được invite đã trả lời đạt 10 câu hỏi
     * 20 - Câu trả lời của mình đã nhận được "thích" (gộp notification type 2)
     * 21 - User được thưởng 1 coin khi lần đầu tiên cập nhật hobby (thông tin chung)
     * 22 - User được thưởng 1 coin khi bài post được trả lời (1 lần đầu tiên)
     * 23 - User được thưởng coin khi cập nhật thông tin nghề nghiệp
     * 24 - User được thưởng coin khi cập nhật thông tin giới tính (không thưởng khi đăng ký mới)
     * @return int[]
     */
    public function getObjectIds()
    {
        $postId = 0;
        $answerId = 0;
        $parentAnswerId = 0;
        $jobId = 0;
        $childId = 0;
        $rootId = 0;
        $communityId = 0;

        $type = (int) $this->type;
        switch ($type) {
            /**
             * post
             */
            case 0:
            case 9:
            case 10:
            case 12:
            case 13:
                $postId = $this->object_id;
                if ($type === 0) {
                    $data = (array) $this->data;
                    $answerId = Arr::get($data, 'answer_id', 0);
                }

                break;

            /**
             * post answer
             */
            case 1:
            case 2:
            case 20:
            case 3:
            case 11:
                $answer = $this->object;
                $answerId = $this->object_id;
                $parentAnswerId = (int) $answer->parent_id;
                $postId = $answer->post_id;

                if ($parentAnswerId && $answer->relationLoaded('parent')) {
                    $rootId = (int) $answer->parent->parent_id;
                }

                if ($type === 3) {
                    $data = (array) $this->data;
                    $childId = Arr::get($data, 'answer_id', 0);
                }

                break;

            /**
             * community
             */
            case 18:
                $communityId = $this->object_id;
                break;

            default:
                break;
        }

        return [$postId, $answerId, $parentAnswerId, $jobId, $childId, $rootId, $communityId];
    }

    /**
     * @return string
     */
    public function getMessage(array $users = [], ?User $reactionUser = null)
    {
        if (in_array($this->type, [19, 21, 22])) {
            return __('notification.' . $this->type);
        }

        $object = $this->object;
        $postContent = '';
        $answerContent = '';
        $job = '';
        $amount = 0;
        $and = '';
        $rewardedText = '';
        $community = '';

        $userCount = ($this->getAttribute('user_count') ?? 0);

        /**
         * merge notification
         */
        if (in_array($this->type, [0, 3, 9, 20])) {
            $users = array_map(fn ($record) => $record->user, $users);

            if ($userCount >= 3) {
                if ($userCount === 3) {
                    /** @var User $latestUser */
                    $latestUser = end($users);

                    $and = __('and', [
                        'username' => ($latestUser->name ?: $latestUser->phone) ?? '',
                    ]);
                }
                else {
                    $and = __('and.other', [
                        'amount' => $userCount - 2,
                    ]);
                }

                $users = array_slice($users, 0, 2);
                $users = array_map(function ($user) {
                    /** @var User $record */
                    $user->name = $user->name ?: $user->phone;

                    return $user;
                }, $users);
            }

            $username = implode('さん、', Arr::pluck($users, 'name'));
        }
        else {
            $username = ($reactionUser?->name ?: $reactionUser?->getPhoneNumber()) ?? '';
            $amount = $this->reaction?->count ?? 0;
        }

        /**
         * update amount = badge_count
         * 0 - answer post
         * 8 - user was followed
         * 11 - answer was voted
         * 14 - User được giới thiệu đăng ký thành công (qua link invited)
         * 15 - User mời người khác đăng ký thành công
         * 17 - Friend accepted
         * 19 - User được tăng lượt mời do user được mời trả lời đạt 10 câu hỏi
         * 23 - User được thưởng coin khi cập nhật thông tin nghề nghiệp
         * 24 - User được thưởng coin khi cập nhật thông tin giới tính (không thưởng khi đăng ký mới)
         */
        $total = 1;
        $unit = 'coin';

        if (in_array($this->type, [0, 8, 11, 14, 15, 17, 19, 23, 24])) {
            $amount = $this->badge_count ?? $amount;
        }

        if ($this->type == 15) {
            $total = max(count($users), 1);
        }

        if (! is_null($object)) {
            if ($object instanceof Post) {
                $postContent = $object->content;
            }

            /**
             * post answer
             */
            elseif ($object instanceof PostAnswer) {
                $answerContent = $object->content;
                $postContent = $object->post->content;
            }

            /**
             * community
             */
            elseif ($object instanceof Community) {
                $community = $object->name;
            }
        }

        /**
         * get rewarded coin if exists
         */
        if ($this->type === 11) {
            $data = (array) $this->data;
            $rewardedCoin = (int) Arr::get($data, 'rewarded_coin', 0);

            if ($rewardedCoin > 0) {
                $rewardedText = __('rewarded', [
                    'amount' => $rewardedCoin,
                ]);
            }
        }

        return __('notification.' . $this->type, [
            'username' => $username,
            'post_content' => mb_strlen($postContent) > 8 ? mb_substr($postContent, 0, 8) . '...' : $postContent,
            'answer_content' => mb_strlen($answerContent) > 8 ? mb_substr($answerContent, 0, 8) . '...' : $answerContent,
            'job' => $job,
            'amount' => $amount,
            'unit' => __('quest.unit.' . $unit),
            'and' => $and,
            'total' => $total,
            'rewardedText' => $rewardedText,
            'community' => $community,
        ]);
    }
}
