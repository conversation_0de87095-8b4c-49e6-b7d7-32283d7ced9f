<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @class SchoolReport
 * @property int $id
 * @property int $school_id
 * @property int $user_id
 * @property int $object_id
 * @property string $object_type
 * @property string|Carbon $date
 */
class SchoolReport extends Model
{
    /**
     * @var string
     */
    public $table = 'school_reports';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'school_id',
        'user_id',
        'object_id',
        'object_type',
        'date',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'school_id' => 'integer',
            'user_id' => 'integer',
            'object_id' => 'integer',
            'object_type' => 'string',
            'date' => 'date',
        ];
    }
}
