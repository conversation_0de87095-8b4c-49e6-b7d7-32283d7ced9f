<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @class MetaData
 * @property integer $id
 * @property integer $metadata_id
 * @property string $metadata_type
 * @property string $url
 * @property string $title
 * @property string $description
 * @property string $image
 * @property integer $status
 */
class MetaData extends Model
{
    /**
     * @var string
     */
    public $table = 'ogp_metadata';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'metadata_id',
        'metadata_type',
        'url',
        'title',
        'description',
        'image',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'metadata_id' => 'integer',
            'metadata_type' => 'string',
            'url' => 'string',
            'title' => 'string',
            'description' => 'string',
            'image' => 'string',
            'status' => 'integer',
        ];
    }
}
