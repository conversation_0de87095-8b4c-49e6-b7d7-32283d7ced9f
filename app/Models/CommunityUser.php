<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @class CommunityUser
 * @property int $id
 * @property int $community_id
 * @property int $user_id
 * @property int $status
 * @property Community $community
 * @property User $user
 */
class CommunityUser extends Model
{
    /**
     * @var string
     */
    public $table = 'community_users';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'community_id',
        'user_id',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'community_id' => 'integer',
            'user_id' => 'integer',
            'status' => 'integer',
        ];
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function community()
    {
        return $this->belongsTo(Community::class, 'community_id');
    }

    /**
     * @return bool
     */
    public function isInActive()
    {
        return 0 === (int) $this->status;
    }

    /**
     * @return bool
     */
    public function wasBlocked()
    {
        return 2 === (int) $this->status;
    }

    /**
     * @param $communityId
     * @return bool
     */
    public function isCommunity($communityId)
    {
        $communityId = (int) $communityId;

        return $communityId === (int) $this->community_id;
    }
}
