<?php

namespace App\Models;

use App\Models\Traits\HasStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * class Quest
 * @property integer $id
 * @property string $title
 * @property string $description
 * @property string $image
 * @property integer $amount
 * @property string $unit
 * @property string $type
 * @property integer $sort
 * @property integer $is_dark
 * @property integer $status
 * @property Carbon $created_at
 */
class Quest extends Model
{
    use HasStatus;

    /**
     * @var string
     */
    public $table = 'quests';

    /**
     * @var string
     */
    public $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'description',
        'image',
        'amount',
        'unit',
        'type',
        'sort',
        'is_dark',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'title' => 'string',
            'description' => 'string',
            'image' => 'string',
            'amount' => 'integer',
            'unit' => 'string',
            'type' => 'string',
            'sort' => 'integer',
            'is_dark' => 'integer',
            'status' => 'integer',
        ];
    }
}
