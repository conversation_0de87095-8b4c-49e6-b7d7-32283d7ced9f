<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @class UserRelation
 * @property integer $id
 * @property integer $user_id
 * @property integer $target_id
 * @property integer $is_friend
 * @property Carbon|null $friend_at
 * @property integer $is_follow
 * @property Carbon|null $followed_at
 * @property integer $is_blocked
 * @property Carbon|null $blocked_at
 * @property User $user
 * @property User $target
 */
class UserRelation extends Model
{
    /**
     * @var string
     */
    public $table = 'user_relations';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'target_id',
        'is_friend',
        'friend_at',
        'is_follow',
        'followed_at',
        'is_blocked',
        'blocked_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'integer',
            'target_id' => 'integer',
            'is_friend' => 'integer',
            'friend_at' => 'datetime',
            'is_follow' => 'integer',
            'followed_at' => 'datetime',
            'is_blocked' => 'integer',
            'blocked_at' => 'datetime',
        ];
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function target()
    {
        return $this->belongsTo(User::class, 'target_id');
    }

    /**
     * @return bool
     */
    public function isFriend()
    {
        return 1 === (int) $this->is_friend;
    }

    /**
     * @return bool
     */
    public function isPendingFriend()
    {
        return 2 === (int) $this->is_friend;
    }

    /**
     * @return bool
     */
    public function isFollow()
    {
        return 1 === (int) $this->is_follow;
    }

    /**
     * @return bool
     */
    public function isBlocked()
    {
        return 1 === (int) $this->is_blocked;
    }
}
