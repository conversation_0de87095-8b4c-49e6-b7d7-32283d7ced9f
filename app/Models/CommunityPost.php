<?php

namespace App\Models;

use App\Events\PostedInCommunity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @class CommunityPost
 * @property int $id
 * @property int $community_id
 * @property int $post_id
 * @property int $community_status
 * @property Community $community
 * @property Post $post
 */
class CommunityPost extends Model
{
    /**
     * @var string
     */
    public $table = 'community_posts';

    /**
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'community_id',
        'post_id',
        'community_status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'community_id' => 'integer',
            'post_id' => 'integer',
            'community_status' => 'integer',
        ];
    }

    /**
     * @return BelongsTo
     */
    public function community(): BelongsTo
    {
        return $this->belongsTo(Community::class, 'community_id');
    }

    /**
     * @return BelongsTo
     */
    public function post(): BelongsTo
    {
        return $this->belongsTo(Post::class, 'post_id');
    }

    /**
     * @return bool
     */
    protected function isClosed()
    {
        return 2 === (int) $this->community_status;
    }

    /**
     * @return void
     */
    protected static function booted(): void
    {
        static::created(function ($model) {
            /**
             * post trong community thì thông báo tới các members trong community
             * @var static $model
             */
            PostedInCommunity::dispatch($model);
        });
    }
}
