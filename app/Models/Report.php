<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @class Report
 * @property int $report_id
 * @property string $reportable_type
 * @property int $reportable_id
 * @property int $user_id
 * @property int $reason
 * @property string $description
 * @property User $user
 * @property Post|PostAnswer $reportable
 */
class Report extends Model
{
    /**
     * @var string
     */
    public $table = 'reports';

    /**
     * @var string
     */
    protected $primaryKey = 'report_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'reportable_type',
        'reportable_id',
        'user_id',
        'reason',
        'description',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'reportable_type' => 'string',
            'reportable_id' => 'integer',
            'user_id' => 'integer',
            'reason' => 'integer',
            'description' => 'string',
        ];
    }

    /**
     * @return MorphTo
     */
    public function reportable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
