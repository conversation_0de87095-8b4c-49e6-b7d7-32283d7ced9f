<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @class Survey
 * @property integer $survey_id
 * @property string $title
 * @property string|null $description
 * @property int $status
 * @property Question[]|\Illuminate\Database\Eloquent\Collection $questions
 * @property AttachedSurvey[]|\Illuminate\Database\Eloquent\Collection $attachedSurveys
 * @property Answer[]|\Illuminate\Database\Eloquent\Collection $answers
 */
class Survey extends Model
{
    /**
     * @var string
     */
    public $table = 'surveys';

    /**
     * @var string
     */
    public $primaryKey = 'survey_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'description',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
        'status',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'title' => 'string',
            'description' => 'string',
            'status' => 'integer',
        ];
    }

    /**
     * @return HasMany
     */
    public function questions(): HasMany
    {
        return $this->hasMany(Question::class, 'survey_id');
    }

    /**
     * @return HasMany
     */
    public function answers(): HasMany
    {
        return $this->hasMany(Answer::class, 'survey_id');
    }

    /**
     * @return hasMany
     */
    public function attachedSurveys()
    {
        return $this->hasMany(AttachedSurvey::class, 'to_survey_id');
    }
}
