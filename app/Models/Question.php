<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @class Question
 * @property integer $question_id
 * @property integer $survey_id
 * @property string $content
 * @property integer $point
 * @property string $type
 * @property string $public
 * @property Survey $survey
 * @property QuestionChoice[]|Collection $choices
 */
class Question extends Model
{
    /**
     * @var string
     */
    public $table = 'questions';

    /**
     * @var string
     */
    public $primaryKey = 'question_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'survey_id',
        'content',
        'point',
        'type',
        'public',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'survey_id' => 'integer',
            'content' => 'string',
            'point' => 'integer',
            'type' => 'string',
            'public' => 'string',
        ];
    }

    /**
     * @return BelongsTo
     */
    public function survey(): BelongsTo
    {
        return $this->belongsTo(Survey::class, 'survey_id');
    }

    /**
     * @return HasMany
     */
    public function choices(): HasMany
    {
        return $this->hasMany(QuestionChoice::class, 'question_id');
    }
}
