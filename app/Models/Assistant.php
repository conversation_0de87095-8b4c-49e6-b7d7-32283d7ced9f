<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use <PERSON>vel\Scout\Searchable;

/**
 * @class Assistant
 * @property integer $assistant_id
 * @property string $name
 * @property string $work
 * @property string $expertise
 * @property string $description
 * @property string $language
 * @property integer $searchable
 * @property string|Carbon $created_at
 */
class Assistant extends Model
{
    use Searchable;

    /**
     * @var string
     */
    public $table = 'assistants';

    /**
     * @var string
     */
    public $primaryKey = 'assistant_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'work',
        'expertise',
        'description',
        'language',
        'searchable',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'name' => 'string',
            'work' => 'string',
            'expertise' => 'string',
            'description' => 'string',
            'language' => 'string',
            'searchable' => 'integer',
        ];
    }

    /**
     * @return array
     */
    public function toSearchableArray()
    {
        $workProfile = $this->workProfileData();

        return [
            'id' => (string) $this->getKey(),
            'work_profile' => $workProfile,
            'language' => $this->language,
            'created_at' => $this->created_at->getTimestamp(),
        ];
    }

    /**
     * @return string
     */
    public function workProfileData(): string
    {
        return implode("\n", [
            'User work profile:',
            'Area of work: ' . $this->work,
            'Area of expertise: ' . $this->expertise,
            'Current work description: ' . $this->description,
        ]);
    }

    /**
     * @return string
     */
    public function searchableAs(): string
    {
        if (config('app.env') === 'staging') {
            return 'staging_assistants';
        }

        return 'assistants';
    }
}
