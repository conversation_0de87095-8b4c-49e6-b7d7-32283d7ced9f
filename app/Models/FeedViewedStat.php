<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @class FeedViewedStat
 * @property integer $id
 * @property Carbon|string $viewed_at
 * @property integer $user_id
 */
class FeedViewedStat extends Model
{
    /**
     * @var string
     */
    public $table = 'feed_viewed_stats';

    const CREATED_AT = 'viewed_at';

    const UPDATED_AT = null;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'feed',
        'viewed_at',
        'user_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'viewed_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'feed' => 'string',
            'viewed_at' => 'datetime',
            'user_id' => 'integer',
        ];
    }
}
