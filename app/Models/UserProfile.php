<?php

namespace App\Models;

use App\Enums\ProfileType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Lara<PERSON>\Scout\Searchable;

/**
 * @class UserProfile
 * @property int $user_id
 * @property int $school_id
 * @property string $work
 * @property string $expert
 * @property string $service_in_charge
 * @property string $brief
 * @property School $school
 * @property User $user
 * @property PostAnswer[]|Collection $answers
 */
class UserProfile extends Model
{
    use Searchable;

    /**
     * @var string
     */
    public $table = 'profiles';

    /**
     * @var string
     */
    protected $primaryKey = 'user_id';

    /**
     * @var bool
     */
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'school_id',
        'work',
        'expert',
        'service_in_charge',
        'brief',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritdoc
     */
    protected function casts(): array
    {
        return [
            'user_id' => 'integer',
            'school_id' => 'integer',
            'work' => 'string',
            'expert' => 'string',
            'service_in_charge' => 'string',
            'brief' => 'string',
        ];
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function school()
    {
        return $this->belongsTo(School::class, 'school_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function answers()
    {
        return $this->hasMany(PostAnswer::class, 'user_id', 'user_id')
            ->where('status', 1)
            ->where('voted', 1)
            ->orderByDesc('answer_id');
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    protected function makeAllSearchableUsing(Builder $query): Builder
    {
        return $query->with([
            'answers' => fn ($q) => $q->where('status', 1)->where('voted', 1)->orderByDesc('answer_id'),
            'answers.post' => fn ($q) => $q->where('status', 1),
            'user',
        ]);
    }

    /**
     * @param Collection $models
     * @return Collection
     */
    public function makeSearchableUsing(Collection $models): Collection
    {
        return $models->load([
            'answers' => fn ($q) => $q->where('status', 1)->where('voted', 1)->orderByDesc('answer_id'),
            'answers.post' => fn ($q) => $q->where('status', 1),
            'user',
        ]);
    }

    /**
     * @return array
     */
    public function getVotedQuestions()
    {
        $questions = [];
        foreach ($this->answers as $answer) {
            $questions[] = $answer->post->content;
        }

        return $questions;
    }

    /**
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'id' => (string) $this->getKey(),
            'profile' => $this->profileData() . $this->votedPostContent(),
            'community_ids' => $this->getCommunities(),
            'gender' => $this->user->gender,
        ];
    }

    /**
     * @return string
     */
    protected function votedPostContent()
    {
        $instruction = [
            '',
            'アンサーしました質問一覧:',
        ];

        $questions = $this->getVotedQuestions();
        if ($questions) {
            $questions = array_slice($questions, 0, 20);
        }

        return implode("\n", array_merge($instruction, $questions));
    }

    /**
     * @return string
     */
    public function profileData(): string
    {
        $user = $this->user;

        $data = [
            'ユーザーのプロフィール',
            '・自己紹介: ' . $user->position,
            '・得意なこと、興味があること: ' . $this->brief,
        ];

        if ($this->user->profile_type !== ProfileType::EMPLOYEE->value) {
            /**
             * sinh viên
             */
            if ($this->school_id) {
                $data[] = '・学校名: ' . $this->school->name;
            }
        }

        /**
         * người đi làm
         */
        else {
            $data[] = '・担当している仕事の分野: ' . $this->work;
            $data[] = '・解決できる課題や分野: ' . $this->expert;
            $data[] = '・担当しているサービス: ' . $this->service_in_charge;
        }

        return implode("\n", $data);
    }

    /**
     * @return string
     */
    public function searchableAs(): string
    {
        return 'profiles';
    }

    /**
     * @return bool
     */
    public function shouldBeSearchable(): bool
    {
        try {
            $user = $this->user;
            if ($user->getKey() <= 3) {
                return false;
            }

            return $user->isEnabled();
        } catch (\Throwable $e) {
            chatWork()->report([
                'message' => 'Debug Profile Index: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        return false;
    }

    /**
     * @return string[]
     */
    protected function getCommunities()
    {
        $communities = $this->user->getJoinedCommunities();
        if ($communities->count() > 0) {
            $ids = $communities->pluck('community_id')->toArray();

            return array_map(function($id) {
                return (string) $id;
            }, $ids);
        }

        return [''];
    }
}
