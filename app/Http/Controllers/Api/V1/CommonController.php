<?php

namespace App\Http\Controllers\Api\V1;

use App\Actions\Common\FetchListQuest;
use App\Actions\Common\FetchListSchool;
use App\Actions\Common\ReportContent;
use App\Enums\QuestType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Common\ReportRequest;
use App\Http\Resources\Api\PostResource;
use App\Http\Resources\Api\QuestResource;
use App\Http\Resources\Api\UserResource;
use App\Models\Quest;
use App\Models\User;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class CommonController extends Controller
{
    /**
     * @param ReportRequest $request
     * @param ReportContent $processor
     * @return array
     */
    public function report(ReportRequest $request, ReportContent $processor)
    {
        $processor->execute($request);

        return apiResponse()->success();
    }

    /**
     * @param Request $request
     * @param FetchListSchool $processor
     * @return array
     */
    public function schools(Request $request, FetchListSchool $processor)
    {
        return apiResponse()->success([
            'schools' => $processor->execute($request->get('search')),
        ]);
    }

    /**
     * @param Request $request
     * @param FetchListQuest $fetcher
     * @return array
     * @throws BindingResolutionException
     */
    public function quests(Request $request, FetchListQuest $fetcher)
    {
        $quests = $fetcher->execute();

        /** @var User $user */
        $user = $request->user();
        $user->loadCount([
            'friends as friends_count' => function ($query) {
                $query->whereHas('target', fn ($q) => $q->where('users.status', 1));
            },
            'posts as has_answered' => function ($query) use ($user) {
                $query->where('status', 1)
                    ->whereHas('answers', function ($query) use ($user) {
                        $query->where('status', 1)
                            ->where('level', 1)
                            ->where('user_id', '!=', $user->getKey())
                            ->whereHas('createdBy', fn ($q) => $q->where('status', 1));
                    });
            },
            'profile',
        ]);

        $profile = $user->profile;

        $stats = [
            QuestType::ANSWERED->value => $user->getAttribute('has_answered') > 0,
            QuestType::INVITED->value => $user->invited_user_count > 0,
            QuestType::HOBBY->value => ! empty($profile?->getAttribute('brief')),
            QuestType::CAREER->value => ! empty($user->profile_type),
            QuestType::GENDER->value => ! $user->emptyGender(),
        ];

        /**
         * does not return completed quests
         */
        $quests = array_filter($quests, function ($quest) use ($stats) {
            $type = Arr::get($quest, 'type');

            return ! $stats[$type] ?? false;
        });

        $quests = collect(array_values($quests));

        $limit = 100;
        $rewardPosts = postRepository()->getRewardPosts($limit + 1);
        if ($hasNextPage = $rewardPosts->count() > $limit) {
            $rewardPosts = $rewardPosts->pop();
        }

        return apiResponse()->success([
            'quests' => $quests->transform(fn ($quest) => QuestResource::make($quest, $stats)->toArray($request)),
            'posts' => $rewardPosts->transform(fn ($post) => PostResource::make($post)->toArray($request)),
            'hasNextPage' => (int) $hasNextPage,
            'user' => UserResource::make($user)->showProfile()->toArray($request),
        ]);
    }
}
