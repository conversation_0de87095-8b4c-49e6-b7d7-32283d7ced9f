<?php

namespace App\Http\Controllers\Api\V1;

use App\Actions\Post\DeletePostAnswer;
use App\Actions\Post\FetchListPostAnswer;
use App\Actions\Post\FetchListPostAnswerByParent;
use App\Actions\Post\MutePostAnswerNotification;
use App\Actions\Post\PinPostAnswer;
use App\Actions\Post\StorePostAnswer;
use App\Actions\Post\UnPinPostAnswer;
use App\Actions\Post\ViewedPostAnswer;
use App\Actions\Reaction\CancelDonatePostAnswer;
use App\Actions\Reaction\DonatePostAnswer;
use App\Actions\Reaction\LikePostAnswer;
use App\Actions\Reaction\UnlikePostAnswer;
use App\Actions\Reaction\VoteBest;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HasPagination;
use App\Http\Controllers\Traits\HasPostOrPostAnswerResponse;
use App\Http\Requests\Api\Post\CancelDonatePostAnswerRequest;
use App\Http\Requests\Api\Post\DeletePostAnswerRequest;
use App\Http\Requests\Api\Post\DetailPostAnswerRequest;
use App\Http\Requests\Api\Post\DonatePostAnswerRequest;
use App\Http\Requests\Api\Post\LikePostAnswerRequest;
use App\Http\Requests\Api\Post\ListPostAnswerByParentRequest;
use App\Http\Requests\Api\Post\ListPostAnswerRequest;
use App\Http\Requests\Api\Post\MutePostAnswerNotificationRequest;
use App\Http\Requests\Api\Post\PinPostAnswerRequest;
use App\Http\Requests\Api\Post\StorePostAnswerRequest;
use App\Http\Requests\Api\Post\UnlikePostAnswerRequest;
use App\Http\Requests\Api\Post\UnPinPostAnswerRequest;
use App\Http\Requests\Api\Post\ViewedPostAnswerRequest;
use App\Http\Requests\Api\Post\VoteBestPostAnswerRequest;
use App\Http\Resources\Api\PostAnswerResource;
use App\Http\Resources\Api\PostResource;
use App\Models\Post;
use App\Models\PostAnswer;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class PostAnswerController extends Controller
{
    use HasPagination;
    use HasPostOrPostAnswerResponse;

    /**
     * @param StorePostAnswerRequest $request
     * @param StorePostAnswer $saver
     * @return array
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(StorePostAnswerRequest $request, StorePostAnswer $saver): array
    {
        $postAnswer = $saver->execute($request);

        $postAnswer->load([
            'metadata' => fn ($q) => $q->where('status', 1),
        ]);

        return apiResponse()->success([
            'answer' => PostAnswerResource::make($postAnswer, $request->user())->toArray($request),
        ]);
    }

    /**
     * @param ListPostAnswerRequest $request
     * @param FetchListPostAnswer $fetcher
     * @return array
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function list(ListPostAnswerRequest $request, FetchListPostAnswer $fetcher)
    {
        list ($page, $limit) = $this->parsePagination($request, 1000);
        $offset = ($page - 1) * $limit;

        $orderDirection = 'desc';
        $objectId = null;
        $userId = (int) $request->get('user_id');
        if ($objectType = $request->get('object_type')) {
            $userId = null;
            $objectId = $request->get('object_id');

            if ($objectType === 'answer') {
                $orderDirection = 'asc';
            }
        }

        $payload = [
            'orderBy' => 'answer_id',
            'orderDirection' => $orderDirection,
            'fetchTotal' => false,
            'object_type' => $objectType,
            'object_id' => $objectId,
            'userId' => $userId,
            'status' => 1,
        ];

        /** @var Collection $answers */
        $answers = $fetcher->execute($payload, $limit + 1, $page, $offset)->getCollection();
        if ($hasNextPage = $answers->count() > $limit) {
            $answers->pop();
        }

        /**
         * load answer post
         */
        if ($userId) {
            $answers->loadMissing([
                'post',
            ]);
        }

        $authID = $request->user()->getKey();
        $this->loadPostAnswerMissing($answers, $authID);

        return apiResponse()->success([
            'parent' => when($request->getParentObject(), function ($parent) use ($request, $authID) {
                /** @var Post|PostAnswer $parent */
                $type = $parent instanceof Post ? 'post' : 'answer';

                if ($type === 'post') {
                    $this->loadPostAnswerCount($parent, $authID);
                }

                return [
                    'type' => $type,
                    'data' => $type === 'post' ? PostResource::make($parent)->toArray($request) : PostAnswerResource::make($parent)->toArray($request),
                    'parent' => ($type === 'answer' && $parent->parent_id) ? [
                        'type' => 'answer',
                        'data' => PostAnswerResource::make($parent->parent)->toArray($request),
                    ] : null,
                ];
            }),
            'posts' => [],
            'answers' => $answers->transform(function($answer) use ($request) {
                return PostAnswerResource::make($answer)->toArray($request);
            }),
            'hasNextPage' => (int) $hasNextPage,
        ]);
    }

    /**
     * @param ListPostAnswerByParentRequest $request
     * @param FetchListPostAnswerByParent $processor
     * @return array
     */
    public function listByParent(ListPostAnswerByParentRequest $request, FetchListPostAnswerByParent $processor)
    {
        /** @var User $user */
        $user = $request->user();
        $userId = $user->getKey();

        $blockedIds = blockHelper()->getBlockedIds($userId);

        $parentAnswer = $request->getRootPostAnswer();
        $parentAnswer->loadMissing([
            'reactions' => fn ($q) => $q->where('user_id', $userId),
        ]);

        $parentAnswer->loadCount([
            'children' => function (Builder $query) use ($blockedIds) {
                $query->where('status', 1)
                    ->whereNotIn('user_id', $blockedIds)
                    ->whereHas('createdBy', fn ($q) => $q->where('status', 1));
            }
        ]);

        $answers = $processor->execute($parentAnswer, $blockedIds);

        $this->loadPostAnswerMissing($answers, $userId, true);

        $post = $parentAnswer->post;
        $post->loadMissing([
            'communityPost',
            'communityPost.community',
        ]);

        $this->loadPostAnswerCount($post, $userId);
        $this->loadAnswerExistedWithoutUser($post, $userId);

        return apiResponse()->success([
            'answer' => PostAnswerResource::make($parentAnswer)->toArray($request),
            'post' => PostResource::make($post)->toArray($request),
            'children' => $answers->transform(function($answer) use ($request) {
                return PostAnswerResource::make($answer)->toArray($request);
            }),
        ]);
    }

    /**
     * @param LikePostAnswerRequest $request
     * @param LikePostAnswer $like
     * @return array
     */
    public function like(LikePostAnswerRequest $request, LikePostAnswer $like)
    {
        $answer = $request->getPostAnswer();
        $user = $request->user();
        $userId = $user->getKey();

        $like->execute($user, $answer, (int) $request->post('count'));

        /**
         * load relations
         */
        $answer->load([
            'reactions' => function (MorphMany $query) use ($userId) {
                $query->where('user_id', $userId);
            },
        ]);

        return apiResponse()->success([
            'answer' => PostAnswerResource::make($answer)->toArray($request),
        ]);
    }

    /**
     * @param UnlikePostAnswerRequest $request
     * @param UnlikePostAnswer $unlike
     * @return array
     */
    public function unlike(UnlikePostAnswerRequest $request, UnlikePostAnswer $unlike)
    {
        return apiResponse()->success(
            $unlike->execute($request->user(), $request->getPostAnswer())
        );
    }

    /**
     * @param DonatePostAnswerRequest $request
     * @param DonatePostAnswer $action
     * @return array
     */
    public function donate(DonatePostAnswerRequest $request, DonatePostAnswer $action)
    {
        $answer = $request->getPostAnswer();
        $user = $request->user();
        $userId = $user->getKey();

        $action->execute($user, $answer, (int) $request->post('count'));

        $answer->refresh();

        /**
         * load relations
         */
        $answer->load([
            'reactions' => function (MorphMany $query) use ($userId) {
                $query->where('user_id', $userId);
            },
        ]);

        return apiResponse()->success([
            'answer' => PostAnswerResource::make($answer)->toArray($request),
        ]);
    }

    /**
     * @param CancelDonatePostAnswerRequest $request
     * @param CancelDonatePostAnswer $action
     * @return array
     */
    public function cancelDonate(CancelDonatePostAnswerRequest $request, CancelDonatePostAnswer $action)
    {
        return apiResponse()->success(
            $action->execute($request->user(), $request->getReaction(), $request->getPostAnswer())
        );
    }

    /**
     * @param VoteBestPostAnswerRequest $request
     * @param VoteBest $vote
     * @return array
     */
    public function voteBest(VoteBestPostAnswerRequest $request, VoteBest $vote)
    {
        $vote->execute($request->getPostAnswer());

        return apiResponse()->success();
    }

    /**
     * @param DetailPostAnswerRequest $request
     * @return array
     */
    public function detail(DetailPostAnswerRequest $request)
    {
        $answer = $request->getPostAnswer();
        $userId = $request->user()->getKey();

        $answer->loadMissing([
            'metadata' => fn ($q) => $q->where('status', 1),
            'reactions' => function (MorphMany $query) use ($userId) {
                $query->where('user_id', $userId);
            },
        ]);

        return apiResponse()->success([
            'answer' => PostAnswerResource::make($answer)->toArray($request),
        ]);
    }

    /**
     * @param DeletePostAnswerRequest $request
     * @param DeletePostAnswer $processor
     * @return array
     */
    public function delete(DeletePostAnswerRequest $request, DeletePostAnswer $processor)
    {
        $processor->execute($request->getPostAnswer());

        return apiResponse()->success();
    }

    /**
     * @param MutePostAnswerNotificationRequest $request
     * @param MutePostAnswerNotification $processor
     * @return array
     */
    public function muteNotification(MutePostAnswerNotificationRequest $request, MutePostAnswerNotification $processor)
    {
        $processor->execute($request);

        return apiResponse()->success();
    }

    /**
     * @param PinPostAnswerRequest $request
     * @param PinPostAnswer $processor
     * @return array
     */
    public function pin(PinPostAnswerRequest $request, PinPostAnswer $processor)
    {
        $processor->execute($request->getPostAnswer());

        return apiResponse()->success();
    }

    /**
     * @param UnPinPostAnswerRequest $request
     * @param UnPinPostAnswer $processor
     * @return array
     */
    public function unpin(UnPinPostAnswerRequest $request, UnPinPostAnswer $processor)
    {
        $processor->execute($request->getPostAnswer());

        return apiResponse()->success();
    }

    /**
     * @param ViewedPostAnswerRequest $request
     * @param ViewedPostAnswer $processor
     * @return array
     */
    public function viewed(ViewedPostAnswerRequest $request, ViewedPostAnswer $processor)
    {
        $processor->execute($request->user(), $request->getAnswerIds());

        return apiResponse()->success();
    }
}
