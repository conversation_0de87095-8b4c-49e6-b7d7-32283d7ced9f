<?php

namespace App\Http\Controllers\Api\V1;

use App\Actions\News\FetchListNews;
use App\Events\ListNewsFetched;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HasPagination;
use App\Http\Resources\Api\NewsResource;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    use HasPagination;

    /**
     * @param Request $request
     * @param FetchListNews $processor
     * @return array
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function list(Request $request, FetchListNews $processor)
    {
        list ($page, $limit) = $this->parsePagination($request, 100);
        $offset = ($page - 1) * $limit;

        $payload = [
            'status' => 'enable',
            'orderBy' => 'news_id',
            'orderDirection' => 'desc',
            'fetchTotal' => false,
        ];

        $items = $processor->execute($payload, $limit + 1, $page, $offset)->getCollection();
        if ($hasNextPage = $items->count() > $limit) {
            $items->pop();
        }

        /**
         * reset news_badge_count
         */
        ListNewsFetched::dispatch($request->user());

        return apiResponse()->success([
            'items' => $items->transform(fn($news) => NewsResource::make($news)->toArray($request)),
            'hasNextPage' => (int) $hasNextPage,
        ]);
    }
}
