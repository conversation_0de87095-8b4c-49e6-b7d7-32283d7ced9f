<?php

namespace App\Http\Controllers\Api\V1;

use App\Actions\Survey\AnswerSurveyQuestion;
use App\Actions\Survey\ApiFetchListSurvey;
use App\Events\UserAnsweredSurveyQuestion;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Survey\AnswerRequest;
use App\Http\Requests\Api\Survey\ListSurveyRequest;
use Illuminate\Contracts\Container\BindingResolutionException;

class SurveyController extends Controller
{
    /**
     * @param ListSurveyRequest $request
     * @param ApiFetchListSurvey $fetcher
     * @return array
     */
    public function listSurveys(ListSurveyRequest $request, ApiFetchListSurvey $fetcher)
    {
        return apiResponse()->success([
            'surveys' => $fetcher->execute($request),
        ]);
    }

    /**
     * @param AnswerRequest $request
     * @param AnswerSurveyQuestion $creator
     * @return array
     * @throws BindingResolutionException
     */
    public function answer(AnswerRequest $request, AnswerSurveyQuestion $creator)
    {
        $answer = $creator->create($request);

        UserAnsweredSurveyQuestion::dispatch($request->user(), $answer);

        return apiResponse()->success();
    }
}
