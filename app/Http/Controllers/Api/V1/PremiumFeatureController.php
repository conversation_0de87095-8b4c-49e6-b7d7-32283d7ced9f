<?php

namespace App\Http\Controllers\Api\V1;

use App\Actions\PremiumFeature\ActivatePremiumFeature;
use App\Actions\PremiumFeature\FetchListPremiumFeature;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\PremiumFeature\ActivatePremiumFeatureRequest;
use App\Http\Resources\Api\PremiumFeatureResource;
use Illuminate\Http\Request;

class PremiumFeatureController extends Controller
{
    /**
     * @param FetchListPremiumFeature $fetcher
     * @return array
     */
    public function list(FetchListPremiumFeature $fetcher)
    {
        $items = $fetcher->execute();

        return apiResponse()->success([
            'features' => PremiumFeatureResource::collection($items),
        ]);
    }

    /**
     * @param ActivatePremiumFeatureRequest $request
     * @param ActivatePremiumFeature $processor
     * @return array
     */
    public function activeFeature(ActivatePremiumFeatureRequest $request, ActivatePremiumFeature $processor)
    {
        $processor->execute($request->user(), $request->getPremiumFeature());

        return apiResponse()->success();
    }
}
