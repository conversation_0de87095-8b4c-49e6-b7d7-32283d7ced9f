<?php

namespace App\Http\Controllers\Api\V1;

use App\Actions\Community\AddMember;
use App\Actions\Community\DeleteCommunity;
use App\Actions\Community\FetchListCommunity;
use App\Actions\Community\FetchListMember;
use App\Actions\Community\LeaveCommunity;
use App\Actions\Community\FetchListPost;
use App\Actions\Community\RemoveMember;
use App\Actions\Community\StoreCommunity;
use App\Events\CommunityUserJoined;
use App\Events\FeedViewed;
use App\Events\UpdateSearchableProfile;
use App\Events\UserWasRemovedFromCommunity;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HasPagination;
use App\Http\Controllers\Traits\HasPostOrPostAnswerResponse;
use App\Http\Requests\Api\Community\CommunityAddMemberRequest;
use App\Http\Requests\Api\Community\CommunityJoinRequest;
use App\Http\Requests\Api\Community\CommunityLeaveRequest;
use App\Http\Requests\Api\Community\CommunityRemoveMemberRequest;
use App\Http\Requests\Api\Community\DeleteCommunityRequest;
use App\Http\Requests\Api\Community\DetailCommunityRequest;
use App\Http\Requests\Api\Community\StoreCommunityRequest;
use App\Http\Requests\Api\User\FetchProfileRequest;
use App\Http\Resources\Api\CommunityResource;
use App\Http\Resources\Api\PostResource;
use App\Http\Resources\Api\UserResource;
use App\Models\Community;
use App\Models\CommunityUser;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class CommunityController extends Controller
{
    use HasPagination;
    use HasPostOrPostAnswerResponse;

    /**
     * @param StoreCommunityRequest $request
     * @param StoreCommunity $processor
     * @return array
     */
    public function store(StoreCommunityRequest $request, StoreCommunity $processor)
    {
        $community = $request->getCommunity();

        $processor->execute($community, [
            'name' => $request->post('name'),
            'description' => (string) $request->post('description'),
            'avatar' => $request->file('avatar'),
            'closed' => $request->post('closed'),
        ]);

        return apiResponse()->success([
            'community' => CommunityResource::make($community)->loadRelation()->toArray($request),
        ]);
    }

    /**
     * @param DetailCommunityRequest $request
     * @return array
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function detail(DetailCommunityRequest $request)
    {
        $page = 1;
        $limit = 100;
        $offset = 0;

        $community = $request->getCommunity();
        $user = $request->user();
        [$posts, $hasNextPage, $isCommunityAdmin] = $this->fetchCommunityPosts($user, $community, $page, $limit, $offset);

        $blockedIds = [];
        if ($isCommunityAdmin) {
            $blockedIds = blockHelper()->getBlockedIds($user->getKey());
        }

        FeedViewed::dispatch($request->user(), 'community_' . $community->getKey());

        return apiResponse()->success([
            'community' => CommunityResource::make($request->getCommunity())->loadRelation()->toArray($request),
            'posts' => $posts->transform(fn ($post) => PostResource::make($post)->withBlockedUsers($blockedIds)->toArray($request)),
            'hasNextPage' => (int) $hasNextPage,
        ]);
    }

    /**
     * @param CommunityAddMemberRequest $request
     * @param AddMember $processor
     * @return array
     */
    public function addMember(CommunityAddMemberRequest $request, AddMember $processor)
    {
        $communityUser = $request->getCommunityUser();
        $community = $request->getCommunity();

        $success = $processor->execute($community, $communityUser->getKey());

        CommunityUserJoined::dispatch($community, $communityUser, $request->user()->getKey());

        return apiResponse()->success([
            'success' => (int) $success,
        ]);
    }

    /**
     * @param CommunityRemoveMemberRequest $request
     * @param RemoveMember $processor
     * @return array
     */
    public function removeMember(CommunityRemoveMemberRequest $request, RemoveMember $processor)
    {
        $community = $request->getCommunity();
        $removedUserId = (int) $request->post('user_id');

        $processor->execute($community, $removedUserId);

        /**
         * remove type 18 notification
         */
        UserWasRemovedFromCommunity::dispatch($community, $removedUserId);

        return apiResponse()->success();
    }

    /**
     * @param CommunityJoinRequest $request
     * @param AddMember $processor
     * @return array
     */
    public function joinCommunity(CommunityJoinRequest $request, AddMember $processor)
    {
        $userId = $request->user()->getKey();
        $processor->execute($request->getCommunity(), $userId);

        /**
         * rebuild profile search data
         */
        UpdateSearchableProfile::dispatch($userId);

        return apiResponse()->success();
    }

    /**
     * @param CommunityLeaveRequest $request
     * @param LeaveCommunity $processor
     * @return array
     */
    public function leaveCommunity(CommunityLeaveRequest $request, LeaveCommunity $processor)
    {
        $processor->execute($request->getCommunity(), $request->getCommunityUser());

        /**
         * rebuild profile search data
         */
        UpdateSearchableProfile::dispatch($request->user()->getKey());

        return apiResponse()->success();
    }

    /**
     * @param DetailCommunityRequest $request
     * @return array
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function posts(DetailCommunityRequest $request)
    {
        [$page, $limit] = $this->parsePagination($request, 100);
        $offset = ($page - 1) * $limit;

        $user = $request->user();
        [$posts, $hasNextPage, $isCommunityAdmin] = $this->fetchCommunityPosts($user, $request->getCommunity(), $page, $limit, $offset);

        $blockedIds = [];
        if ($isCommunityAdmin) {
            $blockedIds = blockHelper()->getBlockedIds($user->getKey());
        }

        return apiResponse()->success([
            'posts' => $posts->transform(fn ($post) => PostResource::make($post)->withBlockedUsers($blockedIds)->toArray($request)),
            'hasNextPage' => (int) $hasNextPage,
        ]);
    }

    /**
     * @param User $user
     * @param Community $community
     * @param int $page
     * @param int $limit
     * @param int $offset
     * @return array
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    protected function fetchCommunityPosts(User $user, Community $community, int $page = 1, int $limit = 100, int $offset = 0)
    {
        $userId = $user->getKey();
        $isCommunityAdmin = $community->ownerBy($userId);

        $payload = [
            'status' => 1,
            'fetchTotal' => false,
            'orderBy' => 'sort_answered_at',
            'orderDirection' => 'desc',
            'communityId' => $community->getKey(),
            'checkBlocked' => ! $isCommunityAdmin,
        ];

        /** @var FetchListPost $processor */
        $processor = app(FetchListPost::class);

        /** @var Collection $posts */
        $posts = $processor->execute($payload, $limit + 1, $page, $offset)->getCollection();
        if ($hasNextPage = $posts->count() > $limit) {
            $posts->pop();
        }

        $this->loadPostMissing($posts, $userId);
        $this->loadUserRelationshipAnswered($posts, $userId);

        return [$posts, $hasNextPage, $isCommunityAdmin];
    }

    /**
     * @param DetailCommunityRequest $request
     * @param FetchListMember $processor
     * @return array
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function members(DetailCommunityRequest $request, FetchListMember $processor)
    {
        [$page, $limit] = $this->parsePagination($request, 100);
        $offset = ($page - 1) * $limit;
        $community = $request->getCommunity();

        $payload = [
            'communityId' => $community->getKey(),
            'status' => 1,
            'orderBy' => 'updated_at',
            'orderDirection' => 'desc',
        ];

        /** @var Collection|CommunityUser[] $members */
        $members = $processor->execute($payload, $limit + 1, $page, $offset);

        if ($hasNextPage = $members->count() > $limit) {
            $members = $members->pop();
        }

        $members->load([
            'user' => fn ($q) => $q->where('status', 1),
        ]);

        $users = [];
        foreach ($members as $member) {
            $users[] = $member->user;
        }

        return apiResponse()->success([
            'owner_id' => (int) $community->user_id,
            'users' => UserResource::collection($users)->toArray($request),
            'hasNextPage' => (int) $hasNextPage,
        ]);
    }

    /**
     * @param FetchProfileRequest $request
     * @param FetchListCommunity $processor
     * @return array
     */
    public function listCommunities(FetchProfileRequest $request, FetchListCommunity $processor)
    {
        $currentUser = $request->getCurrentUser();
        $communities = $processor->execute($currentUser);

        $authUser = $request->user();
        $isSameUser = (int) $authUser->isID($currentUser->getKey());

        if (! $isSameUser) {
            $authUser->load('communityRelations');
        }

        return apiResponse()->success([
            'communities' => $communities->transform(fn ($community) => CommunityResource::make($community)->markAsJoined($isSameUser)->toArray($request)),
        ]);
    }

    /**
     * @param DeleteCommunityRequest $request
     * @param DeleteCommunity $processor
     * @return array
     */
    public function terminate(DeleteCommunityRequest $request, DeleteCommunity $processor)
    {
        $processor->execute($request->getCommunity());

        return apiResponse()->success();
    }
}
