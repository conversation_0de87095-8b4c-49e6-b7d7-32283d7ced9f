<?php

namespace App\Http\Controllers\Api\V1;

use App\Actions\Post\DeletePost;
use App\Actions\Post\FetchFeedPost;
use App\Actions\Post\FetchQAPost;
use App\Actions\Post\MutePostNotification;
use App\Actions\Post\StorePost;
use App\Actions\Reaction\LikePost;
use App\Actions\Reaction\UnlikePost;
use App\Enums\FeedType;
use App\Enums\PostType;
use App\Events\FeedViewed;
use App\Events\PostDetailViewed;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HasPagination;
use App\Http\Controllers\Traits\HasPostOrPostAnswerResponse;
use App\Http\Requests\Api\Post\DeletePostRequest;
use App\Http\Requests\Api\Post\DetailPostRequest;
use App\Http\Requests\Api\Post\LikePostRequest;
use App\Http\Requests\Api\Post\ListFeedPostRequest;
use App\Http\Requests\Api\Post\ListQAPostRequest;
use App\Http\Requests\Api\Post\MutePostNotificationRequest;
use App\Http\Requests\Api\Post\StorePostRequest;
use App\Http\Requests\Api\Post\UnlikePostRequest;
use App\Http\Resources\Api\PostResource;
use App\Models\Community;
use App\Models\Post;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class PostController extends Controller
{
    use HasPagination;
    use HasPostOrPostAnswerResponse;

    /**
     * @param StorePostRequest $request
     * @param StorePost $saver
     * @return array
     */
    public function store(StorePostRequest $request, StorePost $saver): array
    {
        $post = $saver->execute($request);
        $user = $request->user();

        $post->load('media');
        $this->loadPostDetailRelations($post, $user->getKey());

        return apiResponse()->success([
            'post' => PostResource::make($post, $user)->toArray($request),
        ]);
    }

    /**
     * @param DetailPostRequest $request
     * @return array
     */
    public function detail(DetailPostRequest $request)
    {
        $post = $request->getPost();
        $user = $request->user();
        $userId = $user->getKey();

        if ($communityID = $post->communityPost?->community_id) {
            $user->loadMissing([
                'communityRelations' => function (HasMany $query) use ($communityID) {
                    $query->where('community_id', $communityID);
                },
            ]);
        }

        $post->load('media');
        $this->loadPostDetailRelations($post, $userId);

        $answers = $post->answers;
        $this->loadPostAnswerChildrenCount($answers, $userId);

        PostDetailViewed::dispatch($user, $post);

        $blockedUserIds = blockHelper()->getBlockedIds($userId);

        return apiResponse()->success([
            'post' => PostResource::make($post)->withBlockedUsers($blockedUserIds)->toArray($request),
        ]);
    }

    /**
     * @param Request $request
     * @param FetchFeedPost $fetcher
     * @return array
     */
    public function feeds(Request $request, FetchFeedPost $fetcher)
    {
        /** @var User $user */
        $user = $request->user();
        $userId = $user->getKey();
        $limit = 3;

        $types = $this->getSortedFeeds($user);
        $feedData = [];

        $posts = Collection::make();
        foreach ($types as $type => $label) {
            /** @var Collection|Post[] $items */
            $items = $fetcher->execute($user, $type, ($type === FeedType::TOPICS->value) ? 10 : $limit);

            foreach ($items as $item) {
                $item->forceFill([
                    'extra_type' => $type,
                ]);

                $posts->add($item);
            }
        }

        $user->loadMissing([
            'communityRelations',
        ]);

        $this->loadPostMissing($posts, $userId);
        $this->loadUserRelationshipAnswered($posts, $userId);

        foreach ($types as $type => $label) {
            $items = $posts->filter(function ($post) use ($type) {
                return $post->getAttribute('extra_type') === $type;
            });

            $feedData[] = [
                'type' => $type,
                'label' => $label,
                'posts' => $items->count() ? array_values($items->transform($this->transform($request))->toArray()) : [],
            ];
        }

        return apiResponse()->success([
            'feeds' => $feedData,
        ]);
    }

    /**
     * @param ListQAPostRequest $request
     * @param FetchQAPost $fetcher
     * @return array
     */
    public function qaPosts(ListQAPostRequest $request, FetchQAPost $fetcher)
    {
        $next = (int) $request->get('next');
        $totalPage = 3;

        /** @var User $user */
        $user = $request->user();
        [$posts, $hasNextPage] = $fetcher->execute($request->user(), $next, $totalPage);

        $user->loadMissing([
            'communityRelations',
        ]);

        /**
         * load posts missing relations
         */
        $userId = $user->getKey();
        $this->loadPostMissing($posts, $userId);
        $this->loadUserRelationshipAnswered($posts, $userId);

        return apiResponse()->success([
            'posts' => $posts->transform($this->transform($request)),
            'hasNextPage' => (int) $hasNextPage,
        ]);
    }

    /**
     * @param ListFeedPostRequest $request
     * @param FetchFeedPost $fetcher
     * @return array
     */
    public function feedPosts(ListFeedPostRequest $request, FetchFeedPost $fetcher)
    {
        [$page, $limit] = $this->parsePagination($request);
        $offset = ($page - 1) * $limit;

        $type = $request->get('type');

        /** @var User $user */
        $user = $request->user();
        $posts = $fetcher->execute($user, $type, $limit + 1, $offset);

        /** @var Collection|Post[] $posts */
        if ($hasNextPage = $posts->count() > $limit) {
            $posts->pop();
        }

        /**
         * prepend selected post at the beginning
         */
        if (($post = $request->getPost()) && ! $posts->contains($post->getKey())) {
            $posts->prepend($post);
        }

        $user->loadMissing([
            'communityRelations',
        ]);

        /**
         * load posts missing relations
         */
        $userId = $user->getKey();
        $this->loadPostMissing($posts, $userId);
        $this->loadUserRelationshipAnswered($posts, $userId);

        if ($type !== FeedType::TOPICS->value) {
            FeedViewed::dispatch($user, $type);
        }

        return apiResponse()->success([
            'posts' => $posts->transform($this->transform($request)),
            'hasNextPage' => (int) $hasNextPage,
        ]);
    }

    /**
     * @param Request $request
     * @return \Closure
     */
    protected function transform(Request $request)
    {
        return fn ($post) => PostResource::make($post)->toArray($request);
    }

    /**
     * @param LikePostRequest $request
     * @param LikePost $like
     * @return array
     */
    public function like(LikePostRequest $request, LikePost $like)
    {
        $like->execute($request->user(), $request->getPost());

        return apiResponse()->success();
    }

    /**
     * @param UnlikePostRequest $request
     * @param UnlikePost $unlike
     * @return array
     */
    public function unlike(UnlikePostRequest $request, UnlikePost $unlike)
    {
        $unlike->execute($request->user(), $request->getReaction(), $request->getPost());

        return apiResponse()->success();
    }

    /**
     * @param MutePostNotificationRequest $request
     * @param MutePostNotification $processor
     * @return array
     */
    public function muteNotification(MutePostNotificationRequest $request, MutePostNotification $processor)
    {
        $processor->execute($request);

        return apiResponse()->success();
    }

    /**
     * @param DeletePostRequest $request
     * @param DeletePost $processor
     * @return array
     */
    public function delete(DeletePostRequest $request, DeletePost $processor)
    {
        $processor->execute($request->getPost());

        return apiResponse()->success();
    }

    /**
     * @param Request $request
     * @return array
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function fetchRewardPosts(Request $request)
    {
        $limit = 100;
        [$page] = $this->parsePagination($request);

        $posts = postRepository()->getRewardPosts($limit + 1, $page);

        if ($hasNextPage = $posts->count() > $limit) {
            $posts = $posts->pop();
        }

        return apiResponse()->success([
            'posts' => $posts->transform($this->transform($request)),
            'hasNextPage' => (int) $hasNextPage,
        ]);
    }

    /**
     * @param User $user
     * @return array
     */
    protected function getSortedFeeds(User $user)
    {
        $defaultTypes = [
            FeedType::FRIEND->value => FeedType::FRIEND->getLabel(),
            FeedType::RECOMMENDED_ANSWER->value => FeedType::RECOMMENDED_ANSWER->getLabel(),
            FeedType::LAUGH->value => FeedType::LAUGH->getLabel(),
            FeedType::GENERAL->value => FeedType::GENERAL->getLabel(),
            FeedType::RAISE_HAND->value => FeedType::RAISE_HAND->getLabel(),
            FeedType::LATEST->value => FeedType::LATEST->getLabel(),
            FeedType::OWNER->value => FeedType::OWNER->getLabel(),
        ];

        /**
         * add gender feed if user has
         */
        if ($user->isMale() || $user->isFemale()) {
            $defaultTypes[FeedType::GENDER->value] = __('gender.' . $user->gender);
        }

        $types = [
            FeedType::TOPICS->value => FeedType::TOPICS->getLabel(),
        ];

        /** @var Collection|Community[] $withinDayCommunities */
        $joinedCommunities = $user->getJoinedCommunities();
        $joinedCommunities = $joinedCommunities->sortByDesc('joined_at');

        /**
         * hiển thị danh sách cộng đồng vừa tham gia trong vòng 24h trên đầu
         * @var Collection|Community[] $withinDayCommunities
         */
        $withinDayCommunities = $joinedCommunities->filter(function (Community $community) {
            return $community->getAttribute('joined_at') > now('UTC')->subHours(24)->toDateTimeString();
        });

        foreach ($withinDayCommunities as $community) {
            $communityId = $community->getKey();
            $types['community_' . $communityId] = $community->name;

            /**
             * remove community from joined
             */
            $joinedCommunities = $joinedCommunities->filter(fn ($c) => $c->isNotID($communityId));
        }

        /**
         * sắp xếp danh sách cộng đồng còn lại và những feed type khác
         * theo thứ tự được xem nhiều nhất trong 50 lượt gần nhất
         */
        $feedViewedStats = feedViewedRepository()->getStatByUser($user);
        arsort($feedViewedStats);

        foreach ($feedViewedStats as $feedType => $count) {
            if (str_starts_with($feedType, 'community_')) {
                $id = str_replace('community_', '', $feedType);

                /** @var Community|null $community */
                $community = $joinedCommunities->first(fn ($c) => $c->isID($id));
                if (! is_null($community)) {
                    $joinedCommunities = $joinedCommunities->filter(fn ($c) => $c->isNotID($id));
                    $types[$feedType] = $community->name;
                }
            }

            else {
                $type = $defaultTypes[$feedType] ?? null;
                if ($type) {
                    $types[$feedType] = $type;
                    unset($defaultTypes[$feedType]);
                }
            }
        }

        /**
         * types còn lại
         */
        if (count($defaultTypes) > 0) {
            foreach ($defaultTypes as $type => $label) {
                $types[$type] = $label;
            }
        }

        /**
         * communities còn lại
         */
        if ($joinedCommunities->count() > 0) {
            foreach ($joinedCommunities as $community) {
                $types['community_' . $community->getKey()] = $community->name;
            }
        }

        return $types;
    }

    /**
     * @param Request $request
     * @return array
     */
    public function unlockPosts(Request $request)
    {
        $unlockPosts = settingRepository()->getUnlockPosts();
        if ($unlockPosts) {
            $postIds = Arr::pluck($unlockPosts, 'post_id');
            $posts = postRepository()->findIn($postIds);
            $posts->load([
                'communityPost',
                'communityPost.community',
            ]);

            $now = now('UTC');
            foreach ($unlockPosts as $key => $record) {
                /** @var Post|null $post */
                $post = $posts->where('post_id', $record['post_id'])->first();
                $community = $post->communityPost?->community;

                if (! $post || ($community && !$community->isPublic())) {
                    unset($unlockPosts[$key]);
                    continue;
                }

                $unlockType = (int) Arr::get($record, 'unlock_time', 1);
                $time = $now->clone();
                if ($unlockType === 2) {
                    $time->addDay();
                }

                $time->endOfDay();

                $record['unlocked_until'] = $time->toDateTimeString();
                $record['unlocked_until_hour'] = (int) $now->diffInUTCHours($time);

                $type = in_array($post->type, PostType::consoleValidationRuleArray()) ? $post->type : 'normal';
                $record['type'] = $type;
                $record['post'] = PostResource::make($post)->toArray($request);

                unset($record['post_id']);
                $unlockPosts[$key] = $record;
            }
        }

        return apiResponse()->success([
            'posts' => $unlockPosts,
        ]);
    }
}
