<?php

namespace App\Http\Controllers\Web;

use App\Actions\Post\FetchQAPost;
use App\Actions\User\DeleteUser;
use App\Actions\User\FetchListUserPagination;
use App\Actions\User\FetchUserAttribute;
use App\Actions\User\FetchUserData;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HasPagination;
use App\Http\Requests\Web\DeleteUserRequest;
use App\Http\Resources\Web\PostResource;
use App\Http\Resources\Web\UserResource;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Inertia\Inertia;

class UserController extends Controller
{
    use HasPagination;

    /**
     * @param Request $request
     * @param FetchListUserPagination $fetcher
     * @return \Inertia\Response
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function index(Request $request, FetchListUserPagination $fetcher)
    {
        list ($page, $limit) = $this->parsePagination($request);
        $offset = ($page - 1) * $limit;

        $users = $fetcher->execute($request, $limit, $page, $offset);
        $users->setCollection($users->getCollection()->transform(fn ($user) => UserResource::make($user)->toArray($request)));

        $filters = $request->only(['search', 'id', 'limit']);

        return Inertia::render('User/Index', [
            'filters' => $filters,
            'users' => $users->appends($filters),
        ]);
    }

    /**
     * @param User $user
     * @param FetchUserData $fetcher
     * @return \Inertia\Response
     */
    public function detail(User $user, FetchUserData $fetcher)
    {
        return Inertia::render('User/Detail', [
            'user' => $fetcher->execute($user),
        ]);
    }

    /**
     * @param User $user
     * @param FetchUserAttribute $fetcher
     * @param Request $request
     * @return \Inertia\Response
     */
    public function attribute(User $user, FetchUserAttribute $fetcher, Request $request)
    {
        return Inertia::render('User/Attribute', [
            'attributes' => $fetcher->execute($user),
            'showToIndex' => 7,
            'user' => UserResource::make($user)->toArray($request),
        ]);
    }

    /**
     * @param User $user
     * @param FetchQAPost $fetcher
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|\Inertia\Response
     */
    public function feed(User $user, FetchQAPost $fetcher, Request $request)
    {
        $next = (int) $request->get('next');
        $totalPage = 10;

        /** @var Collection $posts */
        [$posts, $hasNextPage] = $fetcher->execute($user, $next, $totalPage);

        $posts->load('createdBy');

        $response = [
            'posts' => $posts->transform(fn ($post) => PostResource::make($post)->toArray($request)),
            'hasNextPage' => $hasNextPage,
        ];

        if ($request->has('axios')) {
            return response()->json($response);
        }

        return Inertia::render('User/Feed', [
            ...$response,
            'user' => UserResource::make($user)->toArray($request),
        ]);
    }

    /**
     * @param DeleteUserRequest $request
     * @param DeleteUser $deleteAction
     * @return \Illuminate\Http\RedirectResponse
     */
    public function delete(DeleteUserRequest $request, DeleteUser $deleteAction)
    {
        $deleteAction->execute($request->getDeleteUser());

        return back()->with('flash', [
            'message' => __('You have successfully deleted data.'),
        ]);
    }
}
