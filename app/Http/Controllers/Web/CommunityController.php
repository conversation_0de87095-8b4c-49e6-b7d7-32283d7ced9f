<?php

namespace App\Http\Controllers\Web;

use App\Actions\Community\ConsoleStoreCommunity;
use App\Actions\Community\DeleteCommunity;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Community\DeleteCommunityRequest;
use App\Http\Requests\Web\StoreCommunityRequest;
use App\Http\Resources\Web\CommunityResource;
use App\Models\Community;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;

class CommunityController extends Controller
{
    /**
     * @return JsonResponse
     */
    public function listAdminCommunities()
    {
        $communities = communityRepository()->listAdminCommunities();

        $data = [];
        foreach ($communities as $community) {
            $data[] = [
                'community_id' => $community->getKey(),
                'name' => $community->name,
            ];
        }

        return response()->json($data);
    }

    /**
     * @param StoreCommunityRequest $request
     * @param ConsoleStoreCommunity $processor
     * @return \Illuminate\Http\RedirectResponse
     * @throws ValidationException
     */
    public function storeCommunity(StoreCommunityRequest $request, ConsoleStoreCommunity $processor)
    {
        $community = $processor->execute($request);

        if ($request->has('redirect') && $community->wasRecentlyCreated) {
            return response()->redirectToRoute('community.list')->with('flash', [
                'message' => __('You have successfully saved data.'),
            ]);
        }

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
            'community' => [
                'community_id' => $community->getKey(),
                'name' => $community->name,
            ],
        ]);
    }

    /**
     * @return \Inertia\Response
     */
    public function index()
    {
        $communities = communityRepository()->listAdminCommunities();

        return Inertia::render('Community/Index', [
            'communities' => CommunityResource::collection($communities),
        ]);
    }

    /**
     * @param Community $community
     * @return \Inertia\Response
     */
    public function form(Community $community)
    {
        if ($community->exists && (! $community->ownerBy(1) || $community->isInActive())) {
            abort(404);
        }

        return Inertia::render('Community/Form', [
            'action' => $community->exists ? 'update' : 'create',
            'community' => [
                'community_id' => $community->getKey(),
                'name' => $community->name,
                'description' => $community->description,
                'image' => (string) storage()->url($community->avatar),
                'redirect' => true,
            ],
        ]);
    }

    /**
     * @param DeleteCommunityRequest $request
     * @param DeleteCommunity $processor
     * @return \Illuminate\Http\RedirectResponse
     */
    public function terminate(DeleteCommunityRequest $request, DeleteCommunity $processor)
    {
        $processor->execute($request->getCommunity());

        if ($request->has('redirect')) {
            return response()->redirectToRoute('community.list')->with('flash', [
                'message' => __('You have successfully deleted the community.'),
            ]);
        }

        return back()->with('flash', [
            'message' => __('You have successfully deleted the community.'),
        ]);
    }
}
