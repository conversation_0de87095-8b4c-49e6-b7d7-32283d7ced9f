<?php

namespace App\Http\Controllers\Web;

use App\Actions\News\FetchListNews;
use App\Actions\News\StoreNews;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HasPagination;
use App\Http\Requests\Web\DeleteNewsRequest;
use App\Http\Requests\Web\StoreNewsRequest;
use App\Http\Resources\Web\NewsResource;
use App\Models\News;
use Illuminate\Http\Request;
use Illuminate\Http\Request as HttpRequest;
use Inertia\Inertia;

class NewsController extends Controller
{
    use HasPagination;

    /**
     * @param HttpRequest $request
     * @param FetchListNews $fetcher
     * @return \Inertia\Response
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function index(Request $request, FetchListNews $fetcher)
    {
        list ($page, $limit) = $this->parsePagination($request);
        $offset = ($page - 1) * $limit;

        $payload = [
            'search' => $request->get('search'),
            'status' => $request->get('status'),
            'orderBy' => 'news_id',
            'orderDirection' => 'desc',
        ];

        $items = $fetcher->execute($payload, $limit, $page, $offset);
        $items->setCollection($items->getCollection()->transform(fn ($news) => NewsResource::make($news)->toArray($request)));

        $filters = $request->only([
            'search',
            'status',
            'limit',
        ]);

        return Inertia::render('News/Index', [
            'filters' => $filters,
            'news' => $items->appends($filters)->onEachSide(2),
        ]);
    }

    /**
     * @param StoreNewsRequest $request
     * @param StoreNews $processor
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StoreNewsRequest $request, StoreNews $processor)
    {
        $processor->execute($request->getNews(), $request->only([
            'title',
            'content',
        ]));

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
        ]);
    }

    /**
     * @param DeleteNewsRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(DeleteNewsRequest $request)
    {
        $news = $request->getDeleteNews();
        $news->updateQuietly([
            'status' => 0,
        ]);

        return back()->with('flash', [
            'message' => __('You have successfully deleted data.'),
        ]);
    }

    /**
     * @param Request $request
     * @param News $news
     * @return \Inertia\Response
     */
    public function form(HttpRequest $request, News $news)
    {
        return Inertia::render('News/Form', [
            'action' => $news->exists ? 'update' : 'create',
            'news' => $news->exists ? NewsResource::make($news)->showContent()->toArray($request) : [
                'news_id' => '',
                'title' => '',
                'content' => '',
            ],
        ]);
    }
}
