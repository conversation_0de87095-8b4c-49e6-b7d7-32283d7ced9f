<?php

namespace App\Http\Controllers\Web;

use App\Actions\Quest\FetchListQuest;
use App\Actions\Quest\StoreQuest;
use App\Enums\QuestType;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HasPagination;
use App\Http\Requests\Web\StoreQuestRequest;
use App\Http\Requests\Web\ToggleQuestRequest;
use App\Http\Resources\Web\QuestResource;
use App\Models\Quest;
use App\Repositories\QuestRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Request as HttpRequest;
use Inertia\Inertia;

class QuestController extends Controller
{
    use HasPagination;

    public function __construct(protected QuestRepository $repository)
    {

    }

    /**
     * @param HttpRequest $request
     * @param FetchListQuest $fetcher
     * @return \Inertia\Response
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function index(Request $request, FetchListQuest $fetcher)
    {
        list ($page, $limit) = $this->parsePagination($request);
        $offset = ($page - 1) * $limit;

        $payload = [
            'search' => $request->get('search'),
            'status' => $request->get('status'),
            'orderBy' => 'sort',
            'orderDirection' => 'asc',
        ];

        $items = $fetcher->execute($payload, $limit, $page, $offset);
        $items->setCollection($items->getCollection()->transform(fn ($quest) => QuestResource::make($quest)->toArray($request)));

        $filters = $request->only([
            'search',
            'status',
            'limit',
        ]);

        return Inertia::render('Quest/Index', [
            'filters' => $filters,
            'quests' => $items->appends($filters),
        ]);
    }

    /**
     * @param StoreQuestRequest $request
     * @param StoreQuest $processor
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StoreQuestRequest $request, StoreQuest $processor)
    {
        $processor->execute($request);

        $this->repository->clearActiveQuest();

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
        ]);
    }

    /**
     * @param ToggleQuestRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function toggle(ToggleQuestRequest $request)
    {
        $quest = $request->getQuest();
        $status = (int) $quest->status;

        $quest->updateQuietly([
            'status' => $status ^ 1,
        ]);

        $this->repository->clearActiveQuest();

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
        ]);
    }

    /**
     * @param Request $request
     * @param Quest $quest
     * @return \Inertia\Response
     */
    public function form(HttpRequest $request, Quest $quest)
    {
        return Inertia::render('Quest/Form', [
            'action' => $quest->exists ? 'update' : 'create',
            'types' => QuestType::typeMapping(),
            'quest' => $quest->exists ? QuestResource::make($quest)->toArray($request) : [
                'id' => '',
                'title' => '',
                'description' => '',
                'amount' => '',
                'type' => '',
                'sort' => '',
                'image' => '',
            ],
        ]);
    }
}
