<?php

namespace App\Http\Controllers\Web;

use App\Actions\Post\DeletePostAnswer;
use App\Actions\Post\FetchListPostAnswer;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HasPagination;
use App\Http\Requests\Web\DeletePostAnswerRequest;
use App\Http\Resources\Web\PostAnswerResource;
use App\Models\PostAnswer;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PostAnswerController extends Controller
{
    use HasPagination;

    /**
     * @param Request $request
     * @param FetchListPostAnswer $fetcher
     * @return \Inertia\Response
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function index(Request $request, FetchListPostAnswer $fetcher)
    {
        list ($page, $limit) = $this->parsePagination($request);
        $offset = ($page - 1) * $limit;

        $payload = [
            'with' => ['createdBy', 'post', 'post.createdBy'],
            'search' => $request->get('search'),
            'reported' => $request->get('reported'),
            'orderBy' => 'answer_id',
            'orderDirection' => 'desc',
            'all' => true,
        ];

        if ($post = $request->get('post')) {
            $payload[is_numeric($post) ? 'postId' : 'searchPost'] = $post;
        }

        if ($user = $request->get('user')) {
            $payload[is_numeric($user) ? 'userId' : 'searchUser'] = $user;
        }

        $posts = $fetcher->execute($payload, $limit, $page, $offset);
        $posts->setCollection($posts->getCollection()->transform(fn ($post) => PostAnswerResource::make($post)->toArray($request)));

        $filters = $request->only([
            'search',
            'post',
            'user',
            'reported',
            'limit',
        ]);

        return Inertia::render('PostAnswer/Index', [
            'filters' => $filters,
            'answers' => $posts->appends($filters),
        ]);
    }

    /**
     * @param PostAnswer $postAnswer
     * @return \Inertia\Response
     */
    public function detail(PostAnswer $postAnswer)
    {
        if ($postAnswer->exists) {
            $postAnswer->loadMissing(['post', 'post.createdBy', 'createdBy']);
        }

        return Inertia::render('PostAnswer/Detail', [
            'answer' => PostAnswerResource::make($postAnswer)->toArray(request()),
        ]);
    }

    /**
     * @param DeletePostAnswerRequest $request
     * @param DeletePostAnswer $deleteAction
     * @return \Illuminate\Http\RedirectResponse
     */
    public function delete(DeletePostAnswerRequest $request, DeletePostAnswer $deleteAction)
    {
        $deleteAction->execute($request->getPostAnswerInstance());

        return back()->with('flash', [
            'message' => __('You have successfully deleted data.'),
        ]);
    }
}
