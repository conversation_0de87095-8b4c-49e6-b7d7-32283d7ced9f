<?php

namespace App\Http\Controllers\Web;

use App\Actions\Common\FetchSystemSetting;
use App\Actions\Common\StoreSetting;
use App\Actions\Community\ConsoleStoreCommunity;
use App\Http\Controllers\Controller;
use App\Http\Requests\Web\StoreCommunityRequest;
use App\Http\Requests\Web\StoreSettingRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;

class CommonController extends Controller
{
    /**
     * @param FetchSystemSetting $fetcher
     * @return \Inertia\Response
     */
    public function settings(FetchSystemSetting $fetcher)
    {
        [$qaSettings, $timeSettings] = $fetcher->execute();

        return Inertia::render('Common/Setting', [
            'settings' => $qaSettings,
            'timeSettings' => $timeSettings,
        ]);
    }

    /**
     * @param StoreSettingRequest $request
     * @param StoreSetting $saver
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeSettings(StoreSettingRequest $request, StoreSetting $saver)
    {
        $saver->execute($request);

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function listPublicCommunities(Request $request)
    {
        $userId = $request->user()->getKey();

        return response()->json(communityRepository()->listPublicCommunities($userId));
    }

    /**
     * @param StoreCommunityRequest $request
     * @param ConsoleStoreCommunity $processor
     * @return \Illuminate\Http\RedirectResponse
     * @throws ValidationException
     */
    public function storeCommunity(StoreCommunityRequest $request, ConsoleStoreCommunity $processor)
    {
        $community = $processor->execute($request);

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
            'community' => [
                'community_id' => $community->getKey(),
                'name' => $community->name,
            ],
        ]);
    }
}
