<?php

namespace App\Http\Controllers\Web;

use App\Actions\Common\FetchSystemSetting;
use App\Actions\Common\StoreSetting;
use App\Actions\Community\ConsoleStoreCommunity;
use App\Enums\PostType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Web\StoreCommunityRequest;
use App\Http\Requests\Web\StoreSettingRequest;
use App\Http\Requests\Web\StoreUnlockPostRequest;
use App\Models\Post;
use App\Models\Setting;
use App\Repositories\SettingRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;

class CommonController extends Controller
{
    /**
     * @param FetchSystemSetting $fetcher
     * @return \Inertia\Response
     */
    public function settings(FetchSystemSetting $fetcher)
    {
        [$qaSettings, $timeSettings] = $fetcher->execute();

        return Inertia::render('Common/Setting', [
            'settings' => $qaSettings,
            'timeSettings' => $timeSettings,
        ]);
    }

    /**
     * @param StoreSettingRequest $request
     * @param StoreSetting $saver
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeSettings(StoreSettingRequest $request, StoreSetting $saver)
    {
        $saver->execute($request);

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function listPublicCommunities(Request $request)
    {
        $userId = $request->user()->getKey();

        return response()->json(communityRepository()->listPublicCommunities($userId));
    }

    /**
     * @param StoreCommunityRequest $request
     * @param ConsoleStoreCommunity $processor
     * @return \Illuminate\Http\RedirectResponse
     * @throws ValidationException
     */
    public function storeCommunity(StoreCommunityRequest $request, ConsoleStoreCommunity $processor)
    {
        $community = $processor->execute($request);

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
            'community' => [
                'community_id' => $community->getKey(),
                'name' => $community->name,
            ],
        ]);
    }

    /**
     * @param SettingRepository $settingRepository
     * @return \Inertia\Response
     */
    public function unlockPosts(SettingRepository $settingRepository)
    {
        $records = $settingRepository->getUnlockPosts();
        if ($records) {
            $postIds = Arr::pluck($records, 'post_id');
            $posts = postRepository()->findIn($postIds);

            foreach ($records as $key => $record) {
                /** @var Post|null $post */
                $post = $posts->where('post_id', $record['post_id'])->first();
                if (! $post) {
                    unset($records[$key]);
                    continue;
                }

                $record['content'] = $post->content;

                $type = in_array($post->type, PostType::consoleValidationRuleArray()) ? $post->type : 'normal';
                $record['type'] = __('postType.' . $type);

                $records[$key] = $record;
            }
        }

        dump($records);

        return Inertia::render('Common/UnlockPost', [
            'posts' => $records,
        ]);
    }

    /**
     * @param StoreUnlockPostRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeUnlockPosts(StoreUnlockPostRequest $request)
    {
        $data = $request->post('data');

        Setting::query()
            ->firstOrNew([
                'user_id' => 0,
                'key' => 'unlock_posts',
            ])
            ->fill([
                'value' => json_encode($data),
            ])
            ->save();

        /**
         * clear cached settings
         */
        settingRepository()->clearSystemSettings();

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
        ]);
    }
}
