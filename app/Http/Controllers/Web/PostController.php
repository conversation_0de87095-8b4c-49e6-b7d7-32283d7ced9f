<?php /** @noinspection ALL */

namespace App\Http\Controllers\Web;

use App\Actions\Post\DeletePost;
use App\Actions\Post\FetchPostHistory;
use App\Actions\Post\StoreAdminPost;
use App\Actions\Post\TogglePostFeature;
use App\Actions\Post\WebFetchListPost;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HasPagination;
use App\Http\Requests\Web\DeletePostRequest;
use App\Http\Requests\Web\StorePostRequest;
use App\Http\Requests\Web\TogglePostFeatureRequest;
use App\Http\Resources\Web\PostHistoryResource;
use App\Http\Resources\Web\PostResource;
use App\Models\Post;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PostController extends Controller
{
    use HasPagination;

    /**
     * @param Request $request
     * @param WebFetchListPost $fetcher
     * @return Response
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function index(Request $request, WebFetchListPost $fetcher)
    {
        list ($page, $limit) = $this->parsePagination($request);
        $offset = ($page - 1) * $limit;

        $payload = [
            'with' => ['createdBy', 'communityPost', 'communityPost.community'],
            'search' => $request->get('search'),
            'reported' => $request->get('reported'),
            'type' => $request->get('type'),
            'topics' => $request->get('topics'),
            'orderBy' => 'post_id',
            'orderDirection' => 'desc',
            'all' => true,
        ];

        if ($user = $request->get('user')) {
            $payload[is_numeric($user) ? 'userId' : 'searchUser'] = $user;
        }

        $posts = $fetcher->execute($payload, $limit, $page, $offset);
        $posts->setCollection($posts->getCollection()->transform(fn ($post) => PostResource::make($post)->toArray($request)));

        $filters = $request->only([
            'search',
            'user',
            'reported',
            'type',
            'topics',
            'limit',
        ]);

        return Inertia::render('Post/Index', [
            'filters' => $filters,
            'posts' => $posts->appends($filters),
        ]);
    }

    /**
     * @param Post $post
     * @return Response
     */
    public function detail(Post $post)
    {
        return Inertia::render('Post/Detail', [
            'post' => PostResource::make($post)->toArray(request()),
        ]);
    }

    /**
     * @param DeletePostRequest $request
     * @param DeletePost $deleteAction
     * @return RedirectResponse
     */
    public function delete(DeletePostRequest $request, DeletePost $deleteAction)
    {
        $deleteAction->execute($request->getPostInstance());

        return back()->with('flash', [
            'message' => __('You have successfully deleted data.'),
        ]);
    }

    /**
     * @param Request $request
     * @param FetchPostHistory $fetcher
     * @return Response
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function history(Request $request, FetchPostHistory $fetcher)
    {
        list ($page, $limit) = $this->parsePagination($request);
        $offset = ($page - 1) * $limit;

        $filters = $request->only([
            'user',
            'post',
            'limit',
        ]);

        if ($date = $request->get('date')) {
            try {
                $format = 'Y-m-d';
                $filters['date'] = Carbon::make($date)->format($format);
            } catch (\Throwable $e) {
                $filters['date'] = '';
            }
        }

        $payload = [
            'with' => [
                'post' => function (BelongsTo $query) {
                    $query->select('post_id', 'content', 'answer_count');
                },
                'user' => function (BelongsTo $query) {
                    $query->select('user_id', 'name', 'phone');
                },
            ],
            'orderBy' => 'id',
            'orderDirection' => 'desc',
            ...$filters,
        ];

        $logs = $fetcher->execute($payload, $limit, $page, $offset);
        $logs->setCollection($logs->getCollection()->transform(fn ($log) => PostHistoryResource::make($log)->toArray($request)));

        return Inertia::render('Post/History', [
            'filters' => $filters,
            'logs' => $logs->appends($filters),
        ]);
    }

    /**
     * @param TogglePostFeatureRequest $request
     * @param TogglePostFeature $processor
     * @return RedirectResponse
     */
    public function toggleFeature(TogglePostFeatureRequest $request, TogglePostFeature $processor)
    {
        $processor->execute($request->getPostInstance());

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
        ]);
    }

    /**
     * @param Request $request
     * @param Post $post
     * @return \Inertia\Response
     */
    public function form(Request $request, Post $post)
    {
        return Inertia::render('Post/Form', [
            'action' => $post->exists ? 'update' : 'create',
            'post' => [
                'post_id' => $post?->getKey() ?? '',
                'content' => $post?->content,
                'image' => storage()->url($post?->image),
                'type' => $post?->type ?? '',
                'community_id' => $post?->communityPost?->community_id ?? '',
                'camera_roll' => $post?->camera_roll ?? 0,
            ],
        ]);
    }

    /**
     * @param StorePostRequest $request
     * @param StoreAdminPost $processor
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StorePostRequest $request, StoreAdminPost $processor)
    {
        $processor->execute($request);

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
        ]);
    }
}
