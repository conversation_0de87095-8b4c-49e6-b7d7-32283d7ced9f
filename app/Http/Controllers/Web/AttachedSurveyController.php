<?php

namespace App\Http\Controllers\Web;

use App\Actions\AttachedSurvey\DeleteAttachedSurvey;
use App\Actions\AttachedSurvey\FetchListAttachedSurveyPagination;
use App\Actions\AttachedSurvey\StoreAttachedSurvey;
use App\Events\AttachedSurveyStored;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HasPagination;
use App\Http\Requests\Web\DeleteAttachedSurveyRequest;
use App\Http\Requests\Web\StoreAttachedSurveyRequest;
use App\Http\Resources\Web\AttachedSurveyResource;
use App\Models\AttachedSurvey;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AttachedSurveyController extends Controller
{
    use HasPagination;

    /**
     * @param Request $request
     * @param FetchListAttachedSurveyPagination $fetcher
     * @return \Inertia\Response
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function index(Request $request, FetchListAttachedSurveyPagination $fetcher)
    {
        list ($page, $limit) = $this->parsePagination($request);
        $offset = ($page - 1) * $limit;

        $attachedItems = $fetcher->execute($request, $limit, $page, $offset);
        $attachedItems->setCollection($attachedItems->transform(function ($item) use ($request) {
            return AttachedSurveyResource::make($item)->toArray($request);
        }));

        return Inertia::render('AttachedSurvey/Index', [
            'filters' => $request->only('search'),
            'attachedItems' => $attachedItems,
        ]);
    }

    /**
     * @param AttachedSurvey $attachedSurvey
     * @return \Inertia\Response
     */
    public function form(AttachedSurvey $attachedSurvey)
    {
        return Inertia::render('AttachedSurvey/Form', [
            'title' => __($attachedSurvey->exists ? 'updateAttachedSurvey' : 'createNewAttachedSurvey'),
            'attachedSurvey' => $attachedSurvey->exists ? AttachedSurveyResource::make($attachedSurvey) : [
                'attached_id' => '',
                'title' => '',
                'survey_id' => '',
                'to_survey_id' => '',
                'choices' => [
                    [
                        'question_id' => '',
                        'choice_id' => '',
                        'show' => true,
                    ]
                ],
            ],
        ]);
    }

    /**
     * @param StoreAttachedSurveyRequest $request
     * @param StoreAttachedSurvey $updater
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StoreAttachedSurveyRequest $request, StoreAttachedSurvey $updater)
    {
        $attachedSurvey = $updater->execute($request);

        AttachedSurveyStored::dispatch($attachedSurvey);

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
        ]);
    }

    /**
     * @param DeleteAttachedSurveyRequest $request
     * @param DeleteAttachedSurvey $deleter
     * @return \Illuminate\Http\RedirectResponse
     */
    public function delete(DeleteAttachedSurveyRequest $request, DeleteAttachedSurvey $deleter)
    {
        $deleter->execute($request->getAttachedSurvey());

        return back()->with('flash', [
            'message' => __('You have successfully deleted data.'),
        ]);
    }
}
