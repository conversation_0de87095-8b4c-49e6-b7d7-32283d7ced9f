<?php

namespace App\Http\Controllers\Web;

use App\Actions\Assistant\FetchListAssistant;
use App\Actions\Assistant\StoreAssistant;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HasPagination;
use App\Http\Requests\Web\StoreAssistantRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AssistantController extends Controller
{
    use HasPagination;

    /**
     * @param Request $request
     * @param FetchListAssistant $fetcher
     * @return \Inertia\Response
     */
    public function index(Request $request, FetchListAssistant $fetcher)
    {
        [$page, $limit] = $this->parsePagination($request);

        $filters = [
            'search' => $request->get('search'),
            'model' => $request->get('model'),
            'language' => $request->get('language', 'vi'),
        ];

        return Inertia::render('Assistant/Index', [
            'filters' => $filters,
            'assistants' => $fetcher->execute($filters, $limit, $page)->appends($filters),
        ]);
    }

    /**
     * @param StoreAssistantRequest $request
     * @param StoreAssistant $store
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StoreAssistantRequest $request, StoreAssistant $store)
    {
        $store->execute($request->only([
            'name',
            'work',
            'expertise',
            'description',
            'language',
        ]));

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
        ]);
    }
}
