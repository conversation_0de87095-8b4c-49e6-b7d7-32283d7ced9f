<?php

namespace App\Http\Controllers\Web;

use App\Actions\PremiumFeature\ConsoleFetchListPremiumFeature;
use App\Actions\PremiumFeature\StorePremiumFeature;
use App\Enums\PremiumFeatureType;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HasPagination;
use App\Http\Requests\Web\StorePremiumFeatureRequest;
use App\Http\Resources\Web\PremiumFeatureResource;
use App\Models\PremiumFeature;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PremiumFeatureController extends Controller
{
    use HasPagination;

    /**
     * @param Request $request
     * @param ConsoleFetchListPremiumFeature $fetcher
     * @return \Inertia\Response
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function index(Request $request, ConsoleFetchListPremiumFeature $fetcher)
    {
        list ($page, $limit) = $this->parsePagination($request);
        $offset = ($page - 1) * $limit;

        $payload = [
            'search' => $request->get('search'),
            'orderBy' => 'premium_id',
            'orderDirection' => 'desc',
        ];

        $items = $fetcher->execute($payload, $limit, $page, $offset);
        $items->setCollection($items->getCollection()->transform(fn ($feature) => PremiumFeatureResource::make($feature)->toArray($request)));

        $filters = $request->only([
            'search',
            'limit',
        ]);

        return Inertia::render('PremiumFeature/Index', [
            'filters' => $filters,
            'features' => $items->appends($filters),
        ]);
    }

    /**
     * @param StorePremiumFeatureRequest $request
     * @param StorePremiumFeature $processor
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StorePremiumFeatureRequest $request, StorePremiumFeature $processor)
    {
        $processor->execute($request);

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
        ]);
    }

    /**
     * @param Request $request
     * @param PremiumFeature $feature
     * @return \Inertia\Response
     */
    public function form(Request $request, PremiumFeature $feature)
    {
        return Inertia::render('PremiumFeature/Form', [
            'action' => $feature->exists ? 'update' : 'create',
            'types' => PremiumFeatureType::typeMapping(),
            'feature' => $feature->exists ? PremiumFeatureResource::make($feature)->toArray($request) : [
                'premium_id' => '',
                'name' => '',
                'description' => '',
                'price' => '',
                'type' => '',
                'image' => '',
            ],
        ]);
    }
}
