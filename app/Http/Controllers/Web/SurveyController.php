<?php

namespace App\Http\Controllers\Web;

use App\Actions\Survey\DeleteSurvey;
use App\Actions\Survey\FetchListSurveyQuestion;
use App\Actions\Survey\FetchListSortedSurvey;
use App\Actions\Survey\WebFetchListSurveyPagination;
use App\Actions\Survey\StoreSortedSurvey;
use App\Actions\Survey\StoreSurvey;
use App\Actions\Survey\WebFetchListSurveyForSelectBox;
use App\Events\SortedSurveyStored;
use App\Events\SurveyDeleted;
use App\Events\SurveyStored;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HasPagination;
use App\Http\Requests\Web\DeleteSurveyRequest;
use App\Http\Requests\Web\StoreSortedSurveyRequest;
use App\Http\Requests\Web\StoreSurveyRequest;
use App\Http\Resources\Web\QuestionResource;
use App\Http\Resources\Web\SortedSurveyResource;
use App\Http\Resources\Web\SurveyResource;
use App\Models\Survey;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\Request;
use Inertia\Inertia;

class SurveyController extends Controller
{
    use HasPagination;

    /**
     * @param Request $request
     * @param WebFetchListSurveyPagination $fetcher
     * @return \Inertia\Response
     * @throws BindingResolutionException
     */
    public function listSurvey(Request $request, WebFetchListSurveyPagination $fetcher)
    {
        list ($page, $limit) = $this->parsePagination($request);
        $offset = ($page - 1) * $limit;

        $filters = $request->only('search');

        return Inertia::render('Survey/Index', [
            'filters' => $filters,
            'surveys' => $fetcher->execute($request, $limit, $page, $offset),
        ]);
    }

    /**
     * @param Survey $survey
     * @return \Inertia\Response
     */
    public function form(Survey $survey)
    {
        return Inertia::render('Survey/Form', [
            'title' => __($survey->exists ? 'updateSurvey' : 'createNewSurvey'),
            'survey' => $survey->exists ? SurveyResource::make($survey) : [
                'survey_id' => '',
                'title' => '',
                'questions' => [
                    [
                        'question_id' => '',
                        'type' => '',
                        'content' => '',
                        'point' => '',
                        'public' => '',
                        'choices' => [
                            [
                                'choice_id' => '',
                                'content' => '',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }

    /**
     * @param StoreSurveyRequest $request
     * @param StoreSurvey $updater
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StoreSurveyRequest $request, StoreSurvey $updater)
    {
        $survey = $updater->execute($request);

        /**
         * clear all cached surveys
         */
        surveyRepository()->clearAllCachedSurveys();

        SurveyStored::dispatch($survey);

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
        ]);
    }

    /**
     * @return \Inertia\Response
     */
    public function surveySortView(Request $request, FetchListSortedSurvey $fetcher)
    {
        $data = [];
        if ($sortedSurveys = $fetcher->execute()) {
            $data = SortedSurveyResource::collection($sortedSurveys)->toArray($request);
        }

        return Inertia::render('Survey/Sort', [
            'formData' => [
                'surveys' => $data,
            ],
        ]);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function piniaStoreData(WebFetchListSurveyForSelectBox $fetcher)
    {
        return response()->json($fetcher->execute());
    }

    /**
     * @param StoreSortedSurveyRequest $request
     * @param StoreSortedSurvey $updater
     * @return \Illuminate\Http\RedirectResponse
     */
    public function surveySortStore(StoreSortedSurveyRequest $request, StoreSortedSurvey $updater)
    {
        $updater->execute($request->post('ids'));

        SortedSurveyStored::dispatch();

        return back()->with('flash', [
            'message' => __('You have successfully saved data.'),
        ]);
    }

    /**
     * @param Request $request
     * @param FetchListSurveyQuestion $fetcher
     * @return \Illuminate\Http\JsonResponse
     */
    public function listQuestion(Request $request, FetchListSurveyQuestion $fetcher)
    {
        $data = [];
        if ($questions = $fetcher->execute($request)) {
            $data = QuestionResource::collection($questions)->toArray($request);
        }

        return response()->json($data);
    }

    /**
     * @param DeleteSurveyRequest $request
     * @param DeleteSurvey $deleter
     * @return \Illuminate\Http\RedirectResponse
     */
    public function delete(DeleteSurveyRequest $request, DeleteSurvey $deleter)
    {
        $survey = $request->getSurvey();

        $deleter->execute($survey);

        /**
         * clear all cached surveys
         */
        surveyRepository()->clearAllCachedSurveys();

        SurveyDeleted::dispatch($survey);

        return back()->with('flash', [
            'message' => __('You have successfully deleted data.'),
        ]);
    }
}
