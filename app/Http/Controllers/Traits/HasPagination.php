<?php

namespace App\Http\Controllers\Traits;

use Illuminate\Http\Request;

trait HasPagination
{
    protected function parsePagination(Request $request, int $defaultLimit = 10)
    {
        /**
         * limit
         */
        $limit = (int) $request->get('limit', $defaultLimit);
        if ($limit < $defaultLimit) {
            $limit = $defaultLimit;
        }

        if ($limit > 200) {
            $limit = 200;
        }

        /**
         * page
         */
        $page = (int) $request->get('page', 1);
        if ($page < 1) {
            $page = 1;
        }

        return [$page, $limit];
    }
}
