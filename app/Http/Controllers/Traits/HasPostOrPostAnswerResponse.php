<?php

namespace App\Http\Controllers\Traits;

use App\Models\Post;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Facades\DB;

trait HasPostOrPostAnswerResponse
{
    /**
     * @param MorphMany $query
     * @param int $userId
     * @return void
     */
    protected function loadAnswerReaction(MorphMany $query, int $userId)
    {
        $query->where('user_id', $userId)
            ->orWhere(function (Builder $query) use ($userId) {
                $query->whereExists(
                    DB::table('user_relations')
                        ->whereColumn('user_relations.target_id', 'reactions.user_id')
                        ->where('user_relations.user_id', $userId)
                        ->where('user_relations.is_friend', 1)
                )->whereIn('action', ['viewed', 'liked']);
            });
    }

    /**
     * @param Collection $posts
     * @param int $userId
     * @return void
     */
    protected function loadPostMissing(Collection $posts, int $userId)
    {
        $posts->loadMissing([
            'media',
            'metadata' => fn ($q) => $q->where('status', 1),

            /**
             * 2 latest answers
             */
            'answers' => function (HasMany $query) use ($userId) {
                $limit = 2;
                $this->latestPostAnswers($query, $userId, $limit, true);
            },
            'answers.reactions' => function (MorphMany $query) use ($userId) {
                $this->loadAnswerReaction($query, $userId);
            },
            'answers.metadata' => fn ($q) => $q->where('status', 1),
            'reactions' => fn ($q) => $q->where('user_id', $userId),
            'communityPost',
            'communityPost.community',
        ]);

        /**
         * load answer count
         */
        $this->loadPostAnswerCount($posts, $userId);

        /**
         * load answer existed state
         */
        $this->loadAnswerExistedWithoutUser($posts, $userId);
    }

    /**
     * @param Collection|Post $posts
     * @param int $userId
     * @return void
     */
    public function loadAnswerExistedWithoutUser(Collection|Post $posts, int $userId)
    {
        $posts->loadExists([
            'answers' => function (Builder $query) use ($userId) {
                $query->where('post_answers.status', 1)
                    ->where('post_answers.user_id', '!=', $userId)
                    ->whereHas('createdBy', fn ($q) => $q->where('status', 1));
            },
        ]);
    }

    /**
     * @param Collection $answers
     * @param int $userId
     * @param bool $withChildren
     * @return void
     */
    public function loadPostAnswerMissing(Collection $answers, int $userId, bool $withChildren = false)
    {
        $answers->loadMissing([
            'reactions' => function (MorphMany $query) use ($userId) {
                $this->loadAnswerReaction($query, $userId);
            },
            'metadata' => fn ($q) => $q->where('status', 1),
        ]);

        /**
         * load children count
         */
        $answers->loadCount([
            'children' => function (Builder $query) use ($userId) {
                $query->where('status', 1)
                    ->where(fn ($q) => $this->applyIgnoreBlocked($q, $userId))
                    ->whereHas('createdBy', fn ($q) => $q->where('status', 1));
            }
        ]);

        if ($withChildren) {
            $answers->loadMissing([
                'children.reactions' => fn ($q) => $q->where('user_id', $userId),
                'children.metadata' => fn ($q) => $q->where('status', 1),
            ]);
        }
    }

    /**
     * @param Post $post
     * @param int $userId
     * @return void
     */
    protected function loadPostDetailRelations(Post $post, int $userId)
    {
        $post->loadMissing([
            'answers' => function (HasMany $query) use ($userId) {
                $this->latestPostAnswers($query, $userId);
            },
            'answers.reactions' => function (MorphMany $query) use ($userId) {
                $this->loadAnswerReaction($query, $userId);
            },
            'answers.metadata' => fn ($q) => $q->where('status', 1),
            'reactions' => fn ($q) => $q->where('user_id', $userId),
            'communityPost',
            'communityPost.community',
            'metadata' => fn ($q) => $q->where('status', 1),
        ]);

        /**
         * load answer count
         */
        $this->loadPostAnswerCount($post, $userId);

        /**
         * load answer existed state
         */
        $this->loadAnswerExistedWithoutUser($post, $userId);
    }

    /**
     * @param Collection $posts
     * @param int $userId
     * @return void
     */
    public function loadUserRelationshipAnswered(Collection $posts, int $userId)
    {
        $posts->loadMissing([
            'relationshipAnswered' => function (HasOne $query) use ($userId) {
                $query->selectRaw('`post_answers`.`post_id`, GROUP_CONCAT(DISTINCT `post_answers`.`user_id` ORDER BY `post_answers`.`answer_id` DESC SEPARATOR ",") AS `answered_users`')
                    ->where('post_answers.status', 1)
                    ->where('post_answers.level', 1)
                    ->where('user_relations.user_id', $userId)
                    ->where(fn ($q) => $this->applyIgnoreBlocked($q, $userId, 'post_answers'))
                    ->whereHas('createdBy', fn ($q) => $q->where('status', 1))
                    ->groupBy('post_answers.post_id');
            },
        ]);
    }

    /**
     * @param Builder $query
     * @param int $userId
     * @param string|null $table
     * @return void
     */
    protected function applyIgnoreBlocked(Builder $query, int $userId, ?string $table = null)
    {
        $column = 'user_id';
        if ($table) {
            $column = $table . '.' . $column;
        }

        $query->when(blockHelper()->getBlockedIds($userId), function (Builder $query, $blockedIds) use ($column) {
            $query->whereNotIn($column, $blockedIds);
        });
    }

    /**
     * @param Collection|Post $posts
     * @param int $userId
     * @return void
     */
    public function loadPostAnswerCount(Collection|Post $posts, int $userId)
    {
        $posts->loadCount([
            'answers as total_answer' => function (Builder $query) use ($userId) {
                $query->where('status', 1)
                    ->where('level', 1)
                    ->whereHas('createdBy', fn ($q) => $q->where('status', 1))

                    /**
                     * ignore blocked users
                     */
                    ->where(fn ($q) => $this->applyIgnoreBlocked($q, $userId));
            }
        ]);
    }

    /**
     * @param Collection $answers
     * @param int $userId
     * @return void
     */
    public function loadPostAnswerChildrenCount(Collection $answers, int $userId)
    {
        $answers->loadCount([
            'children' => function (Builder $query) use ($userId) {
                $query->where('status', 1)
                    ->where(fn ($q) => $this->applyIgnoreBlocked($q, $userId))
                    ->whereHas('createdBy', fn ($q) => $q->where('status', 1));
            }
        ]);
    }

    /**
     * @param HasMany $query
     * @param int $userId
     * @param int $limit
     * @param bool $countChildren
     * @return void
     */
    public function latestPostAnswers(HasMany $query, int $userId, int $limit = 0, bool $countChildren = false)
    {
        $query->select('post_answers.*')
            ->leftJoin('user_relations', function ($join) use ($userId) {
                $join->on('user_relations.target_id', '=', 'post_answers.user_id')
                     ->where('user_relations.user_id', $userId)
                     ->where('user_relations.is_friend', 1);
            })
            ->where('post_answers.status', 1)
            ->where('post_answers.level', 1)
            ->whereHas('createdBy', fn ($q) => $q->where('status', 1))

            /**
             * ignore blocked users
             */
            ->where(fn ($q) => $this->applyIgnoreBlocked($q, $userId, 'post_answers'))

            /**
             * Sắp xếp theo thứ tự: pinned → voted → friend's answers → like_user_count → answer_id
             * Sử dụng orderByRaw cho friend check để tránh lỗi với ROW_NUMBER() OVER PARTITION
             */
            ->orderByDesc('post_answers.pinned')
            ->orderByDesc('post_answers.voted')
            ->orderByRaw('CASE WHEN `user_relations`.`user_id` IS NOT NULL THEN 1 ELSE 0 END DESC')
            ->orderByDesc('post_answers.like_user_count')
            ->orderByDesc('post_answers.answer_id');

        if ($limit > 0) {
            $query->limit($limit);
        }

        /**
         * load children count
         */
        if ($countChildren) {
            $query->withCount([
                'children as _children_count' => function (Builder $query) use ($userId) {
                    $query->where('status', 1)
                        ->where(fn ($q) => $this->applyIgnoreBlocked($q, $userId))
                        ->whereHas('createdBy', fn ($q) => $q->where('status', 1));
                }
            ]);
        }
    }
}
