<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\Api\Traits\ValidateSignature;
use App\Http\Requests\BaseRequest;
use App\Models\User;
use Illuminate\Validation\Validator;

class CheckPhoneExistRequest extends BaseRequest
{
    use ValidateSignature;

    /**
     * @var User|null
     */
    protected $user = null;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'phone' => [
                'required',
                'string',
                'regex:/^(\+84|\+81)\d{9,10}$/i',
                // 'unique:users',
                function (string $attribute, $value, $fail) {
                    if ($value) {
                        /** @var User $user */
                        $user = $this->getUserByPhone($value);

                        if ($user && ! $user->isDisabled()) {
                            $this->user = $user;
                        }
                    }
                }
            ],
            'aesKey' => [
                'required',
            ],
            'iv' => [
                'required',
            ],
            'encrypted_data' => [
                'required',
            ],
        ];
    }

    /**
     * @param string $value
     * @return User|null
     */
    protected function getUserByPhone(string $value)
    {
        return userRepository()->findBy($value);
    }

    /**
     * @return bool
     */
    public function phoneExisted()
    {
        return ! is_null($this->user);
    }

    /**
     * @param $validator
     * @return void
     */
    protected function withValidator($validator)
    {
        $validator->after(function (Validator $validator) {
            $error = $validator->errors();

            if ($error->has('phone')) {
                abort(131, $error->first('phone'));
            }
        });
    }
}
