<?php

namespace App\Http\Requests\Api\User;

use App\Enums\ProfileType;
use App\Http\Requests\BaseRequest;
use App\Models\School;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class UpdateCareerRequest extends BaseRequest
{
    /**
     * @var School|null
     */
    protected $newSchool;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'type' => [
                'required',
                Rule::in(ProfileType::toArray()),
            ],
            'school_id' => [
                'nullable',
                'required_if:type,' . ProfileType::STUDENT->value,
                function ($attr, $value, $fail) {
                    if ($value) {
                        $school = School::query()->find($value);
                        if (! $school) {
                            $fail(__('Invalid school ID.'));
                        }

                        $this->newSchool = $school;
                    }
                },
            ],
            'work' => [
                'nullable',
                'required_if:type,' . ProfileType::EMPLOYEE->value,
                'string',
                'max:190',
            ],
            'expert' => [
                'nullable',
                'required_if:type,' . ProfileType::EMPLOYEE->value,
                'string',
                'max:500',
            ],
            'service_in_charge' => [
                'nullable',
                'required_if:type,' . ProfileType::EMPLOYEE->value,
                'string',
                'max:1000',
            ],
            /*'override_school' => [
                'nullable',
                'required_with:school_id',
                Rule::in([0, 1]),
            ],*/
        ];
    }

    /**
     * @return School|null
     */
    public function getNewSchool()
    {
        return $this->newSchool;
    }

    /**
     * @param $validator
     * @return void
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function withValidator($validator)
    {
        $validator->after(function (Validator $validator) {
            $user = $this->user();

            if ($user->profile_type === ProfileType::STUDENT->value && $user->profile?->school_id) {
                $override = $this->input('override_school');
                if (is_null($override)) {
                    $this->setError('override_school', __('validation.required', [
                        'attribute' => __('validation.attributes.override_school'),
                    ]));
                }

                if (! in_array($override, [0, 1])) {
                    $this->setError('override_school', __('validation.in', [
                        'attribute' => __('validation.attributes.override_school'),
                    ]));
                }
            }
        });
    }
}
