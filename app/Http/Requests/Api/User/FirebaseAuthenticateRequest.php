<?php

namespace App\Http\Requests\Api\User;

use App\Enums\Gender;
use App\Http\Requests\BaseRequest;
use App\Models\InviteToken;
use App\Models\School;
use App\Models\User;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;
use Kreait\Firebase\Auth\UserRecord;
use Kreait\Laravel\Firebase\Facades\Firebase;

class FirebaseAuthenticateRequest extends BaseRequest
{
    /**
     * @var UserRecord|null $firebaseUser
     */
    protected $firebaseUser = null;

    /**
     * @var InviteToken|null
     */
    protected $inviteToken;

    /**
     * @var User|null
     */
    protected ?User $referrerUser = null;

    /**
     * @var User|null
     */
    protected ?User $registeredUser = null;

    /**
     * @var int|null
     */
    protected $age = null;

    /**
     * @var School|null
     */
    protected $school = null;

    /**
     * @return array
     */
    public function rules(): array
    {
        $this->firebaseUser = $this->_getFirebaseUser((string) $this->post('accessToken'));

        if ($this->firebaseUser && ($user = $this->getUserByFirebaseID($this->firebaseUser->uid))) {
            $this->registeredUser = $user;
        }

        return [
            'action' => [
                'required',
                Rule::in(['register', 'login']),
                function ($attribute, $value, $fail) {
                    if ($value === 'login' && is_null($this->registeredUser)) {
                        abort(103, __('Your account does not exist.'));
                    }
                }
            ],
            'birthday' => [
                'nullable',
                'required_if:action,register',
                'date_format:Y-m-d',
                function ($attr, $value, $fail) {
                    if ($value) {
                        $age = datetime()->getAgeFromDate($value);
                        if ($age <= 17) {
                            $fail(__('Age must be greater than 17 years old.'));
                        }

                        $this->age = $age;
                    }
                }
            ],
            'gender' => [
                'nullable',
                'required_if:action,register',
                Rule::in(Gender::toArray()),
            ],
            'referrer_code' => [
                'required_if:action,register',
                function ($attribute, $value, $fail) {
                    if ($value) {
                        $inviteToken = $this->getInviteToken($value);

                        if (!$inviteToken || !$inviteToken->isValid()) {
                            abort(102, __('Your referrer code is invalid.'));
                        }

                        /** @var User $user */
                        $user = $inviteToken->user;

                        if (!$user || !$user->isEnabled()) {
                            abort(102, __('Your referrer code is invalid.'));
                        }

                        $this->inviteToken = $inviteToken;
                        $this->referrerUser = $user;
                    }
                },
            ],
        ];
    }

    /**
     * @param $validator
     * @return void
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function withValidator($validator)
    {
        $validator->after(function (Validator $validator) {
            $age = (int) $this->getAge();
            if ($age >= 18 && $age <= 24 && ($schoolId = $this->input('school_id'))) {
                if (! ($school = $this->getSchoolByID($schoolId))) {
                    $this->setError('school_id', __('Invalid school ID.'));
                }

                $this->school = $school;
            }
        });
    }

    /**
     * @param $schoolId
     * @return School|\Illuminate\Database\Eloquent\Collection|\Illuminate\Database\Eloquent\Model|null
     */
    protected function getSchoolByID($schoolId)
    {
        return School::query()->find($schoolId);
    }

    /**
     * @param string $accessToken
     * @return UserRecord
     */
    protected function _getFirebaseUser(string $accessToken)
    {
        $env = config('app.env');
        if (in_array($env, ['local', 'dev']) && str_starts_with($accessToken, 'fake-access-token:')) {
            $phone = str_replace('fake-access-token:', '', $accessToken);

            $user = User::query()->where('phone', $phone)->first();
            if (! $user) {
                abort(125, __('Invalid firebase access token.'));
            }

            $this->registeredUser = $user;

            return null;
        }

        try {
            $firebaseAuth = Firebase::auth();
            $verifiedIdToken = $firebaseAuth->verifyIdToken($accessToken);
            $uid = $verifiedIdToken->claims()->get('sub');
            $user = $firebaseAuth->getUser($uid);
        } catch (\Throwable $e) {
            abort(125, __('Invalid firebase access token.'));
        }

        return $user;
    }

    /**
     * @return UserRecord|null
     */
    public function getFirebaseUser()
    {
        return $this->firebaseUser;
    }

    /**
     * @param string $token
     * @return InviteToken|null
     */
    protected function getInviteToken(string $token)
    {
        return InviteToken::query()->where('token', $token)->first();
    }

    /**
     * @param string $uid
     * @return User|null
     */
    protected function getUserByFirebaseID(string $uid)
    {
        return User::query()->where('firebase_auth_id', $uid)->first();
    }

    /**
     * @return User|null
     */
    public function getReferrerUser()
    {
        return $this->referrerUser;
    }

    /**
     * @return InviteToken|null
     */
    public function getInviteTokenInstance()
    {
        return $this->inviteToken;
    }

    /**
     * @return User|null
     */
    public function getRegisteredUser()
    {
        return $this->registeredUser;
    }

    /**
     * @return int|null
     */
    public function getAge()
    {
        return $this->age;
    }

    /**
     * @return School|null
     */
    public function getSchool()
    {
        return $this->school;
    }
}
