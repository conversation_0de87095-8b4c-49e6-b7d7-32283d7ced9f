<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;
use App\Models\User;

class InviteFriendRequest extends BaseRequest
{
    /**
     * @var User
     */
    protected $friend;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                'required',
                function ($attr, $value, $fail) {
                    /** @var User $user */
                    $user = userRepository()->find($value);
                    $userID = $user?->getKey();

                    $currentUser = $this->user();
                    if (! $user || $user->isDisabled() || $currentUser->isID($userID)) {
                        abort(103, __('Invalid user ID.'));
                    }

                    if ($currentUser->wasBlocked($userID)) {
                        abort(109);
                    }

                    $condition = [
                        'target_id' => $user->getKey(),
                        'is_friend' => 1,
                    ];

                    if ($currentUser->relationship()->where($condition)->exists()) {
                        abort(126, __('You cannot send the friend invitation.'));
                    }

                    $this->friend = $user;
                },
            ],
        ];
    }

    /**
     * @return User
     */
    public function getFriend()
    {
        return $this->friend;
    }
}
