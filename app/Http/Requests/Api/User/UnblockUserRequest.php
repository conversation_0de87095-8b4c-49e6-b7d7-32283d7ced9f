<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;
use App\Models\User;

class UnblockUserRequest extends BaseRequest
{
    /**
     * @var User|null
     */
    protected ?User $blockedUser = null;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                'required',
                function ($attribute, $value, $fail) {
                    $blockedUser = $this->getUserByID($value);
                    if (! $blockedUser || ! $blockedUser->isEnabled()) {
                        abort(103, __('Invalid user ID.'));
                    }

                    /** @var User $user */
                    $user = $this->user();

                    $condition = [
                        'target_id' => $blockedUser->getKey(),
                        'is_blocked' => 1,
                    ];

                    if (! $user->relationship()->where($condition)->exists()) {
                        abort(123, __('Invalid user ID.'));
                    }

                    $this->blockedUser = $blockedUser;
                }
            ],
        ];
    }

    /**
     * @param int $id
     * @return User|null
     */
    protected function getUserByID($id)
    {
        return userRepository()->find($id);
    }

    /**
     * @return null|User
     */
    public function getBlockedUser()
    {
        return $this->blockedUser;
    }
}
