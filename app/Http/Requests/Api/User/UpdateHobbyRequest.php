<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;

class UpdateHobbyRequest extends BaseRequest
{
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'brief' => [
                'required',
                'string',
                'min:5',
                'max:1000',
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes(): array
    {
        return [
            'brief' => __('validation.attributes.hobby'),
        ];
    }
}
