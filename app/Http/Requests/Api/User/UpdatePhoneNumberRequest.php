<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class UpdatePhoneNumberRequest extends BaseRequest
{
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'phone' => [
                'required',
                'regex:/^(\+84|\+81)\d{9,10}$/i',
                Rule::unique('users')->ignore($this->user()->getKey(), 'user_id'),
            ],
        ];
    }
}
