<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;
use App\Models\Notification;

class MarkNotificationWasReadRequest extends BaseRequest
{
    /**
     * @var Notification|null
     */
    protected $notification;

    /**
     * @param int $id
     * @return Notification|\Illuminate\Database\Eloquent\Collection|\Illuminate\Database\Eloquent\Model|null
     */
    protected function getNotificationInstance(int $id)
    {
        /** @var Notification $model */
        $model = app(Notification::class);

        return $model->newQuery()->find($id);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                function ($attr, $value, $fail) {
                    $value = (int) $value;
                    if (! $value) {
                        $fail(__('Invalid notification ID.'));
                    }

                    $notification = $this->getNotificationInstance($value);
                    if (! $notification || $this->invalidOwner($notification)) {
                        $fail(__('Invalid notification ID.'));
                    }

                    $this->notification = $notification;
                },
            ],
        ];
    }

    /**
     * @param Notification $notification
     * @return bool
     */
    protected function invalidOwner(Notification $notification): bool
    {
        $notificationUserId = (int) $notification->user_id;
        $userId = (int) $this->user()->getKey();

        return $notificationUserId !== $userId;
    }

    /**
     * @return Notification|null
     */
    public function getNotification()
    {
        return $this->notification;
    }
}
