<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;
use App\Models\User;

class FetchProfileRequest extends BaseRequest
{
    /**
     * @var User|null
     */
    protected $currentUser;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                function ($attr, $value, $fail) {
                    $value = (int) $value;
                    $currentUser = $this->user();

                    if ($value) {
                        /** @var User $user */
                        $user = userRepository()->find($value);
                        $userId = $user?->getKey();

                        if (! $user || $user->isDisabled()) {
                            abort(103, __('Invalid user ID.'));
                        }

                        if ($currentUser->isNotID($userId) && $currentUser->wasBlocked($userId)) {
                            abort(109);
                        }

                        $currentUser = $user;
                    }

                    $this->currentUser = $currentUser;
                },
            ],
        ];
    }

    /**
     * @return User
     */
    public function getCurrentUser()
    {
        return $this->currentUser ?? $this->user();
    }
}
