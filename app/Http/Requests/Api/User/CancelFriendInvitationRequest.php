<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;
use App\Models\User;

class CancelFriendInvitationRequest extends BaseRequest
{
    /**
     * @var User
     */
    protected $friend;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                'required',
                function ($attr, $value, $fail) {
                    /** @var User $user */
                    $user = userRepository()->find($value);

                    $currentUser = $this->user();

                    if (! $user || $user->isDisabled() || $currentUser->isID($user->getKey())) {
                        abort(122, __('Invalid user ID.'));
                    }

                    $condition = [
                        'target_id' => $user->getKey(),
                        'is_friend' => 2,
                    ];

                    if (! $currentUser->relationship()->where($condition)->exists()) {
                        abort(129, __('You cannot cancel the friend invitation.'));
                    }

                    $this->friend = $user;
                },
            ],
        ];
    }

    /**
     * @return User
     */
    public function getFriend()
    {
        return $this->friend;
    }
}
