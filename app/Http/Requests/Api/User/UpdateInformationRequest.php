<?php

namespace App\Http\Requests\Api\User;

use App\Enums\Gender;
use App\Enums\ProfileType;
use App\Http\Requests\BaseRequest;
use App\Models\School;
use App\Models\User;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class UpdateInformationRequest extends BaseRequest
{
    /**
     * @var integer
     */
    protected $maxFileSize = 5;

    /**
     * @var School|null
     */
    protected $newSchool;

    /**
     * @return array
     */
    public function rules(): array
    {
        /** @var User $user */
        $user = $this->user();
        $profile = $user->profile;

        $briefRules = [];
        if ($profile && ! empty($profile->brief)) {
            $briefRules = [
                'required',
                'string',
                'max:1000',
            ];
        }

        $profileRules = empty($user->profile_type) ? [] : [
            'required',
            Rule::in(ProfileType::toArray()),
        ];

        return [
            'name' => [
                'required',
                'string',
                'min:2',
                'max:190',
            ],
            'position' => [
                'required',
                'string',
                'min:5',
                'max:190',
            ],
            'birthday' => [
                'bail',
                'required',
                'date_format:Y-m-d',
                function ($attr, $value, $fail) {
                    $age = datetime()->getAgeFromDate($value);
                    if ($age <= 12) {
                        $fail(__('Age must be greater than 12 years old.'));
                    }
                }
            ],
            'avatar' => [
                'mimes:png,jpeg,jpg,webp,svg',
                'max:' . $this->maxFileSize * 1024,
            ],

            /**
             * profile
             */
            'profile_type' => $profileRules,
            'school_id' => [
                'required_if:profile_type,' . ProfileType::STUDENT->value,
                function ($attr, $value, $fail) {
                    when($this->input('profile_type') === ProfileType::STUDENT->value, function () use ($value, $fail) {
                        if (! $value || ! ($school = School::query()->find($value))) {
                            $fail(__('Invalid school ID.'));
                        }

                        $this->newSchool = $school ?? null;
                    });
                },
            ],
            /*'override_school' => [
                'nullable',
                'required_with:school_id',
                Rule::in([0, 1]),
            ],*/
            'work' => [
                'nullable',
                'required_if:profile_type,' . ProfileType::EMPLOYEE->value,
                'string',
                'max:190',
            ],
            'expert' => [
                'nullable',
                'required_if:profile_type,' . ProfileType::EMPLOYEE->value,
                'string',
                'max:500',
            ],
            'service_in_charge' => [
                'nullable',
                'required_if:profile_type,' . ProfileType::EMPLOYEE->value,
                'string',
                'max:1000',
            ],
            'brief' => $briefRules,
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => __('validation.custom.user.name'),
        ];
    }

    /**
     * @return School|null
     */
    public function getNewSchool()
    {
        return $this->newSchool;
    }

    /**
     * @param $validator
     * @return void
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function withValidator($validator)
    {
        $validator->after(function (Validator $validator) {
            $user = $this->user();

            if ($user->profile_type === ProfileType::STUDENT->value && $user->profile?->school_id) {
                $override = $this->input('override_school');
                if (is_null($override)) {
                    $this->setError('override_school', __('validation.required', [
                        'attribute' => __('validation.attributes.override_school'),
                    ]));
                }

                if (! in_array($override, [0, 1])) {
                    $this->setError('override_school', __('validation.in', [
                        'attribute' => __('validation.attributes.override_school'),
                    ]));
                }
            }

            /**
             * validate gender
             * khi đã có gender thì bắt buộc phải nhập
             */
            if (! $user->emptyGender()) {
                $gender = $this->post('gender');
                if (! $gender) {
                    $this->setError('gender', __('validation.required', [
                        'attribute' => __('validation.attributes.gender'),
                    ]));
                }

                if (! in_array($gender, Gender::toArray())) {
                    $this->setError('gender', __('validation.in', [
                        'attribute' => __('validation.attributes.gender'),
                    ]));
                }
            }
        });
    }
}
