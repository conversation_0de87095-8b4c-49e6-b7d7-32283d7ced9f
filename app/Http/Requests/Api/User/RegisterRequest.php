<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\Api\Traits\ValidateSignature;
use App\Http\Requests\BaseRequest;
use App\Models\InviteToken;
use App\Models\User;

class RegisterRequest extends BaseRequest
{
    use ValidateSignature;

    /**
     * @var InviteToken|null
     */
    protected $inviteToken;

    /**
     * @var User|null
     */
    protected ?User $referrerUser = null;

    /**
     * @var null|User
     */
    protected ?User $registeredUser = null;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'birthday' => [
                //'nullable',
                'required',
                'date_format:Y-m-d',
                function ($attr, $value, $fail) {
                    if ($value) {
                        $age = datetime()->getAgeFromDate($value);
                        if ($age <= 12) {
                            $fail(__('Age must be greater than 12 years old.'));
                        }
                    }
                }
            ],
            'phone' => [
                'required',
                'regex:/^(\+84|\+81)\d{9,10}$/i',
                // 'unique:users',
                function ($attribute, $value, $fail) {
                    $user = $this->getUserByPhone($value);
                    if ($user && ! $user->isPending()) {
                        abort(101, __('Your phone number has been already existed.'));
                    }

                    $this->registeredUser = $user;
                }
            ],
            'referrer_code' => [
                'required',
                function ($attribute, $value, $fail) {
                    $inviteToken = $this->getInviteToken($value);

                    if (!$inviteToken || !$inviteToken->isValid()) {
                        abort(102, __('Your referrer code is invalid.'));
                    }

                    /** @var User $user */
                    $user = $inviteToken->user;

                    if (!$user || !$user->isEnabled()) {
                        abort(102, __('Your referrer code is invalid.'));
                    }

                    $this->inviteToken = $inviteToken;
                    $this->referrerUser = $user;
                },
            ],
            'aesKey' => [
                'required',
            ],
            'iv' => [
                'required',
            ],
            'encrypted_data' => [
                'required',
            ],
        ];
    }

    /**
     * @param string $value
     * @return User|null
     */
    protected function getUserByPhone(string $value)
    {
        return userRepository()->findBy($value);
    }

    /**
     * @param string $uuid
     * @return User|null
     */
    public function getUserByUuid(string $uuid)
    {
        return userRepository()->findBy($uuid, 'uuid');
    }

    /**
     * @param string $token
     * @return InviteToken|null
     */
    protected function getInviteToken(string $token)
    {
        return InviteToken::query()->where('token', $token)->first();
    }

    /**
     * @return User|null
     */
    public function getReferrerUser()
    {
        return $this->referrerUser;
    }

    /**
     * @return InviteToken|null
     */
    public function getInviteTokenInstance()
    {
        return $this->inviteToken;
    }

    /**
     * @return null|User
     */
    public function getRegisteredUser()
    {
        return $this->registeredUser;
    }
}
