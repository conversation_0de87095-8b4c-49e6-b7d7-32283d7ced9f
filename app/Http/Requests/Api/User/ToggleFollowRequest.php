<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;
use App\Models\User;

class ToggleFollowRequest extends BaseRequest
{
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                'bail',
                'required',
                function ($attr, $value, $fail) {
                    /** @var User $user */
                    $user = userRepository()->find($value);

                    if (! $user || $user->isDisabled() || $user->isID($this->user()->getKey())) {
                        abort(122, __('Invalid user ID.'));
                    }
                },
            ],
        ];
    }
}
