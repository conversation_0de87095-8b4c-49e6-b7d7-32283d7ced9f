<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;
use App\Models\User;

class UnfriendRequest extends BaseRequest
{
    /**
     * @var User
     */
    protected $friend;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                'required',
                function ($attr, $value, $fail) {
                    /** @var User $user */
                    $user = userRepository()->find($value);
                    $userID = $user?->getKey();

                    $currentUser = $this->user();

                    if (! $user || $user->isDisabled() || $currentUser->isID($userID)) {
                        abort(103, __('Invalid user ID.'));
                    }

                    $condition = [
                        'target_id' => $userID,
                        'is_friend' => 1,
                    ];

                    if (! $currentUser->relationship()->where($condition)->exists()) {
                        abort(127, __('You cannot unfriend this user because you are not friends.'));
                    }

                    $this->friend = $user;
                },
            ],
        ];
    }

    /**
     * @return User
     */
    public function getFriend()
    {
        return $this->friend;
    }
}
