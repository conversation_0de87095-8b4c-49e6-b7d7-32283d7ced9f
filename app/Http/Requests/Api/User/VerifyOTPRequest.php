<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;

class VerifyOTPRequest extends BaseRequest
{
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'otp' => [
                'bail',
                'required',
                function ($attr, $value, $fail) {
                    if (! otp()->isValid($this->user(), $value)) {
                        $fail(__('Invalid OTP or OTP was expired.'));
                    }
                }
            ],
        ];
    }
}
