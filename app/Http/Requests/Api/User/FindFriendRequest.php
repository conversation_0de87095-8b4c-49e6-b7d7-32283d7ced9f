<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;
//use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class FindFriendRequest extends BaseRequest
{
    /**
     * @var array
     */
    protected $phones = [];

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'aesKey' => [
                'required',
            ],
            'iv' => [
                'required',
            ],
            'phoneData' => [
                'required',
            ],
            /*'country_code' => [
                'required',
                Rule::in(['+81', '+84']),
            ],*/
        ];
    }

    /**
     * @param $validator
     * @return void
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function withValidator($validator)
    {
        $validator->after(function (Validator $validator) {
            $error = $validator->errors();

            if (! $error->has('aesKey') && ! $error->has('iv') && ! $error->has('phoneData')) {
                $phoneData = $this->input('phoneData');
                $aesKey = $this->input('aesKey');
                $iv = $this->input('iv');

                if (! $data = encryptor()->decode($phoneData, $aesKey, $iv)) {
                    $this->setError('phoneData', __('Can not decrypt the phone data.'));
                }

                $this->phones = $this->preparePhoneData($data);
            }
        });
    }

    /**
     * @param string $data
     * @return array
     */
    protected function preparePhoneData(string $data): array
    {
        $phones = explode(',', $data);

        return array_map(fn ($phone) => $this->formatPhone($phone), $phones);
    }

    /**
     * @param string $phone
     * @return string
     */
    protected function formatPhone(string $phone)
    {
        $phone = str_replace([' ', '-', '(', ')'], '', $phone);

        /**
         * số đt bắt đầu bằng số 0
         * => chuyển số 0 đầu tiên thành mã quốc gia
         */
        if (str_starts_with($phone, '0')) {
            $phone = preg_replace('/^0?/', $this->post('country_code', '+81'), $phone);
        }

        return $phone;
    }

    /**
     * @return array
     */
    public function getPhones()
    {
        return $this->phones;
    }
}
