<?php

namespace App\Http\Requests\Api\User;

use App\Enums\Gender;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class UpdateGenderRequest extends BaseRequest
{
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'gender' => [
                'required',
                Rule::in(Gender::toArray()),
            ],
        ];
    }
}
