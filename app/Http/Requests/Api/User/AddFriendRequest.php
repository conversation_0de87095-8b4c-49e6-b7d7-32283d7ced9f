<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;
use App\Models\User;

class AddFriendRequest extends BaseRequest
{
    /**
     * @var User
     */
    protected $friend;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                'required',
                function ($attr, $value, $fail) {
                    /** @var User $user */
                    $user = userRepository()->find($value);
                    $userID = $user?->getKey();

                    $currentUser = $this->user();
                    if (! $user || $user->isDisabled() || $currentUser->isID($userID)) {
                        abort(103, __('Invalid user ID.'));
                    }

                    if ($currentUser->wasBlocked($userID)) {
                        abort(109);
                    }

                    $condition = [
                        'target_id' => $currentUser->getKey(),
                        'is_friend' => 2, // 2 = pending friend
                    ];

                    if (! $user->relationship()->where($condition)->exists()) {
                        abort(126, __('You cannot add this person because there is no friend request yet.'));
                    }

                    $this->friend = $user;
                },
            ],
        ];
    }

    /**
     * @return User
     */
    public function getFriend()
    {
        return $this->friend;
    }
}
