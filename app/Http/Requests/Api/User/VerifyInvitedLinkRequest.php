<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;
use App\Models\User;

class VerifyInvitedLinkRequest extends BaseRequest
{
    /**
     * @var User|null
     */
    protected ?User $user = null;

    /**
     *
     * /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'referrer_code' => [
                'required',
                function (string $attribute, $value, $fail) {
                    /** @var User $user */
                    $user = $this->getUserByUuid($value);

                    if (!$user || !$user->isEnabled() || !$user->canInviteUser()) {
                        abort(102, __('Your referrer code is invalid.'));
                    }

                    $this->user = $user;
                },
            ],
        ];
    }

    /**
     * @param string $uuid
     * @return User|null
     */
    protected function getUserByUuid(string $uuid)
    {
        return userRepository()->findBy($uuid, 'uuid');
    }

    /**
     * @return User|null
     */
    public function getReferrerUser()
    {
        return $this->user;
    }
}
