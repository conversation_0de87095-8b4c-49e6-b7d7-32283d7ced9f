<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class GetNotificationRequest extends BaseRequest
{
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'type' => [
                'required',
                Rule::in(['all', 'notification', 'pin']),
            ],
        ];
    }
}
