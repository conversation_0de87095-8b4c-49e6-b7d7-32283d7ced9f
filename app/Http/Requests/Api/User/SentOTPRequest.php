<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\Api\Traits\ValidateSignature;
use App\Http\Requests\BaseRequest;
use App\Models\User;
use Illuminate\Validation\Validator;
use Lara<PERSON>\Sanctum\PersonalAccessToken;

class SentOTPRequest extends BaseRequest
{
    use ValidateSignature;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'aesKey' => [
                'required',
            ],
            'iv' => [
                'required',
            ],
            'encrypted_data' => [
                'required',
            ],
        ];
    }

    protected function withValidator($validator)
    {
        $validator->after(function (Validator $validator) {
            $error = $validator->errors();

            if ($error->isEmpty()) {
                /**
                 * validate can resend time
                 * @var User $user
                 */
                $user = $this->user();
                $token = $user->currentAccessToken();

                if ($token instanceof PersonalAccessToken) {
                    [, $canResendUtil] = explode('_', $token->name);

                    $now = now('UTC')->getTimestamp();
                    if ($now < $canResendUtil) {
                        $this->setError('otp', trans_choice('otp_choice', [
                            'second' => $canResendUtil - $now,
                        ]));
                    }
                }
            }
        });
    }
}
