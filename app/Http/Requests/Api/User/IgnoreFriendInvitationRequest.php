<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;
use App\Models\User;

class IgnoreFriendInvitationRequest extends BaseRequest
{
    /**
     * @var User
     */
    protected $friend;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                'required',
                function ($attr, $value, $fail) {
                    /** @var User $user */
                    $user = userRepository()->find($value);

                    $currentUser = $this->user();

                    if (! $user || $user->isDisabled() || $currentUser->isID($user->getKey())) {
                        abort(122, __('Invalid user ID.'));
                    }

                    $condition = [
                        'target_id' => $currentUser->getKey(),
                        'is_friend' => 2,
                    ];

                    if (! $user->relationship()->where($condition)->exists()) {
                        abort(128, __('You cannot ignore the friend invitation.'));
                    }

                    $this->friend = $user;
                },
            ],
        ];
    }

    /**
     * @return User
     */
    public function getFriend()
    {
        return $this->friend;
    }
}
