<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\BaseRequest;

class MultipleFollowRequest extends BaseRequest
{
    /**
     * @var array
     */
    protected $ids = [];

    /**
     * @var array
     */
    protected $ignoreIds = [];

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'user_ids' => [
                'nullable',
                function ($attr, $value, $fail) {
                    if ($value = explode(',', $value)) {
                        if (in_array($this->user()->getKey(), $value)) {
                            $fail(__('Invalid user ID.'));
                        }

                        $users = userRepository()->findMany($value);
                        if ($users->count() !== count($value)) {
                            $fail(__('Invalid user ID.'));
                        }

                        $this->ids = $value;
                    }
                },
            ],
            'ignores' => [
                'nullable',
                function ($attr, $value, $fail) {
                    if ($value = explode(',', $value)) {
                        if (in_array($this->user()->getKey(), $value)) {
                            $fail(__('Invalid user ID.'));
                        }

                        $users = userRepository()->findMany($value);
                        if ($users->count() !== count($value)) {
                            $fail(__('Invalid user ID.'));
                        }

                        $this->ignoreIds = $value;
                    }
                },
            ],
        ];
    }

    /**
     * @return array
     */
    public function getUserIds()
    {
        return $this->ids;
    }

    /**
     * @return array
     */
    public function getIgnoreIds()
    {
        return $this->ignoreIds;
    }
}
