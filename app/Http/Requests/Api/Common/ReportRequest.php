<?php

namespace App\Http\Requests\Api\Common;

use App\Http\Requests\BaseRequest;
use App\Models\Contracts\Reportable;
use App\Models\Traits\HasReportable;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Illuminate\Validation\Validator;

class ReportRequest extends BaseRequest
{
    use HasReportable;

    /**
     * @var Reportable
     */
    protected $reportObject;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                'integer',
            ],
            'type' => [
                'required',
                Rule::in(['post', 'post_answer', 'comment']),
            ],
            'reason' => [
                'required',
                Rule::in([1, 2, 3, 4]),
            ],
            'description' => [
                'required_if:reason,4',
                'string',
                'max:500',
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'id' => __('validation.attributes.report.id'),
            'type' => __('validation.attributes.report.type'),
            'reason' => __('validation.attributes.report.reason'),
            'description' => __('validation.attributes.report.description'),
        ];
    }

    /**
     * @param $validator
     * @return void
     * @throws ValidationException
     */
    protected function withValidator($validator)
    {
        $validator->after(function (Validator $validator) {
            $error = $validator->errors();

            if (! $error->has('id') && ! $error->has('type')) {
                if (! $object = $this->getReportObject($this->input('id'), $this->input('type'))) {
                    $this->setError('id', __('Invalid object ID.'));
                }

                $this->reportObject = $object;
            }
        });
    }

    /**
     * @return Reportable
     */
    public function getReportObjectInstance()
    {
        return $this->reportObject;
    }
}
