<?php

namespace App\Http\Requests\Api\Community;

use App\Http\Requests\BaseRequest;
use App\Models\Community;
use App\Models\CommunityUser;

class CommunityLeaveRequest extends BaseRequest
{
    /**
     * @var Community|null
     */
    protected $community;

    /**
     * @var CommunityUser|null
     */
    protected $communityUser;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'community_id' => [
                'required',
                function ($attr, $value, $fail) {
                    $currentUserId = $this->user()->getKey();

                    /** @var Community $community */
                    $community = communityRepository()->find($value);
                    if (! $community || $community->ownerBy($currentUserId)) {
                        abort(131, __('Invalid community ID.'));
                    }

                    $this->community = $community;

                    /** @var CommunityUser $record */
                    $record = $community->members()->where('user_id', $currentUserId)->first();

                    if (! $record || $record->isInActive() || $record->wasBlocked()) {
                        abort(131, __('Invalid community ID.'));
                    }

                    $this->communityUser = $record;
                },
            ],
        ];
    }

    /**
     * @return Community|null
     */
    public function getCommunity()
    {
        return $this->community;
    }

    /**
     * @return CommunityUser
     */
    public function getCommunityUser()
    {
        return $this->communityUser;
    }
}
