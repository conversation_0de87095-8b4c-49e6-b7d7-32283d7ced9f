<?php

namespace App\Http\Requests\Api\Community;

use App\Http\Requests\BaseRequest;
use App\Models\Community;

class DetailCommunityRequest extends BaseRequest
{
    /**
     * @var Community|null
     */
    protected $community;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'community_id' => [
                'required',
                function ($attr, $value, $fail) {
                    /** @var Community|null $community */
                    $community = communityRepository()->find($value);

                    if (! $community || $community->isInActive()) {
                        abort(131, __('Invalid community ID.'));
                    }

                    /**
                     * khi community là nhóm kín
                     * thì chỉ có thành viên mới được xem nhóm
                     */
                    if ($community->isClosed() && $this->user()->isNotMemberOf($community->getKey())) {
                        abort(132, __('You do not have permission to view this community group.'));
                    }

                    $this->community = $community;
                },
            ],
        ];
    }

    /**
     * @return Community
     */
    public function getCommunity()
    {
        return $this->community;
    }
}
