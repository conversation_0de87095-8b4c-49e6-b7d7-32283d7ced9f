<?php

namespace App\Http\Requests\Api\Community;

use App\Http\Requests\BaseRequest;
use App\Models\Community;
use App\Models\User;

class CommunityRemoveMemberRequest extends BaseRequest
{
    /**
     * @var Community|null
     */
    protected $community;

    /**
     * @return array
     */
    public function rules(): array
    {
        $currentUserId = $this->user()->getKey();

        return [
            'community_id' => [
                'required',
                function ($attr, $value, $fail) use ($currentUserId) {
                    /** @var Community $community */
                    $community = communityRepository()->find($value);
                    if (! $community || $community->isInActive() || $community->wasNotOwnedBy($currentUserId)) {
                        abort(131, __('Invalid community ID.'));
                    }

                    $this->community = $community;
                },
            ],
            'user_id' => [
                'required',
                function ($attr, $value, $fail) use ($currentUserId) {
                    /** @var User $user */
                    $user = userRepository()->find($value);
                    if (! $user || $user->isID($currentUserId)) {
                        abort(103, __('Invalid user ID.'));
                    }
                },
            ],
        ];
    }

    /**
     * @return Community
     */
    public function getCommunity()
    {
        return $this->community;
    }
}
