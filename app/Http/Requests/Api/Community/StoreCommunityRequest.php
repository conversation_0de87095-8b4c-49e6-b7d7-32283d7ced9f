<?php

namespace App\Http\Requests\Api\Community;

use App\Http\Requests\BaseRequest;
use App\Models\Community;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;

class StoreCommunityRequest extends BaseRequest
{
    /**
     * @var Community|null
     */
    protected $community;

    /**
     * @var integer
     */
    protected $maxFileSize = 15;

    /**
     * @return array
     */
    public function rules(): array
    {
        if (! $this->input('community_id')) {
            $user = $this->user();
            $user->loadCount([
                'friends as friends_count' => function ($query) {
                    $query->whereHas('target', fn ($q) => $q->where('users.status', 1));
                },
            ]);

            $friendCount = (int) $user->getAttribute('friends_count');
            if ($friendCount < 5) {
                abort(135, __('You do not have permission to perform this action.'));
            }
        }

        return [
            'community_id' => [
                function ($attr, $value, $fail) {
                    if ($value = (int) $value) {
                        /** @var Community $community */
                        $community = communityRepository()->find($value);
                        if (! $community || ! $community->ownerBy($this->user()->getKey())) {
                            abort(131, __('Invalid community ID.'));
                        }

                        $this->community = $community;
                    }
                },
            ],
            'name' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $length = grapheme_strlen($value);

                    if ($length < 2) {
                        $fail(__('validation.min.string', [
                            'attribute' => $this->getAttribute($attribute),
                            'min' => 2,
                        ]));
                    }

                    if ($length > 15) {
                        $fail(__('validation.max.string', [
                            'attribute' => $this->getAttribute($attribute),
                            'max' => 15,
                        ]));
                    }
                },
            ],
            'description' => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    $length = grapheme_strlen($value);

                    if ($length > 60) {
                        $fail(__('validation.max.string', [
                            'attribute' => $this->getAttribute($attribute),
                            'max' => 60,
                        ]));
                    }
                }
            ],
            'avatar' => [
                'required_without:community_id',
                'mimes:png,jpeg,jpg,webp,svg',
                'max:' . $this->maxFileSize * 1024,
            ],
            'closed' => [
                'required',
                Rule::in([0, 1]),
            ],
        ];
    }

    /**
     * @param string $attribute
     * @return string
     */
    public function getAttribute(string $attribute)
    {
        $attributes = $this->attributes();

        return Arr::get($attributes, $attribute, '');
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => __('validation.attributes.community_name'),
            'description' => __('validation.attributes.community_description'),
            'closed' => __('validation.attributes.community_closed'),
        ];
    }

    /**
     * @return Community
     */
    public function getCommunity()
    {
        return $this->community ?? app(Community::class);
    }
}
