<?php

namespace App\Http\Requests\Api\Community;

use App\Http\Requests\BaseRequest;
use App\Models\Community;
use App\Models\User;

class CommunityAddMemberRequest extends BaseRequest
{
    /**
     * @var Community|null
     */
    protected $community;

    /**
     * @var User|null
     */
    protected $communityUser;

    /**
     * @return array
     */
    public function rules(): array
    {
        $currentUserId = $this->user()->getKey();

        return [
            'community_id' => [
                'required',
                function ($attr, $value, $fail) use ($currentUserId) {
                    /** @var Community $community */
                    $community = communityRepository()->find($value);
                    if (! $community || $community->isInActive() || $this->user()->isNotMemberOf($community->getKey())) {
                        abort(131, __('Invalid community ID.'));
                    }

                    $this->community = $community;
                },
            ],
            'user_id' => [
                'required',
                function ($attr, $value, $fail) use ($currentUserId) {
                    /** @var User $user */
                    $user = userRepository()->find($value);
                    if (! $user || $user->isDisabled() || $user->isID($currentUserId)) {
                        abort(103, __('Invalid user ID.'));
                    }

                    if ($this->user()->wasBlocked($user->getKey())) {
                        abort(109);
                    }

                    $this->communityUser = $user;
                },
            ],
        ];
    }

    /**
     * @return Community
     */
    public function getCommunity()
    {
        return $this->community;
    }

    /**
     * @return User|null
     */
    public function getCommunityUser()
    {
        return $this->communityUser;
    }
}
