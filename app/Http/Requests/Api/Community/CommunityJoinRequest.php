<?php

namespace App\Http\Requests\Api\Community;

use App\Http\Requests\BaseRequest;
use App\Models\Community;

class CommunityJoinRequest extends BaseRequest
{
    /**
     * @var Community|null
     */
    protected $community;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'community_id' => [
                'required',
                function ($attr, $value, $fail) {
                    /** @var Community $community */
                    $community = communityRepository()->find($value);
                    if (! $community || $community->isInActive() || $community->isClosed() || $community->wasBlocked($this->user()->getKey())) {
                        abort(131, __('Invalid community ID.'));
                    }

                    $this->community = $community;
                },
            ],
        ];
    }

    /**
     * @return Community
     */
    public function getCommunity()
    {
        return $this->community;
    }
}
