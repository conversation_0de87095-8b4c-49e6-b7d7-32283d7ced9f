<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\Api\Traits\ValidatePostAnswer;
use App\Http\Requests\BaseRequest;
use App\Models\PostAnswer;
use App\Models\User;

class DonatePostAnswerRequest extends BaseRequest
{
    use ValidatePostAnswer;

    /**
     * @var PostAnswer|null
     */
    protected $postAnswer;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'answer_id' => [
                'bail',
                'required',
                function ($attr, $value, $fail) {
                    $postAnswer = $this->getPostAnswerInstance($value, $fail);

                    /** @var User $user */
                    $user = $this->user();

                    if ($user->isID($postAnswer->user_id)) {
                        abort(113, __('You can not donate your post answer.'));
                    }

                    if ($user->wasBlocked($postAnswer->user_id)) {
                        abort(109, __('Invalid post answer ID.'));
                    }

                    $this->postAnswer = $postAnswer;
                },
            ],
            'count' => [
                'required',
                'integer',
                'min:1',
                'max:500',
                function ($attr, $value, $fail) {
                    /** @var User $user */
                    $user = $this->user();

                    $value = (int) $value;
                    if (! $user->hasEnoughCoins($value)) {
                        abort(107, __('You do not have enough coins to perform this action.'));
                    }
                },
            ],
        ];
    }

    /**
     * @return PostAnswer|null
     */
    public function getPostAnswer()
    {
        return $this->postAnswer;
    }
}
