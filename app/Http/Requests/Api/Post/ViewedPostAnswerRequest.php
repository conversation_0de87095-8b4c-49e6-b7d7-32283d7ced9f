<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\BaseRequest;

class ViewedPostAnswerRequest extends BaseRequest
{
    /**
     * @var array
     */
    protected $ids = [];

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                function ($attr, $value, $fail) {
                    $this->ids = explode(',', $value);
                },
            ],
        ];
    }

    /**
     * @return array
     */
    public function getAnswerIds()
    {
        return $this->ids;
    }
}
