<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\Api\Traits\ValidatePostAnswer;
use App\Http\Requests\BaseRequest;
use App\Models\PostAnswer;
use App\Models\Reaction;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class CancelDonatePostAnswerRequest extends BaseRequest
{
    use ValidatePostAnswer;

    /**
     * @var PostAnswer|null
     */
    protected $postAnswer;

    /**
     * @var Reaction|null
     */
    protected $reaction;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'answer_id' => [
                'bail',
                'required',
                function ($attr, $value, $fail) {
                    $postAnswer = $this->getPostAnswerInstance($value, $fail);

                    /**
                     * check user blocked
                     */
                    if ($this->user()->wasBlocked($postAnswer->user_id)) {
                        abort(109, __('Invalid post answer ID.'));
                    }

                    $postAnswer->load([
                        'reactions' => function (MorphMany $query) {
                            $query->where('user_id', $this->user()->getKey())
                                ->where('action', 'donated');
                        },
                    ]);

                    if (! $reaction = $postAnswer->latestDonated()) {
                        abort(114, __('You have not donated this post answer yet.'));
                    }

                    elseif (! $reaction->canBeCancel()) {
                        abort(114, __('You cannot cancel this donation because it has expired.'));
                    }

                    $this->reaction = $reaction;
                    $this->postAnswer = $postAnswer;
                },
            ],
        ];
    }

    /**
     * @return PostAnswer|null
     */
    public function getPostAnswer()
    {
        return $this->postAnswer;
    }

    /**
     * @return Reaction|null
     */
    public function getReaction()
    {
        return $this->reaction;
    }
}
