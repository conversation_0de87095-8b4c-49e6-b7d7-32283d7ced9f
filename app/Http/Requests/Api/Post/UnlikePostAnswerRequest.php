<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\Api\Traits\ValidatePostAnswer;
use App\Http\Requests\BaseRequest;
use App\Models\PostAnswer;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class UnlikePostAnswerRequest extends BaseRequest
{
    use ValidatePostAnswer;

    /**
     * @var PostAnswer|null
     */
    protected $postAnswer;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'answer_id' => [
                'bail',
                'required',
                function ($attr, $value, $fail) {
                    $postAnswer = $this->getPostAnswerInstance($value, $fail);

                    /**
                     * check user blocked
                     */
                    if ($this->user()->wasBlocked($postAnswer->user_id)) {
                        abort(109, __('Invalid post answer ID.'));
                    }

                    $postAnswer->load([
                        'reactions' => function (MorphMany $query) {
                            $query->where('user_id', $this->user()->getKey())->where('action', 'liked');
                        },
                    ]);

                    if (! $postAnswer->latestLiked()) {
                        abort(112, __('You have not liked this answer yet.'));
                    }

                    $this->postAnswer = $postAnswer;
                },
            ],
        ];
    }

    /**
     * @return PostAnswer|null
     */
    public function getPostAnswer()
    {
        return $this->postAnswer;
    }
}
