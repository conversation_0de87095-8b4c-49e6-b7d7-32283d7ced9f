<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\Api\Traits\ValidatePost;
use App\Http\Requests\BaseRequest;
use App\Models\Post;

class DetailPostRequest extends BaseRequest
{
    use ValidatePost;

    /**
     * @var Post|null
     */
    protected $post;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'post_id' => [
                'required',
                function ($attr, $value, $fail) {
                    $this->post = $this->getPostInstance($value, $fail);

                    $postOwnerID = $this->post->user_id;
                    $user = $this->user();
                    $community = $this->post->communityPost?->community;

                    $isCommunityAdmin = (bool) $community?->ownerBy($user->getKey());
                    $canViewPost = $user->isID($postOwnerID) || ! $user->wasBlocked($postOwnerID) || $isCommunityAdmin;

                    if (! $canViewPost) {
                        abort(109, __('Invalid post ID.'));
                    }
                },
            ],
        ];
    }

    /**
     * @return Post
     */
    public function getPost()
    {
        return $this->post;
    }
}
