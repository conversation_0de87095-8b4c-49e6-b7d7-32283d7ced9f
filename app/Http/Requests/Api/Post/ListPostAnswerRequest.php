<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\BaseRequest;
use App\Models\Post;
use App\Models\PostAnswer;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Illuminate\Validation\Validator;

class ListPostAnswerRequest extends BaseRequest
{
    /**
     * @var Post|PostAnswer|null
     */
    protected $object;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'object_type' => [
                'bail',
                'required_without:user_id',
                Rule::in(['post', 'answer']),
            ],
            'object_id' => [
                'required_without:user_id',
            ],
            'user_id' => [
                'required_without:object_type,object_id',
            ],
        ];
    }

    /**
     * @param $validator
     * @return void
     * @throws ValidationException
     */
    protected function withValidator($validator)
    {
        $validator->after(function (Validator $validator) {
            $type = $this->input('object_type');

            $user = $this->user();

            /**
             * validate blocking user
             */
            $userId = $this->input('user_id');
            if ($userId && $user->wasBlocked($userId)) {
                abort(109, __('Invalid user ID.'));
            }

            $id = $this->input('object_id');
            if (in_array($type, ['post', 'answer']) && ! empty($id)) {
                /** @var Post|PostAnswer|null $object */
                $object = $type === 'post' ? postRepository()->find($id) : postAnswerRepository()->find($id);

                if (! $object || $object->wasDeleted()) {
                    $this->setError('object_id', __('Invalid object ID.'));
                }

                /**
                 * validate blocking user
                 */
                if ($user->wasBlocked($object->getAttribute('user_id'))) {
                    abort(109, __('Invalid object ID.'));
                }

                if ($this->has('withParent')) {
                    $this->object = $object;
                }

                unset($object);
            }
        });
    }

    /**
     * @return Post|PostAnswer|null
     */
    public function getParentObject()
    {
        return $this->object;
    }
}
