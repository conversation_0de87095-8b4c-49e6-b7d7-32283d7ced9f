<?php

namespace App\Http\Requests\Api\Post;

use App\Enums\PremiumFeatureType;
use App\Http\Requests\Api\Traits\ValidatePostAnswer;
use App\Http\Requests\BaseRequest;
use App\Models\PostAnswer;

class PinPostAnswerRequest extends BaseRequest
{
    use ValidatePostAnswer;

    /**
     * @var PostAnswer|null
     */
    protected $postAnswer;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'answer_id' => [
                'required',
                function ($attr, $value, $fail) {
                    $postAnswer = $this->getPostAnswerInstance($value, $fail);
                    if ($postAnswer->level != 1) {
                        abort(111, __('Invalid post answer ID.'));
                    }

                    if ($postAnswer->post->isNotOwnerBy($this->user()->getKey())) {
                        abort(141, __('You do not have permission to pin this answer.'));
                    }

                    $features = $this->user()->premiumFeatures->pluck('type')->toArray();
                    if (! in_array(PremiumFeatureType::PIN_POST_ANSWER->value, $features)) {
                        abort(141, __('You do not have permission to pin this answer.'));
                    }

                    $this->postAnswer = $postAnswer;
                },
            ],
        ];
    }

    /**
     * @return PostAnswer|null
     */
    public function getPostAnswer()
    {
        return $this->postAnswer;
    }
}
