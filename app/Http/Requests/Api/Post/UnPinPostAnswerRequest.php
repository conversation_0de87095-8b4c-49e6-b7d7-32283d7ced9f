<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\Api\Traits\ValidatePostAnswer;
use App\Http\Requests\BaseRequest;
use App\Models\PostAnswer;

class UnPinPostAnswerRequest extends BaseRequest
{
    use ValidatePostAnswer;

    /**
     * @var PostAnswer|null
     */
    protected $postAnswer;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'answer_id' => [
                'required',
                function ($attr, $value, $fail) {
                    $postAnswer = $this->getPostAnswerInstance($value, $fail);

                    if ($postAnswer->post->isNotOwnerBy($this->user()->getKey())) {
                        abort(141, __('You do not have permission to pin this answer.'));
                    }

                    if ($postAnswer->isNotPinned()) {
                        abort(142, __('Invalid post answer ID.'));
                    }

                    $this->postAnswer = $postAnswer;
                },
            ],
        ];
    }

    /**
     * @return PostAnswer|null
     */
    public function getPostAnswer()
    {
        return $this->postAnswer;
    }
}
