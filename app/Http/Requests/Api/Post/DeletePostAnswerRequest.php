<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\BaseRequest;
use App\Models\PostAnswer;

class DeletePostAnswerRequest extends BaseRequest
{
    /**
     * @var PostAnswer|null
     */
    protected $postAnswer;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                function ($attr, $value, $fail) {
                    when((int) $value, function ($answerId) use ($fail) {
                        /** @var PostAnswer|null $postAnswer */
                        $postAnswer = postAnswerRepository()->find($answerId);

                        if (! $postAnswer || $postAnswer->wasDeleted()) {
                            abort(109, __('Invalid post answer ID.'));
                        }

                        $user = $this->user();
                        $userId = $user->getKey();

                        $canDeleteAnswer = $user->canDeleteAnswer($postAnswer);
                        $community = $postAnswer->post->communityPost?->community;

                        $canDeleteAnswer = $canDeleteAnswer || $community?->ownerBy($userId);
                        if (! $canDeleteAnswer) {
                            abort(117, __('You do not have permission to perform this action.'));
                        }

                        $this->postAnswer = $postAnswer;
                    });
                },
            ],
        ];
    }

    /**
     * @return PostAnswer
     */
    public function getPostAnswer()
    {
        return $this->postAnswer;
    }
}
