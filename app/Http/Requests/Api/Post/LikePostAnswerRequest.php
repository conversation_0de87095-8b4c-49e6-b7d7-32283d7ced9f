<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\Api\Traits\ValidatePostAnswer;
use App\Http\Requests\BaseRequest;
use App\Models\PostAnswer;
use App\Models\User;

class LikePostAnswerRequest extends BaseRequest
{
    use ValidatePostAnswer;

    /**
     * @var PostAnswer|null
     */
    protected $postAnswer;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'answer_id' => [
                'bail',
                'required',
                function ($attr, $value, $fail) {
                    $postAnswer = $this->getPostAnswerInstance($value, $fail);

                    /** @var User $user */
                    $user = $this->user();

                    if ($user->isID($postAnswer->user_id)) {
                        abort(110, __('You can not like your answer.'));
                    }

                    if ($user->wasBlocked($postAnswer->user_id)) {
                        abort(109, __('Invalid post answer ID.'));
                    }

                    $this->postAnswer = $postAnswer;
                },
            ],
            'count' => [
                'required',
                'integer',
                'min:1',
                'max:500',
            ],
        ];
    }

    /**
     * @return PostAnswer
     */
    public function getPostAnswer()
    {
        return $this->postAnswer;
    }

    /**
     * @return bool
     */
    protected function userActivatedUnlimitedPoint()
    {
        return false;

        /*return $this->user()
            ->premiumFeatures()
            ->where('type', PremiumFeatureType::UNLIMIT_POINT->value)
            ->exists();*/
    }
}
