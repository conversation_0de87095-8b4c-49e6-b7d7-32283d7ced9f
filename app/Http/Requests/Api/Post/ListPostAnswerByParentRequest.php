<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\BaseRequest;
use App\Models\PostAnswer;

class ListPostAnswerByParentRequest extends BaseRequest
{
    /**
     * @var PostAnswer|null
     */
    protected $rootPostAnswer;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'parent_answer_id' => [
                'required',
                function ($attr, $value, $fail) {
                    /** @var PostAnswer $answer */
                    $answer = postAnswerRepository()->find($value);

                    if (! $answer || $answer->wasDeleted() || (1 !== (int) $answer->level)) {
                        abort(111, __('Invalid post answer ID.'));
                    }

                    $this->rootPostAnswer = $answer;
                },
            ],
        ];
    }

    /**
     * @return PostAnswer|null
     */
    public function getRootPostAnswer()
    {
        return $this->rootPostAnswer;
    }
}
