<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\Api\Traits\ValidatePost;
use App\Http\Requests\BaseRequest;
use App\Models\Post;
use App\Models\Reaction;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class UnlikePostRequest extends BaseRequest
{
    use ValidatePost;

    /**
     * @var Post|null
     */
    protected $post;

    /**
     * @var Reaction|null
     */
    protected $reaction;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'post_id' => [
                'bail',
                'required',
                function ($attr, $value, $fail) {
                    $post = $this->getPostInstance($value, $fail);

                    if ($this->user()->wasBlocked($post->user_id)) {
                        abort(109, __('Invalid post ID.'));
                    }

                    $post->load([
                        'reactions' => function (MorphMany $query) {
                            $query->where('user_id', $this->user()->getKey())
                                ->where('action', 'liked');
                        },
                    ]);

                    if (! $reaction = $post->reactions->first()) {
                        abort(120, __('You have not liked this post yet.'));
                    }

                    $this->reaction = $reaction;
                    $this->post = $post;
                },
            ],
        ];
    }

    /**
     * @return Post
     */
    public function getPost()
    {
        return $this->post;
    }

    /**
     * @return Reaction|null
     */
    public function getReaction()
    {
        return $this->reaction;
    }
}
