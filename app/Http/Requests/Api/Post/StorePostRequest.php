<?php

namespace App\Http\Requests\Api\Post;

use App\Enums\PostType;
use App\Enums\PremiumFeatureType;
use App\Http\Requests\BaseRequest;
use App\Models\Community;
use App\Models\Media;
use App\Models\Post;
use Illuminate\Validation\Rule;

class StorePostRequest extends BaseRequest
{
    /**
     * @var Post|null
     */
    protected $post;

    /**
     * @var Post|null
     */
    protected $parentPost;

    /**
     * @var Community|null
     */
    protected $community;

    /**
     * @var integer
     */
    protected $maxFileSize = 15;

    /**
     * @return array
     */
    public function rules(): array
    {
        $types = PostType::apiValidationRuleArray();
        $currentUserId = (int) $this->user()->getKey();

        $remove = PostType::WITH_TAG->value;
        if (($key = array_search($remove, $types)) !== false) {
            unset($types[$key]);
        }

        /**
         * temporarily ignore multiple type
         */
        if (($key = array_search(PostType::MULTIPLE->value, $types)) !== false) {
            unset($types[$key]);
        }

        $minLength = 1;
        $maxLength = 10;

        return [
            'post_id' => [
                function ($attr, $value, $fail) use ($currentUserId) {
                    if ($value = (int) $value) {
                        /** @var Post $post */
                        $post = postRepository()->find($value);
                        if (! $post || ! $post->ownerBy($currentUserId)) {
                            $fail(__('Invalid post ID.'));
                        }

                        $this->post = $post;
                    }
                },
            ],
            'content' => [
                'nullable',
                'required_without:media',
                'string',
                'max:1000',
            ],
            'media' => [
                'nullable',
                'required_without:content',
                'array',
                'min:' . $minLength,
                'max:' . $maxLength,
            ],
            'media.*.content' => [
                'nullable',
                'string',
                function ($attr, $value, $fail) {
                    $this->validateMediaContentMaxLength($attr, $value, $fail);
                },
            ],
            'media.0.content' => [
                'required_without:content',
                'string',
                function ($attr, $value, $fail) {
                    $this->validateMediaContentMaxLength($attr, $value, $fail);
                },
            ],
            'media.*.id' => [
                'nullable',
                function ($attr, $value, $fail) use ($currentUserId) {
                    if ($value) {
                        /** @var Media $media */
                        $media = Media::query()->find($value);
                        if (! $media || ! $media->ownerBy($currentUserId)) {
                            $fail(__('Invalid media ID.'));
                        }
                    }
                }
            ],
            'media.*.cover' => [
                'required_without:post_id,media.*.id',
                'mimes:png,jpeg,jpg,webp,svg',
                'max:' . $this->maxFileSize * 1024,
            ],
            'type' => [
                'required',
                'string',
                Rule::in($types),
                function ($attr, $value, $fail) {
                    if (! is_null($this->post) && $this->post->type !== $value) {
                        $fail('You cannot change the post type.');
                    }

                    /**
                     * check user permission when posting in gender post type
                     */
                    if ($value === PostType::GENDER->value && $this->userDoseNotHaveGenderFeature()) {
                        abort(140, __('You do not have permission to perform this action.'));
                    }
                },
            ],
            'reward_amount' => [
                'nullable',
                'integer',
                'min:0',
                'max:100',
                function ($attr, $value, $fail) {
                    $value = (int) $value;

                    /**
                     * trường hợp update thì phải trừ đi số coin đã holding trước đó
                     */
                    $value -= (int) $this->post?->getRewardAmount();

                    if ($value > $this->user()->getRemainingCoins()) {
                        abort(130, __('You do not have enough coins to perform this action.'));
                    }
                }
            ],
            'community_id' => [
                function ($attr, $value, $fail) {
                    if (! $this->input('post_id') && $value) {
                        /** @var Community|null $community */
                        $community = communityRepository()->find($value);

                        if (! $community || $community->isInActive()) {
                            abort(131, __('Invalid community ID.'));
                        }

                        /**
                         * chỉ có thành viên mới post vào nhóm
                         * nếu post vào answer_topic thì không cần phải là thành viên,
                         * nhưng phải là cộng đồng mở
                         */
                        $parentPostId = (int) $this->input('parent_post_id');
                        if ($parentPostId !== 0) {
                            /** @var Post $parentPost */
                            $parentPost = postRepository()->find($parentPostId);
                            if (! $parentPost || $parentPost->wasDeleted() || ($parentPost->type !== PostType::ANSWERR_TOPIC->value)) {
                                abort(121, __('Invalid parent post ID.'));
                            }

                            if ($community->isClosed()) {
                                abort(131, __('Invalid community ID.'));
                            }

                            $this->parentPost = $parentPost;
                        }

                        elseif ($this->user()->isNotMemberOf($community->getKey())) {
                            abort(136, __('You do not have permission to perform this action in this community.'));
                        }

                        $this->community = $community;
                    }
                },
            ],
            'ogp_url' => [
                'nullable',
                'url',
                'max:280',
            ],
            'ogp_title' => [
                'nullable',
                'required_with:ogp_url',
                'string',
                'max:250',
            ],
            'ogp_description' => [
                'nullable',
                'string',
                'max:500',
            ],
            'ogp_image' => [
                'nullable',
                'string',
                'max:191',
            ],
        ];
    }

    /**
     * @param $attr
     * @param $value
     * @param $fail
     * @return void
     */
    protected function validateMediaContentMaxLength($attr, $value, $fail) {
        $max = 48;
        if ($url = $this->input('ogp_url')) {
            $max += mb_strlen($url);
        }

        if (! is_null($value) && (grapheme_strlen($value) > $max)) {
            $fail(__('validation.max.string', [
                'attribute' => __('validation.attributes.post_content'),
                'max' => $max,
            ]));
        }
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'media.*.cover' => __('validation.attributes.post_cover'),
            'media.*.content' => __('validation.attributes.post_content'),
            'type' => __('validation.attributes.post_type'),
        ];
    }

    /**
     * @return bool
     */
    protected function userDoseNotHaveGenderFeature()
    {
        return ! $this->user()
            ->premiumFeatures()
            ->where('type', PremiumFeatureType::GENDER->value)
            ->exists();
    }

    /**
     * @return Post
     */
    public function getPost()
    {
        return $this->post ?? app(Post::class);
    }

    /**
     * @return Community|null
     */
    public function getCommunity()
    {
        return $this->community;
    }

    /**
     * @return Post|null
     */
    public function getParentPost()
    {
        return $this->parentPost;
    }
}
