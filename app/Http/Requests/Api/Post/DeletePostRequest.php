<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\BaseRequest;
use App\Models\Post;

class DeletePostRequest extends BaseRequest
{
    /**
     * @var Post|null
     */
    protected $post;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                function ($attr, $value, $fail) {
                    when((int) $value, function ($postID) use ($fail) {
                        /** @var Post|null $post */
                        $post = postRepository()->find($postID);

                        if (! $post || $post->wasDeleted()) {
                            abort(109, __('Invalid post ID.'));
                        }

                        $userId = $this->user()->getKey();
                        $community = $post->communityPost?->community;

                        $canDelete = $post->ownerBy($userId) || $community?->ownerBy($userId);
                        if (! $canDelete) {
                            abort(124, __('You do not have permission to perform this action.'));
                        }

                        $this->post = $post;
                    });
                },
            ],
        ];
    }

    /**
     * @return Post|null
     */
    public function getPost()
    {
        return $this->post;
    }
}
