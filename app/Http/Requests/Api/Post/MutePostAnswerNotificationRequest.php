<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\Api\Traits\ValidatePostAnswer;
use App\Http\Requests\BaseRequest;
use App\Models\PostAnswer;

class MutePostAnswerNotificationRequest extends BaseRequest
{
    use ValidatePostAnswer;

    /**
     * @var PostAnswer|null
     */
    protected $postAnswer;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'answer_id' => [
                'required',
                function ($attr, $value, $fail) {
                    $this->postAnswer = $this->getPostAnswerInstance($value, $fail);
                },
            ],
        ];
    }

    /**
     * @return PostAnswer|null
     */
    public function getPostAnswer()
    {
        return $this->postAnswer;
    }
}
