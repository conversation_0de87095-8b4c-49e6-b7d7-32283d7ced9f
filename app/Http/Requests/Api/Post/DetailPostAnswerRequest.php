<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\Api\Traits\ValidatePostAnswer;
use App\Http\Requests\BaseRequest;
use App\Models\PostAnswer;

class DetailPostAnswerRequest extends BaseRequest
{
    use ValidatePostAnswer;

    /**
     * @var PostAnswer|null
     */
    protected $postAnswer;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'answer_id' => [
                'required',
                function ($attr, $value, $fail) {
                    $this->postAnswer = $this->getPostAnswerInstance($value, $fail);

                    if ($this->user()->wasBlocked($this->postAnswer->user_id)) {
                        abort(109, __('Invalid post answer ID.'));
                    }
                },
            ],
        ];
    }

    /**
     * @return PostAnswer
     */
    public function getPostAnswer()
    {
        return $this->postAnswer;
    }
}
