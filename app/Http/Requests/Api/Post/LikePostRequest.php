<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\Api\Traits\ValidatePost;
use App\Http\Requests\BaseRequest;
use App\Models\Post;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

class LikePostRequest extends BaseRequest
{
    use ValidatePost;

    /**
     * @var Post|null
     */
    protected $post;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'post_id' => [
                'bail',
                'required',
                function ($attr, $value, $fail) {
                    $post = $this->getPostInstance($value, $fail);

                    /** @var User $user */
                    $user = $this->user();

                    if ($user->isID($post->user_id)) {
                        abort(118, __('You can not like your post.'));
                    }

                    if ($user->wasBlocked($post->user_id)) {
                        abort(109, __('Invalid post ID.'));
                    }

                    $post->loadExists([
                        'reactions' => function (Builder $query) {
                            $query->where('user_id', $this->user()->getKey())
                                ->where('action', 'liked');
                        },
                    ]);

                    if ((bool) $post->getAttribute('reactions_exists')) {
                        abort(119, __('You have liked this post.'));
                    }

                    $this->post = $post;
                },
            ],
        ];
    }

    /**
     * @return Post
     */
    public function getPost()
    {
        return $this->post;
    }
}
