<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\Api\Traits\ValidatePostAnswer;
use App\Http\Requests\BaseRequest;
use App\Models\PostAnswer;
use App\Models\User;

class VoteBestPostAnswerRequest extends BaseRequest
{
    use ValidatePostAnswer;

    /**
     * @var PostAnswer|null
     */
    protected $postAnswer;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'answer_id' => [
                'bail',
                'required',
                function ($attr, $value, $fail) {
                    $postAnswer = $this->getPostAnswerInstance($value, $fail);

                    /** @var User $user */
                    $user = $this->user();

                    /**
                     * check user blocked
                     */
                    if ($user->wasBlocked($postAnswer->user_id)) {
                        abort(109, __('Invalid post answer ID.'));
                    }

                    $postOwnerID = $postAnswer->post->user_id;

                    /**
                     * vote cho câu trả lời của mình,
                     * hoặc vote cho câu hỏi không phải của mình,
                     * hoặc vote cho câu hỏi không phải level 1
                     */
                    if ($user->isID($postAnswer->user_id) || ! $user->isID($postOwnerID) || $postAnswer->isNotLevelOne()) {
                        abort(115, __('You do not have permission to perform this action.'));
                    }

                    /**
                     * đã voted cho câu hỏi
                     */
                    if ($postAnswer->post->wasVoted()) {
                        abort(116, __('You have already voted best-answer for this post.'));
                    }

                    $this->postAnswer = $postAnswer;
                },
            ],
        ];
    }

    /**
     * @return PostAnswer
     */
    public function getPostAnswer()
    {
        return $this->postAnswer;
    }
}
