<?php

namespace App\Http\Requests\Api\Post;

use App\Enums\FeedType;
use App\Http\Requests\Api\Traits\ValidatePost;
use App\Http\Requests\BaseRequest;
use App\Models\Community;
use App\Models\Post;
use Illuminate\Validation\Rule;

class ListFeedPostRequest extends BaseRequest
{
    use ValidatePost;

    /**
     * @var Post|null
     */
    protected $post;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'type' => [
                'bail',
                'required',
                function ($attribute, $value, $fail) {
                    $isCommunity = str_starts_with($value, 'community_');
                    if ($isCommunity) {
                        $communityId = str_replace('community_', '', $value);

                        /** @var Community|null $community */
                        $community = communityRepository()->find($communityId);

                        if (! $community || $community->isInActive()) {
                            abort(131, __('Invalid community ID.'));
                        }
                    }

                    elseif (! in_array($value, FeedType::toArray())) {
                        $fail(__('validation.in', [
                            'attribute' => __('validation.attributes.feed_type'),
                        ]));
                    }
                },
            ],
            'post_id' => [
                function ($attr, $value, $fail) {
                    if ($value = (int) $value) {
                        $this->post = $this->getPostInstance($value, $fail);
                    }
                },
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'type' => __('validation.attributes.feed_type'),
        ];
    }

    /**
     * @return Post
     */
    public function getPost()
    {
        return $this->post;
    }
}
