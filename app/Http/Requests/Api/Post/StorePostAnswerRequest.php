<?php

namespace App\Http\Requests\Api\Post;

use App\Http\Requests\BaseRequest;
use App\Models\Community;
use App\Models\Post;
use App\Models\PostAnswer;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Illuminate\Validation\Validator;

class StorePostAnswerRequest extends BaseRequest
{
    /**
     * @var integer
     */
    protected $maxFileSize = 15;

    /**
     * @var PostAnswer|null
     */
    protected $postAnswer;

    /**
     * @var PostAnswer|null
     */
    protected $parentPostAnswer;

    /**
     * @var Community|null
     */
    protected $community;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'answer_id' => [
                function ($attr, $value, $fail) {
                    when((int) $value, function ($answerId) use ($fail) {
                        /** @var PostAnswer|null $record */
                        $record = postAnswerRepository()->find($answerId);

                        if ($record?->wasDeleted() ?? true) {
                            abort(111, __('Invalid post answer ID.'));
                        }

                        $this->postAnswer = $record;
                    });
                },
            ],
            'post_id' => [
                'required_without:answer_id',
                function ($attr, $value, $fail) {
                    when((int) $value, function ($postId) use ($fail) {
                        /** @var Post|null $post */
                        $post = postRepository()->find($postId);

                        if (! $post || $post->wasDeleted() || $post->createdBy->isDisabled()) {
                            abort(121, __('Invalid post ID.'));
                        }

                        $this->validatePostInCommunity($post);

                        /**
                         * checking block state
                         */
                        if ($this->user()->wasBlocked($post->user_id)) {
                            abort(137, __('この質問には回答できません。'));
                        }
                    });
                },
            ],
            'object_type' => [
                'required_without:answer_id',
                Rule::in(['post', 'answer']),
            ],
            'content' => [
                'required',
                'string',
                'max:1000',
            ],
            'image' => [
                'mimes:png,jpeg,jpg,webp,svg',
                'max:' . $this->maxFileSize * 1024,
            ],
            'ogp_url' => [
                'nullable',
                'url',
                'max:280',
            ],
            'ogp_title' => [
                'nullable',
                'required_with:ogp_url',
                'string',
                'max:250',
            ],
            'ogp_description' => [
                'nullable',
                'string',
                'max:500',
            ],
            'ogp_image' => [
                'nullable',
                'string',
                'max:191',
            ],
        ];
    }

    /**
     * @param Post $post
     * @return void
     */
    protected function validatePostInCommunity(Post $post)
    {
        /**
         * kiểm tra nếu trả lời cho bài post trong community
         */
        if ($community = $post->communityPost?->community) {
            /**
             * chỉ có thành viên mới trả lời cho bài post của nhóm
             */
            $isNotMember = $this->user()->isNotMemberOf($community->getKey());
            if ($community->isInActive() || ($isNotMember && $community->isClosed())) {
                abort(136, __('You do not have permission to perform this action in this community.'));
            }

            $this->community = $community;
        }
    }

    /**
     * @param $validator
     * @return void
     * @throws ValidationException
     */
    protected function withValidator($validator)
    {
        $validator->after(function (Validator $validator) {
            if ('answer' === $this->input('object_type')) {
                $id = $this->input('parent_id');

                /** @var PostAnswer $parentAnswer */
                $parentAnswer = postAnswerRepository()->find($id);

                if (! $parentAnswer || $parentAnswer->wasDeleted() || ($parentAnswer->level >= 3) || (((int) $parentAnswer->post_id) !== (int) $this->input('post_id'))) {
                    abort(111, __('Invalid parent answer ID.'));
                }

                /**
                 * checking block state
                 */
                if ($this->user()->wasBlocked($parentAnswer->user_id)) {
                    abort(137, __('この質問には回答できません。'));
                }

                $this->parentPostAnswer = $parentAnswer;
            }
        });
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'content' => __('validation.attributes.post_answer_content'),
        ];
    }

    /**
     * @return PostAnswer
     */
    public function getPostAnswer()
    {
        return $this->postAnswer ?? app(PostAnswer::class);
    }

    /**
     * @return PostAnswer|null
     */
    public function getParentAnswer()
    {
        return $this->parentPostAnswer;
    }

    /**
     * @return Community|null
     */
    public function getCommunity()
    {
        return $this->community;
    }
}
