<?php

namespace App\Http\Requests\Api\Traits;

use Illuminate\Validation\Validator;

trait ValidateSignature
{
    /**
     * @param $validator
     * @return void
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function withValidator($validator)
    {
        $validator->after(function (Validator $validator) {
            $error = $validator->errors();

            if (! $error->has('aesKey') && ! $error->has('iv') && ! $error->has('encrypted_data')) {
                $encryptedData = $this->input('encrypted_data');
                $aesKey = $this->input('aesKey');
                $iv = $this->input('iv');

                if (! ($data = encryptor()->decode($encryptedData, $aesKey, $iv)) || $data !== 'honne') {
                    $this->setError('encrypted_data', __('Can not decrypt the data.'));
                }
            }
        });
    }
}
