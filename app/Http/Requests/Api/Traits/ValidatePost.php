<?php

namespace App\Http\Requests\Api\Traits;

use App\Models\Post;
use Closure;

trait ValidatePost
{
    /**
     * @param int $id
     * @param Closure $fail
     * @return Post
     */
    protected function getPostInstance(int $id, Closure $fail)
    {
        /** @var Post $post */
        $post = postRepository()->find($id);
        if (! $post || $post->wasDeleted()) {
            abort(121, __('Invalid post ID.'));
        }

        return $post;
    }
}
