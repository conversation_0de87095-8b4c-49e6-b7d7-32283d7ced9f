<?php

namespace App\Http\Requests\Api\Traits;

use App\Models\PostAnswer;
use Closure;

trait ValidatePostAnswer
{
    /**
     * @param int $id
     * @param Closure $fail
     * @return PostAnswer
     */
    protected function getPostAnswerInstance(int $id, Closure $fail)
    {
        /** @var PostAnswer $postAnswer */
        $postAnswer = postAnswerRepository()->find($id);
        if (! $postAnswer || $postAnswer->wasDeleted() || $postAnswer->post->wasDeleted()) {
            abort(111, __('Invalid post answer ID.'));
        }

        return $postAnswer;
    }
}
