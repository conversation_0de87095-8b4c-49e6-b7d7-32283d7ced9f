<?php

namespace App\Http\Requests\Api\Survey;

use App\Enums\QuestionType;
use App\Http\Requests\BaseRequest;
use App\Models\Question;
use Illuminate\Validation\Rule;

class AnswerRequest extends BaseRequest
{
    /**
     * @var Question|null
     */
    protected $question;

    /**
     * @var array
     */
    protected $answerIds = [];

    /**
     * @return array
     */
    public function rules(): array
    {
        if ($questionId = $this->input('question_id')) {
            $this->question = questionRepository()->find($questionId);
        }

        if (! $this->question) {
            return [
                'question_id' => [
                    'required',
                    'numeric',
                    function ($attr, $value, $fail) {
                        if ($value) {
                            $fail(__('Invalid question ID.'));
                        }
                    }
                ],
            ];
        }

        return match($this->question->type) {
            QuestionType::CHECK_BOX->value => $this->answerCheckBoxRules(),
            QuestionType::SELECT_BOX->value => $this->answerSelectBoxRules(),
            QuestionType::TEXT->value => $this->answerTextRules(),
            QuestionType::TEXT_WITH_STAR->value => $this->answerTextWithStarRules(),
            default => [],
        };
    }

    /**
     * @return array
     */
    protected function getChoiceIds()
    {
        return $this->question->choices->pluck('choice_id')->toArray();
    }

    /**
     * @return array
     */
    protected function answerCheckBoxRules()
    {
        return [
            'answer_ids' => [
                'bail',
                'required',
                function ($attr, $value, $fail) {
                    $values = explode(',', $value);
                    if (! empty(array_diff($values, $this->getChoiceIds()))) {
                        $fail(__('Invalid answer IDs.'));
                    }

                    $this->answerIds = $values;
                },
            ],
        ];
    }

    /**
     * @return array[]
     */
    protected function answerSelectBoxRules()
    {
        return [
            'answer_ids' => [
                'bail',
                'required',
                function ($attr, $value, $fail) {
                    if (! in_array($value, $this->getChoiceIds())) {
                        $fail(__('Invalid answer ID.'));
                    }

                    $this->answerIds = [$value];
                },
            ],
        ];
    }

    /**
     * @return array
     */
    protected function answerTextRules()
    {
        return [
            'content' => [
                'bail',
                'required',
                'string',
                'max:1000',
            ],
            'public' => [
                'bail',
                'required',
                Rule::in([
                    'yes',
                    'no',
                ]),
            ],
        ];
    }

    /**
     * @return array
     */
    protected function answerTextWithStarRules()
    {
        $rules = $this->answerTextRules();
        $rules['star'] = [
            'required',
            'int',
            'min:1',
            'max:5',
        ];

        return $rules;
    }

    /**
     * @return Question|null
     */
    public function getQuestion()
    {
        return $this->question;
    }

    /**
     * @return array
     */
    public function getAnswerIds()
    {
        return $this->answerIds;
    }
}
