<?php

namespace App\Http\Requests\Api\PremiumFeature;

use App\Http\Requests\BaseRequest;
use App\Models\PremiumFeature;

class ActivatePremiumFeatureRequest extends BaseRequest
{
    /**
     * @var PremiumFeature|null
     */
    protected $feature = null;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'type' => [
                'required',
                function ($attribute, $value, $fail) {
                    /** @var PremiumFeature $feature */
                    $feature = $this->getPremiumFeatureByType($value);
                    if (! $feature) {
                        abort(138, __('validation.exists', [
                            'attribute' => $this->getAttribute($attribute),
                        ]));
                    }

                    $userFeatures = $this->getUserActivePremiumFeatures();
                    if (in_array($value, $userFeatures)) {
                        abort(139, __('You already have this premium feature activated.'));
                    }

                    /**
                     * check if user has enough coins to activate the feature
                     */
                    if (! $this->user()->hasEnoughCoins($feature->price)) {
                        abort(140, __('You do not have enough coins to perform this action.'));
                    }

                    $this->feature = $feature;
                },
            ],
        ];
    }

    /**
     * @return array
     */
    protected function getUserActivePremiumFeatures(): array
    {
        return $this->user()
            ->premiumFeatures()
            ->pluck('type')
            ->toArray();
    }

    /**
     * @param string $type
     * @return bool
     */
    protected function getPremiumFeatureByType(string $type)
    {
        return PremiumFeature::query()->where('type', $type)->first();
    }

    /**
     * @param string $attribute
     * @return string
     */
    protected function getAttribute(string $attribute): string
    {
        return $this->attributes()[$attribute] ?? $attribute;
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'type' => __('validation.attributes.premium_feature.type'),
        ];
    }

    /**
     * @return PremiumFeature|null
     */
    public function getPremiumFeature()
    {
        return $this->feature;
    }
}
