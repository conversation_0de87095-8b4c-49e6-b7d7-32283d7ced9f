<?php

namespace App\Http\Requests\Web;

use App\Http\Requests\BaseRequest;
use App\Models\Quest;

class ToggleQuestRequest extends BaseRequest
{
    /**
     * @var Quest
     */
    protected $quest;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'id' => [
                function ($attr, $value, $fail) {
                    $repository = questRepository();

                    /** @var Quest $quest */
                    $quest = $repository->find($value);
                    if (! $quest) {
                        $fail(__('Invalid quest ID.'));
                    }

                    $status = ((int) $quest->status) ^ 1;
                    if ($status === 1 && $repository->existedType($quest->type, $value)) {
                        $fail(__('existedQuestForType', [
                            'type' => $quest->type,
                        ]));
                    }

                    $this->quest = $quest;
                }
            ],
        ];
    }

    /**
     * @return Quest
     */
    public function getQuest()
    {
        return $this->quest;
    }
}
