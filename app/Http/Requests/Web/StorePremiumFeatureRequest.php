<?php

namespace App\Http\Requests\Web;

use App\Enums\PremiumFeatureType;
use App\Http\Requests\BaseRequest;
use App\Models\PremiumFeature;
use Illuminate\Validation\Rule;

class StorePremiumFeatureRequest extends BaseRequest
{
    /**
     * @var PremiumFeature|null
     */
    protected $feature;

    /**
     * @var int
     */
    protected $maxFileSize = 15; // Maximum file size in MB

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'premium_id' => [
                function ($attr, $value, $fail) {
                    if ($value) {
                        $feature = PremiumFeature::query()->find($value);
                        if (! $feature) {
                            $fail(__('Invalid feature ID.'));
                        }

                        $this->feature = $feature;
                    }
                },
            ],
            'name' => [
                'required',
                'string',
                'max:190',
            ],
            'description' => [
                'required',
                'string',
                'max:190',
            ],
            'type' => [
                'required',
                Rule::in(PremiumFeatureType::toArray()),
                Rule::unique('premium_features')->ignore($this->input('premium_id'), 'premium_id'),
            ],
            'price' => [
                'required',
                'integer',
                'min:0',
                'max:10',
            ],
            'image' => [
                'required_without:premium_id',
                'mimes:png,jpeg,jpg,webp,svg',
                'max:' . $this->maxFileSize * 1024,
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => __('validation.attributes.premium_feature.name'),
            'description' => __('validation.attributes.premium_feature.description'),
            'type' => __('validation.attributes.premium_feature.type'),
            'price' => __('validation.attributes.premium_feature.price'),
        ];
    }

    /**
     * @return PremiumFeature|\Illuminate\Foundation\Application|mixed|object
     */
    public function getPremiumFeature()
    {
        return $this->feature ?? app(PremiumFeature::class);
    }
}
