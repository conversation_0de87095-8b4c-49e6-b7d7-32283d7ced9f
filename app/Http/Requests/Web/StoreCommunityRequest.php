<?php

namespace App\Http\Requests\Web;

use App\Http\Requests\BaseRequest;
use App\Models\Community;
use Illuminate\Support\Arr;

class StoreCommunityRequest extends BaseRequest
{
    /**
     * @var Community
     */
    protected $community;

    /**
     * @var int
     */
    private $maxFileSize = 15;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'community_id' => [
                function ($attr, $value, $fail) {
                    if ($value) {
                        $community = communityRepository()->find($value);
                        if (! $community) {
                            $fail(__('Invalid community ID.'));
                        }

                        $this->community = $community;
                    }
                },
            ],
            'name' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $length = grapheme_strlen($value);

                    if ($length < 2) {
                        $fail(__('validation.min.string', [
                            'attribute' => $this->getAttribute($attribute),
                            'min' => 2,
                        ]));
                    }

                    if ($length > 15) {
                        $fail(__('validation.max.string', [
                            'attribute' => $this->getAttribute($attribute),
                            'max' => 15,
                        ]));
                    }
                },
            ],
            'description' => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    $length = grapheme_strlen($value);

                    if ($length > 60) {
                        $fail(__('validation.max.string', [
                            'attribute' => $this->getAttribute($attribute),
                            'max' => 60,
                        ]));
                    }
                }
            ],
            'image' => [
                'required_without:community_id',
                'mimes:png,jpeg,jpg,webp,svg',
                'max:' . $this->maxFileSize * 1024,
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => __('validation.attributes.community_name'),
            'image' => __('validation.attributes.image'),
        ];
    }

    /**
     * @param string $attribute
     * @return string
     */
    public function getAttribute(string $attribute)
    {
        $attributes = $this->attributes();

        return Arr::get($attributes, $attribute, '');
    }

    /**
     * @return Community|\Illuminate\Foundation\Application|mixed|object
     */
    public function getCommunity()
    {
        return $this->community ?? app(Community::class);
    }
}
