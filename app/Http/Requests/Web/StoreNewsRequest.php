<?php

namespace App\Http\Requests\Web;

use App\Http\Requests\BaseRequest;
use App\Models\News;

class StoreNewsRequest extends BaseRequest
{
    /**
     * @var News|null
     */
    protected $news;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'news_id' => [
                function ($attr, $value, $fail) {
                    if ($value) {
                        $news = newsRepository()->find($value);
                        if (! $news) {
                            $fail(__('Invalid news ID.'));
                        }

                        $this->news = $news;
                    }
                },
            ],
            'title' => [
                'required',
                'string',
                'max:190',
            ],
            'content' => [
                'required',
                'string',
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'title' => __('validation.attributes.news.title'),
            'content' => __('validation.attributes.news.content'),
        ];
    }

    /**
     * @return News|\Illuminate\Foundation\Application|mixed|object
     */
    public function getNews()
    {
        return $this->news ?? app(News::class);
    }
}
