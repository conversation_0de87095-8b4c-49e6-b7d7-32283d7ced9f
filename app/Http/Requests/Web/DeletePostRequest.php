<?php

namespace App\Http\Requests\Web;

use App\Http\Requests\BaseRequest;
use App\Models\Post;

class DeletePostRequest extends BaseRequest
{
    /**
     * @var Post|null
     */
    protected $deletePost;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                function ($attr, $value, $fail) {
                    $this->deletePost = postRepository()->find($value);
                }
            ],
        ];
    }

    /**
     * @return Post|null
     */
    public function getPostInstance()
    {
        return $this->deletePost;
    }
}
