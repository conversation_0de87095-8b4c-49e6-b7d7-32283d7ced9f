<?php

namespace App\Http\Requests\Web;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class StoreAssistantRequest extends BaseRequest
{
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:190',
            ],
            'work' => [
                'required',
                'string',
                'max:190',
            ],
            'expertise' => [
                'required',
                'string',
                'max:190',
            ],
            'description' => [
                'required',
                'string',
                'max:300',
            ],
            'language' => [
                'required',
                Rule::in(['vi', 'ja']),
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => __('validation.attributes.assistant_name'),
            'work' => __('validation.attributes.assistant_work'),
            'expertise' => __('validation.attributes.assistant_expertise'),
            'description' => __('validation.attributes.assistant_description'),
            'language' => __('validation.attributes.language'),
        ];
    }
}
