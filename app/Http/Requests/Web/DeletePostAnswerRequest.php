<?php

namespace App\Http\Requests\Web;

use App\Http\Requests\BaseRequest;
use App\Models\PostAnswer;

class DeletePostAnswerRequest extends BaseRequest
{
    /**
     * @var PostAnswer|null
     */
    protected $deletePostAnswer;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                function ($attr, $value, $fail) {
                    $this->deletePostAnswer = postAnswerRepository()->find($value);
                }
            ],
        ];
    }

    /**
     * @return PostAnswer|null
     */
    public function getPostAnswerInstance()
    {
        return $this->deletePostAnswer;
    }
}
