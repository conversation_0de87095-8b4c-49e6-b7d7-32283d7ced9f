<?php

namespace App\Http\Requests\Web;

use App\Http\Requests\BaseRequest;
use App\Models\AttachedSurvey;

class DeleteAttachedSurveyRequest extends BaseRequest
{
    /**
     * @var AttachedSurvey
     */
    protected $record;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'id' => [
                'bail',
                'required',
                function ($attr, $value, $fail) {
                    /** @var AttachedSurvey $model */
                    $model = app(AttachedSurvey::class);
                    if (! $record = $model->newQuery()->find($value)) {
                        $fail(__('Invalid attached ID.'));
                    }

                    $this->record = $record;
                }
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'id' => __('validation.attributes.attached_id'),
        ];
    }

    /**
     * @return AttachedSurvey
     */
    public function getAttachedSurvey()
    {
        return $this->record;
    }
}
