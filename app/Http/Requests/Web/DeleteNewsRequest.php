<?php

namespace App\Http\Requests\Web;

use App\Http\Requests\BaseRequest;
use App\Models\News;

class DeleteNewsRequest extends BaseRequest
{
    /**
     * @var News
     */
    protected $deleteNews;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'id' => [
                function ($attr, $value, $fail) {
                    /** @var News $news */
                    $news = newsRepository()->find($value);
                    if (! $news || ! $news->isEnable()) {
                        $fail(__('Invalid news ID.'));
                    }

                    $this->deleteNews = $news;
                }
            ],
        ];
    }

    /**
     * @return News
     */
    public function getDeleteNews()
    {
        return $this->deleteNews;
    }
}
