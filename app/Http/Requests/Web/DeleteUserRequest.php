<?php

namespace App\Http\Requests\Web;

use App\Http\Requests\BaseRequest;
use App\Models\User;

class DeleteUserRequest extends BaseRequest
{
    /**
     * @var User
     */
    protected $deleteUser;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'id' => [
                function ($attr, $value, $fail) {
                    /** @var User $user */
                    $user = userRepository()->find($value);
                    if (! $user || $user->isSuperAdmin()) {
                        $fail(__('Invalid user ID.'));
                    }

                    $this->deleteUser = $user;
                }
            ],
        ];
    }

    /**
     * @return User
     */
    public function getDeleteUser()
    {
        return $this->deleteUser;
    }
}
