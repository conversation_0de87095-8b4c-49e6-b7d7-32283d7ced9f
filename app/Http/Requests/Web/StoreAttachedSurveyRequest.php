<?php

namespace App\Http\Requests\Web;

use App\Http\Requests\BaseRequest;
use App\Models\AttachedSurvey;
use App\Models\Survey;
use Illuminate\Database\Query\Builder;
use Illuminate\Validation\Rule;

class StoreAttachedSurveyRequest extends BaseRequest
{
    /**
     * @var AttachedSurvey|null
     */
    protected $attachedSurvey;

    /**
     * @var Survey
     */
    protected $toSurvey;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'attached_id' => [
                function ($attr, $value, $fail) {
                    if ($value && ! ($this->attachedSurvey = app(AttachedSurvey::class)->find($value))) {
                        $fail(__('Invalid attached survey ID.'));
                    }
                },
            ],
            'title' => [
                'bail',
                'required',
                'string',
                'max:190',
            ],
            'survey_id' => [
                'bail',
                'required',
                'numeric',
                function ($attr, $value, $fail) {
                    if (! surveyRepository()->find($value)) {
                        $fail(__('Invalid survey ID.'));
                    }
                },
            ],
            'to_survey_id' => [
                'bail',
                'required',
                'numeric',
                Rule::unique('attached_surveys')
                    ->where(function(Builder $query) {
                        $query->where('survey_id', $this->input('survey_id'));

                        if ($id = $this->input('attached_id')) {
                            $query->where('attached_id', '<>', $id);
                        }
                    }),
                function ($attr, $value, $fail) {
                    $surveyId = (int) $this->input('survey_id');
                    $value = (int) $value;

                    if (($surveyId && ($surveyId === $value)) || ! ($this->toSurvey = surveyRepository()->find($value))) {
                        $fail(__('Invalid survey ID.'));
                    }

                    $this->toSurvey?->load([
                        'questions.choices',
                    ]);
                },
            ],
            'choices' => [
                'required',
                'array',
            ],
            'choices.*.question_id' => [
                'bail',
                'required',
                'numeric',
            ],
            'choices.*.choice_id' => [
                'bail',
                'required',
                'numeric',
                function ($attr, $value, $fail) {
                    if ($questions = $this->toSurvey?->questions ?? []) {
                        $choiceIds = [];
                        foreach ($questions as $question) {
                            $choiceIds = array_merge($choiceIds, $question->choices->pluck('choice_id')->toArray());
                        }

                        if (! in_array($value, $choiceIds)) {
                            $fail(__('Invalid choice ID.'));
                        }
                    }
                },
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'title' => __('validation.attributes.attached_survey_title'),
            'choices.*.question_id' => __('validation.attributes.question'),
            'choices.*.choice_id' => __('validation.attributes.answer'),
        ];
    }

    /**
     * @return AttachedSurvey
     */
    public function getAttachedSurvey()
    {
        return $this->attachedSurvey ?? app(AttachedSurvey::class);
    }
}
