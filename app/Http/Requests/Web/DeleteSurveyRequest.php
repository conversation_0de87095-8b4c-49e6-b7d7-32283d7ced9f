<?php

namespace App\Http\Requests\Web;

use App\Http\Requests\BaseRequest;
use App\Models\Survey;

class DeleteSurveyRequest extends BaseRequest
{
    /**
     * @var Survey
     */
    protected $record;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'id' => [
                'bail',
                'required',
                function ($attr, $value, $fail) {
                    if (! $record = surveyRepository()->find($value)) {
                        $fail(__('Invalid survey ID.'));
                    }

                    $this->record = $record;
                }
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'id' => __('validation.attributes.attached_id'),
        ];
    }

    /**
     * @return Survey
     */
    public function getSurvey()
    {
        return $this->record;
    }
}
