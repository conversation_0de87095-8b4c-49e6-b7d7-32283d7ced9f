<?php

namespace App\Http\Requests\Web;

use App\Enums\FeedType;
use App\Http\Requests\BaseRequest;

class StoreSettingRequest extends BaseRequest
{
    /**
     * @var string[]
     */
    protected $types = [
        'friend',
        'similar',
        'like',
        'general',
        'latest',
    ];

    /**
     * @return array
     */
    public function rules(): array
    {
        $rules = [];
        foreach ($this->types as $type) {
            $rules[$type . '.index'] = [
                'required',
                'integer',
                'min:1',
                'max:10',
            ];

            $rules[$type . '.limit'] = [
                'required',
                'integer',
                'min:1',
                'max:10',
            ];
        }

        $timeKeys = [
            FeedType::LAUGH->value,
            FeedType::GENERAL->value,
            FeedType::RAISE_HAND->value,
            FeedType::RECOMMENDED_ANSWER->value,
        ];

        foreach ($timeKeys as $key) {
            $rules['time.' . $key] = [
                'required',
                'integer',
                'min:1',
                'max:1000',
            ];
        }

        return $rules;
    }

    /**
     * @return array
     */
    public function attributes()
    {
        $attributes = [];
        foreach ($this->types as $type) {
            $attributes[$type . '.index'] = __('validation.attributes.order');
            $attributes[$type . '.limit'] = __('validation.attributes.rate');
        }

        $timeKeys = [
            FeedType::LAUGH->value,
            FeedType::GENERAL->value,
            FeedType::RAISE_HAND->value,
            FeedType::RECOMMENDED_ANSWER->value,
        ];

        foreach ($timeKeys as $key) {
            $attributes['time.' . $key] = __('validation.attributes.time');
        }

        return $attributes;
    }
}
