<?php

namespace App\Http\Requests\Web;

use App\Enums\PostType;
use App\Http\Requests\BaseRequest;
use App\Models\Community;
use App\Models\Post;
use Illuminate\Validation\Rule;

class StorePostRequest extends BaseRequest
{
    /**
     * @var Post|null
     */
    protected $post;

    /**
     * @var integer
     */
    protected $maxFileSize = 15;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'post_id' => [
                function ($attr, $value, $fail) {
                    if ($value) {
                        $post = postRepository()->find($value);
                        if (! $post) {
                            $fail(__('Invalid post ID.'));
                        }

                        $this->post = $post;
                    }
                },
            ],
            'type' => [
                'required',
                Rule::in(PostType::consoleValidationRuleArray()),
            ],
            'content' => [
                'required',
                'string',
                'max:1000',
            ],
            'image' => [
                'nullable',
                'mimes:png,jpeg,jpg,webp,svg',
                'max:' . $this->maxFileSize * 1024,
            ],
            'community_id' => [
                'required',
                function ($attr, $value, $fail) {
                    /** @var Community|null $community */
                    $community = communityRepository()->find($value);

                    if (! $community || $community->isInActive() || $community->isClosed()) {
                        $fail(__('Invalid community ID.'));
                    }
                },
            ],
            'camera_roll' => [
                'required',
                Rule::in([0, 1]),
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'content' => __('validation.attributes.question_content'),
            'type' => __('validation.attributes.question_type'),
            'image' => __('validation.attributes.image'),
        ];
    }

    /**
     * @return Post
     */
    public function getPostInstance()
    {
        return $this->post ?? app(Post::class);
    }
}
