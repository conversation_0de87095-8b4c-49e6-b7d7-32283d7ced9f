<?php

namespace App\Http\Requests\Web;

use App\Http\Requests\BaseRequest;
use App\Models\Post;

class TogglePostFeatureRequest extends BaseRequest
{
    /**
     * @var Post|null
     */
    protected $post;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                function ($attr, $value, $fail) {
                    $this->post = postRepository()->find($value);

                    if (! $this->post) {
                        $fail(__('Invalid post ID.'));
                    }
                }
            ],
        ];
    }

    /**
     * @return Post|null
     */
    public function getPostInstance()
    {
        return $this->post;
    }
}
