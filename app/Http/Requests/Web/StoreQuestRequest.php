<?php

namespace App\Http\Requests\Web;

use App\Enums\QuestType;
use App\Http\Requests\BaseRequest;
use App\Models\Quest;
use Illuminate\Validation\Rule;

class StoreQuestRequest extends BaseRequest
{
    /**
     * @var Quest|null
     */
    protected $quest;

    /**
     * @var integer
     */
    protected $maxFileSize = 15;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'id' => [
                function ($attr, $value, $fail) {
                    if ($value) {
                        $quest = questRepository()->find($value);
                        if (! $quest) {
                            $fail(__('Invalid quest ID.'));
                        }

                        $this->quest = $quest;
                    }
                },
            ],
            'title' => [
                'required',
                'string',
                'max:190',
            ],
            'description' => [
                'required',
                'string',
                'max:1000',
            ],
            'image' => [
                'required_without:id',
                'mimes:png,jpeg,jpg,webp,svg',
                'max:' . $this->maxFileSize * 1024,
            ],
            'type' => [
                'required',
                Rule::in(QuestType::toArray()),
                function ($attr, $value, $fail) {
                    if (questRepository()->existedType($value, $this->input('id'))) {
                        $fail(__('existedQuestForType', [
                            'type' => $value,
                        ]));
                    }
                },
            ],
            'amount' => [
                'required',
                'integer',
                'min:0',
                'max:100',
            ],
            'sort' => [
                'required',
                'integer',
                'min:0',
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'title' => __('validation.attributes.quest.title'),
            'description' => __('validation.attributes.quest.description'),
            'type' => __('validation.attributes.quest.type'),
            'amount' => __('validation.attributes.quest.amount'),
            'sort' => __('validation.attributes.quest.sort'),
            'image' => __('validation.attributes.quest.image'),
        ];
    }

    /**
     * @return Quest|\Illuminate\Foundation\Application|mixed|object
     */
    public function getQuest()
    {
        return $this->quest ?? app(Quest::class);
    }
}
