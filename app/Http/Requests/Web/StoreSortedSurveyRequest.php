<?php

namespace App\Http\Requests\Web;

use App\Http\Requests\BaseRequest;

class StoreSortedSurveyRequest extends BaseRequest
{
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'bail',
                'required',
                'array',
                function ($attr, $value, $fail) {
                    $surveys = surveyRepository()->findMany($value);
                    $ids = $surveys->pluck('survey_id')->toArray();
                    if (array_diff($value, $ids)) {
                        $fail(__('Invalid survey ID.'));
                    }
                },
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'ids' => __('validation.attributes.survey'),
        ];
    }
}
