<?php

namespace App\Http\Requests\Web;

use App\Enums\QuestionPublicTarget;
use App\Enums\QuestionType;
use App\Http\Requests\BaseRequest;
use App\Models\Question;
use App\Models\QuestionChoice;
use App\Models\Survey;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class StoreSurveyRequest extends BaseRequest
{
    /**
     * @var Survey|null
     */
    protected $survey;

    /**
     * @var array
     */
    protected $questionIds = [];

    /**
     * @var Collection|Question[]
     */
    protected $questions;

    /**
     * @var Collection|QuestionChoice[]
     */
    protected $choices;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'survey_id' => [
                function ($attr, $value, $fail) {
                    if ($value) {
                        if (! ($survey = surveyRepository()->find($value))) {
                            $fail(__('Invalid survey ID.'));
                        }

                        $this->survey = $survey;
                    }
                },
            ],
            'title' => [
                'required',
                'string',
                'max:190',
            ],
            'questions' => [
                'required',
                'array',
            ],
            'questions.*.type' => [
                'required',
                Rule::in(QuestionType::toArray()),
            ],
            'questions.*.public' => [
                'required',
                Rule::in(QuestionPublicTarget::toArray()),
            ],
            'questions.*.point' => [
                'required',
                'numeric',
                'min:1',
                'max:1000000000',
            ],
            'questions.*.content' => [
                'required',
                'string',
                'max:190',
            ],
            'questions.*.choices' => [
                'required_if:questions.*.type,' . QuestionType::CHECK_BOX->value . ',' . QuestionType::SELECT_BOX->value,
            ],
            'questions.*.choices.*.content' => [
                'required_if:questions.*.type,' . QuestionType::CHECK_BOX->value . ',' . QuestionType::SELECT_BOX->value,
                'max:190',
            ],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'title' => __('validation.attributes.survey_title'),
            'questions.*.type' => __('validation.attributes.question_type'),
            'questions.*.public' => __('validation.attributes.question_public'),
            'questions.*.point' => __('validation.attributes.point'),
            'questions.*.content' => __('validation.attributes.question_content'),
            'questions.*.choices.*.content' => __('validation.attributes.question_choice'),
        ];
    }

    /**
     * validate valid question ID & choice ID
     * @param Validator $validator
     * @return void
     */
    protected function withValidator(Validator $validator)
    {
        $validator->after(function () {
            $questions = $this->input('questions', []);

            $questionIds = [];
            $choiceIds = [];

            /**
             * prepare ids data
             */
            foreach ($questions as $question) {
                if ($questionId = Arr::get($question, 'question_id')) {
                    $questionIds[] = $questionId;
                    if (in_array(Arr::get($question, 'type'), [QuestionType::CHECK_BOX->value, QuestionType::SELECT_BOX->value])) {
                        $choices = Arr::get($question, 'choices');
                        foreach ($choices as $choice) {
                            if ($choiceId = Arr::get($choice, 'choice_id')) {
                                $choiceIds[] = $choiceId;
                            }
                        }
                    }
                }
            }

            /**
             * fetch from db
             */
            $questionCollection = questionRepository()->findMany($questionIds);
            $choiceCollection = QuestionChoice::query()->findMany($choiceIds);

            /**
             * validate
             */
            foreach ($questions as $questionKey => $questionData) {
                /**
                 * validate question
                 */
                if ($questionId = Arr::get($questionData, 'question_id')) {
                    /** @var Question $question */
                    $question = $questionCollection->firstWhere('question_id', $questionId);
                    if (! $question || ($this->survey !== null && $question->survey_id !== $this->survey->getKey())) {
                        $this->setError(
                            'questions.' .$questionKey. '.question_id',
                            __('Invalid question ID.'),
                        );

                        return;
                    }

                    /**
                     * validate choice
                     */
                    if (in_array(Arr::get($question, 'type'), [QuestionType::CHECK_BOX->value, QuestionType::SELECT_BOX->value])) {
                        $choices = Arr::get($question, 'choices');
                        foreach ($choices as $choiceKey => $choiceData) {
                            if ($choiceId = Arr::get($choiceData, 'choice_id')) {
                                /** @var QuestionChoice $choice */
                                $choice = $choiceCollection->firstWhere('choice_id', $choiceId);
                                if (! $choice || ($choice->question_id !== $question->getKey())) {
                                    $this->setError(
                                        'questions.' .$questionKey. '.choices.' . $choiceKey . '.choice_id',
                                        __('Invalid choice ID.'),
                                    );
                                }
                            }
                        }
                    }
                }
            }

            $this->questionIds = $questionIds;
            $this->questions = $questionCollection;
            $this->choices = $choiceCollection;
        });
    }

    /**
     * @return Survey
     */
    public function getSurvey()
    {
        return $this->survey ?? app(Survey::class);
    }

    /**
     * @return Question[]|Collection
     */
    public function getQuestions()
    {
        return $this->questions;
    }

    /**
     * @return QuestionChoice[]|Collection
     */
    public function getChoices()
    {
        return $this->choices;
    }

    /**
     * @return array
     */
    public function getQuestionIds()
    {
        return $this->questionIds;
    }
}
