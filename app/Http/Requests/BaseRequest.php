<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

/**
 * Class BaseRequest
 * @package App\Http\Requests
 * @method User user()
 */
abstract class BaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * @param string $key
     * @param string $message
     * @return void
     * @throws ValidationException
     */
    public function setError(string $key, string $message)
    {
        $validator = $this->getValidatorInstance();
        $validator->errors()->add($key, $message);

        /**
         * throw exception
         */
        $this->failedValidation($validator);
    }
}
