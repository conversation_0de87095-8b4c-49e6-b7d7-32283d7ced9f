<?php

namespace App\Http\Resources\Traits;

use App\Models\Post;
use App\Models\User;

trait WithLazyData
{
    /**
     * @param User $user
     * @return void
     */
    protected function addUser(User $user)
    {
        lazyData()->addUser($user);
    }

    /**
     * @param User[] $users
     * @return void
     */
    protected function addUsers(array $users)
    {
        lazyData()->addUsers($users);
    }

    /**
     * @param int $userId
     * @return void
     */
    protected function addUserID(int $userId)
    {
        lazyData()->addUserID($userId);
    }

    /**
     * @param array $userIds
     * @return void
     */
    protected function addUserIDs(array $userIds)
    {
        lazyData()->addUserIDs($userIds);
    }

    /**
     * @param Post $post
     * @return void
     */
    protected function addPost(Post $post)
    {
        lazyData()->addPost($post);
    }

    /**
     * @param Post[] $posts
     * @return void
     */
    protected function addPosts(array $posts)
    {
        lazyData()->addPosts($posts);
    }

    /**
     * @param int $postId
     * @return void
     */
    protected function addPostID(int $postId)
    {
        lazyData()->addPostID($postId);
    }
}
