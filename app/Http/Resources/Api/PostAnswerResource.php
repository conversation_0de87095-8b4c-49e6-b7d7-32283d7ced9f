<?php

namespace App\Http\Resources\Api;

use App\Http\Resources\Traits\WithLazyData;
use App\Models\PostAnswer;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PostAnswerResource extends JsonResource
{
    use WithLazyData;

    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * @var array
     */
    protected $blockedUserIds = [];

    /**
     * construction
     */
    public function __construct(mixed $resource, protected ?User $user = null)
    {
        parent::__construct($resource);
    }

    /**
     * @param array $ids
     * @return $this
     */
    public function withBlockedUsers(array $ids)
    {
        $this->blockedUserIds = $ids;

        return $this;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var PostAnswer $answer */
        $answer = $this->resource;

        is_null($this->user) ? $this->addUserID($answer->user_id) : $this->addUser($this->user);

        /**
         * add post to response
         */
        if ($answer->relationLoaded('post')) {
            $this->addPost($answer->post);
        }

        $liked = false;
        $likedCount = 0;
        $latestLikedAt = '';

        $donated = false;
        $donatedCount = 0;
        $latestDonatedAt = '';

        $reported = false;
        $muted = false;
        if ($answer->relationLoaded('reactions')) {
            $reactions = $answer->reactions->sortByDesc('id');

            /**
             * like
             */
            if ($reaction = $reactions->firstWhere('action', 'liked')) {
                $liked = true;
                $likedCount = (int) $reaction->count;
                $latestLikedAt = $reaction->updated_at->toDateTimeString();
            }

            /**
             * donate
             */
            if ($reaction = $reactions->firstWhere('action', 'donated')) {
                $donated = true;
                $donatedCount = (int) $reaction->count;
                $latestDonatedAt = $reaction->updated_at->toDateTimeString();
            }

            $reported = ! is_null($reactions->firstWhere('action', 'reported'));
            $muted = ! is_null($reactions->firstWhere('action', 'muted'));
        }

        $childrenCountKey = $answer->hasAttribute('_children_count') ? '_children_count' : 'children_count';

        return [
            'answer_id' => $answer->getKey(),
            'post_id' => (int) $answer->post_id,
            'user_id' => (int) $answer->user_id,
            'content' => $answer->content,
            'comment_count' => (int) $answer->comment_count,
            'like_count' => (int) $answer->like_count,
            'liked' => (int) $liked,
            'latest_liked_count' => $likedCount,
            'latest_liked_at' => $latestLikedAt,
            'donated' => (int) $donated,
            'latest_donated_count' => $donatedCount,
            'latest_donated_at' => $latestDonatedAt,
            'coin_count' => (int) $answer->coin_count,
            'best_answer' => (int) $answer->voted,
            'tab_index' => 0,
            'parent_id' => (int) $answer->parent_id,
            'children_count' => (int) $answer->getAttribute($childrenCountKey),
            'level' => (int) $answer->level,
            'created_at' => $answer->created_at->toDateTimeString(),
            'reported' => (int) $reported,
            'muted' => (int) $muted,
            'children' => $this->whenLoaded('children', function ($children) use ($request) {
                return $children->transform(function ($child) use ($request) {
                    return static::make($child)->toArray($request);
                });
            }, []),
            'blocked' => (int) in_array($answer->user_id, $this->blockedUserIds),
            'pinned' => $answer->pinned,
            'image' => (string) storage()->url($answer->image),
            'ogp' => $this->whenLoaded('metadata', fn () => MetaDataResource::make($answer->metadata)->toArray($request), null),
        ];
    }
}
