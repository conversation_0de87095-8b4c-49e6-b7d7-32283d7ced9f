<?php

namespace App\Http\Resources\Api;

use App\Actions\Post\StorePost;
use App\Http\Resources\Traits\WithLazyData;
use App\Models\Post;
use App\Models\Reaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

/**
 * @property Post $resource
 */
class PostResource extends JsonResource
{
    use WithLazyData;

    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * @var array
     */
    protected $blockedUserIds = [];

    /**
     * construction
     */
    public function __construct(mixed $resource, protected ?User $user = null)
    {
        parent::__construct($resource);
    }

    /**
     * @param array $ids
     * @return $this
     */
    public function withBlockedUsers(array $ids)
    {
        $this->blockedUserIds = $ids;

        return $this;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $post = $this->resource;

        $relationshipAnswered = $post->relationLoaded('relationshipAnswered') ? $post->getAttribute('relationshipAnswered') : null;

        $userIds = [];
        if (! is_null($relationshipAnswered)) {
            $userIds = explode(',', $relationshipAnswered->getAttribute('answered_users'));
            $userIds = array_map(fn ($id) => (int) $id, $userIds);
            $userIds = array_slice($userIds, 0, 3);

            $this->addUserIDs($userIds);
        }

        is_null($this->user) ? $this->addUserID($post->user_id) : $this->addUser($this->user);

        $liked = false;
        $likedAt = '';
        $muted = false;
        $reported = false;
        if ($post->relationLoaded('reactions')) {
            $reactions = $post->reactions;
            $actions = $reactions->pluck('action')->toArray();

            if ($liked = in_array('liked', $actions)) {
                $reaction = $reactions->firstWhere('action', 'liked');
                /** @var Reaction $reaction */
                $likedAt = $reaction->updated_at->toDateTimeString();
            }

            $muted = in_array('muted', $actions);
            $reported = in_array('reported', $actions);
        }

        $media = $post->media;

        $community = null;
        if ($post->relationLoaded('communityPost')) {
            $community = $post->communityPost?->community;
        }

        $ogp = $this->whenLoaded('metadata', fn () => MetaDataResource::make($post->metadata)->toArray($request), null);
        $cover = (string) storage()->url($post->image);
        $hasCover = ! in_array($cover, StorePost::$defaultImages);

        if (! $hasCover && ! is_null($ogp)) {
            $ogpImage = Arr::get($ogp, 'image');
            if ($ogpImage) {
                $cover = $ogpImage;
            }
        }

        return [
            'post_id' => $post->getKey(),
            'user_id' => (int) $post->user_id,
            'cover' => $cover,
            'has_cover' => (int) $hasCover,
            'type' => $post->type,
            'tag' => '',
            'content' => $media->count() ? '' : (string) $post->content,
            'media' => MediaResource::collection($media)->toArray($request),
            'like_count' => (int) $post->like_count,
            'liked' => (int) $liked,
            'liked_at' => $likedAt,
            'answer_count' => (int) $post->getAttribute('total_answer'),
            'answers' => $this->getAnswers($request, $post),
            'best_answer_selected' => (int) $post->voted,
            'best_answer_selection_enabled' => $this->whenExistsLoaded('answers', fn() => (int) $post->getAttribute('answers_exists'), 0),
            'qa_type' => (string) $post->getAttribute('qa_type'),
            'muted' => (int) $muted,
            'reported' => (int) $reported,
            'answered_friends' => $userIds,
            'reward_amount' => $post->getRewardAmount(),
            'community' => $this->when(! is_null($community), fn () => CommunityResource::make($community)->toArray($request), null),
            'blocked' => (int) in_array($post->user_id, $this->blockedUserIds),
            'ogp' => $ogp,
            'camera_roll' => (int) $post->camera_roll,
        ];
    }

    /**
     * @param Request $request
     * @param Post $post
     * @return array
     */
    protected function getAnswers(Request $request, Post $post)
    {
        if (! $post->relationLoaded('answers')) {
            return [];
        }

        return $post->answers->transform(fn ($answer) => PostAnswerResource::make($answer)->withBlockedUsers($this->blockedUserIds)->toArray($request));
    }
}
