<?php

namespace App\Http\Resources\Api;

use App\Models\PremiumFeature;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PremiumFeatureResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var PremiumFeature $feature */
        $feature = $this->resource;

        /** @var User $user */
        $user = $request->user();
        $activatedFeatures = $user->premiumFeatures->pluck('type')->toArray();

        return [
            'premium_id' => $feature->getKey(),
            'name' => $feature->name,
            'description' => $feature->description,
            'type' => $feature->type,
            'price' => $feature->price,
            'image' => storage()->url($feature->image),
            'activated' => in_array($feature->type, $activatedFeatures),
        ];
    }
}
