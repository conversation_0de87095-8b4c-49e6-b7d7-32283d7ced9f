<?php

namespace App\Http\Resources\Api;

use App\Enums\QuestionType;
use App\Models\Question;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class QuestionResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * @param $resource
     * @param array $questionAnsweredIds
     */
    public function __construct($resource, protected array $questionAnsweredIds)
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Question $question */
        $question = $this->resource;
        $questionId = $question->getKey();

        $options = [];
        if (in_array($question->type, [QuestionType::CHECK_BOX->value, QuestionType::SELECT_BOX->value])) {
            $options = OptionResource::collection($question->choices)->toArray($request);
        }

        return [
            'question_id' => $questionId,
            'question' => $question->content,
            'type' => $question->type,
            'point' => (int) $question->point,
            'answered' => (int) in_array($questionId, $this->questionAnsweredIds),
            'options' => $options,
        ];
    }
}
