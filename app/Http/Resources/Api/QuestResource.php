<?php

namespace App\Http\Resources\Api;

use App\Models\Quest;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

class QuestResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * @param mixed $resource 
     * @param array $stats 
     * @return void 
     */
    public function __construct($resource, protected array $stats = [])
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Quest $quest */
        $quest = (object) $this->resource;

        return [
            'id' => (int) $quest->id,
            'title' => $quest->title,
            'description' => $quest->description,
            'image' => storage()->url($quest->image),
            'amount' => (int) $quest->amount,
            'unit' => $quest->unit,
            'type' => $quest->type,
            'is_dark' => (int) $quest->is_dark,
            'completed' => (int) Arr::get($this->stats, $quest->type, 0),
        ];
    }
}
