<?php

namespace App\Http\Resources\Api;

use App\Models\Media;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property Media $resource
 */
class MediaResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $media = $this->resource;

        return [
            'id' => $media->getKey(),
            'image' => storage()->url($media->image),
            'content' => (string) $media->content,
            'is_dark' => (int) $media->is_dark,
        ];
    }
}
