<?php

namespace App\Http\Resources\Api;

use App\Models\News;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NewsResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var News $news */
        $news = $this->resource;
        $newsId = $news->getKey();

        return [
            'news_id' => $newsId,
            'title' => $news->title,
            'url' => route('news.detail', [
                'news' => $newsId,
            ]),
        ];
    }
}
