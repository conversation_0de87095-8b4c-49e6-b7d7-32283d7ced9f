<?php

namespace App\Http\Resources\Api;

use App\Models\QuestionChoice;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OptionResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var QuestionChoice $option */
        $option = $this->resource;

        return [
            'option_id' => $option->getKey(),
            'content' => $option->content,
        ];
    }
}
