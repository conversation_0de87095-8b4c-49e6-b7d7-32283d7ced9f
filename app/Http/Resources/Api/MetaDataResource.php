<?php

namespace App\Http\Resources\Api;

use App\Models\MetaData;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property MetaData $resource
 */
class MetaDataResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $metadata = $this->resource;
        $host = parse_url($metadata->url, PHP_URL_HOST);

        return [
            'url' => $metadata->url,
            'host' => (string) $host,
            'title' => $metadata->title,
            'description' => (string) $metadata->description,
            'image' => (string) storage()->url($metadata->image),
        ];
    }
}
