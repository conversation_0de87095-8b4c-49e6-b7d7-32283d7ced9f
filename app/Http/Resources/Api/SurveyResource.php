<?php

namespace App\Http\Resources\Api;

use App\Models\AttachedSurvey;
use App\Models\Survey;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SurveyResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Survey $survey */
        $survey = $this->resource;

        $attachedSurveyData = [];

        /** @var AttachedSurvey[]|Collection $attachedSurveys */
        if ($attachedSurveys = $survey->attachedSurveys) {
            foreach ($attachedSurveys as $attachedSurvey) {
                $_survey = $attachedSurvey->survey;
                $questionAnsweredIds = $_survey->answers->pluck('question_id')->toArray();

                $attachedSurveyData[] = [
                    'survey_id' => $_survey->getKey(),
                    'title' => $_survey->title,
                    'answer_ids' => $attachedSurvey->choices->pluck('choice_id')->toArray(),
                    'questions' => $this->getQuestionResources($request, $_survey->questions, $questionAnsweredIds),
                    'surveys' => [],
                ];
            }
        }

        $questionAnsweredIds = $survey->answers->pluck('question_id')->toArray();

        return [
            'survey_id' => $survey->getKey(),
            'title' => $survey->title,
            'answer_ids' => [],
            'questions' => $this->getQuestionResources($request, $survey->questions, $questionAnsweredIds),
            'surveys' => $attachedSurveyData,
        ];
    }

    /**
     * @param Request $request
     * @param Collection $questions
     * @param array $questionAnsweredIds
     * @return array
     */
    protected function getQuestionResources(Request $request, Collection $questions, array $questionAnsweredIds)
    {
        $resources = [];
        foreach ($questions as $question) {
            $resources[] = QuestionResource::make($question, $questionAnsweredIds)->toArray($request);
        }

        return $resources;
    }
}
