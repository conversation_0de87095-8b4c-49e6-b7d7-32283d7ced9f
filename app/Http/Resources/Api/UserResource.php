<?php

namespace App\Http\Resources\Api;

use App\Models\User;
use App\Models\UserProfile;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property User $resource
 */
class UserResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * @var bool
     */
    protected $showProfile = false;

    /**
     * @return $this
     */
    public function showProfile()
    {
        /**
         * load user profile
         */
        $this->resource->loadProfile();
        $this->showProfile = true;

        return $this;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $user = $this->resource;
        $userId = $user->getKey();

        $isMyself = false;
        $isFollowing = 0;
        $isFollower = 0;
        $isFriend = 0;
        $hasPendingFriendRequest = 0;
        $hasFriendInvitationSent = 0;

        /** @var User $authUser */
        $authUser = $request->user();
        if ($authUser) {
            $isMyself = $authUser->isID((int) $userId);

            if (! $isMyself) {
                /**
                 * prepare relationship data data
                 */
                if (! $authUser->relationLoaded('friends')) {
                    $authUser->prepareRelationshipData();
                }

                //$isFollowing = $authUser->isFollowing($userId);
                //$isFollower = $authUser->isFollower($userId);
                $isFriend = $authUser->isFriend($userId);
                $hasPendingFriendRequest = $authUser->isPendingFriend($userId);
                $hasFriendInvitationSent = $authUser->isFriendInvitationSent($userId);
            }
        }

        $inviteRemain = 0;
        $referrer = null;
        if ($this->showProfile) {
            $inviteRemain = max($user->max_invite_count - $user->invited_user_count, 0);

            if ($referrerUser = $user->referrer) {
                $referrer = [
                    'user_id' => $referrerUser->getKey(),
                    'name' => (string) $referrerUser->name,
                    'phone' => (string) $referrerUser->getPhoneNumber(),
                    'avatar' => $referrerUser->profile_photo_path ? $referrerUser->getAttribute('profile_photo_url') : '',
                ];
            }
        }

        $usedCoin = $user->totalUsedCoin();
        $number = $userId - 4;
        if ($number < 0) {
            $number = 0;
        }

        $communities = null;
        if ($this->showProfile) {
            $communities = $user->getJoinedCommunities();
        }

        /**
         * fake unlocked until
         */
        $allowUserIds = $this->getViewableUserIds($isMyself);
        $timezone = 'Asia/Tokyo';
        $unlockedUntil = $isMyself ? (in_array($userId, $allowUserIds) ? now($timezone)->addDays(10)->toDateTimeString() : ($user->unlocked_until?->setTimezone($timezone)?->toDateTimeString() ?? '')) : '';

        return [
            'user_id' => (int) $userId,
            'number' => (int) $number,
            'uuid' => $this->showProfile ? (string) $user->uuid : '',
            'name' => (string) $user->name,
            'phone' => (string) $user->getPhoneNumber(),
            'position' => (string) $user->position,
            'birthday' => $user->birthday ? $user->birthday->toDateString() : '',
            'gender' => $isMyself ? $user->genderString() : '',
            'avatar' => $user->profile_photo_path ? $user->getAttribute('profile_photo_url') : '',
            'total_coin' => (int) $user->total_coin,
            'used_coin' => $usedCoin,
            'current_coin' => (int) max($user->total_coin - $usedCoin, 0),
            'total_point' => 0,
            'used_point' => 0,
            'current_point' => 0,
            'best_answer_count' => (int) $user->best_answer_count,
            'is_myself' => (int) $isMyself,
            'is_following' => (int) $isFollowing,
            'is_follower' => (int) $isFollower,
            'is_friend' => (int) $isFriend,
            'has_pending_friend_request' => (int) $hasPendingFriendRequest,
            'has_invitation_sent' => (int) $hasFriendInvitationSent,
            'post_answer_status' => (int) ($user->answer_count > 0),
            'last_answered_post_at' => '',
            'unlocked_until' => $unlockedUntil,
            'now' => $isMyself ? now($timezone)->toDateTimeString() : '',
            'follower_count' => $this->whenCounted('followers', fn() => (int) $this->resource->getAttribute('followers_count'), 0),
            'following_count' => $this->whenCounted('followings', fn() => (int) $this->resource->getAttribute('followings_count'), 0),
            'friend_count' => $this->whenCounted('friends', fn() => (int) $this->resource->getAttribute('friends_count'), 0),
            'badge_count' => (int) $user->badge_count,
            'news_badge_count' => (int) $user->news_badge_count,
            'profile_type' => (string) $user->profile_type,
            'profile' => $this->parseProfile($user),
            'status' => (int) $user->status,
            'invite_remain_count' => (int) $inviteRemain,
            'referrer' => $referrer,
            'communities' => $this->when($communities?->count(), function () use ($communities, $request, $authUser) {
                $isSameUser = (int) $authUser->isID($this->resource->getKey());
                if (! $isSameUser) {
                    $authUser->load('communityRelations');
                }

                return $communities->transform(fn ($community) => CommunityResource::make($community)->markAsJoined($isSameUser)->toArray($request));
            }, null),
            'premium_features' => $this->whenLoaded('premiumFeatures', function () use ($user) {
                return $user->premiumFeatures->pluck('type')->toArray();
            }, []),
        ];
    }

    /**
     * @param bool $isMyself
     * @return array|int[]
     */
    protected function getViewableUserIds(bool $isMyself)
    {
        if (! $isMyself) {
            return [];
        }

        $env = (string) config('app.env');

        return match ($env) {
            'production' => [26, 27, 28],
            default => [],
        };
    }

    /**
     * @param User $user
     * @return array|null
     */
    protected function parseProfile(User $user)
    {
        if (! $user->relationLoaded('profile') || ! ($profile = $user->profile)) {
            return null;
        }

        return [
            'work' => (string) $profile->work,
            'expert' => (string) $profile->expert,
            'service_in_charge' => (string) $profile->service_in_charge,
            'brief' => (string) $profile->brief,
            'school_id' => (int) $profile->school_id,
            'school_name' => (string) $profile->school?->name,
            'school_student_count' => $this->getSchoolStudentCount($profile),
        ];
    }

    /**
     * @param UserProfile $profile
     * @return int
     */
    protected function getSchoolStudentCount(UserProfile $profile)
    {
        if (! $profile->school_id) {
            return 0;
        }

        return UserProfile::query()
            ->where('school_id', $profile->school_id)
            ->whereHas('user', fn ($q) => $q->where('status', 1)->where('user_id', '!=', $profile->user_id))
            ->count();
    }
}
