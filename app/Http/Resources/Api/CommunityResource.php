<?php

namespace App\Http\Resources\Api;

use App\Models\Community;
use App\Models\CommunityUser;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class CommunityResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * @var int
     */
    protected $relationStatus = 0;

    /**
     * @param int $state
     * @return $this
     */
    public function markAsJoined(int $state)
    {
        $this->relationStatus = $state;

        return $this;
    }

    public function loadRelation()
    {
        /** @var Community $community */
        $community = $this->resource;

        $community->loadMissing([
            'createdBy',
            'members' => function (HasMany $query) {
                $query->where('status', 1)
                    ->whereExists(
                        DB::table('user_relations', 'r')
                            ->whereColumn('r.target_id', 'community_users.user_id')
                            ->where('r.is_friend', 1)
                            ->where('r.is_blocked', 0)
                    )
                    ->orderBy('updated_at', 'desc')
                    ->limit(5);
            },
            'members.user',
        ]);

        $community->loadCount([
            'members as members_count' => fn ($q) => $q->where('status', 1)->whereHas('user', fn ($q) => $q->where('status', 1)),
        ]);

        /** @var User $user */
        $user = auth()->user();

        if (! $user->relationLoaded('communityRelations')) {
            $user->loadMissing([
                'communityRelations' => function (HasMany $query) use ($community) {
                    $query->where('community_id', $community->getKey());
                },
            ]);
        }

        return $this;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Community $community */
        $community = $this->resource;

        /**
         * trạng thái tham gia
         */
        $relationStatus = $this->relationStatus;

        /** @var User $user */
        $user = $request->user();

        if (! $relationStatus && $user->relationLoaded('communityRelations')) {
            /** @var CommunityUser $communityRelation */
            if ($communityRelation = $user->communityRelations->first(fn ($c) => $c->isCommunity($community->getKey()))) {
                $relationStatus = (int) $communityRelation->status;
            }
        }

        $members = [];
        if ($community->relationLoaded('members')) {
            foreach ($community->members as $member) {
                $members[] = $member->user;
            }
        }

        return [
            'community_id' => (int) $community->getKey(),
            'name' => (string) $community->name,
            'description' => (string) $community->description,
            'avatar' => $community->avatar ? (string) storage()->url($community->avatar) : 'https://cdn.answerr.app/assets/images/default-school-icon.png',
            'status' => (int) $community->status,
            'relation_status' => $relationStatus,
            'isOwner' => (int) $community->ownerBy($user->getKey()),
            'member_count' => $this->whenCounted('members_count', fn () => $community->getAttribute('members_count'), 0),
            'createdBy' => $this->whenLoaded('createdBy', fn () => $community->createdBy?->toResource($request), null),
            'members' => $this->whenLoaded('members', fn () => UserResource::collection($members)->toArray($request), []),
            'type' => $community->school_id ? 'school' : 'community',
        ];
    }
}
