<?php

namespace App\Http\Resources\Api;

use App\Http\Resources\Traits\WithLazyData;
use App\Models\Notification;
use App\Models\NotificationUser;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NotificationResource extends JsonResource
{
    use WithLazyData;

    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request, bool $addUser = true): array
    {
        /** @var Notification $notification */
        $notification = $this->resource;

        $reactionUser = $notification->relationLoaded('reactionUser') ? $notification->reactionUser : null;

        if ($addUser && $reactionUser) {
            $this->addUser($reactionUser);
        }

        [$postId, $answerId, $parentAnswerId, $jobId, $childId, $rootId, $communityId] = $notification->getObjectIds();

        $createdAt = $notification->updated_at->toDateTimeString();
        $userId = ($reactionUser?->getKey() ?? '0');

        $users = [];
        if (in_array($notification->type, [0, 3, 9, 15, 20])) {
            $users = $notification->relationLoaded('users') ? $notification->users->all() : [];

            if (! empty($users)) {
                /** @var NotificationUser $record */
                $record = $users[0];

                // $createdAt = $record->updated_at->toDateTimeString();

                $userId = $record->user_id;
                if ($addUser) {
                    $this->addUser($record->user);
                }
            }
        }

        $system = in_array($notification->type, [4, 5, 6, 7, 10, 14, 15, 19, 21, 22, 23, 24]) ? 1 : 0;

        return [
            'notification' => [
                'body' => $notification->getMessage($users, $reactionUser),
            ],
            'data' => [
                'id' => (string) $notification->getKey(),
                'type' => (string) $notification->type,
                'answer_id' => (string) $answerId,
                'post_id' => (string) $postId,
                'user_id' => (string) $userId,
                'root_id' => (string) $rootId,
                'parent_answer_id' => (string) $parentAnswerId,
                'child_id' => (string) $childId,
                'job_id' => (string) $jobId,
                'community_id' => (string) $communityId,
                'status' => (string) $notification->status,
                'created_at' => $createdAt,
                'system' => (string) $system,
            ],
            'apns' => [
                'payload' => [
                    'aps' => [
                        'badge' => 1,
                        'sound' => 'default',
                    ],
                ],
            ],
        ];
    }
}
