<?php

namespace App\Http\Resources\Web;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var User $user */
        $user = $this->resource;

        $answered = $user->getAttribute('answers_count') > 0;

        return [
            'user_id' => $user->getKey(),
            'name' => $user->name,
            'phone' => $user->getPhoneNumber(),
            'birthday' => $user->birthday?->toDateString(),
            'created_at' => $user->created_at->toDateTimeString(),
            'total_point' => $user->total_point,
            'used_point' => $user->used_point,
            'current_point' => $user->total_point - $user->used_point,
            'answer_started_at' => $user->answer_started_at?->toDateTimeString() ?? '',
            'answer_ended_at' => $user->answer_ended_at?->toDateTimeString() ?? '',
            'answer_status' => 1 === (int) $user->answer_status ? 1 : ($answered ? 2 : 0),
            'role' => $user->role,
            'is_super_admin' => $user->isSuperAdmin(),
            'post_count' => $user->post_count,
            'answer_count' => $user->answer_count,
            'comment_count' => $user->comment_count,
            'best_answer_count' => $user->best_answer_count,
            'status' => $user->status,
            'status_label' => __('user.status.' . $user->status),
            'last_logged_in_at' => $user->last_logged_in_at?->toDateTimeString() ?? '',
        ];
    }
}
