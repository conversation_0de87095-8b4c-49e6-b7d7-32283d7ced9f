<?php

namespace App\Http\Resources\Web;

use App\Models\PostViewHistory;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PostHistoryResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var PostViewHistory $log */
        $log = $this->resource;

        $post = $log->post;

        return [
            'id' => $log->getKey(),
            'post_id' => $log->post_id,
            'answer_count' => $post->answer_count,
            'content' => $post->content,
            'user_id' => $log->user_id,
            'username' => $log->user->name ?: $log->user->getPhoneNumber(),
            'viewed_at' => $log->created_at->toDateTimeString(),
        ];
    }
}
