<?php

namespace App\Http\Resources\Web;

use App\Models\Survey;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SurveyResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Survey $survey */
        $survey = $this->resource;

        return [
            'survey_id' => $survey->getKey(),
            'title' => $survey->title,
            'questions' => QuestionResource::collection($survey->questions)->toArray($request),
        ];
    }
}
