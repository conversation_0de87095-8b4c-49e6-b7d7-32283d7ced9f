<?php

namespace App\Http\Resources\Web;

use App\Models\PublishedSurvey;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SortedSurveyResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var PublishedSurvey $item */
        $item = $this->resource;

        $survey = $item->survey;

        return [
            'survey_id' => $survey->getKey(),
            'title' => $survey->title,
        ];
    }
}
