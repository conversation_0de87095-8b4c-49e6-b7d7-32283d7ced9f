<?php

namespace App\Http\Resources\Web;

use App\Models\PostAnswer;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PostAnswerResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var PostAnswer $answer */
        $answer = $this->resource;

        return [
            'answer_id' => $answer->getKey(),
            'content' => $answer->content,
            'report_count' => $answer->report_count,
            'post_id' => (int) $answer->post_id,
            'post_content' => $answer->post->content,
            'post_userID' => (int) $answer->post->user_id,
            'post_username' => $answer->post->createdBy->name,
            'user_id'   => (int) $answer->user_id,
            'username' => $answer->createdBy->name,
            'status' => (int) $answer->status,
            'status_label' => __('postAnswer.status.' . $answer->status),
            'created_at' => $answer->created_at->toDateTimeString(),
            'type' => __('postAnswer.level.' . $answer->level)
        ];
    }
}
