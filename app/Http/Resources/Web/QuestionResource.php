<?php

namespace App\Http\Resources\Web;

use App\Enums\QuestionPublicTarget;
use App\Enums\QuestionType;
use App\Models\Question;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class QuestionResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Question $question */
        $question = $this->resource;

        return [
            'question_id' => $question->getKey(),
            'type' => $question->type,
            'typeLabel' => QuestionType::from($question->type)->toLabel(),
            'content' => $question->content,
            'point' => $question->point,
            'public' => $question->public,
            'publicLabel' => QuestionPublicTarget::from($question->public)->toLabel(),
            'choices' => ChoiceResource::collection($question->choices)->toArray($request),
        ];
    }
}
