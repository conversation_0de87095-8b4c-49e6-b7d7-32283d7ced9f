<?php

namespace App\Http\Resources\Web;

use App\Enums\PostType;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

class PostResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Post $post */
        $post = $this->resource;

        return [
            'post_id' => $post->getKey(),
            'user_id' => $post->user_id,
            'username' => $post->createdBy->name,
            'content' => nl2br($post->content),
            'tag' => $post->type === PostType::WITH_TAG->value ? '' : __('postType.' . $post->type),
            'answer_count' => $post->answer_count,
            'report_count' => $post->report_count,
            'created_at' => $post->created_at->toDateTimeString(),
            'status' => $post->status,
            'status_label' => __('post.status.' . $post->status),
            'qa_type' => (string) $post->getAttribute('qa_type'),
            'featured' => (int) $post->featured,
            'community' => $post->communityPost?->community->name ?? '',
            'type' => $post->type,
        ];
    }
}
