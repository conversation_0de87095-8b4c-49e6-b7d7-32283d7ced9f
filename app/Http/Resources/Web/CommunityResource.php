<?php

namespace App\Http\Resources\Web;

use App\Models\Community;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property Community $resource
 */
class CommunityResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $community = $this->resource;

        return [
            'community_id' => $community->getKey(),
            'name' => $community->name,
            'description' => $community->description,
            'image' => $community->avatar ? (string) storage()->url($community->avatar) : 'https://cdn.answerr.app/assets/images/default-school-icon.png',
        ];
    }
}
