<?php

namespace App\Http\Resources\Web;

use App\Models\AttachedSurvey;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttachedSurveyResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var AttachedSurvey $attachedSurvey */
        $attachedSurvey = $this->resource;

        $survey = $attachedSurvey->survey;
        $toSurvey = $attachedSurvey->toSurvey;

        $choiceData = [];
        foreach ($attachedSurvey->choices as $choice) {
            $choiceModel = $choice->choice;
            $choiceData[] = [
                'choice_id' => (int) $choiceModel->choice_id,
                'content' => $choiceModel->content,
                'question_id' => (int) $choiceModel->question_id,
                'question_content' => $choiceModel->question->content,
                'show' => true,
            ];
        }

        return [
            'attached_id' => $attachedSurvey->getKey(),
            'title' => $attachedSurvey->title,
            'survey_id' => $attachedSurvey->survey_id,
            'survey' => [
                'survey_id' => $survey->getKey(),
                'title' => $survey->title,
            ],
            'toSurvey' => [
                'survey_id' => $toSurvey->getKey(),
                'title' => $toSurvey->title,
            ],
            'to_survey_id' => $attachedSurvey->to_survey_id,
            'choices' => $choiceData,
        ];
    }
}
