<?php

namespace App\Http\Resources\Web;

use App\Models\Quest;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class QuestResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Quest $quest */
        $quest = $this->resource;

        return [
            'id' => $quest->getKey(),
            'title' => $quest->title,
            'description' => $quest->description,
            'amount' => $quest->amount,
            'amount_label' => __('quest.' . $quest->type . '.amount', [
                'amount' => $quest->amount,
                'unit' => __('quest.unit.' . $quest->unit),
            ]),
            'unit' => $quest->unit,
            'type' => $quest->type,
            'image' => storage()->url($quest->image),
            'sort' => $quest->sort,
            'status' => (int) $quest->status,
            'status_label' => __('quest.status.' . $quest->status),
            'created_at' => $quest->created_at->toDateTimeString(),
        ];
    }
}
