<?php

namespace App\Http\Resources\Web;

use App\Models\News;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NewsResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * @var bool
     */
    protected $showContent = false;

    /**
     * @return static
     */
    public function showContent()
    {
        $this->showContent = true;

        return $this;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var News $news */
        $news = $this->resource;

        return [
            'news_id' => $news->getKey(),
            'title' => $news->title,
            'content' => $this->showContent ? $news->content : '',
            'status' => (int) $news->status,
            'status_label' => __('news.status.' . $news->status),
            'created_at' => $news->created_at->toDateTimeString(),
        ];
    }
}
