<?php

namespace App\Http\Resources\Web;

use App\Models\QuestionChoice;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChoiceResource extends JsonResource
{
    /**
     * @var string
     */
    public static $wrap = '';

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var QuestionChoice $choice */
        $choice = $this->resource;

        return [
            'choice_id' => $choice->getKey(),
            'content' => $choice->content,
        ];
    }
}
