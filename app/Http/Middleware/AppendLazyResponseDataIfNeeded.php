<?php

namespace App\Http\Middleware;

use App\Http\Resources\Api\PostResource;
use App\Http\Resources\Api\UserResource;
use App\Models\Post;
use App\Models\User;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class AppendLazyResponseDataIfNeeded
{
    public function handle(Request $request, Closure $next)
    {
        /** @var JsonResponse $response */
        $response = $next($request);

        if ($response->isOk()) {
            $originalData = $response->getOriginalContent();
            $wasChanged = false;

            /**
             * user data
             */
            if (! empty($userData = $this->getUserData($request))) {
                $originalData['data']['users'] = $userData;
                $wasChanged = true;
            }

            /**
             * post data
             */
            if (! empty($postData = $this->getPostData($request))) {
                $originalData['data']['posts'] = $postData;
                $wasChanged = true;
            }

            if ($wasChanged) {
                $response->setData($originalData);
                $response->setEncodingOptions(JSON_UNESCAPED_UNICODE);
            }
        }

        return $response;
    }

    /**
     * @return array
     */
    protected function getUserData(Request $request)
    {
        $userData = [];
        if ($users = lazyData()->getUsers()) {
            $userData = $this->getUserTransformedData($request, $users);
        }

        if ($userIds = lazyData()->getUserIds()) {
            $users = userRepository()->findMany($userIds);
            $userData = array_merge($userData, $this->getUserTransformedData($request, $users->all()));
        }

        return array_values($userData);
    }

    /**
     * @param Request $request
     * @param User[]|array $users
     * @return array
     */
    protected function getUserTransformedData(Request $request, array $users)
    {
        return UserResource::collection($users)->toArray($request);
    }

    /**
     * @return array
     */
    protected function getPostData(Request $request)
    {
        $postData = [];
        if ($posts = lazyData()->getPosts()) {
            $postData = $this->getPostTransformedData($request, $posts);
        }

        if ($postIds = lazyData()->getPostIds()) {
            $posts = postRepository()->findMany($postIds);
            $postData = array_merge($postData, $this->getPostTransformedData($request, $posts->all()));
        }

        return array_values($postData);
    }

    /**
     * @param Request $request
     * @param Post[]|array $posts
     * @return array
     */
    protected function getPostTransformedData(Request $request, array $posts)
    {
        return Arr::map($posts, function ($post) use ($request) {
            return PostResource::make($post)->toArray($request);
        });
    }
}
