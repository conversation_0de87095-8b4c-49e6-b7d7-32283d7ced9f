<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use <PERSON>vel\Sanctum\PersonalAccessToken;

class VerifyAuthenticatedUser
{
    /**
     * @var array
     */
    protected $routes = [
        'verifyOTP',
        'sentOTP',
    ];

    /**
     * @param User $user
     * @param string $routeName
     * @return bool
     */
    protected function isNotAllowUser(User $user, string $routeName): bool
    {
        if ($user->isPending()) {
            return ! in_array($routeName, $this->routes);
        }

        if ($user->isVerified()) {
            return ! in_array($routeName, array_merge(
                $this->routes,
                ['updateInformation'],
            ));
        }

        return false;
    }

    /**
     * @param PersonalAccessToken|mixed $token
     * @param string $routeName
     * @return bool
     */
    protected function isInvalidAccessToken(mixed $token, string $routeName): bool
    {
        if (! $token instanceof PersonalAccessToken) {
            return false;
        }

        return (0 === (int) $token->getAttribute('status')) && ! in_array($routeName, ['verifyOTP', 'sentOTP']);
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $routeName = (string) $request->route()->getName();

        when($request->user(), function (User $user) use ($routeName) {
            $userWasDisabled = $user->isDisabled();

            /** @var PersonalAccessToken|mixed $token */
            $token = $user->currentAccessToken();

            if (
                $userWasDisabled
                || $this->isNotAllowUser($user, $routeName)
                || $this->isInvalidAccessToken($token, $routeName)
            ) {
                auth('web')->logout();
                $token->delete();

                if ($userWasDisabled) {
                    abort(104, __('Your account has been deleted or disabled.'));
                }

                abort(401, 'Unauthenticated.');
            }
        });

        return $next($request);
    }
}
