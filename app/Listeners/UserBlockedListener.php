<?php

namespace App\Listeners;

use App\Events\UserBlocked;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Query\Builder;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class UserBlockedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     */
    public function handle(UserBlocked $event): void
    {
        $user = $event->getUser();
        $userId = $user->getKey();
        $blockedId = $event->getBlockedUserId();

        /**
         * unfriend, unfollow each other
         */
        DB::table('user_relations')
            ->where([
                'user_id' => $userId,
                'target_id' => $blockedId,
            ])
            ->orWhere(function ($query) use ($blockedId, $userId) {
                $query->where([
                    'user_id' => $blockedId,
                    'target_id' => $userId,
                ]);
            })
            ->update([
                'is_friend' => 0,
                'is_follow' => 0,
            ]);

        /**
         * ẩn những thông liên quan đến người chặn và người bị chặn
         * trong những thông báo gộp
         */
        DB::table('notification_users', 'u')
            ->where(function (Builder $query) use ($userId, $blockedId) {
                $query->where('user_id', $blockedId)
                    ->whereExists(
                        DB::table('notifications', 'n')
                            ->whereRaw('`n`.`id` = `u`.`notification_id`')
                            ->where('n.user_id', $userId),
                    );
            })
            ->orWhere(function (Builder $query) use ($userId, $blockedId) {
                $query->where('user_id', $userId)
                    ->whereExists(
                        DB::table('notifications', 'n')
                            ->whereRaw('`n`.`id` = `u`.`notification_id`')
                            ->where('n.user_id', $blockedId),
                    );
            })
            ->update([
                'status' => 0,
            ]);

        /**
         * ẩn những thông báo liên quan đến người bị chặn và ngược lại
         * và những thông báo gộp khi không còn users nào active
         */
        DB::table('notifications', 'n')
            ->where(function (Builder $query) use ($userId, $blockedId) {
                $query->where('user_id', $userId)
                    ->where('reaction_user_id', $blockedId);
            })
            ->orWhere(function (Builder $query) use ($userId, $blockedId) {
                $query->where('user_id', $blockedId)
                    ->where('reaction_user_id', $userId);
            })
            ->orWhere(function (Builder $query) use ($userId, $blockedId) {
                $query->whereIn('user_id', [$userId, $blockedId])
                    /**
                     * merged notification
                     */
                    ->whereIn('type', [0, 3, 9, 15])
                    ->whereNotExists(
                        DB::table('notification_users', 'u')
                            ->whereColumn('u.notification_id', 'n.id')
                            ->where('u.status', 1)
                    );
            })
            ->update([
                'active' => 0,
            ]);

        /**
         * clear blocked cache
         */
        blockHelper()->clearCache($userId, $blockedId);
    }
}
