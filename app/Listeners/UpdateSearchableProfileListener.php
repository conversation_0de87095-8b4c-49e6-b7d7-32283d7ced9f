<?php

namespace App\Listeners;

use App\Events\UpdateSearchableProfile;
use App\Models\UserProfile;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class UpdateSearchableProfileListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     */
    public function handle(UpdateSearchableProfile $event): void
    {
        $profile = UserProfile::query()->where('user_id', $event->getUserId())->first();
        $profile?->touch();
    }
}
