<?php

namespace App\Listeners;

use App\Actions\Reaction\CreateNewNotification;
use App\Events\PostAnswerVoted;

class PostAnswerVotedListener
{
    /**
     * Handle the event.
     */
    public function handle(PostAnswerVoted $event): void
    {
        $postAnswer = $event->getPostAnswer();

        /** @var CreateNewNotification $notificationCreator */
        $notificationCreator = app(CreateNewNotification::class);

        $notificationCreator->execute($postAnswer->createdBy, [
            'object_type' => 'post_answer',
            'object_id' => $postAnswer->getKey(),
            'type' => 11,
            'reaction_user_id' => $postAnswer->post->user_id,
            'data' => [
                'rewarded_coin' => $event->getRewardedCoin(),
            ],
        ]);
    }
}
