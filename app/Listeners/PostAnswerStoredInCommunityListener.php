<?php

namespace App\Listeners;

use App\Events\PostAnswerStoredInCommunity;
use App\Models\PostAnswer;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class PostAnswerStoredInCommunityListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     */
    public function handle(PostAnswerStoredInCommunity $event): void
    {
        $user = $event->getUser();
        $answer = $event->getPostAnswer();
        $post = $answer->post;

        $answered = $this->userHasOtherAnsweredPost($user, $answer);
        if (! $answered) {
            $post->updateQuietly([
                'sort_answered_at' => $answer->created_at->toDateTimeString(),
            ]);
        }
    }

    /**
     * @param User $user
     * @param PostAnswer $answered
     * @return bool
     */
    protected function userHasOtherAnsweredPost(User $user, PostAnswer $answered)
    {
        return $user->postAnswers()
            ->where([
                'post_id' => $answered->post_id,
                'level' => 1,
            ])
            ->where('answer_id', '!=', $answered->getKey())
            ->exists();
    }
}
