<?php

namespace App\Listeners;

use App\Actions\Reaction\CreateNewNotification;
use App\Enums\QuestType;
use App\Events\CareerUpdated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CareerUpdatedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * @param CreateNewNotification $notificationCreator
     */
    public function __construct(protected CreateNewNotification $notificationCreator)
    {

    }

    /**
     * Handle the event.
     */
    public function handle(CareerUpdated $event): void
    {
        $questHelper = questHelper();
        $type = QuestType::CAREER->value;
        $amount = $questHelper->getQuestRewardAmount($type);
        if ($amount) {
            $questHelper->applyQuestReward($event->getUser(), $type, $amount);

            // notification
            $this->notificationCreator->execute($event->getUser(), [
                'type' => 23,
            ], $amount);
        }
    }
}
