<?php

namespace App\Listeners;

use App\Events\FriendInvitationCanceled;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class FriendInvitationCanceledListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     */
    public function handle(FriendInvitationCanceled $event): void
    {
        $userID = $event->getUser()->getKey();
        $friendID = $event->getFriend()->getKey();

        DB::table('notifications')
            ->where([
                'user_id' => $friendID,
                'type' => 16,
                'reaction_user_id' => $userID,
            ])
            ->update([
                'active' => 0,
            ]);
    }
}
