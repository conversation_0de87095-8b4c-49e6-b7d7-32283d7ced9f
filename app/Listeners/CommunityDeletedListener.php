<?php

namespace App\Listeners;

use App\Events\CommunityDeleted;
use App\Models\CommunityUser;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Query\Builder;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class CommunityDeletedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     */
    public function handle(CommunityDeleted $event): void
    {
        $community = $event->getCommunity();
        $communityId = $community->getKey();

        /**
         * xóa thông báo
         */
        DB::table('notifications', 'n')
            /**
             * của nhóm
             */
            ->where([
                'object_type' => 'community',
                'object_id' => $community->getKey(),
            ])

            /**
             * của những bài posts trong nhóm
             */
            ->orWhere(function (Builder $query) use ($communityId) {
                $query->where('object_type', 'post')
                    ->whereExists(
                        DB::table('community_posts', 'cp')
                            ->whereColumn('cp.post_id', 'n.object_id')
                            ->where('cp.community_id', $communityId)
                    );
            })

            /**
             * của post-answers trong nhóm
             */
            ->orWhere(function (Builder $query) use ($communityId) {
                $query->where('object_type', 'post_answer')
                    ->whereExists(
                        DB::table('post_answers', 'pa')
                            ->whereColumn('pa.answer_id', 'n.object_id')
                            ->whereExists(
                                DB::table('community_posts', 'cp')
                                    ->whereColumn('cp.post_id', 'pa.post_id')
                                    ->where('cp.community_id', $communityId)
                            )
                    );
            })
            ->update([
                'active' => 0,
            ]);

        /**
         * xóa những bài post trong nhóm
         */
        DB::table('posts', 'p')
            ->whereExists(
                DB::table('community_posts', 'cp')
                    ->whereColumn('cp.post_id', 'p.post_id')
                    ->where('cp.community_id', $community->getKey())
            )
            ->update([
                'status' => 0,
            ]);

        /**
         * đồng bộ lại dữ liệu profile các members trong community
         */
        $community->members()
            ->with('user')
            ->where('status', 1)
            ->chunkById(100, function ($records) {
                /** @var CommunityUser[] $records */
                foreach ($records as $record) {
                    $record->user?->profile?->touch();
                }
            });
    }
}
