<?php

namespace App\Listeners;

use App\Events\UserWasDeleted;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Kreait\Laravel\Firebase\Facades\Firebase;

class UserWasDeletedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     */
    public function handle(UserWasDeleted $event): void
    {
        $userDeleted = $event->getDeletedUser();

        /**
         * remove all tokens
         */
        $userDeleted->tokens()->delete();

        /**
         * remove from scout search
         */
        $userDeleted->profile?->unsearchable();

        $userDeletedID = $userDeleted->getKey();

        /**
         * sync stats
         */
        $stats = DB::table('post_answers')
            ->select('post_id')
            ->where('user_id', $userDeletedID)
            ->where('status', 1)
            ->groupBy('post_id')
            ->orderByDesc('post_id')
            ->get()
            ->pluck('post_id')
            ->toArray();

        $stats = array_chunk($stats, 100);
        foreach ($stats as $stat) {
            $ids = implode(',', $stat);

            /**
             * sync post answer_count
             */
            DB::scalar(
                'UPDATE posts p LEFT JOIN (
                    SELECT post_id, COUNT(*) AS total_answer
                    FROM post_answers a
                        WHERE a.status=1 and a.level=1 AND EXISTS (SELECT * FROM users u WHERE u.user_id = a.user_id AND u.status=1)
                    GROUP BY a.post_id
                ) a ON p.post_id = a.post_id
                SET p.answer_count = COALESCE(a.total_answer, 0)
                WHERE p.post_id IN (' .$ids. ') AND p.status=1 AND EXISTS (
                        SELECT * FROM users pu WHERE pu.user_id = p.user_id AND pu.status=1
                );'
            );

            /**
             * sync post_answers children_count
             */
            DB::scalar(
                'UPDATE post_answers AS pa LEFT JOIN (
                    SELECT c.parent_id, COUNT(*) AS cnt
                    FROM post_answers c
                    WHERE c.status = 1 AND EXISTS (
                            SELECT * FROM users cu WHERE cu.user_id = c.user_id AND cu.status=1
                        )
                    GROUP BY c.parent_id
                ) AS child_counts ON pa.answer_id = child_counts.parent_id
                SET pa.children_count = COALESCE(child_counts.cnt, 0)
                WHERE pa.post_id IN (' .$ids. ') AND pa.status=1 AND EXISTS (
                        SELECT * FROM users au WHERE au.user_id = pa.user_id AND au.status=1
                );'
            );
        }

        /**
         * remove from user_relations
         */
        DB::table('user_relations')
            ->where('user_id', $userDeletedID)
            ->orWhere('target_id', $userDeletedID)
            ->update([
                'is_friend' => 0,
                'is_follow' => 0,
                'is_blocked' => 0,
            ]);

        /**
         * remove from merged notifications
         */
        DB::table('notification_users')
            ->where('user_id', $userDeletedID)
            ->update([
                'status' => 0,
            ]);

        /**
         * remove from notifications
         */
        DB::table('notifications')
            ->whereIn(
                'id',
                DB::table('notification_users', 'a')
                    ->select('notification_id')
                    ->whereNotExists(
                        DB::table('notification_users', 'b')
                            ->whereColumn('a.notification_id', 'b.notification_id')
                            ->where('b.status', 1)
                    ),
            )
            ->orWhere('reaction_user_id', $userDeletedID)
            ->update([
                'active' => 0,
            ]);

        /**
         * delete from firebase
         */
        $this->deleteFromFirebase($userDeleted);
    }

    /**
     * @param User $user
     * @return void
     */
    protected function deleteFromFirebase(User $user)
    {
        try {
            $firebaseAuth = Firebase::auth();
            $firebaseAuth->deleteUser($user->firebase_auth_id);
        } catch (\Throwable $e) {

        }
    }
}
