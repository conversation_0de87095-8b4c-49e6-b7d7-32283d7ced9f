<?php

namespace App\Listeners;

use App\Actions\Reaction\CreateNewNotification;
use App\Events\PostAnswerLiked;
use Illuminate\Support\Facades\Cache;
use Throwable;

class PostAnswerLikedListener
{
    /**
     * Handle the event.
     */
    public function handle(PostAnswerLiked $event): void
    {
        $postAnswer = $event->getPostAnswer();
        $user = $event->getUser();

        $lockKey = $user->getKey() . '_liking_' . $postAnswer->getKey();
        $lock = Cache::lock($lockKey, 5);

        if ($lock->get()) {
            try {
                $reactionId = $event->getReactionId();
                $postAnswerId = $postAnswer->getKey();
                $userId = $user->getKey();

                /**
                 * kiểm tra nếu đang xử lý tạo notification & push cho action like bài post
                 * thì không tạo thêm nữa (có thể like nhiều lần liên tiếp trong vòng 2 giây)
                 */
                $notificationCreator = app(CreateNewNotification::class);
                $notificationCreator->execute($postAnswer->createdBy, [
                    'object_type' => 'post_answer',
                    'object_id' => $postAnswerId,
                    'type' => 20,
                    'reaction_user_id' => $userId,
                    'reaction_id' => $reactionId,
                ]);
            } catch (Throwable $e) {

            }
        }
    }
}
