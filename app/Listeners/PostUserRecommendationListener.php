<?php

namespace App\Listeners;

use App\Actions\Reaction\CreateNewNotification;
use App\Events\PostUserRecommendation;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class PostUserRecommendationListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     */
    public function handle(PostUserRecommendation $event): void
    {
        $userIds = $event->getUserIds();
        $post = $event->getPost();

        $users = User::query()
            ->whereIn('user_id', $userIds)
            ->where('status', 1)
            ->get();

        /** @var CreateNewNotification $notificationCreator */
        $notificationCreator = app(CreateNewNotification::class);
        foreach ($users as $user) {
            $notificationCreator->execute($user, [
                'object_type' => 'post',
                'object_id' => $post->getKey(),
                'type' => 12,
                'reaction_user_id' => $post->user_id,
            ]);
        }
    }
}
