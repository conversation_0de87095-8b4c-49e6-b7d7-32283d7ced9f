<?php

namespace App\Listeners;

use App\Events\ListNewsFetched;
use Illuminate\Support\Facades\DB;

class ListNewsFetchedListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ListNewsFetched $event): void
    {
        $user = $event->getUser();

        DB::table('users')
            ->where('user_id', $user->getKey())
            ->update([
                'news_badge_count' => 0,
            ]);
    }
}
