<?php

namespace App\Listeners;

use App\Actions\Reaction\CreateNewNotification;
use App\Events\PostLiked;

class PostLikedListener
{
    /**
     * Create the event listener.
     */
    public function __construct(protected CreateNewNotification $notificationCreator)
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PostLiked $event): void
    {
        $post = $event->getPost();
        $this->notificationCreator->execute($post->createdBy, [
            'object_type' => 'post',
            'object_id' => $post->getKey(),
            'type' => 9,
            'reaction_user_id' => $event->getUser()->getKey(),
            'reaction_id' => $event->getReactionId(),
        ]);
    }
}
