<?php

namespace App\Listeners;

use App\Actions\Reaction\CreateNewNotification;
use App\Enums\PostType;
use App\Events\PostStored;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class PostStoredListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Create the event listener.
     */
    public function __construct(protected CreateNewNotification $notificationCreator)
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PostStored $event): void
    {
        $post = $event->getPost();

        /**
         * push thông báo cho những người bạn của post owner
         * ngoại trừ post là loại free (default)
         */
        if ($post->type === PostType::DEFAULT->value) {
            return;
        }

        $user = $event->getUser();
        $user->loadMissing([
            'friends',
            'friends.target',
        ]);

        $postId = $post->getKey();
        $userId = $user->getKey();

        $friends = $user->friends;
        foreach ($friends as $friend) {
            if ($friend->target) {
                if ($post->type === PostType::GENDER->value && $user->gender !== $friend->target->gender) {
                    continue;
                }

                $this->notificationCreator->execute($friend->target, [
                    'object_type' => 'post',
                    'object_id' => $postId,
                    'type' => 13,
                    'reaction_user_id' => $userId,
                ]);
            }
        }
    }
}
