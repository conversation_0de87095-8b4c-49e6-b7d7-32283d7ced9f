<?php

namespace App\Listeners;

use App\Actions\Reaction\CreateNewNotification;
use App\Events\FriendInvited;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class FriendInvitedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * @param CreateNewNotification $notificationCreator
     */
    public function __construct(protected CreateNewNotification $notificationCreator)
    {

    }

    /**
     * Handle the event.
     */
    public function handle(FriendInvited $event): void
    {
        $user = $event->getUser();
        $friend = $event->getFriend();

        $this->notificationCreator->execute($friend, [
            'type' => 16,
            'reaction_user_id' => $user->getKey(),
        ]);
    }
}
