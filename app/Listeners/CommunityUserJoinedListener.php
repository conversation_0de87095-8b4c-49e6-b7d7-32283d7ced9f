<?php

namespace App\Listeners;

use App\Actions\Reaction\CreateNewNotification;
use App\Events\CommunityUserJoined;
use App\Events\UpdateSearchableProfile;
use App\Helpers\CommunityHelper;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CommunityUserJoinedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    public function __construct(protected CreateNewNotification $notificationCreator)
    {

    }

    /**
     * Handle the event.
     */
    public function handle(CommunityUserJoined $event): void
    {
        $user = $event->getCommunityUser();
        $userId = $user->getKey();

        $reactionUserId = $event->getReactionUserId();
        $community = $event->getCommunity();

        /**
         * xóa cache ids của communities kín
         * khi user join vào community kín
         */
        if ($community->isClosed()) {
            CommunityHelper::clearClosedCommunityIds($userId);
        }

        /**
         * gử<PERSON> thông báo đượ<PERSON> add vào nhóm kín
         */
        $this->notificationCreator->execute($user, [
            'object_type' => 'community',
            'object_id' => $community->getKey(),
            'type' => 18,
            'reaction_user_id' => $reactionUserId,
        ]);

        /**
         * rebuild profile search data
         */
        UpdateSearchableProfile::dispatch($userId);
    }
}
