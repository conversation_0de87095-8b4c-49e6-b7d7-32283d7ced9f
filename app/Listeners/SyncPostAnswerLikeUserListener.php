<?php

namespace App\Listeners;

use App\Events\SyncPostAnswerLikeUser;
use App\Models\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class SyncPostAnswerLikeUserListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     */
    public function handle(SyncPostAnswerLikeUser $event): void
    {
        $postAnswer = $event->getPostAnswer();

        $postAnswer->loadCount([
            'likedReactions as user_liked_count' => function ($query) {
                $query->select(DB::raw('COUNT(DISTINCT user_id)'));
            },
        ]);

        $postAnswer->updateQuietly([
            'like_user_count' => $postAnswer->getAttribute('user_liked_count'),
        ]);

        /**
         * check to disable notification for unlike post answer
         * @var Notification $notification
         */
        $notification = $postAnswer->createdBy
            ->notifications()
            ->withCount([
                'users' => fn ($q) => $q->where('status', 1),
            ])
            ->where([
                'object_type' => 'post_answer',
                'object_id' => $postAnswer->getKey(),
                'type' => 20,
            ])
            ->first();

        if ($notification && !$notification->getAttribute('users_count')) {
            $notification->update([
                'active' => 0,
            ]);
        }
    }
}
