<?php

namespace App\Listeners;

use App\Events\PostAnswerDeleted;
use App\Models\PostAnswer;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class PostAnswerDeletedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * @param PostAnswer $answer
     * @return int
     */
    protected function getChildrenCount(PostAnswer $answer)
    {
        if (! $answer->parent_id) {
            return $answer->post->answer_count;
        }

        return $answer->parent->children_count;
    }

    /**
     * Handle the event.
     */
    public function handle(PostAnswerDeleted $event): void
    {
        $answer = $event->getPostAnswer();

        if ($answer->isVoted()) {
            $createdBy = $answer->createdBy;
            $createdBy->incrementOrDecrementCount('best_answer_count', 'decrement');

            /**
             * rebuild profile for search
             */
            $createdBy->rebuildProfileData();
        }

        $notificationType = 3;
        $objectId = $answer->parent_id;
        $objectType = 'post_answer';
        if (! $answer->parent_id) {
            $notificationType = 0;
            $objectId = $answer->post_id;
            $objectType = 'post';
        }

        $count = (int) $this->getChildrenCount($answer);
        $removeNotification = false;

        /**
         * nếu không tồn tại câu trả lời nào cùng cấp
         */
        if (! $count) {
            /**
             * xóa reactions
             * (auto remove from notification_users - foreign key triggered)
             */
            DB::table('reactions')
                ->where([
                    'object_id' => $objectId,
                    'object_type' => $objectType,
                    'action' => 'answered',
                ])
                ->delete();

            $removeNotification = true;
        }

        /**
         * vẫn còn câu trả lời cùng cấp (của tất cả users)
         */
        else {
            $isPost = $objectType === 'post';
            $objectIdField = $isPost ? 'post_id' : 'parent_id';

            /**
             * lấy danh sách user của những câu trả lời còn lại (cùng cấp với câu trả lời đã bị xóa)
             */
            $remainingUserAnsweredIds = DB::table('post_answers')
                ->select('user_id')
                ->where([
                    $objectIdField => $objectId,
                    'status' => 1,
                    'level' => $isPost ? 1 : $answer->level,
                ])
                ->groupBy('user_id')
                ->get()
                ->pluck('user_id')
                ->toArray();

            /**
             * nếu không tồn tại câu trả lời nào của user đã xóa câu trả lời thì xóa reaction
             * (tự động xóa notification_users)
             */
            if (! in_array($answer->user_id, $remainingUserAnsweredIds)) {
                DB::table('reactions')
                    ->where([
                        'object_id' => $objectId,
                        'object_type' => $objectType,
                        'user_id' => $answer->user_id,
                        'action' => 'answered',
                    ])
                    ->delete();
            }

            /**
             * nếu chỉ còn những câu trả lời của người hỏi (tự hỏi và trả lời)
             * thì xóa notification
             */
            $parentOwnerId = $isPost ? $answer->post->user_id : $answer->parent->user_id;
            if (! array_diff($remainingUserAnsweredIds, [$parentOwnerId])) {
                $removeNotification = true;
            }
        }

        /**
         * xóa notification
         */
        if ($removeNotification) {
            DB::table('notifications')
                ->where([
                    'object_id' => $objectId,
                    'object_type' => $objectType,
                    'type' => $notificationType,
                ])
                ->update([
                    'active' => 0,
                ]);
        }

        /**
         * xóa thông báo của answer đó (liked, donated ...) hoặc của children
         */
        $this->removeChildrenNotifications($answer);
    }

    /**
     * @param PostAnswer $answer
     * @return void
     */
    protected function removeChildrenNotifications(PostAnswer $answer)
    {
        if (! $answer->children_count) {
            return;
        }

        $answerId = $answer->getKey();

        DB::table('notifications')
            ->whereRaw('`object_id` = ? OR `object_id` IN (SELECT `answer_id` FROM `post_answers` WHERE `parent_id` = ?)', [$answerId, $answerId])
            ->where([
                'object_type' => 'post_answer',
            ])
            ->update([
                'active' => 0,
            ]);
    }
}
