<?php

namespace App\Listeners;

use App\Actions\Reaction\CreateNewNotification;
use App\Enums\QuestType;
use App\Events\FriendAdded;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class FriendAddedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * @param CreateNewNotification $notificationCreator
     */
    public function __construct(protected CreateNewNotification $notificationCreator)
    {

    }

    /**
     * Handle the event.
     */
    public function handle(FriendAdded $event): void
    {
        $user = $event->getUser();
        $friend = $event->getFriend();

        $userId = $user->getKey();
        $friendId = $friend->getKey();

        /**
         * push notification
         */
        $this->notificationCreator->execute($user, [
            'type' => 17,
            'reaction_user_id' => $friendId,
        ]);

        /**
         * clear invite notification
         */
        DB::table('notifications')
            ->where([
                'user_id' => $friendId,
                'type' => 16,
                'reaction_user_id' => $userId,
            ])
            ->orWhere(function ($query) use ($userId, $friendId) {
                $query->where([
                    'user_id' => $userId,
                    'type' => 16,
                    'reaction_user_id' => $friendId,
                ]);
            })
            ->update([
                'active' => 0,
            ]);
    }
}
