<?php

namespace App\Listeners;

use App\Events\UpdateSearchableProfile;
use App\Events\UserWasRemovedFromCommunity;
use App\Helpers\CommunityHelper;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class UserWasRemovedFromCommunityListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     */
    public function handle(UserWasRemovedFromCommunity $event): void
    {
        $userId = $event->getRemovedUserId();
        $community = $event->getCommunity();

        /**
         * xóa cache ids của communities kín
         * khi user bị xóa khỏi community kín
         */
        if ($community->isClosed()) {
            CommunityHelper::clearClosedCommunityIds($userId);
        }

        DB::table('notifications')->where([
            'object_type' => 'community',
            'object_id' => $community->getKey(),
            'user_id' => $userId,
            'type' => 18,
        ])->update([
            'active' => 0,
        ]);

        /**
         * rebuild profile search data
         */
        UpdateSearchableProfile::dispatch($userId);
    }
}
