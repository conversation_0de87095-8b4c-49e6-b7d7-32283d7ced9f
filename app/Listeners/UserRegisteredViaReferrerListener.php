<?php

namespace App\Listeners;

use App\Actions\Reaction\CreateNewNotification;
use App\Enums\QuestType;
use App\Events\UserRegisteredViaReferrer;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;

class UserRegisteredViaReferrerListener
{
    /**
     * Create the event listener.
     */
    public function __construct(protected CreateNewNotification $notificationCreator)
    {
        //
    }

    /**
     * Handle the event.
     * @noinspection PhpUndefinedMethodInspection
     */
    public function handle(UserRegisteredViaReferrer $event): void
    {
        $user = $event->getUser();
        $referrerId = (int) $user->referrer_id;

        if (! $referrerId) {
            return;
        }

        DB::transaction(function () use ($user, $referrerId) {
            /** @var User $referrer */
            $referrer = User::lockForUpdate()->find($referrerId);

            if (! $referrer || ! $referrer->isEnabled()) {
                throw new Exception('Invalid referrer invite.');
            }

            /**
             * apply quest reward
             */
            $questHelper = questHelper();
            $type = QuestType::INVITED->value;
            $amount = $questHelper->getQuestRewardAmount($type);

            /**
             * apply reward for user & push notification
             */
            if ($amount > 0) {
                $referrerIdCanReceiveReward = $referrer->canInviteUser() ? $referrerId : null;
                $questHelper->applyQuestReward($user, $type, $amount, $referrerIdCanReceiveReward);
            }

            $userId = $user->getKey();
            $this->notificationCreator->execute($user, [
                'type' => 14,
            ], $amount);

            $this->notificationCreator->execute($referrer, [
                'type' => 15,
                'reaction_user_id' => $userId,
            ], $amount);

            /**
             * auto add friend and follow other
             */
            try {
                $now = now('UTC')->toDateTimeString();

                $followData = [
                    [
                        'user_id' => $userId,
                        'target_id' => $referrerId,
                        'friend_at' => $now,
                        'followed_at' => $now,
                        'is_friend' => 1,
                        'is_follow' => 1,
                    ],
                    [
                        'user_id' => $referrerId,
                        'target_id' => $userId,
                        'friend_at' => $now,
                        'followed_at' => $now,
                        'is_friend' => 1,
                        'is_follow' => 1,
                    ],
                ];

                DB::table('user_relations')->insert($followData);

                // add friend notification
                $this->notificationCreator->execute($referrer, [
                    'type' => 17,
                    'reaction_user_id' => $userId,
                ]);

                $this->notificationCreator->execute($user, [
                    'type' => 17,
                    'reaction_user_id' => $referrerId,
                ]);
            } catch (Throwable $e) {
                // Log the error or handle it as needed
            }
        });
    }
}
