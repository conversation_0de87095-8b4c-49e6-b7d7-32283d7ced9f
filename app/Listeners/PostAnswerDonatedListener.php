<?php

namespace App\Listeners;

use App\Actions\Reaction\CreateNewNotification;
use App\Events\PostAnswerDonated;
use Illuminate\Support\Facades\Cache;

class PostAnswerDonatedListener
{
    /**
     * Handle the event.
     */
    public function handle(PostAnswerDonated $event): void
    {
        $postAnswer = $event->getPostAnswer();
        $user = $event->getUser();

        $lockKey = $user->getKey() . '_donate_' . $postAnswer->getKey();
        $lock = Cache::lock($lockKey, 5);

        if ($lock->get()) {
            try {
                $reactionId = $event->getReactionId();
                $postAnswerId = $postAnswer->getKey();
                $userId = $user->getKey();

                /**
                 * kiểm tra nếu đang xử lý tạo notification & push cho action donate bài post
                 * thì không tạo thêm nữa (có thể donate nhiều lần liên tiếp trong vòng 2 giây)
                 */
                $notificationCreator = app(CreateNewNotification::class);
                $notificationCreator->execute($postAnswer->createdBy, [
                    'object_type' => 'post_answer',
                    'object_id' => $postAnswerId,
                    'type' => 1,
                    'reaction_user_id' => $userId,
                    'reaction_id' => $reactionId,
                ]);
            } finally {
                // Giải phóng khóa sau khi xử lý
                // $lock->release();
            }
        }
    }
}
