<?php

namespace App\Listeners;

use App\Actions\Post\DeletePost;
use App\Actions\Post\DeletePostAnswer;
use App\Events\ContentReported;
use App\Models\Post;
use App\Models\PostAnswer;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class ContentReportedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     */
    public function handle(ContentReported $event): void
    {
        $reportedObject = $event->getReportedObject();

        $post = match (true) {
            $reportedObject instanceof Post => $reportedObject,
            $reportedObject instanceof PostAnswer => $reportedObject->post,
            default => null,
        };

        /**
         * kiểm tra bài post nằm trong community trường học
         * thì object bị report quá 3 lần sẽ bị xóa
         */
        $community = $post?->communityPost?->community;
        if ($community?->school_id) {
            $type = $reportedObject instanceof Post ? 'post' : 'post_answer';

            $sub = DB::table('reports')
                ->select('user_id')
                ->where([
                    'reportable_id' => $reportedObject->getKey(),
                    'reportable_type' => $type,
                ])
                ->groupBy('user_id');

            $userReportedCount = DB::table(DB::raw("({$sub->toSql()}) as `sub`"))
                ->mergeBindings($sub)
                ->count();

            if ($userReportedCount >= 3) {
                $processer = $type === 'post' ? app(DeletePost::class) : app(DeletePostAnswer::class);
                $processer->execute($reportedObject);
            }
        }
    }
}
