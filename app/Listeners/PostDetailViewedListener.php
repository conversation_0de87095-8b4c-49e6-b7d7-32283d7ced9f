<?php

namespace App\Listeners;

use App\Events\PostDetailViewed;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class PostDetailViewedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     */
    public function handle(PostDetailViewed $event): void
    {
        $post = $event->getPost();

        postViewHistoryRepos()->query()->firstOrCreate([
            'user_id' => $event->getUser()->getKey(),
            'view_date' => now('UTC')->toDateString(),
            'post_id' => $post->getKey(),
        ], [
            'post_user_id' => $post->user_id,
        ]);
    }
}
