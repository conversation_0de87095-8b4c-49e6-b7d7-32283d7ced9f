<?php

namespace App\Listeners;

use App\Actions\Reaction\CreateNewNotification;
use App\Enums\PostType;
use App\Events\PostedInCommunity;
use App\Models\CommunityUser;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Queue\InteractsWithQueue;

class PostedInCommunityListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    public function __construct(protected CreateNewNotification $notificationCreator)
    {

    }

    /**
     * Handle the event.
     */
    public function handle(PostedInCommunity $event): void
    {
        $postCommunity = $event->getCommunityPost();
        $postCommunity->load([
            'community',
            'post',
        ]);

        /**
         * post trong nhóm không phải nhóm trường học
         * và bài post không phải của nhóm admin types
         */
        $community = $postCommunity->community;
        $post = $postCommunity->post;
        if (! $community->school_id && ! in_array($post->type, PostType::consoleValidationRuleArray())) {
            $ownerId = $post->user_id;
            $blockedIds = blockHelper()->getBlockedIds($ownerId);
            $blockedIds[] = $ownerId;

            $community->members()
                ->with('user')
                ->where('status', 1)
                ->whereNotIn('user_id', $blockedIds)
                ->whereHas('user', fn ($q) => $q->where('status', 1))
                ->chunkById(100, function ($records) use ($postCommunity, $ownerId) {
                    /** @var Collection|CommunityUser[] $records */
                    foreach ($records as $record) {
                        $this->notificationCreator->execute($record->user, [
                            'object_type' => 'post',
                            'object_id' => $postCommunity->post_id,
                            'type' => 13,
                            'reaction_user_id' => $ownerId,
                        ]);
                    }
                });
        }
    }
}
