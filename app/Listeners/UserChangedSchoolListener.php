<?php

namespace App\Listeners;

use App\Events\UserChangedSchool;
use App\Models\Community;
use App\Models\CommunityUser;
use Illuminate\Support\Facades\DB;

class UserChangedSchoolListener
{
    /**
     * Handle the event.
     */
    public function handle(UserChangedSchool $event): void
    {
        $user = $event->getUser();
        $userId = $user->getKey();

        $override = $event->getOverride();
        $oldSchoolId = $event->getOldSchoolId();
        $newSchool = $event->getNewSchool();

        /**
         * thoát cộng đồng trường cũ
         */
        if ($oldSchoolId && $override) {
            $oldSchoolCommunity = Community::query()->where('school_id', $oldSchoolId)->first();
            DB::table('community_users')->where([
                'user_id' => $userId,
                'community_id' => $oldSchoolCommunity?->getKey(),
            ])->update([
                'status' => 0,
            ]);
        }

        /**
         * join vào cộng đồng trường mới
         */
        if ($newSchool) {
            $newSchoolCommunity = Community::query()->firstOrCreate([
                'school_id' => $newSchool->getKey(),
            ], [
                'name' => $newSchool->name,
                'description' => '',
                'status' => 2, // cộng đồng trường học là cộng đồng kín
            ]);

            /** @var CommunityUser $record */
            $record = $newSchoolCommunity->members()->firstOrCreate([
                'user_id' => $userId,
                'community_id' => $newSchoolCommunity->getKey(),
            ], [
                'status' => 1,
            ]);

            if (! $record->wasRecentlyCreated && ! $record->wasBlocked()) {
                $record->updateQuietly([
                    'status' => 1
                ]);
            }
        }
    }
}
