<?php

namespace App\Listeners;

use App\Events\PostUnliked;
use App\Models\Notification;

class PostUnlikedListener
{
    /**
     * Handle the event.
     */
    public function handle(PostUnliked $event): void
    {
        $post = $event->getPost();

        $notification = Notification::query()
            ->withCount('users as user_count')
            ->where([
                'object_type' => 'post',
                'object_id' => $post->getKey(),
                'type' => 9,
            ])
            ->first();

        /**
         * remove post liked notification
         */
        if (! $notification?->getAttribute('user_count')) {
            $notification?->delete();
        }
    }
}
