<?php

namespace App\Listeners;

use App\Events\PostDeleted;
use App\Models\PostAnswer;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class PostDeletedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     */
    public function handle(PostDeleted $event): void
    {
        $post = $event->getPost();
        $postId = $post->getKey();

        /**
         * clear all notifications
         */
        DB::table('notifications')
            /**
             * posts
             */
            ->where([
                'object_id' => $postId,
                'object_type' => 'post',
            ])

            /**
             * post answers of the post
             */
            ->orWhere(function ($query) use ($postId) {
                $query->whereRaw('`object_id` IN (SELECT `answer_id` FROM `post_answers` WHERE `post_id` = ?)', [$postId])
                    ->where([
                        'object_type' => 'post_answer',
                    ]);
            })
            ->delete();

        /**
         * rebuild user profile if post was voted
         */
        if ($post->voted) {
            /** @var PostAnswer|null $bestAnswered */
            $bestAnswered = $post->answers()
                ->where('status', 1)
                ->where('voted', 1)
                ->first();

            if ($bestAnswered) {
                $createdBy = $bestAnswered->createdBy;
                $createdBy->incrementOrDecrementCount('best_answer_count', 'decrement');
                $createdBy->rebuildProfileData();
            }
        }
    }
}
