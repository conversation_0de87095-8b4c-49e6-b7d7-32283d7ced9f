<?php

namespace App\Listeners;

use App\Actions\Reaction\CreateNewNotification;
use App\Enums\QuestType;
use App\Events\PostAnswerStored;
use App\Models\PostAnswer;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class PostAnswerStoredListener
{
    /**
     * Create the event listener.
     */
    public function __construct(protected CreateNewNotification $notificationCreator)
    {
        //
    }

    /**
     * @param User $user
     * @param PostAnswer $answer
     * @return \App\Models\Reaction
     */
    protected function syncReaction(User $user, PostAnswer $answer)
    {
        $attributes = [
            'object_type' => $answer->parent_id ? 'post_answer' : 'post',
            'object_id' => $answer->parent_id ? $answer->parent_id : $answer->post_id,
            'action' => 'answered',
        ];

        /** @var \App\Models\Reaction $reaction */
        return $user->reactions()->firstOrCreate($attributes, [
            'count' => 1,
            'object_user_id' => $answer->post->user_id,
            'react_date' => now('UTC')->toDateString(),
        ]);
    }

    /**
     * Handle the event.
     */
    public function handle(PostAnswerStored $event): void
    {
        DB::transaction(function () use ($event) {
            $postAnswer = $event->getPostAnswer();
            $parentAnswer = $event->getParentAnswer();
            $user = $event->getUser();

            $post = $postAnswer->post;
            $postOwner = $post->createdBy;

            /**
             * create new reaction
             */
            $reaction = $this->syncReaction($user, $postAnswer);
            $reactionId = $reaction->getKey();
            $reactUserId = (int) $user->getKey();

            /**
             * kiểm tra số lượt trả lời
             */
            $referralRewarded = (int) $user->referral_rewarded;
            $referralApplyReward = false;
            if (! $referralRewarded && $user->referrer_id) {
                $user->loadCount([
                    'postAnswers as answers_count' => function (Builder $query) {
                        $query->where('status', 1)
                            ->where('level', 1)
                            ->whereHas('post', fn ($q) => $q->where('status', 1));
                    },
                ]);

                $totalAnswered = (int) $user->getAttribute('answers_count');
                if ($totalAnswered > 10) {
                    $referralRewarded = 1;
                    $referralApplyReward = true;
                }
            }

            $user->incrementOrDecrementCount('answer_count', 'increment', 1, [
                'referral_rewarded' => $referralRewarded,
            ]);

            /**
             * nếu user trả lời từ đạt 10 câu hỏi
             * thì người giới thiệu sẽ được tăng thêm lượt mời
             */
            if ($referralApplyReward && ($referrer = $user->referrer)) {
                $referrer->incrementOrDecrementCount('max_invite_count');

                $this->notificationCreator->execute($referrer, [
                    'type' => 19,
                    'reaction_user_id' => $user->getKey(),
                ]);
            }

            /**
             * sync parent answer_count
             */
            $parentAnswer?->updateChildrenCount();

            /**
             * trả lời cho bài post
             */
            if (! $parentAnswer) {
                $isNotPostOwner = ! $post->ownerBy($reactUserId);

                /**
                 * sync post answer_count
                 */
                $post->incrementCount('answer_count', 1, $isNotPostOwner);

                /**
                 * sync point & push notification
                 */
                if ($isNotPostOwner) {
                    /**
                     * nếu người trả lời không phải là chủ bài post
                     * vả chưa có phần thưởng cho câu trả lời đầu tiên
                     */
                    if ($postOwner->isNotID($postAnswer->user_id) && ! $postOwner->first_answer_rewarded) {
                        $postOwner->updateQuietly([
                            'first_answer_rewarded' => 1,
                        ]);

                        $this->notificationCreator->execute($postOwner, [
                            'type' => 22,
                            'data' => [
                                'post_id' => $postAnswer->post_id,
                                'answer_id' => $postAnswer->getKey(),
                            ],
                        ]);
                    }

                    if ($postOwner->isNotMuted($postAnswer->post_id)) {
                        $this->notificationCreator->execute($postOwner, [
                            'object_type' => 'post',
                            'object_id' => $postAnswer->post_id,
                            'type' => 0,
                            'reaction_user_id' => $reactUserId,
                            'reaction_id' => $reactionId,
                            'data' => [
                                'answer_id' => $postAnswer->getKey(),
                            ],
                        ]);
                    }
                }
            }

            /**
             * trả lời cho câu trả lời
             */
            elseif (! $parentAnswer->ownerBy($reactUserId)) {
                $parentAnswerOwner = $parentAnswer->createdBy;

                if ($parentAnswerOwner->isNotMutedAny([$postAnswer->post_id, $parentAnswer->getKey()])) {
                    $this->notificationCreator->execute($parentAnswerOwner, [
                        'object_type' => 'post_answer',
                        'object_id' => $postAnswer->parent_id,
                        'type' => 3,
                        'reaction_user_id' => $reactUserId,
                        'reaction_id' => $reactionId,
                        'data' => [
                            'answer_id' => $postAnswer->getKey(),
                        ],
                    ]);
                }
            }
        });
    }
}
