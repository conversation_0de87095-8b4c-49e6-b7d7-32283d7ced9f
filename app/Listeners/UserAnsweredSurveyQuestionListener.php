<?php

namespace App\Listeners;

use App\Events\UserAnsweredSurveyQuestion;
use App\Models\Answer;
use App\Models\AttachedSurvey;
use App\Models\PublishedSurvey;
use App\Models\Survey;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Arr;

class UserAnsweredSurveyQuestionListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * @var User|null
     */
    protected ?User $user;

    /**
     * Handle the event.
     */
    public function handle(UserAnsweredSurveyQuestion $event): void
    {
        $this->user = $event->getUser();

        $answeredData = $this->getAnsweredData();

        /** @var PublishedSurvey $publishedModel */
        $publishedModel = app(PublishedSurvey::class);

        $query = $publishedModel->newQuery()
            ->with([
                'survey' => function (HasOne $query) {
                    $query->where('status', 1);
                },
                'survey.attachedSurveys',
                'survey.attachedSurveys.survey' => function (HasOne $query) {
                    $query->where('status', 1);
                },
                'survey.attachedSurveys.survey.questions' => function (HasMany $query) {
                    $query->orderBy('question_id');
                },
                'survey.questions' => function (HasMany $query) {
                    $query->orderBy('question_id');
                },
            ]);

        /** @var AttachedSurvey[]|Collection $results */
        $results = $query->orderBy('sort')->get();

        $completed = true;
        foreach ($results as $result) {
            if (! $this->isSurveyCompleted($result->survey, $answeredData)) {
                $completed = false;
                break;
            }
        }

        if ($completed) {
            $this->user->markSurveyAnswerStatusAsCompleted();
        }
    }

    /**
     * @return array
     */
    protected function getAnsweredData()
    {
        /** @var Collection|Answer[] $items */
        $items = $this->user->answers()
            ->with('choices')
            ->select('answer_id', 'survey_id', 'question_id')
            ->get();

        $answerData = [];
        foreach ($items as $item) {
            if (! isset($answerData[$item->survey_id])) {
                $answerData[$item->survey_id] = [
                    'questions' => [],
                    'answers' => [],
                ];
            }

            $answerData[$item->survey_id]['questions'][] = $item->question_id;
            foreach ($item->choices as $choice) {
                $answerData[$item->survey_id]['answers'][] = $choice->choice_id;
            }
        }

        return $answerData;
    }

    /**
     * @param Survey $survey
     * @param array $answerData
     * @return bool
     */
    protected function isSurveyCompleted(Survey $survey, array $answerData)
    {
        $questionIds = $survey->questions->pluck('question_id')->toArray();

        $surveyAnsweredData = Arr::get($answerData, $survey->getKey(), []);
        $answeredQuestionIds = Arr::get($surveyAnsweredData, 'questions', []);

        /**
         * check xem đã trả lời câu trả lời có chèn khảo sát không?
         */
        $answeredIds = Arr::get($surveyAnsweredData, 'answers', []);
        if ($attachedSurveys = $survey->attachedSurveys) {
            foreach ($attachedSurveys as $attachedSurvey) {
                $mustAnswerAttachedSurveyIds = $attachedSurvey->choices->pluck('choice_id')->toArray();

                /**
                 * đã trả lời câu hỏi có chèn khảo sát
                 * thì phải trả lời các câu hỏi của khảo sát đã chèn đó mới tính là hoàn thành
                 */
                if (! empty(array_intersect($answeredIds, $mustAnswerAttachedSurveyIds))) {
                    $questionIds = array_merge($questionIds, $attachedSurvey->survey->questions->pluck('question_id')->toArray());
                    $surveyAnsweredData = Arr::get($answerData, $attachedSurvey->survey->getKey(), []);
                    $answeredQuestionIds = array_merge($answeredQuestionIds, Arr::get($surveyAnsweredData, 'questions', []));
                }
            }
        }

        $answeredQuestionIds = array_unique($answeredQuestionIds);
        $questionIds = array_unique($questionIds);

        return empty(array_diff($questionIds, $answeredQuestionIds));
    }
}
