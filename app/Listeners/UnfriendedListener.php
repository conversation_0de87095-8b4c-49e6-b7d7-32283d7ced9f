<?php

namespace App\Listeners;

use App\Events\Unfriended;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class UnfriendedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     */
    public function handle(Unfriended $event): void
    {
        $userID = $event->getUser()->getKey();
        $friendID = $event->getFriend()->getKey();

        DB::table('notifications')
            ->where([
                'user_id' => $userID,
                'type' => 17,
                'reaction_user_id' => $friendID,
            ])
            ->orWhere(function ($query) use ($userID, $friendID) {
                $query->where([
                    'user_id' => $friendID,
                    'type' => 17,
                    'reaction_user_id' => $userID,
                ]);
            })
            ->update([
                'active' => 0,
            ]);
    }
}
