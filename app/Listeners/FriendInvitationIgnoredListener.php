<?php

namespace App\Listeners;

use App\Events\FriendInvitationIgnored;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class FriendInvitationIgnoredListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     */
    public function handle(FriendInvitationIgnored $event): void
    {
        $userID = $event->getUser()->getKey();
        $friendID = $event->getFriend()->getKey();

        DB::table('notifications')
            ->where([
                'user_id' => $userID,
                'type' => 16,
                'reaction_user_id' => $friendID,
            ])
            ->update([
                'active' => 0,
            ]);
    }
}
