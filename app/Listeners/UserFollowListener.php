<?php

namespace App\Listeners;

use App\Actions\Reaction\CreateNewNotification;
use App\Events\UserFollow;
use App\Models\User;
use App\Models\UserRelation;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class UserFollowListener
{
    /**
     * Create the event listener.
     */
    public function __construct(protected CreateNewNotification $notificationCreator)
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserFollow $event): void
    {
        $user = $event->getUser();
        $userId = $user->getKey();
        $followId = $event->getUserId();

        $record = UserRelation::query()->firstOrNew([
            'user_id' => $userId,
            'target_id' => $followId,
        ]);

        $payload = [
            'followed_at' => now('UTC')->toDateTimeString(),
            'is_follow' => $record->exists ? (int) !$record->is_follow : 1,
        ];

        $record->fill($payload);
        $record->save();

        $following = $record->target;

        /**
         * follow lần đầu (những lần sau update status - không xóa)
         */
        $record->isFollow() ? $this->createNotification($following, $userId) : $this->removeNotification([
            'user_id' => $followId,
            'type' => 8,
            'reaction_user_id' => $userId,
        ]);
    }

    /**
     * @param array $payload
     * @return void
     */
    protected function removeNotification(array $payload)
    {
        $userId = Arr::get($payload, 'user_id');
        unset($payload['user_id']);

        DB::table('notifications')
            ->where('user_id', $userId)
            ->where($payload)
            ->update([
                'active' => 0,
            ]);
    }

    /**
     * @param User $user
     * @param int $reactionUserId
     * @return void
     */
    protected function createNotification(User $user, int $reactionUserId)
    {
        $this->notificationCreator->execute($user, [
            'type' => 8,
            'reaction_user_id' => $reactionUserId,
        ]);
    }
}
