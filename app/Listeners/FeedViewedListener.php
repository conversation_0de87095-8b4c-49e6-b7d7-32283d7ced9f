<?php

namespace App\Listeners;

use App\Events\FeedViewed;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class FeedViewedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the queued listener may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Handle the event.
     * @throws BindingResolutionException
     */
    public function handle(FeedViewed $event): void
    {
        $user = $event->getViewedUser();
        $feed = $event->getFeed();

        feedViewedRepository()->create([
            'user_id' => $user->getKey(),
            'viewed_at' => now('UTC'),
            'feed' => $feed,
        ]);
    }
}
