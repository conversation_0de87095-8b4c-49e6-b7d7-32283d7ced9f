<?php

namespace App\Enums;

enum QuestionPublicTarget: string
{
    case NONE = 'none';

    case PLATFORM = 'platform';

    case APPLICATION = 'app';

    case PLATFORM_APPLICATION = 'platform_app';

    /**
     * @return array
     */
    public static function toArray(): array
    {
        return [
            self::NONE->value,
            self::PLATFORM->value,
            self::APPLICATION->value,
            self::PLATFORM_APPLICATION->value,
        ];
    }

    /**
     * @return string
     */
    public function toLabel()
    {
        return __('public.' . $this->value);
    }
}
