<?php

namespace App\Enums;

enum QuestionType: string
{
    case CHECK_BOX = 'checkbox';

    case SELECT_BOX = 'select';

    case TEXT = 'text';

    case TEXT_WITH_STAR = 'text_with_star';

    /**
     * @return array
     */
    public static function toArray(): array
    {
        return [
            self::CHECK_BOX->value,
            self::SELECT_BOX->value,
            self::TEXT->value,
            self::TEXT_WITH_STAR->value,
        ];
    }

    /**
     * @return string
     */
    public function toLabel()
    {
        return __($this->value);
    }
}
