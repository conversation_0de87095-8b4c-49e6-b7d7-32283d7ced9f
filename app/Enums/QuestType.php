<?php

namespace App\Enums;

enum QuestType: string
{
    case ANSWERED = 'answered';

    case INVITED = 'invited';

    case HOBBY = 'hobby';

    case CAREER = 'career';

    case GENDER = 'gender';

    /**
     * @return array
     */
    public static function toArray(): array
    {
        return [
            self::ANSWERED->value,
            self::INVITED->value,
            self::HOBBY->value,
            self::CAREER->value,
            self::GENDER->value,
        ];
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return __('questType.' . $this->value);
    }

    /**
     * @return array
     */
    public static function typeMapping()
    {
        $values = self::toArray();

        $mappings = [];
        foreach ($values as $type) {
            $mappings[] = [
                'label' => __('quest.type.' . $type),
                'value' => $type,
            ];
        }

        return $mappings;
    }
}
