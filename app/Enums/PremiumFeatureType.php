<?php

namespace App\Enums;

enum PremiumFeatureType: string
{
    case GENDER = 'gender';

    case PIN_POST_ANSWER = 'pin_answer';

    /**
     * @return array
     */
    public static function toArray(): array
    {
        return [
            self::GENDER->value,
            self::PIN_POST_ANSWER->value,
        ];
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return __('premiumFeature.' . $this->value);
    }

    /**
     * @return array
     */
    public static function typeMapping()
    {
        $values = self::toArray();

        $mappings = [];
        foreach ($values as $type) {
            $mappings[] = [
                'label' => __('premiumFeature.' . $type),
                'value' => $type,
            ];
        }

        return $mappings;
    }
}
