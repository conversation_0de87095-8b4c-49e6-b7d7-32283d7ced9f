<?php

namespace App\Enums;

enum PostType: string
{
    case DEFAULT = 'default';

    case SMILE = 'smile';

    case NOT_SMILE = 'not_smile';

    case LAUGH = 'laugh';

    case RAISE_HAND = 'raise_hand';

    case WITH_TAG = 'with_tag';

    case MULTIPLE = 'multiple';

    case GENDER = 'gender';

    case ANSWERR_Q = 'answerr_q';

    case ANSWERR_TOPIC = 'answerr_topic';

    /**
     * @return array
     */
    public static function toArray(): array
    {
        return [
            self::DEFAULT->value,
            self::SMILE->value,
            self::NOT_SMILE->value,
            self::LAUGH->value,
            self::RAISE_HAND->value,
            self::WITH_TAG->value,
            self::MULTIPLE->value,
            self::GENDER->value,
            self::ANSWERR_Q->value,
            self::ANSWERR_TOPIC->value,
        ];
    }

    /**
     * @return array
     */
    public static function apiValidationRuleArray(): array
    {
        return [
            self::DEFAULT->value,
            self::SMILE->value,
            self::NOT_SMILE->value,
            self::LAUGH->value,
            self::RAISE_HAND->value,
            self::WITH_TAG->value,
            self::MULTIPLE->value,
            self::GENDER->value,
        ];
    }

    /**
     * @return array 
     */
    public static function consoleValidationRuleArray(): array
    {
        return [
            self::ANSWERR_Q->value,
            self::ANSWERR_TOPIC->value,
        ];
    }

    /**
     * @return string
     */
    public function toLabel()
    {
        return __('postType.' . $this->value);
    }
}
