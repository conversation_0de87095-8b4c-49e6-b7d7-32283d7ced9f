<?php

namespace App\Enums;

enum Gender: string
{
    case MALE = 'male';

    case FEMALE = 'female';

    case OTHER = 'other';

    /**
     * @return array
     */
    public static function toArray(): array
    {
        return [
            self::MALE->value,
            self::FEMALE->value,
            self::OTHER->value,
        ];
    }

    /**
     * @return int
     */
    public function toInt(): int
    {
        return match ($this->value) {
            self::MALE->value => 1,
            self::FEMALE->value => 2,
            self::OTHER->value => 3,
            default => 0,
        };
    }
}
