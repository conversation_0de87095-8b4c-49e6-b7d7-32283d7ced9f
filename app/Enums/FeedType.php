<?php

namespace App\Enums;

enum FeedType: string
{
    case GENERAL = 'general';

    case FRIEND = 'friend';

    case LATEST = 'latest';

    case RECOMMENDED_ANSWER = 'recommended_answer';

    case OWNER = 'owner';

    case TOPICS = 'topics';

    case LAUGH = 'laugh';

    case RAISE_HAND = 'raise_hand';

    case GENDER = 'gender';

    /**
     * @return array
     */
    public static function toArray(): array
    {
        return [
            self::TOPICS->value,
            self::RECOMMENDED_ANSWER->value,
            self::LAUGH->value,
            self::RAISE_HAND->value,
            self::GENERAL->value,
            self::LATEST->value,
            self::OWNER->value,
            self::FRIEND->value,
            self::GENDER->value,
        ];
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return __('feedType.' . $this->value);
    }
}
