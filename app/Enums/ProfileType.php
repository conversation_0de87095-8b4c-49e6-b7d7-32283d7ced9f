<?php

namespace App\Enums;

enum ProfileType: string
{
    case STUDENT = 'student';

    case EMPLOYEE = 'employee';

    case OTHER = 'other';

    /**
     * @return array
     */
    public static function toArray(): array
    {
        return [
            self::STUDENT->value,
            self::EMPLOYEE->value,
            self::OTHER->value,
        ];
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return __('profileType.' . $this->value);
    }
}
