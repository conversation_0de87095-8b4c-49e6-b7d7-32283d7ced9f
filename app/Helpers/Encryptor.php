<?php

namespace App\Helpers;

class Encryptor
{
    /**
     * @var string|null
     */
    protected $encryptPassword;

    /**
     * @var \OpenSSLAsymmetricKey|null
     */
    protected $privateKeyResource;

    /**
     * @return string
     */
    protected function getEncryptPassword()
    {
        if (! $this->encryptPassword) {
            $this->encryptPassword = config('app.encrypt_password');
        }

        return $this->encryptPassword;
    }

    /**
     * @return \OpenSSLAsymmetricKey|null
     */
    protected function getPrivateKeyResource()
    {
        if (! $this->privateKeyResource) {
            $encryptPassword = $this->getEncryptPassword();

            $privateKey = file_get_contents(storage_path('keys/private.pem'));
            $privateKeyResource = openssl_pkey_get_private($privateKey, $encryptPassword);
            if (! $privateKeyResource) {
                return null;
            }

            $this->privateKeyResource = $privateKeyResource;
        }

        return $this->privateKeyResource;
    }

    /**
     * @param string $encryptedData
     * @param string $encryptedAesKey
     * @param string $encryptedIv
     * @return string|null
     */
    public function decode(string $encryptedData, string $encryptedAesKey, string $encryptedIv)
    {
        try {
            if (! $privateKeyResource = $this->getPrivateKeyResource()) {
                return null;
            }

            $encryptedData = base64_decode($encryptedData);
            $encryptedAesKey = base64_decode($encryptedAesKey); // Khóa AES đã mã hóa (Base64)
            $aesIv = base64_decode($encryptedIv); // IV (Base64)

            openssl_private_decrypt($encryptedAesKey, $decryptedAesKey, $privateKeyResource);

            return openssl_decrypt($encryptedData, 'aes-256-cbc', $decryptedAesKey, OPENSSL_RAW_DATA, $aesIv);
        } catch (\Throwable $e) {
            chatWork()->report([
                'message' => 'Decrypt Data Error: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'errors' => [],
            ]);
        }

        return null;
    }

    /**
     * @param string $text
     * @return string[]
     */
    public function encode(string $text)
    {
        // Tạo khóa AES ngẫu nhiên (256-bit)
        $aesKey = openssl_random_pseudo_bytes(32); // 32 bytes = 256 bits

        // Mã hóa khóa AES bằng RSA (sử dụng public key)
        $publicKey = file_get_contents(storage_path('keys/public.pem'));
        openssl_public_encrypt($aesKey, $encryptedAesKey, $publicKey);

        // Mã hóa dữ liệu bằng AES
        $aesIv = openssl_random_pseudo_bytes(16); // AES cần một IV (16 bytes cho AES-256-CBC)
        $encryptedData = openssl_encrypt($text, 'aes-256-cbc', $aesKey, OPENSSL_RAW_DATA, $aesIv);

        return [base64_encode($encryptedData), base64_encode($encryptedAesKey), base64_encode($aesIv)];
    }
}
