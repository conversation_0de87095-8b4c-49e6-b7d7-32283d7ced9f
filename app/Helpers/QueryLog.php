<?php

namespace App\Helpers;

use Illuminate\Support\Arr;

class QueryLog
{
    /**
     * @var array
     */
    protected array $items = [];

    /**
     * @return string
     */
    protected function getRequestId(): string
    {
        return request()->attributes->get('request_id', '');
    }

    /**
     * @param array $query
     * @return void
     */
    public function collect(array $query): void
    {
        $requestId = $this->getRequestId();
        if (! isset($this->userIds[$requestId])) {
            $this->items[$requestId] = [];
        }

        $this->items[$requestId][] = $query;
    }

    /**
     * @return array
     */
    public function items(): array
    {
        $requestId = $this->getRequestId();

        return Arr::get($this->items, $requestId, []);
    }

    /**
     * @return void
     */
    public function clearData(): void
    {
        $requestId = $this->getRequestId();

        unset($this->items[$requestId]);
    }
}
