<?php

namespace App\Helpers;

use Carbon\Carbon;

/**
 * Class DateTimeHelper
 * @package App\Helpers
 */
class DateTimeHelper
{
    /**
     * @param $birthday
     * @return int
     */
    public function getAgeFromDate($birthday)
    {
        $birthday = Carbon::parse($birthday);

        $now = now('UTC');
        $diff = $now->diff($birthday);

        return $diff->y + (($now->month > $birthday->month) || ($now->month === $birthday->month && $now->day >= $birthday->day) ? 1 : 0);
    }
}
