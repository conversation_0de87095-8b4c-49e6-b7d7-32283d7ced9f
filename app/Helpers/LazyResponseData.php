<?php

namespace App\Helpers;

use App\Models\Post;
use App\Models\User;
use Illuminate\Support\Arr;

class LazyResponseData
{
    /**
     * @var array
     */
    protected array $users = [];

    /**
     * @var array
     */
    protected array $userIds = [];

    /**
     * @var array
     */
    protected array $posts = [];

    /**
     * @var array
     */
    protected array $postIds = [];

    /**
     * @return string
     */
    protected function getRequestId(): string
    {
        return request()->attributes->get('request_id', '');
    }

    /**
     * @param User $user
     * @return void
     */
    public function addUser(User $user): void
    {
        $requestId = $this->getRequestId();

        $this->users[$requestId] ??= [];
        $this->users[$requestId][] = $user;
    }

    /**
     * @param User[] $users
     * @return void
     */
    public function addUsers(array $users)
    {
        $requestId = $this->getRequestId();

        $this->users[$requestId] ??= [];
        $this->users[$requestId] = array_merge($this->getUsers($requestId), $users);
    }

    /**
     * @param int $userId
     * @return void
     */
    public function addUserID(int $userId): void
    {
        $requestId = $this->getRequestId();

        $this->userIds[$requestId] ??= [];
        $this->userIds[$requestId][] = $userId;
    }

    /**
     * @param array $userIds
     * @return void
     */
    public function addUserIDs(array $userIds)
    {
        $requestId = $this->getRequestId();

        $this->userIds[$requestId] ??= [];
        $this->userIds[$requestId] = array_merge($this->getUserIds($requestId), $userIds);
    }

    /**
     * @param string|null $requestId
     * @return array
     */
    public function getUsers(?string $requestId = null)
    {
        $requestId ??= $this->getRequestId();

        return collect(Arr::get($this->users, $requestId, []))
            ->unique('user_id')
            ->whenNotEmpty(fn ($collection) => $collection->all(), fn () => []);
    }

    /**
     * @return int[]
     */
    public function getUserIds()
    {
        $requestId = $this->getRequestId();

        return array_unique(Arr::get($this->userIds, $requestId, []));
    }

    /**
     * @param Post $post
     * @return void
     */
    public function addPost(Post $post): void
    {
        $requestId = $this->getRequestId();

        $this->posts[$requestId] ??= [];
        $this->posts[$requestId][] = $post;
    }

    /**
     * @param Post[] $posts
     * @return void
     */
    public function addPosts(array $posts)
    {
        $requestId = $this->getRequestId();

        $this->posts[$requestId] ??= [];
        $this->posts[$requestId] = array_merge($this->getPosts($requestId), $posts);
    }

    /**
     * @param int $postId
     * @return void
     */
    public function addPostID(int $postId): void
    {
        $requestId = $this->getRequestId();

        $this->postIds[$requestId] ??= [];
        $this->postIds[$requestId][] = $postId;
    }

    /**
     * @return Post[]|array
     */
    public function getPosts(?string $requestId = null)
    {
        $requestId ??= $this->getRequestId();

        $posts = Arr::get($this->posts, $requestId, []);

        return collect($posts)
            ->unique('post_id')
            ->whenNotEmpty(fn ($collection) => $collection->all(), fn () => []);
    }

    /**
     * @return int[]
     */
    public function getPostIds()
    {
        $requestId = $this->getRequestId();

        return array_unique(Arr::get($this->postIds, $requestId, []));
    }

    /**
     * @return void
     */
    public function flush(): void
    {
        $requestId = $this->getRequestId();

        unset($this->users[$requestId]);
        unset($this->userIds[$requestId]);
        unset($this->posts[$requestId]);
        unset($this->postIds[$requestId]);
    }
}
