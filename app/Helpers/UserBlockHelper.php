<?php

namespace App\Helpers;

use App\Repositories\UserRepository;

class UserBlockHelper
{
    /**
     * @var string
     */
    protected string $cacheKey = 'blocked_users::';

    /**
     * @var string
     */
    protected string $userColumn = 'user_id';

    /**
     * @var string
     */
    protected string $blockedColumn = 'target_id';

    /**
     * @param UserRepository $repository
     */
    public function __construct(protected UserRepository $repository)
    {

    }

    /**
     * @param int $userId
     * @param int $blockedId
     * @return void
     */
    public function clearCache(int $userId, int $blockedId): void
    {
        $this->repository->forget($this->cacheKey . $userId . '_' . $this->userColumn);
        $this->repository->forget($this->cacheKey . $blockedId . '_' . $this->blockedColumn);
    }

    /**
     * @param int $userId
     * @param string $column
     * @return array
     */
    protected function _getBLockUserIDs(int $userId, string $column): array
    {
        $cacheKey = $this->cacheKey . $userId . '_' . $column;

        return $this->repository->rememberForever($cacheKey, fn () => $this->repository->getBlockedUserIDs($userId, $column));
    }

    /**
     * @param int $userId
     * @return array
     */
    public function getBlockedUserIDs(int $userId): array
    {
        return $this->_getBLockUserIDs($userId, $this->userColumn);
    }

    /**
     * @param int $userId
     * @return array
     */
    public function getBlockedByIDs(int $userId): array
    {
        return $this->_getBLockUserIDs($userId, $this->blockedColumn);
    }

    /**
     * @param int $userId
     * @return array
     */
    public function getBlockedIds(int $userId): array
    {
        return array_merge($this->getBlockedUserIDs($userId), $this->getBlockedByIDs($userId));
    }
}
