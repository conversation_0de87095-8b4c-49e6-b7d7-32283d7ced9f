<?php

namespace App\Helpers;

use App\Models\User;

/**
 * Class OneTimePassword
 * @package App\Helpers
 */
class OneTimePassword
{
    /**
     * @param int $length
     * @return string
     */
    public function generate(int $length = 6)
    {
        $otp = '';
        for ($i = 0; $i < $length; $i++) {
            $otp .= rand(0, 9);
        }

        return $otp;
    }

    /**
     * @param string $phone
     * @param string $otp
     * @return true
     */
    public function send(string $phone, string $otp)
    {
        // TODO: sent OTP implementation

        return true;
    }

    /**
     * @param User $user
     * @param string $otp
     * @return bool
     */
    public function isValid(User $user, string $otp)
    {
        // TODO: verify OTP implementation

        return true;
    }
}
