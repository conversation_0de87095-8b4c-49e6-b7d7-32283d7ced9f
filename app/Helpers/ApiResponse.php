<?php

namespace App\Helpers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

/**
 * Class ApiResponse
 * @package App\Helpers
 */
class ApiResponse
{
    /**
     * @param array $payload
     * @return array
     */
    protected function response(array $payload = []): array
    {
        /** trang thai bat user phai update app hay khong */
        $updateAppStatus = 0;

        /** @var Request $request */
        $request = request();

        $appver = $request->get('appver', 1);
        if (version_compare($appver, '1.1') < 0) {
            $updateAppStatus = 2;
        }

        $errorCode = (int) Arr::get($payload, 'error_code', 0);
        $errorMessage = (string) Arr::get($payload, 'error_message', '');

        /** @var User $user */
        $user = $request->user();

        $response = [
            'error_code' => $errorCode,
            'error_message' => $errorMessage,
            'errors' => null,
            'data' => Arr::get($payload, 'data'),
            'update_app' => $updateAppStatus,
            'news_badge_count' => (int) $user?->news_badge_count,
        ];

        if ($errors = Arr::get($payload, 'errors')) {
            $response['data'] = null;
            $response['errors'] = $errors;
        }

        return $response;
    }

    /**
     * @param array|null $data
     * @return array
     */
    public function success(?array $data = null): array
    {
        return $this->response([
            'data' => $data,
        ]);
    }

    /**
     * @param int $errorCode
     * @param string|null $errorMessage
     * @param array|null $data
     * @param array|null $errors
     * @return array
     */
    public function error(int $errorCode, ?string $errorMessage = null, ?array $data = null, ?array $errors = null): array
    {
        return $this->response([
            'error_code' => $errorCode,
            'error_message' => $errorMessage,
            'data' => $data,
            'errors' => $errors,
        ]);
    }
}
