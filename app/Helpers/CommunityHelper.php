<?php

namespace App\Helpers;

use App\Models\User;
use Illuminate\Support\Facades\Cache;

/**
 * Class CommunityHelper
 * @package App\Helpers
 */
class CommunityHelper
{
    /**
     * @param User $user
     * @return array
     */
    public static function getClosedCommunityIds(User $user)
    {
        return Cache::rememberForever($user->getKey() . '_closed_community_ids', function () use ($user) {
            $joinedCommunities = $user->getJoinedCommunities(true);

            return $joinedCommunities->pluck('community_id')->toArray();
        });
    }

    /**
     * @param int $userId
     * @return void
     */
    public static function clearClosedCommunityIds(int $userId)
    {
        Cache::forget($userId . '_closed_community_ids');
    }
}
