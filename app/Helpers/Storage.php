<?php

namespace App\Helpers;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage as StorageFacade;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Throwable;

/**
 * @class Storage
 */
class Storage
{
    /**
     * @return string
     */
    protected function generateImageFileName()
    {
        return Str::uuid() . '.webp';
    }

    /**
     * @param string|null $path
     * @return string|null
     */
    public function url(?string $path)
    {
        if (! $path) {
            return null;
        }

        if (str_starts_with($path, 'http')) {
            return $path;
        }

        return StorageFacade::disk(config('filesystems.default'))->url($path);
    }

    /**
     * @param UploadedFile $file
     * @param bool $resize
     * @param integer $resizeToWidth
     * @param bool $autoClearTmpFile
     * @return string|null
     */
    public function uploadImage(UploadedFile $file, bool $resize = false, int $resizeToWidth = 1024, bool $autoClearTmpFile = false)
    {
        try {
            $filePath = $file->getRealPath();
            $uploadPath = date('/Y/m/d/');

            /**
             * convert image to webp
             */
            $image = ImageManager::imagick()->read($filePath);

            /**
             * resize to max width
             */
            if ($resize && ($image->width() > $resizeToWidth)){
                $image->scale($resizeToWidth);
            }

            $fileName = $this->generateImageFileName();

            /**
             * check to mkdir
             */
            $destinationPath = storage_path('app/public/' . ltrim($uploadPath, '/'));
            if (! file_exists($destinationPath)) {
                mkdir($destinationPath, 0755, true);
            }

            $tmpFile = $destinationPath . $fileName;
            $image->toWebp(80)->save($tmpFile);

            if (config('filesystems.default') === 's3') {
                $result = StorageFacade::disk('s3')->putFileAs($uploadPath, $tmpFile, $fileName);
                if ($autoClearTmpFile && false !== $result) {
                    @unlink($tmpFile);
                }
            }

            return $uploadPath . $fileName;
        } catch (Throwable $e) {
            // do nothing
            chatWork()->report([
                'message' => 'Upload Image Error: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'errors' => [],
            ]);
        }

        return null;
    }

    /**
     * @param string|string[] $path
     * @return void
     */
    public function removeMedia(array|string $path)
    {
        StorageFacade::disk(config('filesystems.default'))->delete($path);
    }

    /**
     * @param mixed $filename
     * @param int $num
     * @return int|float
     */
    public function getAvgLuminance($filename, $num = 30)
    {
        // needs a mimetype check
        $extension = explode('.', $filename);
        $extension = end($extension);

        $img = match ($extension) {
            'webp' => imagecreatefromwebp($filename),
            'png' => imagecreatefrompng($filename),
            default => imagecreatefromjpeg($filename),
        };

        $width = imagesx($img);
        $height = imagesy($img);

        $x_step = intval($width / $num);
        $y_step = intval($height / $num);

        $total_lum = 0;
        $num = 1;

        for ($x = 0; $x < $width; $x += $x_step) {
            for ($y = 0; $y < $height; $y += $y_step) {

                $rgb = imagecolorat($img, $x, $y);
                $r = ($rgb >> 16) & 0xFF;
                $g = ($rgb >> 8) & 0xFF;
                $b = $rgb & 0xFF;

                // choose a simple luminance formula from here
                // http://stackoverflow.com/questions/596216/formula-to-determine-brightness-of-rgb-color
                $lum = ($r + $r + $b + $g + $g + $g) / 6;

                $total_lum += $lum;
                $num++;
            }
        }

        // work out the average
        $avg_lum = $total_lum / $num;

        return ($avg_lum / 255) * 100;
    }
}
