<?php

namespace App\Helpers;

use App\Enums\QuestType;
use App\Models\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

/**
 * Class QuestHelper
 * @package App\Helpers
 */
class QuestHelper
{
    /**
     * @param User $user
     * @param string $type
     * @param null|int $amount
     * @param null|int $referrerID
     * @return void
     */
    public function applyQuestReward(User $user, string $type, ?int $amount = null, ?int $referrerID = null)
    {
        $quest = $this->getQuestByType($type);
        if (! $quest) {
            return;
        }

        $amount = $amount ?: (int) Arr::get($quest, 'amount');
        if (! $amount) {
            return;
        }

        /**
         * apply reward
         * increment total_point or total_coin
         */
        $user->incrementOrDecrementCount('total_coin', 'increment', $amount);

        /**
         * user mời + 1 coint & increment invited_user_count
         */
        if ($referrerID) {
            $this->syncReferrerData($referrerID, $amount);
        }
    }

    /**
     * @param int $userId
     * @param int $amount
     * @return void
     */
    protected function syncReferrerData(int $userId, int $amount)
    {
        DB::statement("UPDATE users SET `total_coin` = `total_coin` + ?, `invited_user_count` = `invited_user_count` + 1 WHERE `user_id` = ?", [$amount, $userId]);
    }

    /**
     * @param string $type
     * @return array|null
     */
    public function getQuestByType(string $type)
    {
        $activeQuests = $this->getActiveQuests();

        return Arr::first($activeQuests, function ($quest) use ($type) {
            return Arr::get($quest, 'type') === $type;
        });
    }

    /**
     * Get quest reward amount by type
     * @param string $type
     * @return int
     */
    public function getQuestRewardAmount(string $type): int
    {
        $quest = $this->getQuestByType($type);
        if (! $quest) {
            return 0;
        }

        return (int) Arr::get($quest, 'amount', 0);
    }

    /**
     * @return array
     */
    protected function getActiveQuests()
    {
        return questRepository()->getActiveQuests();
    }
}
