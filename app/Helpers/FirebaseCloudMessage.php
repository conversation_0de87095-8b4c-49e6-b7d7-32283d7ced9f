<?php

namespace App\Helpers;

use App\Http\Resources\Api\NotificationResource;
use App\Models\Notification;
use Illuminate\Support\Facades\DB;
use Kreait\Firebase\Contract\Messaging;
use Kreait\Firebase\Messaging\CloudMessage;
use Throwable;

class FirebaseCloudMessage
{
    /**
     * @var Messaging
     */
    protected $messaging;

    /**
     * constructor
     */
    public function __construct()
    {
        $this->messaging = app('firebase.messaging');
    }

    /**
     * @param int $id
     * @return void
     */
    protected function incrementUserBadge(int $id)
    {
        DB::table('users')
            ->where('user_id', $id)
            ->increment('badge_count', 1, [
                'updated_at' => now('UTC')->toDateTimeString(),
            ]);
    }

    /**
     * @param Notification $notification
     * @return void
     */
    public function send(Notification $notification)
    {
        if ($tokens = $notification->user->getDeviceTokens()) {
            try {
                $notification->load([
                    'object',
                    'reaction',
                    'reactionUser',
                    'users' => fn ($q) => $q->limit(3)->orderByDesc('updated_at'),
                    'users.user',
                ]);

                $notification->loadCount([
                    'users as user_count',
                ]);

                if ($notification->object_type === 'post_answer') {
                    $answer = $notification->object;
                    $answer->load([
                        'parent',
                    ]);
                }

                $message = NotificationResource::make($notification)->toArray(request(), false);
                $message['notification']['title'] = 'answerr';

                $this->messaging->sendMulticast(CloudMessage::fromArray($message), $tokens);

                $this->incrementUserBadge($notification->user_id);

                if (config('app.debug')) {
                    chatWork()->report([
                        'data' => $notification->toArray(),
                        'message' => 'Push notification info',
                    ]);
                }
            } catch (Throwable $e) {
                chatWork()->report([
                    'data' => $notification->toArray(),
                    'message' => $e->getMessage() ?: 'Push notification error',
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }
    }
}
