<?php

namespace App\Actions\Post;

use App\Http\Requests\Api\Post\MutePostAnswerNotificationRequest;
use App\Models\Reaction;
use Illuminate\Support\Facades\DB;

class MutePostAnswerNotification
{
    /**
     * @param MutePostAnswerNotificationRequest $request
     * @return void
     */
    public function execute(MutePostAnswerNotificationRequest $request)
    {
        DB::transaction(function () use ($request) {
            $postAnswer = $request->getPostAnswer();

            /**
             * create new reaction
             */
            $attributes = [
                'object_id' => $postAnswer->getKey(),
                'object_type' => 'post_answer',
                'user_id' => $request->user()->getKey(),
                'action' => 'muted',
            ];

            $values = [
                'count' => 1,
                'object_user_id' => $postAnswer->user_id,
                'react_date' => now('UTC')->toDateString(),
            ];

            $reaction = Reaction::query()->firstOrCreate($attributes, $values);
            if (! $reaction->wasRecentlyCreated) {
                $reaction->delete();
            }
        });
    }
}
