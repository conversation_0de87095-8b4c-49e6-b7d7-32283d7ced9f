<?php

namespace App\Actions\Post;

use App\Http\Requests\Web\StorePostRequest;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Throwable;

class StoreAdminPost
{
    /**
     * @param StorePostRequest $request
     * @return void
     */
    public function execute(StorePostRequest $request)
    {
        return DB::transaction(function () use ($request) {
            $type = $request->post('type');

            $content = $request->post('content');

            $attrs = [
                'content' => $content,
                'type' => $type,
            ];

            $media = [
                'content' => $content,
            ];

            $imagePath = null;
            if ($image = $request->file('image')) {
                $storage = storage();
                $imagePath = $storage->uploadImage($image, true, 1320);
                if (! $imagePath) {
                    $request->setError('cover.0', __('An error occurred while uploading the image.'));
                }

                try {
                    $tmpFile = storage_path('app/public/' . ltrim($imagePath, '/'));
                    $avgLuminance = $storage->getAvgLuminance($tmpFile);
                    $isDark = (int) ($avgLuminance < 50);

                    if (config('filesystems.default') === 's3') {
                        @unlink($tmpFile);
                    }
                } catch (Throwable $e) {
                    $isDark = 0;
                }

                $media['is_dark'] = $isDark;
            }

            /**
             * default image
             */
            if (! $imagePath) {
                $key = array_rand(StorePost::$defaultImages);
                $imagePath = StorePost::$defaultImages[$key];
                $media['is_dark'] = 0;
            }

            $attrs['image'] = $imagePath;
            $media['image'] = $imagePath;

            /** @var User $user */
            $user = $request->user();
            $userId = $user->getKey();

            $post = $request->getPostInstance();

            /**
             * post chưa tồn tại
             */
            if (! $post->exists) {
                /**
                 * thêm thời gian để sort posts trong community
                 */
                $attrs['sort_answered_at'] = now('UTC')->toDateTimeString();
                $attrs['user_id'] = $userId;
            }

            /**
             * khi update post
             */
            else {
                $post->media()->delete();
                $post->unsetRelation('media');
            }

            /**
             * save post
             */
            $post->fill($attrs);
            $post->save();

            /**
             * sync post in community
             */
            if ($post->wasRecentlyCreated) {
                $community = $request->getCommunity();
                $community->posts()->create([
                    'post_id' => $post->getKey(),
                    'community_status' => $community->status,
                ]);
            }

            /**
             * sync post media
             */
            $media['user_id'] = $userId;
            $post->media()->create($media);
        });
    }
}
