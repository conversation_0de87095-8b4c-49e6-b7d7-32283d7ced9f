<?php

namespace App\Actions\Post;

use App\Events\PostAnswerDeleted;
use App\Models\PostAnswer;

class DeletePostAnswer
{
    /**
     * @param PostAnswer|null $answer
     * @return void
     * @noinspection PhpUndefinedMethodInspection
     */
    public function execute(?PostAnswer $answer)
    {
        when($answer, function (PostAnswer $answer) {
            $answer->markAsDeleted();

            /**
             * sync count
             */
            if ($answer->createdBy->isEnabled()) {
                optional($answer->parent)->updateChildrenCount('decrement');

                if (! $answer->parent_id) {
                    $answer->post->decrementCount('answer_count');
                }
            }

            /**
             * remove answer reactions
             */
            PostAnswerDeleted::dispatch($answer);
        });
    }
}
