<?php

namespace App\Actions\Post;

use App\Enums\PostType;
use App\Events\PostStored;
use App\Events\PostUserRecommendation;
use App\Http\Requests\Api\Post\StorePostRequest;
use App\Models\Post;
use App\Models\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Throwable;

class StorePost
{
    /**
     * @var array
     */
     public static $defaultImages = [
        'https://cdn.answerr.app/assets/images/image_1.png',
        'https://cdn.answerr.app/assets/images/image_2.png',
        'https://cdn.answerr.app/assets/images/image_3.png',
    ];

    /**
     * @param StorePostRequest $request
     * @return Post
     */
    public function execute(StorePostRequest $request)
    {
        return DB::transaction(function () use ($request) {
            $type = $request->post('type');

            $media = [];
            $content = $request->post('content');
            if ($items = $request->post('media')) {
                $media = $this->parseData($items);
                $content = implode("\n", array_filter(Arr::pluck($media, 'content')));
            }

            /** @var User $user */
            $user = $request->user();
            $userId = $user->getKey();

            $post = $request->getPost();

            /**
             * nếu tạo mới trong cộng đồng
             * thì post type sẽ là default, và không thông báo push
             */
            $community = $request->getCommunity();

            /**
             * nếu post trong answer_topic thì kiểm tra user đã là member của cộng đồng chưa,
             * nếu chưa thì tự động join vào cộng đồng
             */
            $parentPost = $request->getParentPost();
            if ($parentPost && $community) {
                $record = $community->members()->firstOrCreate([
                    'community_id' => $community->getKey(),
                    'user_id' => $userId,
                ], ['status' => 1]);

                if (! $record->wasRecentlyCreated) {
                    $record->fill([
                        'status' => 1,
                    ]);
                    $record->save();
                }
            }

            if (! $post->exists && ! is_null($community)) {
                $type = PostType::DEFAULT->value;

                /**
                 * thêm thời gian để sort posts trong community
                 */
                $post->fill([
                    'sort_answered_at' => now('UTC')->toDateTimeString(),
                ]);
            }

            /**
             * reward coin
             */
            $firstRewardAmount = $post->getRewardAmount();
            $afterRewardAmount = (int) $request->post('reward_amount');

            /**
             * update reward_amount
             */
            $diffAmount = $afterRewardAmount - $firstRewardAmount;

            /**
             * > 0 - tăng reward_amount
             * < 0 - giảm reward_amount
             */
            if ($diffAmount > 0 || $diffAmount < 0) {
                $rewardType = $diffAmount > 0 ? 'increment' : 'decrement';
                $user->incrementOrDecrementCount('holding_coin', $rewardType, $diffAmount);
            }

            /**
             * default image
             */
            if (! ($postImage = (string) $post->image)) {
                $key = array_rand(self::$defaultImages);
                $postImage = self::$defaultImages[$key];
            }

            /**
             * save post
             */
            $post->fill([
                'user_id' => $userId,
                'type' => $type,
                'content' => $content,
                'image' => $postImage,
                'reward_amount' => $afterRewardAmount,
            ]);

            $post->save();
            $postId = $post->getKey();

            /**
             * sync media
             */
            $updatedCover = false;
            $oldMedia = $post->media;
            $keepMedia = [];
            foreach ($media as $key => $data) {
                $oldMediaItem = null;
                $mediaId = Arr::get($data, 'id');
                if ($mediaId) {
                    $oldMediaItem = $oldMedia->firstWhere('media_id', $mediaId);
                }

                $coverPath = null;
                $coverKey = 'media.' . $key . '.cover';
                $cover = $request->file($coverKey);
                if ($cover) {
                    $storage = storage();
                    $coverPath = $storage->uploadImage($cover, true, 1320);
                    if (! $coverPath) {
                        $request->setError($coverKey, __('An error occurred while uploading the image.'));
                    }

                    try {
                        $tmpFile = storage_path('app/public/' . ltrim($coverPath, '/'));
                        $avgLuminance = $storage->getAvgLuminance($tmpFile);
                        $data['is_dark'] = (int) ($avgLuminance < 50);

                        if (config('filesystems.default') === 's3') {
                            @unlink($tmpFile);
                        }
                    } catch (Throwable $e) {
                        // do nothing
                    }
                }

                $data['image'] = $coverPath ?: $oldMediaItem?->image;
                $keepMedia[] = $data['image'];

                /**
                 * make first image as post cover
                 */
                if (! $updatedCover) {
                    $post->updateQuietly([
                        'image' => $data['image'],
                    ]);

                    $updatedCover = true;
                }

                $data['post_id'] = $postId;
                $data['user_id'] = $userId;
                unset($data['id']);

                $media[$key] = Arr::only($data, [
                    'post_id',
                    'user_id',
                    'content',
                    'image',
                    'is_dark',
                ]);
            }

            /**
             * new post
             */
            if ($post->wasRecentlyCreated) {
                $user->incrementOrDecrementCount();

                /**
                 * check post in a community
                 */
                $community?->posts()->create([
                    'post_id' => $postId,
                    'community_status' => $community->status,
                ]);

                /**
                 * nếu post trong answerr_topic thì update thời gian unlock của user
                 */
                if ($parentPost) {
                    userRepository()->updateUnlockTime($user, $parentPost->getKey());
                }
            }

            /**
             * update post (remove old media)
             */
            else {
                $oldImages = $post->media->pluck('image')->toArray();
                $post->media()->delete();
                $post->unsetRelation('media');
                $this->deleteImages(array_diff($oldImages, $keepMedia));
            }

            $post->media()->insert($media);

            /**
             * sync ogp
             */
            $url = $request->post('ogp_url');
            if (! $url) {
                $post->metadata()->first()?->update([
                    'status' => 0,
                ]);
            }

            if ($url) {
                $values = [
                    'url' => $url,
                    'title' => $request->post('ogp_title'),
                    'description' => $request->post('ogp_description'),
                    'image' => $request->post('ogp_image'),
                    'status' => 1,
                ];

                $metadata = $post->metadata()->firstOrCreate([], $values);
                if (! $metadata->wasRecentlyCreated) {
                    $metadata->fill($values)->save();
                }
            }

            /**
             * recommendation
             */
            if ($post->wasRecentlyCreated) {
                /**
                 * post stored event
                 * push notification đến bạn bè khi không post trong community
                 */
                if (is_null($community)) {
                    PostStored::dispatch($user, $post);
                }

                /**
                 * recommendation dispatch
                 */
                $recommendIds = $request->post('recommend_ids');
                if ($recommendIds) {
                    PostUserRecommendation::dispatch($post, explode(',', $recommendIds));
                }
            }

            return $post;
        });
    }

    /**
     * @param array $images
     * @return void
     */
    protected function deleteImages(array $images)
    {
        storage()->removeMedia($images);
    }

    /**
     * @param array $data
     * @return array
     */
    protected function parseData(array $data)
    {
        ksort($data);

        return array_values($data);
    }
}
