<?php

namespace App\Actions\Post;

use App\Http\Requests\Api\Post\MutePostNotificationRequest;
use App\Models\Reaction;
use Illuminate\Support\Facades\DB;

class MutePostNotification
{
    /**
     * @param MutePostNotificationRequest $request
     * @return void
     */
    public function execute(MutePostNotificationRequest $request)
    {
        DB::transaction(function () use ($request) {
            $post = $request->getPost();

            /**
             * create new reaction
             */
            $attributes = [
                'object_id' => $post->getKey(),
                'object_type' => 'post',
                'user_id' => $request->user()->getKey(),
                'action' => 'muted',
            ];

            $values = [
                'count' => 1,
                'object_user_id' => $post->user_id,
                'react_date' => now('UTC')->toDateString(),
            ];

            tap(Reaction::query()->firstOrCreate($attributes, $values), function ($reaction) {
                if (! $reaction->wasRecentlyCreated) {
                    $reaction->delete();
                }
            });
        });
    }
}
