<?php

namespace App\Actions\Post;

use App\Models\PostAnswer;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;

class ViewedPostAnswer
{
    /**
     * @param User $user
     * @param array $ids
     * @return void
     */
    public function execute(User $user, array $ids)
    {
        postAnswerRepository()->getValidAnswers($user, $ids)
            ->chunkById(100, function ($records) use ($user) {

                /** @var Collection|PostAnswer[] $records */
                foreach ($records as $postAnswer) {
                    $user->reactions()->firstOrCreate([
                        'object_id' => $postAnswer->getKey(),
                        'object_type' => 'post_answer',
                        'action' => 'viewed',
                    ], [
                        'count' => 1,
                        'object_user_id' => $postAnswer->user_id,
                        'react_date' => now('UTC')->toDateString(),
                    ]);
                }
            });
    }
}
