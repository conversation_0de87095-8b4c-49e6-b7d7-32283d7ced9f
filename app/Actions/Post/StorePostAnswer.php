<?php

namespace App\Actions\Post;

use App\Events\PostAnswerStored;
use App\Events\PostAnswerStoredInCommunity;
use App\Http\Requests\Api\Post\StorePostAnswerRequest;
use App\Models\PostAnswer;
use App\Models\User;
use Illuminate\Validation\ValidationException;

class StorePostAnswer
{
    /**
     * @param StorePostAnswerRequest $request
     * @return PostAnswer
     * @throws ValidationException
     */
    public function execute(StorePostAnswerRequest $request)
    {
        $answer = $request->getPostAnswer();

        /**
         * parent answer
         */
        $parentAnswer = $request->getParentAnswer();

        /** @var User $user */
        $user = $request->user();

        $attributes = $request->only(['content']);

        /**
         * upload image
         */
        $image = $request->file('image');
        if ($image) {
            $imagePath = storage()->uploadImage($image, true, 1320, true);
            if (! $imagePath) {
                $request->setError('image', __('An error occurred while uploading the image.'));
            }

            $attributes['image'] = $imagePath;
        }

        if (! $answer->exists) {
            $attributes['user_id'] = $user->getKey();
            $attributes['post_id'] = $request->post('post_id');
            $attributes['tab_index'] = 0;
            $attributes['parent_id'] = $parentAnswer?->getKey() ?? 0;
            $attributes['level'] = ($parentAnswer?->level ?? 0) + 1;
        }

        $answer->fill($attributes)->save();

        /**
         * sync ogp
         */
        $url = $request->post('ogp_url');
        if (! $url) {
            $answer->metadata()->first()?->update([
                'status' => 0,
            ]);
        }

        if ($url) {
            $values = [
                'url' => $url,
                'title' => $request->post('ogp_title'),
                'description' => $request->post('ogp_description'),
                'image' => $request->post('ogp_image'),
                'status' => 1,
            ];

            $metadata = $answer->metadata()->firstOrCreate([], $values);
            if (! $metadata->wasRecentlyCreated) {
                $metadata->fill($values)->save();
            }
        }

        $level = (int) $answer->level;

        /**
         * dispatch event
         */
        if ($answer->wasRecentlyCreated) {
            PostAnswerStored::dispatch($user, $answer, $parentAnswer);

            /**
             * nếu trả lời trong các bài post unlock
             * thì cập nhật thời gian unlock
             */
            if ($level === 1) {
                userRepository()->updateUnlockTime($user, $answer->post_id);
            }
        }

        if ($level === 1 && ! is_null($request->getCommunity())) {
            PostAnswerStoredInCommunity::dispatch($user, $answer);
        }

        return $answer;
    }
}
