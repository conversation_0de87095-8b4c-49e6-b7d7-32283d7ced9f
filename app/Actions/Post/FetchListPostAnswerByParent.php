<?php

namespace App\Actions\Post;

use App\Models\PostAnswer;
use Illuminate\Database\Eloquent\Collection;

class FetchListPostAnswerByParent
{
    /**
     * @param PostAnswer $answer
     * @param array $blockedIds
     * @return Collection
     */
    public function execute(PostAnswer $answer, array $blockedIds = [])
    {
        return PostAnswer::query()
            ->with([
                'children' => function ($query) use ($blockedIds) {
                    $query->where('status', 1)
                        ->when($blockedIds, fn ($q) => $q->whereNotIn('user_id', $blockedIds))
                        ->whereHas('createdBy', fn ($q) => $q->where('status', 1))
                        ->orderBy('answer_id');
                },
            ])
            ->where([
                'parent_id' => $answer->getKey(),
                'status' => 1,
            ])
            ->when($blockedIds, fn ($q) => $q->whereNotIn('user_id', $blockedIds))
            ->whereHas('createdBy', fn ($q) => $q->where('status', 1))
            ->orderBy('answer_id')
            ->get();
    }
}
