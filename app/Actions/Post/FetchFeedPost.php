<?php

namespace App\Actions\Post;

use App\Enums\FeedType;
use App\Enums\PostType;
use App\Models\Traits\CommunityPost;
use App\Models\Traits\HasFriendPost;
use App\Models\Traits\HasGenderPost;
use App\Models\Traits\HasGeneralPost;
use App\Models\Traits\HasLatestPost;
use App\Models\Traits\HasOwnerPost;
use App\Models\Traits\HasRecommendedAnswerPost;
use App\Models\Traits\HasTopicsPost;
use App\Models\Traits\IgnoreViewedPost;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class FetchFeedPost
{
    use HasGeneralPost;
    use HasLatestPost;
    use HasOwnerPost;
    use HasRecommendedAnswerPost;
    use HasTopicsPost;
    use HasFriendPost;
    use CommunityPost;
    use IgnoreViewedPost;
    use HasGenderPost;

    /**
     * @var Builder
     */
    protected $queryBuilder;

    /**
     * @return Builder
     */
    protected function getQueryBuilder()
    {
        if (! $this->queryBuilder) {
            $this->queryBuilder = postRepository()->query();
        }

        return $this->queryBuilder->clone();
    }

    /**
     * @param User $user
     * @param string $type
     * @param int $limit
     * @param int $offset
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function execute(User $user, string $type, int $limit = 10, int $offset = 0)
    {
        $query = $this->getQueryBuilder();
        $table = $query->getModel()->getTable();
        $primaryKey = $query->getModel()->getKeyName();

        $parentTable = '`posts`';
        $subQuery = $query->clone()->select('posts.post_id');
        $query->from($table, 'p');

        /**
         * filter by status
         */
        $this->applyEnabledQuery($subQuery);

        $timeSettings = settingRepository()->getHomeTimeSettings();

        switch ($type) {
            case FeedType::TOPICS->value:
                $this->applyTopicsQuery($subQuery);
                $this->applyTopicsSort($query, 'p');
                break;

            case FeedType::RECOMMENDED_ANSWER->value:
                $timeLimit = Arr::get($timeSettings, $type);
                $this->applyRecommendedAnswerQuery($subQuery, $timeLimit);
                $this->applyRecommendedAnswerSort($query, 'p');
                break;

            case FeedType::LAUGH->value:
            case FeedType::GENERAL->value:
            case FeedType::RAISE_HAND->value:
                $timeLimit = Arr::get($timeSettings, $type);
                $postType = match ($type) {
                    FeedType::LAUGH->value => PostType::LAUGH->value,
                    FeedType::RAISE_HAND->value => PostType::RAISE_HAND->value,
                    default => null,
                };

                $this->applyGeneralQuery($subQuery, now('UTC')->subHours($timeLimit)->toDateTimeString(), $postType);
                $this->applyGeneralSort($query, 'p');
                break;

            case FeedType::FRIEND->value:
                $this->applyFriendQuery($subQuery, $user);

                $subQuery = DB::table('reactions')
                    ->fromSub($subQuery, 'tmp')
                    ->selectRaw('`post_id`, MAX(`action_at`) as `action_at`')
                    ->orderByDesc('action_at')
                    ->orderByDesc('post_id')
                    ->groupBy('post_id');

                $query->select('p.*')
                    ->addSelect(DB::raw('`t`.`action_at`'));

                $this->applyFriendSort($query, 'p');

                $parentTable = 'tmp';
                break;

            case FeedType::LATEST->value:
                $this->applyLatestQuery($subQuery);
                $this->applyLatestSort($query, 'p');
                break;

            case FeedType::OWNER->value:
                $this->applyOwnerQuery($subQuery, $user);

                $query->select('p.*')
                    ->addSelect(DB::raw('`t`.`sorted_created_at`'));

                $this->applyOwnerSort($query, 'p');
                break;

            case FeedType::GENDER->value:
                $this->applyGenderQuery($subQuery);
                $this->applyGenderSort($query, 'p');
                break;

            /**
             * lấy bài post trong nhóm community
             */
            default:
                $communityId = (int) str_replace('community_', '', $type);
                $this->applyCommunityQuery($subQuery, $communityId);
                $this->applyCommunitySort($query, 'p');
                break;
        }

        /**
         * không lấy posts trong cộng đồng đóng mà user chưa tham gia
         */
        if (! str_starts_with($type, 'community_')) {
            $this->ignoreCloseCommunityPosts($subQuery, $parentTable . '.`post_id`');
        }

        /**
         * apply owner filter
         */
        if ($type !== FeedType::FRIEND->value) {
            $this->applyCreatedByEnabledQuery($subQuery, $user, $type);
        }

        /**
         * apply pagination
         */
        $subQuery->limit($limit)->offset($offset);

        return $query->joinSub($subQuery->toRawSql(), 't', function (JoinClause $join) use ($primaryKey) {
            $join->on("t.$primaryKey", '=', "p.$primaryKey");
        })->get();
    }

    /**
     * @param Builder $query
     * @param string $tableAlias
     * @return void
     */
    protected function applyEnabledQuery(Builder $query, string $tableAlias = 'posts')
    {
        $query->where($tableAlias . '.status', 1);
    }

    /**
     * @param Builder $query
     * @param User $user
     * @param string $type
     * @return void
     */
    protected function applyCreatedByEnabledQuery(Builder $query, User $user, string $type)
    {
        /**
         * ignore blocked users
         */
        $query->when(blockHelper()->getBlockedIds($user->getKey()), fn ($q, $blockedIds) => $q->whereNotIn('posts.user_id', $blockedIds));

        $query->whereHas('createdBy', function (Builder $query) use ($user, $type) {
            $query->where('users.status', 1);

            if ($type === FeedType::GENDER->value) {
                $query->where('users.gender', $user->gender);
            }

            if ($user->isMale() || $user->isFemale()) {
                $query->whereIn('users.gender', [0, $user->gender]);
            }
        });

        if ($type !== FeedType::GENDER->value) {
            if ($user->isOtherGender() || $user->emptyGender()) {
                $query->where('type', '!=', FeedType::GENDER->value);
            }
        }
    }
}
