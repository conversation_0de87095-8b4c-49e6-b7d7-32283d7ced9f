<?php

namespace App\Actions\Post;

use App\Models\Post;
use App\Models\Traits\IgnoreViewedPost;
use App\Models\Traits\QAPosts\FollowerPost;
use App\Models\Traits\QAPosts\FriendPost;
use App\Models\Traits\QAPosts\GeneralPost;
use App\Models\Traits\QAPosts\LatestPost;
use App\Models\Traits\QAPosts\UserAnswerLikedPost;
use App\Models\Traits\QAPosts\UserSimilarPost;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;

class FetchQAPost
{
    use FollowerPost;
    use GeneralPost;
    use LatestPost;
    use UserAnswerLikedPost;
    use UserSimilarPost;
    use IgnoreViewedPost;
    use FriendPost;

    /**
     * @var Builder
     */
    protected $queryBuilder;

    /**
     * @return array
     */
    protected function getDefaultLimit()
    {
        $default = [];
        $qaSettings = settingRepository()->getQASettings();
        foreach ($qaSettings as $key => $value) {
            $default[$key] = (int) $value['limit'];
        }

        return $default;
    }

    /**
     * @return Builder
     */
    protected function getQueryBuilder()
    {
        if (! $this->queryBuilder) {
            $this->queryBuilder = postRepository()->query();
        }

        return $this->queryBuilder->clone();
    }

    /**
     * @param User $user
     * @param int $next
     * @return array
     */
    protected function getCachedData(User $user, int $next = 1)
    {
        $tokenId = $user->currentTokenId();
        if (! Cache::has('qa_posts_data_' . $tokenId) || $next === 0) {
            viewedPostRepos()->clearViewedPosts($tokenId);

            Cache::put('qa_posts_data_' . $tokenId, [
                'limit' => $this->getDefaultLimit(),
            ], now()->addDays(2));
        }

        return Cache::get('qa_posts_data_' . $tokenId);
    }

    /**
     * @param string $tokenId
     * @param array $cachedData
     * @return void
     */
    protected function updateCachedData(string $tokenId, array $cachedData)
    {
        Cache::put('qa_posts_data_' . $tokenId, $cachedData, now()->addDays(2));
    }

    /**
     * @param array $cached
     * @param string $type
     * @return int
     */
    protected function getActualLimit(array $cached, string $type = 'follow')
    {
        return (int) Arr::get($cached, 'limit.' . $type);
    }

    /**
     * @param User $user
     * @param int $limit
     * @return array
     */
    public function getTestData(User $user, int $limit = 100)
    {
        return [
            $this->getFollowPostsForTest($user, $limit),
            $this->getUserSimilarPostsForTest($user, $limit),
            $this->getUserAnswerLikedPostsForTest($user, $limit),
            $this->getGeneralPostsForTest($user, $limit),
            $this->getLatestPostsForTest($user, $limit),
        ];
    }

    /**
     * @param User $user
     * @param int $next
     * @param int $totalPage
     * @return array
     */
    public function execute(User $user, int $next = 0, int $totalPage = 1)
    {
        $cachedData = $this->getCachedData($user, $next);
        $defaultLimit = $this->getDefaultLimit();

        $perPageLimit = (int) array_sum($defaultLimit);
        $totalLimit = $totalPage * $perPageLimit;
        $fetchLimit = $totalLimit + 1;

        [$friendCollection, $similarPost, $likeCollection, $generalCollection, $latestCollection] = [
            $this->getFriendPosts($user, $cachedData, $fetchLimit),
            $this->getUserSimilarPosts($user, $cachedData, $fetchLimit),
            $this->getUserAnswerLikedPosts($user, $cachedData, $fetchLimit),
            $this->getGeneralPosts($user, $cachedData, $fetchLimit),
            $this->getLatestPosts($user, $cachedData, $fetchLimit),
        ];

        $hasNextPage = ($friendCollection->count() + $similarPost->count() + $likeCollection->count() + $generalCollection->count() + $latestCollection->count()) > $totalLimit;
        $data = [
            // 'follow' => $followCollection,
            'friend' => $friendCollection,
            'similar' => $similarPost,
            'like' => $likeCollection,
            'general' => $generalCollection,
            'latest' => $latestCollection,
        ];

        $ids = [];
        $keys = array_keys($defaultLimit);

        $posts = Collection::make();
        for ($page = 1; $page <= $totalPage; $page++) {
            $originalLimit = [...$defaultLimit];

            while (true) {
                foreach ($keys as $key) {
                    $limit = $originalLimit[$key];
                    if ($limit > 0) {
                        /** @var Collection $collection */
                        $collection = $data[$key];

                        $collection = $collection->filter(fn ($post) => ! in_array($post->getKey(), $ids));

                        /** @var Post $post */
                        $post = $collection->shift();
                        if (! $post) {
                            $originalLimit[$this->getPrevKey($keys, $key, $data) ?? $this->getNextKey($keys, $key)] += $limit;
                            $originalLimit[$key] = 0;
                        }

                        else {
                            $ids[] = $post->getKey();
                            $posts->add($post);
                            $originalLimit[$key] -= 1;
                        }

                        $data[$key] = $collection;
                    }
                }

                $totalPosts = $posts->count();
                $expectedCount = $perPageLimit * $page;
                if ($totalPosts === $expectedCount || $this->isEmpty($data)) {
                    break;
                }
            }
        }

        $ids = $posts->pluck('post_id')->toArray();
        viewedPostRepos()->appends($user->currentTokenId(), $ids);

        return [
            $posts,
            $hasNextPage,
        ];
    }

    /**
     * @param array $keys
     * @param string $currentKey
     * @return string
     */
    protected function getNextKey(array $keys, string $currentKey)
    {
        $index = array_search($currentKey, $keys) + 1;
        if ($index > count($keys) - 1) {
            $index = 0;
        }

        return $keys[$index];
    }

    /**
     * @param array $keys
     * @param string $currentKey
     * @param array $data
     * @return string|null
     */
    protected function getPrevKey(array $keys, string $currentKey, array $data)
    {
        $index = array_search($currentKey, $keys);
        for ($i = 0; $i < $index; $i++) {
            $key = $keys[$i];
            if ($data[$key]->isNotEmpty()) {
                return $key;
            }
        }

        return null;
    }

    /**
     * @param array $data
     * @return bool
     */
    protected function isEmpty(array $data)
    {
        foreach ($data as $collection) {
            /** @var Collection $collection */
            if ($collection->isNotEmpty()) {
                return false;
            }
        }

        return true;
    }
}
