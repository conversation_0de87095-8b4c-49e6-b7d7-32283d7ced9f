<?php

namespace App\Actions\Post;

use App\Models\PostAnswer;

class PinPostAnswer
{
    /**
     * @param PostAnswer $postAnswer
     * @return void
     */
    public function execute(PostAnswer $postAnswer)
    {
        $pinned = PostAnswer::query()
            ->where([
                'post_id' => $postAnswer->post_id,
                'pinned' => 1
            ])
            ->first();

        $pinned?->updateQuietly([
            'pinned' => 0
        ]);

        $postAnswer->updateQuietly([
            'pinned' => 1
        ]);
    }
}
