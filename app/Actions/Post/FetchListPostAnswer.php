<?php

namespace App\Actions\Post;

use App\Repositories\PostAnswerRepository;

class FetchListPostAnswer
{
    /**
     * @param PostAnswerRepository $repository
     */
    public function __construct(protected PostAnswerRepository $repository)
    {

    }

    /**
     * @param array $payload
     * @param int $perPage
     * @param int $currentPage
     * @param int $offset
     * @return \Illuminate\Pagination\LengthAwarePaginator
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function execute(array $payload, int $perPage = 10, int $currentPage = 1, int $offset = 10)
    {
        return $this->repository->pagination($payload, $perPage, $currentPage, $offset);
    }
}
