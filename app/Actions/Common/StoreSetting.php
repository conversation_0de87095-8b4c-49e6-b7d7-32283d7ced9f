<?php

namespace App\Actions\Common;

use App\Http\Requests\Web\StoreSettingRequest;
use App\Models\Setting;

class StoreSetting
{
    /**
     * @param StoreSettingRequest $request
     * @return void
     */
    public function execute(StoreSettingRequest $request)
    {
        $settings = $request->only(['friend', 'similar', 'like', 'general', 'latest']);
        uasort($settings, function ($a, $b) {
            return $a['index'] - $b['index'];
        });

        $index = 1;
        foreach ($settings as $key => $value) {
            $settings[$key] = [
                'index' => $index,
                'limit' => (int) $value['limit'],
            ];

            $index++;
        }

        /** sync to db */
        Setting::query()
            ->firstOrNew([
                'user_id' => 0,
                'key' => 'qa_setting',
            ])
            ->fill([
                'value' => json_encode($settings),
            ])
            ->save();

        $time = $request->post('time');
        Setting::query()
            ->firstOrNew([
                'user_id' => 0,
                'key' => 'home_time_settings',
            ])
            ->fill([
                'value' => json_encode($time),
            ])
            ->save();

        /**
         * clear cached settings
         */
        settingRepository()->clearSystemSettings();
    }
}
