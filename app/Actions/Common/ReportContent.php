<?php

namespace App\Actions\Common;

use App\Events\ContentReported;
use App\Http\Requests\Api\Common\ReportRequest;
use App\Models\Post;
use App\Models\PostAnswer;
use App\Models\Reaction;
use App\Models\Report;
use App\Models\SchoolReport;
use Illuminate\Support\Facades\DB;

class ReportContent
{
    /**
     * @param ReportRequest $request
     * @return void
     */
    public function execute(ReportRequest $request)
    {
        DB::transaction(function () use ($request) {
            /** @var Post|PostAnswer $reportObject */
            $reportObject = $request->getReportObjectInstance();
            $userId = $request->user()->getKey();

            $objectId = (int) $request->post('id');
            $objectType = $request->post('type');

            /**
             * check nếu user report dữ liệu nằm trong community của 1 trường học
             */
            $schoolId = $this->getObjectSchoolCommunityId($reportObject);
            if (! is_null($schoolId)) {
                $today = now('UTC')->toDateString();

                $todayTotalReport = SchoolReport::query()->where([
                    'date' => $today,
                    'user_id' => $userId,
                    'school_id' => $schoolId,
                ])->count();

                /**
                 * nếu trong 1 ngày trong 1 cộng đồng trường học
                 * user đã report 2 lượt thì bỏ qua (không ghi nhận nữa)
                 */
                if ($todayTotalReport >= 2) {
                    return;
                }

                /**
                 * lưu lịch sử report school community
                 */
                SchoolReport::query()->firstOrCreate([
                    'date' => $today,
                    'user_id' => $userId,
                    'school_id' => $schoolId,
                    'object_id' => $objectId,
                    'object_type' => $objectType,
                ]);
            }

            /** @var Report $report */
            $report = Report::query()->create([
                'reportable_id' => $objectId,
                'reportable_type' => $objectType,
                'user_id' => $userId,
                'reason' => $request->post('reason'),
                'description' => $request->post('description'),
            ]);

            /**
             * create new reaction
             */
            $attributes = [
                'object_id' => $objectId,
                'object_type' => $objectType,
                'user_id' => $userId,
                'action' => 'reported',
            ];

            $values = [
                'count' => 1,
                'object_user_id' => $report->reportable->user_id,
                'react_date' => now('UTC')->toDateString(),
            ];

            Reaction::query()->firstOrCreate($attributes, $values);

            /**
             * increment report_count
             */
            $reportObject->incrementReportCount();

            ContentReported::dispatch($reportObject);
        });
    }

    /**
     * @param $reportObject
     * @return int|null
     */
    protected function getObjectSchoolCommunityId($reportObject)
    {
        $post = match (true) {
            $reportObject instanceof Post => $reportObject,
            $reportObject instanceof PostAnswer => $reportObject->post,
            default => null,
        };

        return $post?->communityPost?->community?->school_id;
    }
}
