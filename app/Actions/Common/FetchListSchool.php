<?php

namespace App\Actions\Common;

use App\Models\School;

class FetchListSchool
{
    /**
     * @return array
     */
    public function execute(?string $search)
    {
        if (! $search) {
            return [];
        }

        return School::query()
            ->selectRaw('`school_id`, `name`')
            ->where('status', 1)
            ->where('name', 'LIKE', '%' .$search. '%')
            ->orderBy('name')
            ->limit(20)
            ->get()
            ->toArray();
    }
}
