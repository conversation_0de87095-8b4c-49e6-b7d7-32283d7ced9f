<?php

namespace App\Actions\Quest;

use App\Enums\QuestType;
use App\Http\Requests\Web\StoreQuestRequest;
use App\Models\Quest;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Throwable;

class StoreQuest
{
    /**
     * @param StoreQuestRequest $request
     * @return Quest
     */
    public function execute(StoreQuestRequest $request)
    {
        return DB::transaction(function () use ($request) {
            $quest = $request->getQuest();

            $payload = $request->only([
                'title',
                'description',
                'amount',
                'sort',
            ]);

            if (! $quest->exists) {
                $type = $request->post('type');
                $payload['type'] = $type;
                $payload['unit'] = 'coin';
            }

            $image = $request->file('image');

            /**
             * upload image
             */
            if ($image) {
                $storage = storage();

                $imagePath = $storage->uploadImage($image, true, 1320);
                if (! $imagePath) {
                    $request->setError('image', __('An error occurred while uploading the image.'));
                }

                try {
                    $tmpFile = storage_path('app/public/' . ltrim($imagePath, '/'));
                    $avgLuminance = $storage->getAvgLuminance($tmpFile);
                    $payload['is_dark'] = (int) ($avgLuminance < 50);

                    if (config('filesystems.default') === 's3') {
                        @unlink($tmpFile);
                    }
                } catch (Throwable $e) {
                    // do nothing
                }

                $payload['image'] = $imagePath;
            }

            $quest->fill($payload)->save();

            return $quest;
        });
    }
}
