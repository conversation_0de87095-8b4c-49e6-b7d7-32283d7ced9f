<?php

namespace App\Actions\AttachedSurvey;

use App\Models\AttachedSurvey;
use Illuminate\Support\Facades\DB;

class DeleteAttachedSurvey
{
    /**
     * @return mixed
     */
    public function execute(AttachedSurvey $attachedSurvey)
    {
        return DB::transaction(function () use ($attachedSurvey) {
            $attachedSurvey->choices()->delete();

            $attachedSurvey->delete();
        });
    }
}
