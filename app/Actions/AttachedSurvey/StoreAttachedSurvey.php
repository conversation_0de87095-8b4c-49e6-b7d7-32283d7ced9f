<?php

namespace App\Actions\AttachedSurvey;

use App\Http\Requests\Web\StoreAttachedSurveyRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class StoreAttachedSurvey
{
    /**
     * @return mixed
     */
    public function execute(StoreAttachedSurveyRequest $request)
    {
        return DB::transaction(function () use ($request) {
            $attachedSurvey = $request->getAttachedSurvey();
            $attachedSurvey->fill($request->only([
                'title',
                'survey_id',
                'to_survey_id',
            ]));

            $attachedSurvey->save();

            /**
             * delete old choice data
             */
            if (! $attachedSurvey->wasRecentlyCreated) {
                $attachedSurvey->choices()->delete();
            }

            $choices = Arr::pluck($request->post('choices'), 'choice_id');
            $choices = array_unique($choices);
            // sort($choices);

            $attachedId = $attachedSurvey->getKey();
            $choiceData = [];
            foreach ($choices as $choiceId) {
                $choiceData[] = [
                    'attached_id' => $attachedId,
                    'choice_id' => $choiceId,
                ];
            }

            $attachedSurvey->choices()->insert($choiceData);

            return $attachedSurvey;
        });
    }
}
