<?php

namespace App\Actions\AttachedSurvey;

use App\Repositories\AttachedSurveyRepository;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class FetchListAttachedSurveyPagination
{
    /**
     * @param AttachedSurveyRepository $repository
     */
    public function __construct(protected AttachedSurveyRepository $repository)
    {

    }

    /**
     * @param Request $request
     * @param int $perPage
     * @param int $currentPage
     * @param int $offset
     * @return LengthAwarePaginator
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function execute(Request $request, int $perPage, int $currentPage, int $offset = 0)
    {
        $payload = array_merge($request->only('search'), [
            'with' => [
                'survey',
                'toSurvey',
                'choices',
                'choices.choice',
                'choices.choice.question',
            ],
        ]);

        return $this->repository->pagination($payload, $perPage, $currentPage, $offset);
    }
}
