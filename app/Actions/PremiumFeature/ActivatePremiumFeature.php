<?php

namespace App\Actions\PremiumFeature;

use App\Models\PremiumFeature;
use App\Models\User;

class ActivatePremiumFeature
{
    /**
     * @param User $user
     * @param PremiumFeature $feature
     * @return void
     */
    public function execute(User $user, PremiumFeature $feature)
    {
        $user->premiumFeatures()->attach($feature->getKey());

        $user->incrementOrDecrementCount('used_coin', 'increment', $feature->price);
    }
}
