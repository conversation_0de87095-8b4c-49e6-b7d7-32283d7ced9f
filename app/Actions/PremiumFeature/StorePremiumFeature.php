<?php

namespace App\Actions\PremiumFeature;

use App\Http\Requests\Web\StorePremiumFeatureRequest;
use App\Models\Quest;
use Illuminate\Support\Facades\DB;
use Throwable;

class StorePremiumFeature
{
    /**
     * @param StorePremiumFeatureRequest $request
     * @return Quest
     */
    public function execute(StorePremiumFeatureRequest $request)
    {
        return DB::transaction(function () use ($request) {
            $feature = $request->getPremiumFeature();

            $payload = $request->only([
                'name',
                'description',
                'price',
            ]);

            if (! $feature->exists) {
                $payload['type'] = $request->post('type');
            }

            $image = $request->file('image');

            /**
             * upload image
             */
            if ($image) {
                $storage = storage();

                $imagePath = $storage->uploadImage($image, true, 1320);
                if (! $imagePath) {
                    $request->setError('image', __('An error occurred while uploading the image.'));
                }

                try {
                    $tmpFile = storage_path('app/public/' . ltrim($imagePath, '/'));
                    $avgLuminance = $storage->getAvgLuminance($tmpFile);
                    $payload['is_dark'] = (int) ($avgLuminance < 50);

                    if (config('filesystems.default') === 's3') {
                        @unlink($tmpFile);
                    }
                } catch (Throwable $e) {
                    // do nothing
                }

                $payload['image'] = $imagePath;
            }

            $feature->fill($payload)->save();

            return $feature;
        });
    }
}
