<?php

namespace App\Actions\Reaction;

use App\Events\PostLiked;
use App\Models\Post;
use App\Models\Reaction;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class LikePost
{
    /**
     * @param User $user
     * @param Post $post
     * @return void
     */
    public function execute(User $user, Post $post)
    {
        DB::transaction(function () use ($user, $post) {
            $attributes = [
                'object_id' => $post->getKey(),
                'object_type' => 'post',
                'user_id' => $user->getKey(),
                'action' => 'liked',
            ];

            $reaction = Reaction::query()->firstOrCreate($attributes, [
                'count' => 1,
                'object_user_id' => $post->user_id,
                'react_date' => now('UTC')->toDateString(),
            ]);

            /**
             * sync like_count
             */
            $post->incrementCount('like_count');

            if (! $post->createdBy->isMuted($post->getKey())) {
                PostLiked::dispatch($user, $post, $reaction->getKey());
            }
        });
    }
}
