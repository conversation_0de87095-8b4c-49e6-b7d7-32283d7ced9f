<?php

namespace App\Actions\Reaction;

use App\Jobs\PushNotification;
use App\Models\Notification;
use App\Models\NotificationUser;
use App\Models\User;
use Illuminate\Support\Arr;

class CreateNewNotification
{
    /**
     * 0 - <PERSON><PERSON> câu trả lời cho câu hỏi của mình.
     * 1 - <PERSON><PERSON><PERSON> "Coin" đã được nhấn vào câu trả lời của mình, và mình được tặng n đồng xu.
     * 2 - Câu trả lời của mình đã nhận được "thích" nhờ đó mình đã nhận được n điểm.
     * 3 - C<PERSON><PERSON> trả lời của mình đã nhận được bình luận.
     * 4 - Kết quả công việc đã thực hiện trong tab "Công việc" đ<PERSON><PERSON><PERSON> phê duy<PERSON>, và mình nhận được n đồng xu.
     * 5 - Kế<PERSON> quả công việc đã thực hiện trong tab "Công việc" đ<PERSON><PERSON><PERSON> phê duy<PERSON>t, và mình nhận được n điểm.
     * 6 - Công việc đã thực hiện trong tab "Công việc" không được phê duyệt.
     * 7 - Có công việc từ quản lý gửi đến.
     * 8 - Có người theo dõi mình.
     * 9 - Câu hỏi của mình đã được người khác ủng hộ.
     * 10 - Câu hỏi của mình chưa chọn best answer trong vòng 24h có câu trả lời
     * 11 - Câu trả lời được chọn là best answer, kèm theo data: rewarded_coin nếu bài post có gắn coin
     * 12 - User được gợi ý trả lời câu hỏi
     * 13 - User đăng bài post mới - thông báo cho những người đang follow
     * 14 - User đăng ký thành công qua link invited
     * @param User $user
     * @param array $payload
     * @param int $point
     * @return void
     */
    public function execute(User $user, array $payload, int $point = 1)
    {
        $type = (int) Arr::get($payload, 'type');
        $reactionUserID = Arr::get($payload, 'reaction_user_id');
        $reactionID = Arr::get($payload, 'reaction_id');

        $data = Arr::get($payload, 'data');

        /**
         * 0 - Có câu trả lời cho câu hỏi của mình.
         * 3 - Câu trả lời của mình đã nhận được bình luận.
         * 9 - Câu hỏi của mình đã được người khác ủng hộ.
         * 15 - User mời người khác đăng ký thành công.
         * 19 - User được tăng lượt mời do user được mời trả lời đạt 10 câu hỏi
         */
        $mergeNotification = in_array($type, [0, 3, 9, 15, 19, 20]);

        /**
         * 1 - Nút "Coin" đã được nhấn vào câu trả lời của mình, và mình được tặng n đồng xu.
         * 2 - Câu trả lời của mình đã nhận được "thích" nhờ đó mình đã nhận được n điểm.
         */
        $shouldMergeReaction = in_array($type, [1, 2]) && $reactionID;

        $params = array_filter(Arr::only($payload, [
            'object_type',
            'object_id',
            'type',
        ]));

        /**
         * fix array_filter remove type = 0
         */
        if ($type === 0) {
            $params['type'] = $type;
        }

        if (! $mergeNotification && $reactionUserID) {
            $params['reaction_user_id'] = $reactionUserID;
        }

        /**
         * merge khi người dùng like, donate câu trả lời
         */
        if ($shouldMergeReaction) {
            $params['reaction_id'] = $reactionID;
        }

        $values = [
            'reaction_id' => $mergeNotification ? null : $reactionID,
        ];

        /**
         * 0 - answer post
         * 8 - user was followed
         * 11 - answer was voted
         * 14 - User được giới thiệu đăng ký thành công (qua link invited)
         * 15 - User mời người khác đăng ký thành công
         * 17 - Friend accepted
         * 19 - User được tăng lượt mời do user được mời trả lời đạt 10 câu hỏi
         */
        if (in_array($type, [0, 8, 11, 14, 15, 17, 19])) {
            $values['badge_count'] = $point;
        }

        if ($data) {
            $values['data'] = $data;
        }

        /** @var Notification $notification */
        $notification = $user->notifications()->firstOrCreate($params, $values);

        $wasRecentlyCreated = $notification->wasRecentlyCreated;
        $shouldPushNotification = $wasRecentlyCreated;

        /**
         * sync notification
         */
        if (! $wasRecentlyCreated) {
            $shouldPushNotification = ! $notification->isActive();

            $isUserRelation = in_array($type, [8, 17]);
            if ($isUserRelation) {
                $point = 0;
            }

            if ($shouldPushNotification || $mergeNotification || $isUserRelation) {
                $notification->fill([
                    'active' => 1,
                    // 'status' => 0,
                    'badge_count' => $notification->badge_count + $point,
                    'data' => $data ?? $notification->data,
                ]);

                if ($notification->isDirty()) {
                    $notification->fill(['status' => 0]);
                    $notification->save();
                }
            }
        }

        /**
         * sync reaction users
         */
        if ($mergeNotification) {
            $attrs = [
                'notification_id' => $notification->getKey(),
                'user_id' => $reactionUserID,
            ];

            if ($reactionID) {
                $attrs['reaction_id'] = $reactionID;
            }

            $relation = NotificationUser::query()->firstOrCreate($attrs);

            /**
             * update updated_at
             */
            if (! $relation->wasRecentlyCreated) {
                $relation->fill([
                    'updated_at' => now('UTC')->toDateTimeString(),
                ]);

                if (! $relation->isActive()) {
                    $relation->fill([
                        'status' => 1,
                    ]);
                }

                $relation->saveQuietly();
            }

            $shouldPushNotification = true;
        }

        if ($shouldPushNotification) {
            $this->pushNotification($notification);
        }
    }

    /**
     * @param Notification $notification
     * @return void
     */
    protected function pushNotification(Notification $notification)
    {
        PushNotification::dispatch($notification)->onConnection('redis');
    }
}
