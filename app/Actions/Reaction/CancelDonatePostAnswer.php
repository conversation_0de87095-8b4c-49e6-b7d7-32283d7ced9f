<?php

namespace App\Actions\Reaction;

use App\Models\PostAnswer;
use App\Models\Reaction;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class CancelDonatePostAnswer
{
    /**
     * @param User $user
     * @param Reaction $reaction
     * @param PostAnswer $postAnswer
     * @return mixed
     */
    public function execute(User $user, Reaction $reaction, PostAnswer $postAnswer)
    {
        return DB::transaction(function () use ($user, $reaction, $postAnswer) {
            $originalCount = $count = (int) $reaction->count;
            $createdBy = $postAnswer->createdBy;

            /**
             * restore user total_coin
             */
            $remainCoin = $createdBy->getRemainingCoins();
            if ($remainCoin < $count) {
                $count = $remainCoin;
            }

            $lock = Cache::lock($user->getKey() . '_cancel_donate_' . $postAnswer->getKey(), 3);
            if ($lock->get()) {
                $postAnswer->decrementCount('coin_count', $count);

                $createdBy->incrementOrDecrementCount('used_coin', 'increment', $count);
                $user->incrementOrDecrementCount('used_coin', 'decrement', $count);

                $lock->release();
            }

            /**
             * remove donated reaction (auto remove notification - foreign key)
             */
            $reaction->delete();

            /**
             * check exists donated
             */
            $postAnswer->loadCount([
                'reactions' => function (Builder $query) use ($user) {
                    $query->where('user_id', $user->getKey())
                        ->where('action', 'donated');
                },
            ]);

            return [
                'count' => $originalCount,
                'reality' => $count,
                'donated' => (int) ($postAnswer->getAttribute('reactions_count') > 0),
            ];
        });
    }
}
