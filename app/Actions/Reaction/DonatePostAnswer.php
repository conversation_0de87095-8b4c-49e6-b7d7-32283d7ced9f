<?php

namespace App\Actions\Reaction;

use App\Events\PostAnswerDonated;
use App\Models\PostAnswer;
use App\Models\Reaction;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class DonatePostAnswer
{
    /**
     * @param User $user
     * @param PostAnswer $postAnswer
     * @param int $count
     * @return void
     */
    public function execute(User $user, PostAnswer $postAnswer, int $count)
    {
        $postAnswerOwner = $postAnswer->createdBy;

        $reactionId = DB::transaction(function () use ($user, $postAnswer, $postAnswerOwner, $count) {
            $attributes = [
                'object_id' => $postAnswer->getKey(),
                'object_type' => 'post_answer',
                'user_id' => $user->getKey(),
                'action' => 'donated',
            ];

            $values = [
                'count' => $count,
                'object_user_id' => $postAnswer->user_id,
                'react_date' => now('UTC')->toDateString(),
            ];

            /** @var Reaction $reaction */
            $reaction = Reaction::query()->orderByDesc('id')->firstOrCreate($attributes, $values);

            if (! $reaction->wasRecentlyCreated) {
                if ($reaction->canMerge()) {
                    $reaction->incrementCount($count);
                } else {
                    $reaction = $user->reactions()->create([...$attributes, ...$values]);
                }
            }

            /**
             * sync coin
             */
            $postAnswer->incrementCount('coin_count', $count);

            /**
             * user coin
             */
            $postAnswerOwner->incrementOrDecrementCount('total_coin', 'increment', $count);
            $user->incrementOrDecrementCount('used_coin', 'increment', $count);

            return $reaction->getKey();
        });

        if ($postAnswerOwner->isNotMutedAny([$postAnswer->post_id, $postAnswer->getKey()])) {
            PostAnswerDonated::dispatch(
                $user,
                $postAnswer,
                $reactionId,
            );
        }
    }
}
