<?php

namespace App\Actions\Reaction;

use App\Events\PostAnswerVoted;
use App\Models\PostAnswer;
use Illuminate\Support\Facades\DB;

class VoteBest
{
    /**
     * @param PostAnswer $postAnswer
     * @return void
     */
    public function execute(PostAnswer $postAnswer)
    {
        DB::transaction(function () use ($postAnswer) {
            $postAnswer->updateQuietly([
                'voted' => 1,
            ]);

            $post = $postAnswer->post;
            $post->updateQuietly([
                'voted' => 1,
            ]);

            $postAnswerOwner = $postAnswer->createdBy;
            $postAnswerOwner->incrementOrDecrementCount('best_answer_count');

            /**
             * sync profile data
             */
            $postAnswerOwner->rebuildProfileData();

            /**
             * clear vote the best notification (type 10)
             */
            DB::table('notifications')
                ->where([
                    'user_id' => $post->user_id,
                    'type' => 10,
                    'object_id' => $post->getKey(),
                ])
                ->delete();

            /**
             * apply reward_amount
             */
            $rewardAmount = $post->getRewardAmount();
            if ($rewardAmount > 0) {
                $post->createdBy->incrementOrDecrementCount('used_coin', 'increment', $rewardAmount);
                $post->createdBy->incrementOrDecrementCount('holding_coin', 'decrement', $rewardAmount);
                $postAnswerOwner->incrementOrDecrementCount('total_coin', 'increment', $rewardAmount);
            }

            /**
             * push notification
             */
            if ($postAnswerOwner->isNotMutedAny([$postAnswer->post_id, $postAnswer->getKey()])) {
                PostAnswerVoted::dispatch($postAnswer, $rewardAmount);
            }
        });
    }
}
