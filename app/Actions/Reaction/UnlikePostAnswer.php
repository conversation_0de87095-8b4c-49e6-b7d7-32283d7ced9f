<?php

namespace App\Actions\Reaction;

use App\Events\SyncPostAnswerLikeUser;
use App\Models\PostAnswer;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Facades\DB;

class UnlikePostAnswer
{
    /**
     * @param User $user
     * @param PostAnswer $postAnswer
     * @return mixed
     */
    public function execute(User $user, PostAnswer $postAnswer)
    {
        return DB::transaction(function () use ($user, $postAnswer) {
            if (! $postAnswer->relationLoaded('reactions')) {
                $postAnswer->load([
                    'reactions' => function (MorphMany $query) use ($user) {
                        $query->where('user_id', $user->getKey())->where('action', 'liked');
                    },
                ]);
            }

            $reactions = $postAnswer->reactions;

            $count = 0;
            foreach ($reactions as $reaction) {
                $count += $reaction->count;
                $reaction->delete();
            }

            $reality = min ($count, $postAnswer->like_count);
            $postAnswer->decrementCount('like_count', $reality);

            SyncPostAnswerLikeUser::dispatch($postAnswer);

            return [
                'count' => 0,
                'reality' => $reality,
                'liked' => 0,
            ];
        });
    }
}
