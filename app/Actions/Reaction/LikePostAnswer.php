<?php

namespace App\Actions\Reaction;

use App\Events\PostAnswerLiked;
use App\Events\SyncPostAnswerLikeUser;
use App\Models\PostAnswer;
use App\Models\Reaction;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class LikePostAnswer
{
    /**
     * @param User $user
     * @param PostAnswer $postAnswer
     * @param int $count
     * @return void
     */
    public function execute(User $user, PostAnswer $postAnswer, int $count)
    {
        $postAnswerOwner = $postAnswer->createdBy;

        $reactionId = DB::transaction(function () use ($user, $postAnswer, $postAnswerOwner, $count) {
            $attributes = [
                'object_id' => $postAnswer->getKey(),
                'object_type' => 'post_answer',
                'user_id' => $user->getKey(),
                'action' => 'liked',
            ];

            $values = [
                'count' => $count,
                'object_user_id' => $postAnswer->user_id,
                'react_date' => now('UTC')->toDateString(),
            ];

            /** @var Reaction $reaction */
            $reaction = Reaction::query()->orderByDesc('id')->firstOrCreate($attributes, $values);
            if (! $reaction->wasRecentlyCreated) {
                $reaction->incrementCount($count);
            }

            /**
             * sync like_count & point
             */
            $postAnswer->incrementCount('like_count', $count);

            SyncPostAnswerLikeUser::dispatch($postAnswer);

            return $reaction->getKey();
        });

        if ($postAnswerOwner->isNotMutedAny([$postAnswer->post_id, $postAnswer->getKey()])) {
            PostAnswerLiked::dispatch(
                $user,
                $postAnswer,
                $reactionId,
            );
        }
    }
}
