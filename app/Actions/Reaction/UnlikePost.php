<?php

namespace App\Actions\Reaction;

use App\Events\PostUnliked;
use App\Models\Post;
use App\Models\Reaction;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class UnlikePost
{
    /**
     * @param User $user
     * @param Reaction $reaction
     * @param Post $post
     * @return void
     */
    public function execute(User $user, Reaction $reaction, Post $post)
    {
        DB::transaction(function () use ($user, $reaction, $post) {
            /**
             * restore instance like_count
             */
            $post->decrementCount('like_count');

            /**
             * remove liked reaction (auto remove from notification - foreign key)
             */
            $reaction->delete();

            PostUnliked::dispatch($user, $post);
        });
    }
}
