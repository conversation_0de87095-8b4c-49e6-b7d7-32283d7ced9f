<?php

namespace App\Actions\Fortify;

use App\Models\User;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use <PERSON><PERSON>\Fortify\Contracts\UpdatesUserProfileInformation;

class UpdateUserProfileInformation implements UpdatesUserProfileInformation
{
    /**
     * Validate and update the given user's profile information.
     *
     * @param array<string, mixed> $input
     * @throws ValidationException
     */
    public function update(User $user, array $input): void
    {
        Validator::make($input, [
            'name' => [
                'required',
                'string',
                'max:191',
            ],
            /*'email' => [
                'required',
                'email',
                'max:191',
                Rule::unique('users')->ignore($user->getKey(), 'user_id'),
            ],*/
            'photo' => [
                'nullable',
                'mimes:jpg,jpeg,png',
                'max:1024',
            ],
        ])->validateWithBag('updateProfileInformation');

        if (isset($input['photo'])) {
            $user->updateProfilePhoto($input['photo']);
        }

        if (($input['email'] !== $user->email) && ($user instanceof MustVerifyEmail)) {
            $this->updateVerifiedUser($user, $input);
        } else {
            $user->updateQuietly([
                'name' => $input['name'],
            ]);
        }
    }

    /**
     * Update the given verified user's profile information.
     *
     * @param  array<string, string>  $input
     */
    protected function updateVerifiedUser(User $user, array $input): void
    {
        $user->updateQuietly([
            'name' => $input['name'],
            //'email' => $input['email'],
            //'email_verified_at' => null,
        ]);

        $user->sendEmailVerificationNotification();
    }
}
