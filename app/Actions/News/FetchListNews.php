<?php

namespace App\Actions\News;

use App\Repositories\NewsRepository;

class FetchListNews
{
    /**
     * @param NewsRepository $repository
     */
    public function __construct(protected NewsRepository $repository)
    {

    }

    /**
     * @param array $payload
     * @param int $perPage
     * @param int $currentPage
     * @param int $offset
     * @return \Illuminate\Pagination\LengthAwarePaginator
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function execute(array $payload, int $perPage = 10, int $currentPage = 1, int $offset = 0)
    {
        return $this->repository->pagination($payload, $perPage, $currentPage, $offset);
    }
}
