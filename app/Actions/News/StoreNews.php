<?php

namespace App\Actions\News;

use App\Models\News;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class StoreNews
{
    /**
     * @param News $news
     * @param array $payload
     * @return News
     */
    public function execute(News $news, array $payload)
    {
        return DB::transaction(function () use ($news, $payload) {
            $news->fill(Arr::only($payload, [
                'title',
                'content',
            ]));

            if (! $news->exists) {
                $news->fill([
                    'user_id' => auth()->id(),
                ]);
            }

            $news->save();

            return $news;
        });
    }
}
