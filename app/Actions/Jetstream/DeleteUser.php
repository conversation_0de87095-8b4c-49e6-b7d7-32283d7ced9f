<?php

namespace App\Actions\Jetstream;

use App\Models\User;
use Lara<PERSON>\Jetstream\Contracts\DeletesUsers;

class DeleteUser implements DeletesUsers
{
    /**
     * Delete the given user.
     * @noinspection PhpUndefinedMethodInspection
     */
    public function delete(User $user): void
    {
        $user->deleteProfilePhoto();
        $user->tokens->each->delete();
        $user->delete();
    }
}
