<?php

namespace App\Actions\Community;

use App\Repositories\CommunityUserRepository;

class FetchListMember
{
    public function __construct(protected CommunityUserRepository $repository)
    {

    }

    /**
     * @param array $payload
     * @param int $perPage
     * @param int $currentPage
     * @param int $offset
     * @return \Illuminate\Pagination\LengthAwarePaginator
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function execute(array $payload, int $perPage = 10, int $currentPage = 1, int $offset = 0)
    {
        return $this->repository->pagination($payload, $perPage, $currentPage, $offset);
    }
}
