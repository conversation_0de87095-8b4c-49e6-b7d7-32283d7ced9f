<?php

namespace App\Actions\Community;

use App\Models\Community;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class FetchListCommunity
{
    /**
     * @param User $user
     * @param bool $isClosed
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function execute(User $user, bool $isClosed = false)
    {
        $subQuery = DB::table('community_users as cu')
            ->join('users as u', 'u.user_id', '=', 'cu.user_id')
            ->where('cu.status', 1)
            ->where('u.status', 1)
            ->select('cu.community_id', DB::raw('COUNT(*) as members_count'))
            ->groupBy('cu.community_id');

        return Community::query()
            ->join('community_users as cu', function ($join) use ($user) {
                $join->on('cu.community_id', '=', 'communities.community_id')
                    ->where('cu.user_id', '=', $user->getKey())
                    ->where('cu.status', '=', 1);
            })
            ->joinSub($subQuery, 'mc', function ($join) {
                $join->on('mc.community_id', '=', 'communities.community_id');
            })
            ->where(function ($query) use ($isClosed) {
                $isClosed ? $query->where('communities.status', 2) : $query->where('communities.status', '!=', 0);
            })
            ->select('communities.*', 'cu.created_at as joined_at', 'mc.members_count')
            ->orderByDesc('mc.members_count')
            ->orderByDesc('cu.created_at')
            ->get();
    }

    /**
     * @param array $ignoreIds
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPublicCommunities(array $ignoreIds)
    {
        $subQuery = DB::table('community_users as cu')
            ->join('users as u', 'u.user_id', '=', 'cu.user_id')
            ->where('cu.status', 1)
            ->where('u.status', 1)
            ->select('cu.community_id', DB::raw('COUNT(*) as members_count'))
            ->groupBy('cu.community_id');

        return Community::query()
            ->joinSub($subQuery, 'mc', function ($join) {
                $join->on('mc.community_id', '=', 'communities.community_id');
            })
            ->where('communities.status', 1)
            ->whereNotIn('communities.community_id', $ignoreIds)
            ->select('communities.*', 'mc.members_count')
            ->orderByDesc('mc.members_count')
            ->orderByDesc('communities.community_id')
            ->get();
    }
}
