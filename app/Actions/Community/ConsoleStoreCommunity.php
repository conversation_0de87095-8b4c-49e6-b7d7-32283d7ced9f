<?php

namespace App\Actions\Community;

use App\Events\UpdateSearchableProfile;
use App\Http\Requests\Web\StoreCommunityRequest;
use App\Models\Community;
use Illuminate\Validation\ValidationException;

/**
 * @package App\Actions\Community
 */
class ConsoleStoreCommunity
{
    /**
     * @param StoreCommunityRequest $request
     * @return Community
     * @throws ValidationException
     */
    public function execute(StoreCommunityRequest $request)
    {
        $community = $request->getCommunity();
        $userId = $request->user()->getKey();

        $attrs = [
            'name' => $request->post('name'),
            'description' => $request->post('description') ?? '',
            'status' => 1,
        ];

        if (! $community->exists) {
            $attrs['user_id'] = $userId;
        }

        $storage = storage();
        $image = $request->file('image');
        $imagePath = $storage->uploadImage($image, true, 1320, true);
        if (! $imagePath) {
            $request->setError('image', __('An error occurred while uploading the image.'));
        }

        $attrs['avatar'] = $imagePath;

        $community->fill($attrs);
        $community->save();

        /**
         * auto join current user to new community
         */
        if ($community->wasRecentlyCreated) {
            $community->members()->create([
                'user_id' => $userId,
                'status' => 1,
            ]);

            /**
             * rebuild profile search data
             */
            UpdateSearchableProfile::dispatch($userId);
        }

        return $community;
    }
}
