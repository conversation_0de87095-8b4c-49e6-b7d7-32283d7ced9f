<?php

namespace App\Actions\Community;

use App\Models\Community;
use App\Models\CommunityUser;

class AddMember
{
    /**
     * @param Community $community
     * @param int $userId
     * @return bool
     */
    public function execute(Community $community, int $userId)
    {
        /** @var CommunityUser $record */
        $record = $community->members()->firstOrCreate([
            'user_id' => $userId,
        ]);

        $success = $record->wasRecentlyCreated;
        if (! $record->wasRecentlyCreated && $record->isInActive()) {
            $record->fill([
                'status' => 1,
            ]);

            $record->save();
            $success = true;
        }

        return $success;
    }
}
