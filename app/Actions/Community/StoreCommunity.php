<?php

namespace App\Actions\Community;

use App\Events\UpdateSearchableProfile;
use App\Helpers\CommunityHelper;
use App\Models\Community;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;

class StoreCommunity
{
    /**
     * @param Community $community
     * @param array $payload
     * @return Community|\Illuminate\Database\Eloquent\Model
     */
    public function execute(Community $community, array $payload)
    {
        $params = Arr::only($payload, [
            'name',
            'description',
            'school_id',
        ]);

        $closed = (int) Arr::get($payload, 'closed');

        /**
         * 1 - public
         * 2 - closed
         */
        $params['status'] = $closed === 1 ? 2 : 1;

        /**
         * khi tạo nhóm mới
         */
        $authId = auth()->id();
        if (! $community->exists) {
            $params['user_id'] = $authId;
        }

        /**
         * upload avatar
         * @var UploadedFile $avatar
         */
        if ($avatar = Arr::get($payload, 'avatar')) {
            $avatarPath = storage()->uploadImage($avatar, true, 1320);
            if (! $avatarPath) {
                abort(400, __('An error occurred while uploading the image.'));
            }

            $params['avatar'] = $avatarPath;
        }

        $community->fill($params);
        $changedCommunityStatus = $community->exists && $community->isDirty('status');
        $community->save();

        if ($community->wasRecentlyCreated) {
            $community->members()->create([
                'community_id' => $community->getKey(),
                'user_id' => $authId,
                'status' => 1,
            ]);

            /**
             * rebuild profile search data
             */
            UpdateSearchableProfile::dispatch($authId);
        }

        /**
         * thay đổi trạng thái của community (public <=> closed)
         * thì update trạng thái của community trong bảng community_posts
         */
        if ($changedCommunityStatus) {
            $community->posts()->update([
                'community_status' => $community->status,
            ]);
        }

        CommunityHelper::clearClosedCommunityIds($authId);

        return $community;
    }
}
