<?php

namespace App\Actions\Community;

use App\Helpers\CommunityHelper;
use App\Models\Community;
use App\Models\CommunityUser;

class LeaveCommunity
{
    /**
     * @param Community $community
     * @param CommunityUser $record
     * @return void
     */
    public function execute(Community $community, CommunityUser $record)
    {
        $record->updateQuietly([
            'status' => 0,
        ]);

        /**
         * xóa cache ids của communities kín
         * khi user rời khỏi community kín
         */
        if ($community->isClosed()) {
            CommunityHelper::clearClosedCommunityIds($record->user_id);
        }
    }
}
