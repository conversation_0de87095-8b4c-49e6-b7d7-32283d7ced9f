<?php

namespace App\Actions\User;

use App\Models\Answer;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class FetchUserData
{
    /**
     * @param User $user
     * @return array|null
     */
    public function execute(User $user)
    {
        /** @var Collection|Answer[] $answers */
        $answers = $user->answers()->with([
            'survey',
            'question',
            'choices',
            'choices.choice',
        ])
            ->orderBy('answer_id', 'DESC')
            ->get();

        $surveys = [];
        foreach ($answers as $answer) {
            if (! isset($surveys[$answer->survey_id])) {
                $surveys[$answer->survey_id] = [
                    'title' => $answer->survey->title,
                    'questions' => [],
                ];
            }

            $choices = [];
            foreach ($answer->choices as $choice) {
                $choices[] = $choice->choice->content;
            }

            $answerContent = (string) $answer->content;
            if ($choices) {
                $answerContent = implode('<br />- ', $choices);
                if (count($choices) > 1) {
                    $answerContent = '- ' . $answerContent;
                }
            }

            $surveys[$answer->survey_id]['questions'][$answer->question_id] = [
                'content' => $answer->question->content,
                'answer' => $answerContent,
                'star' => (int) $answer->star,
                'public' => 'public' . ucfirst(Str::camel($answer->question->public)),
            ];
        }

        /**
         * sort by question
         */
        foreach ($surveys as $key => $surveyData) {
            $questions = Arr::get($surveyData, 'questions', []);
            ksort($questions);
            $surveys[$key]['questions'] = array_values($questions);
        }

        $phone = explode('_', $user->phone);
        $phone = $phone[0];

        $profile = $user->profile;
        $profileType = (string) $user->profile_type;

        return [
            'user_id' => $user->getKey(),
            'name' => $user->name,
            'phone' => $phone,
            'birthday' => $user->birthday?->toDateString(),
            'age' => $user->getAge(),
            'role' => $user->role,
            'post_count' => $user->post_count,
            'answer_count' => $user->answer_count,
            'comment_count' => $user->comment_count,
            'status_label' => __('user.status.' . $user->status),
            'created_at' => $user->created_at->toDateTimeString(),
            'last_logged_in_at' => $user->last_logged_in_at?->toDateTimeString() ?? '',
            'surveys' => array_values($surveys),
            'position' => $user->position,
            'profile_type' => $profileType,
            'profile_label' => __('profileType.' . $profileType),
            'work' => $profile?->work ?? '',
            'expert' => $profile?->expert ?? '',
            'service_in_charge' => $profile?->service_in_charge ?? '',
            'brief' => $profile?->brief ?? '',
            'school_name' => $profile?->school?->name ?? '',
        ];
    }
}
