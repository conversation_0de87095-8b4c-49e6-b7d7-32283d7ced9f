<?php

namespace App\Actions\User;

use App\Models\User;

class FindFriend
{
    /**
     * @param array $phones
     * @param int $currentUserId
     * @return \Illuminate\Support\Collection
     */
    public function execute(array $phones, int $currentUserId)
    {
        $users = collect();

        $result = User::query()
            ->where('user_id', '!=', $currentUserId)
            ->when(blockHelper()->getBlockedIds($currentUserId), fn ($q, $blockedIds) => $q->whereNotIn('user_id', $blockedIds))
            ->whereIn('phone', $phones)
            ->lazyById(200, 'user_id');

        foreach ($result as $user) {
            $users->push($user);
        }

        return $users->sortBy([
            ['name', 'asc'],
            ['user_id', 'asc'],
        ]);
    }
}
