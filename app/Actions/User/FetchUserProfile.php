<?php

namespace App\Actions\User;

use App\Models\User;
use App\Http\Resources\Api\UserResource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class FetchUserProfile
{
    /**
     * @param User $user
     * @param Request $request
     * @return array
     */
    public function execute(User $user, Request $request)
    {
        $countRelations = [
            /*'followers' => function (Builder $query) {
                $query->whereHas('user', fn ($q) => $q->where('status', 1));
            },
            'followings' => function (Builder $query) {
                $query->whereHas('target', fn ($q) => $q->where('status', 1));
            },*/
            'friends' => function (Builder $query) {
                $query->whereHas('target', fn ($q) => $q->where('status', 1));
            },
        ];

        /**
         * load notification count
         */
        if ($user->isID($request->user()->getKey())) {
            $countRelations['notifications'] = function (Builder $query) {
                $query->where('status', 0);
            };
        }

        $user->loadCount($countRelations);

        return [
            'user' => UserResource::make($user)->showProfile()->toArray($request),
        ];
    }
}
