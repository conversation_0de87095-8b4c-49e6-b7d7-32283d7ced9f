<?php

namespace App\Actions\User;

use App\Events\FriendInvited;
use App\Models\User;
use App\Models\UserRelation;

class InviteFriend
{
    /**
     * @param User $user
     * @param User $friend
     * @return void
     */
    public function execute(User $user, User $friend)
    {
        /** @var UserRelation $record */
        $record = $user->relationship()->firstOrNew([
            'user_id' => $user->getKey(),
            'target_id' => $friend->getKey(),
        ]);

        if (! $record->isFriend()) {
            $record->fill([
                'is_friend' => 2, // 2 = pending friend
            ]);

            $record->save();
        }

        FriendInvited::dispatch($user, $friend);
    }
}
