<?php

namespace App\Actions\User;

use App\Models\Device;
use App\Models\User;

class AddDeviceToken
{
    /**
     * @param User $user
     * @param string $deviceToken
     * @return void
     */
    public function execute(User $user, string $deviceToken)
    {
        /** @var Device $device */
        $device = Device::query()->firstOrNew([
            'token' => $deviceToken,
        ]);

        $device->fill([
            'user_id' => $user->getKey(),
            'token_id' => $user->currentTokenId(),
            'status' => 1,
        ])->save();
    }
}
