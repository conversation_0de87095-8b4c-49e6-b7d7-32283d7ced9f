<?php

namespace App\Actions\User;

use App\Events\FriendAdded;
use App\Models\User;
use App\Models\UserRelation;

class AddFriend
{
    /**
     * @param User $user
     * @param User $friend
     * @return void
     */
    public function execute(User $user, User $friend)
    {
        $this->_addFriend($user, $friend);
        $this->_addFriend($friend, $user);
    }

    /**
     * @param User $user
     * @param User $friend
     * @return void
     */
    protected function _addFriend(User $user, User $friend)
    {
        /** @var UserRelation $record */
        $record = $user->relationship()->firstOrNew([
            'user_id' => $user->getKey(),
            'target_id' => $friend->getKey(),
        ]);

        $isNewFriend = is_null($record->friend_at);

        $now = now('UTC')->toDateTimeString();
        $record->fill([
            'is_friend' => 1,
            'friend_at' => $now,
            'is_follow' => 1,
            'followed_at' => $now,
        ]);

        $record->save();

        FriendAdded::dispatch($user, $friend, $isNewFriend);
    }
}
