<?php

namespace App\Actions\User;

use App\Http\Requests\Api\User\UpdatePhoneNumberRequest;
use App\Models\User;

class UpdatePhoneNumber
{
    /**
     * @param UpdatePhoneNumberRequest $request
     * @return User
     */
    public function execute(UpdatePhoneNumberRequest $request): User
    {
        /** @var User $user */
        $user = $request->user();

        $user->updateQuietly($request->only([
            'phone',
        ]));

        return $user;
    }
}
