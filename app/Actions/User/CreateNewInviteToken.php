<?php

namespace App\Actions\User;

use App\Models\InviteToken;
use App\Models\User;
use Illuminate\Support\Str;

class CreateNewInviteToken
{
    /**
     * @param User $user
     * @return InviteToken
     */
    public function excute(User $user)
    {
        return InviteToken::query()->create([
            'user_id' => $user->getKey(),
            'token' => $this->generateToken(),
            'status' => 0,
        ]);
    }

    /**
     * @return string
     */
    protected function generateToken(): string
    {
        return (string) Str::uuid7();
    }
}
