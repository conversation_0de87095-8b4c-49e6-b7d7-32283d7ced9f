<?php

namespace App\Actions\User;

use App\Events\UserWasDeleted;
use App\Models\User;
use Illuminate\Support\Str;

class DeleteUser
{
    /**
     * @param User $user
     * @return void
     */
    public function execute(User $user)
    {
        if (! $user->isSuperAdmin()) {
            /**
             * remove all tokens
             */
            $user->tokens()->delete();

            /**
             * update state
             */
            $user->updateQuietly([
                'phone' => $user->phone . '_' . Str::random(8),
                'status' => 0,
            ]);

            UserWasDeleted::dispatch($user);
        }
    }
}
