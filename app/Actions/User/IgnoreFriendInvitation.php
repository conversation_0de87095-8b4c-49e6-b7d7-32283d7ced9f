<?php

namespace App\Actions\User;

use App\Events\FriendInvitationIgnored;
use App\Models\User;

class IgnoreFriendInvitation
{
    /**
     * @param User $user
     * @param User $friend
     * @return void
     */
    public function execute(User $user, User $friend)
    {
        $invitation = $friend->relationship()
            ->where([
                'target_id' => $user->getKey(),
                'is_friend' => 2,
            ])
            ->first();

        $invitation?->update([
            'is_friend' => 0,
        ]);

        FriendInvitationIgnored::dispatch($user, $friend);
    }
}
