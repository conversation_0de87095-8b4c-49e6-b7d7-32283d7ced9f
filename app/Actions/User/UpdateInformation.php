<?php

namespace App\Actions\User;

use App\Enums\Gender;
use App\Enums\ProfileType;
use App\Enums\QuestType;
use App\Events\UserChangedSchool;
use App\Events\UserRegisteredViaReferrer;
use App\Http\Requests\Api\User\UpdateInformationRequest;
use App\Models\User;
use App\Models\UserProfile;
use Illuminate\Validation\ValidationException;

class UpdateInformation
{
    /**
     * @param UpdateInformationRequest $request
     * @return User
     * @throws ValidationException
     */
    public function execute(UpdateInformationRequest $request): User
    {
        $attributes = $request->only([
            'name',
            'position',
            'birthday',
        ]);

        /**
         * sync gender
         */
        if ($gender = $request->post('gender')) {
            $gender = Gender::tryFrom($gender);
            $attributes['gender'] = $gender->toInt();
        }

        /** @var User $user */
        $user = $request->user();
        $isStudent = $user->profile_type === ProfileType::STUDENT->value;

        $profile = $user->profile;
        $oldSchoolId = $isStudent ? (int) $profile?->school_id : 0;

        /**
         * check for upload avatar
         */
        $oldAvatar = null;
        if ($avatar = $request->file('avatar')) {
            /**
             * avatar upload error
             */
            $avatarUrl = storage()->uploadImage($avatar);
            if (! $avatarUrl) {
                $request->setError('avatar', __('An error occurred while uploading the avatar.'));
            }

            $attributes['profile_photo_path'] = $avatarUrl;
            $oldAvatar = $user->profile_photo_path;
        }

        $careerReward = empty($user->profile_type) || ($isStudent && $user->isVerified());
        $profilePayload = [];

        /**
         * check for update profile (career)
         */
        $type = $request->post('profile_type');
        if ($type && in_array($type, ProfileType::toArray())) {
            $attributes['profile_type'] = $type;

            $profilePayload = match($type) {
                ProfileType::STUDENT->value => ['school_id'],
                ProfileType::EMPLOYEE->value => ['work', 'expert', 'service_in_charge'],
                default => [],
            };

            $profilePayload = [
                ...[
                    'school_id' => null,
                    'work' => null,
                    'expert' => null,
                    'service_in_charge' => null,
                ],
                ...$request->only($profilePayload),
            ];
        }

        /**
         * update sở thích
         */
        if ($brief = $request->post('brief')) {
            $profilePayload['brief'] = $brief;
        }

        /**
         * mark user as active
         */
        $referrer = false;
        $forceApplyCareerReward = false;
        $genderReward = false;
        if ($user->isVerified()) {
            $attributes['status'] = 1;
            $referrer = ! empty($user->referrer_id);
            $forceApplyCareerReward = $isStudent;
            $genderReward = ! $user->emptyGender();
        }

        /**
         * tự động tạo profile cho user mới
         */
        $profile ??= UserProfile::query()->firstOrCreate(['user_id' => $user->getKey()], $profilePayload);
        $isNewProfile = $profile->wasRecentlyCreated;
        $newSchoolId = $type === ProfileType::STUDENT->value ? (int) $request->post('school_id') : 0;

        if ($user->updateQuietly($attributes)) {
            /**
             * clear old avatar
             */
            if (! is_null($oldAvatar)) {
                storage()->removeMedia($oldAvatar);
            }

            /**
             * sync profile
             */
            if ($profilePayload) {
                $questHelper = questHelper();

                $hasBrief = !empty($profilePayload['brief']);
                $hasCareer = $forceApplyCareerReward || !empty($profilePayload['school_id']) || !empty($profilePayload['work']);

                /**
                 * new profile
                 */
                if ($isNewProfile) {
                    if ($hasBrief) {
                        $questHelper->applyQuestReward($user, QuestType::HOBBY->value);
                    }

                    if ($hasCareer) {
                        $questHelper->applyQuestReward($user, QuestType::CAREER->value);
                    }
                }

                /**
                 * update profile
                 */
                else {
                    if (empty($profile->brief) && $hasBrief) {
                        $questHelper->applyQuestReward($user, QuestType::HOBBY->value);
                    }

                    if ($careerReward && $hasCareer) {
                        $questHelper->applyQuestReward($user, QuestType::CAREER->value);
                    }

                    $profile->updateQuietly($profilePayload);
                }
            }

            /**
             * sync point & invite count
             */
            if ($referrer) {
                UserRegisteredViaReferrer::dispatch($user);
            }

            /**
             * kiểm tra ghi đè (thoát)
             * thông tin trường học (cộng đồng) cũ
             */
            if ($forceApplyCareerReward || ($oldSchoolId !== $newSchoolId)) {
                $override = $forceApplyCareerReward ?: (bool) $request->post('override_school');
                UserChangedSchool::dispatch($user, $override, $oldSchoolId, $request->getNewSchool());
            }

            /**
             * thưởng coin trong trường hợp cập nhật giới tính
             */
            /*if ($genderReward) {
                questHelper()->applyQuestReward($user, QuestType::GENDER->value);
            }*/
        }

        return $user;
    }
}
