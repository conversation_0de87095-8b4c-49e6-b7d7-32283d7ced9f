<?php

namespace App\Actions\User;

use App\Actions\Reaction\CreateNewNotification;
use App\Enums\QuestType;
use App\Models\User;
use App\Models\UserProfile;
use Illuminate\Support\Arr;

class UpdateHobby
{
    /**
     * @param User $user
     * @param string $hobby
     * @return array
     */
    public function execute(User $user, string $hobby)
    {
        $values = [
            'brief' => $hobby,
        ];

        $profile = UserProfile::query()->firstOrCreate(['user_id' => $user->getKey()], $values);

        $reward = $profile->wasRecentlyCreated;
        if (! $profile->wasRecentlyCreated) {
            $reward = empty($profile->brief);

            $profile->updateQuietly($values);
        }

        $amount = 0;
        $unit = 'coin';

        $type = QuestType::HOBBY->value;
        $questHelper = questHelper();

        $quest = $questHelper->getQuestByType($type);
        if ($quest) {
            $amount = (int) Arr::get($quest, 'amount', 0);
        }

        if ($reward && $amount) {
            $questHelper->applyQuestReward($user, $type, $amount);

            /**
             * thông báo cho người dùng về việc nhận phần thưởng
             * khi hoàn thành lần đầu thông profile
             */
            $notificationCreator = app(CreateNewNotification::class);
            $notificationCreator->execute($user, [
                'type' => 21,
            ]);
        }

        return [$amount, $unit];
    }
}
