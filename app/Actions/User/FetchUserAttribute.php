<?php

namespace App\Actions\User;

use App\Models\Traits\IgnoreViewedPost;
use App\Models\Traits\QAPosts\UserAnswerLikedPost;
use App\Models\Traits\QAPosts\UserSimilarPost;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;

class FetchUserAttribute
{
    use IgnoreViewedPost;
    use UserAnswerLikedPost;
    use UserSimilarPost;

    /**
     * @var Builder
     */
    protected $queryBuilder;

    /**
     * @return Builder
     */
    protected function getQueryBuilder()
    {
        if (! $this->queryBuilder) {
            $this->queryBuilder = postRepository()->query();
        }

        return $this->queryBuilder->clone();
    }

    /**
     * @param User $user
     * @return array
     */
    public function execute(User $user)
    {
        return [
            'similar' => $this->getSimilarData($user),
            'like' => $this->getLikeData($user),
        ];
    }

    /**
     * @param User $user
     * @return array
     */
    protected function getSimilarData(User $user)
    {
        $records = $this->getTopSimilarUserPerDayForStats($user)->get();

        $data = [];
        $ids = [];
        $date = null;
        $enoughData = false;
        foreach ($records as $record) {
            $record = (array) $record;
            $userId = (int) Arr::get($record, 'user_id');

            $date = Arr::get($record, 'view_date');
            $data[$date][] = $userId;

            if (! isset($ids[$userId])) {
                $ids[$userId] = 1;
            }

            if (count($ids) >= 50) {
                $enoughData = true;
                break;
            }
        }

        if ($enoughData && $date) {
            Cache::forever('limit_similar_date_filter_for_user_' . $user->getKey(), $date);
        }

        return [
            'data' => $data,
            'ids' => array_keys($ids),
            'posts' => $this->getUserSimilarPostsForTest($user, -1)->pluck('post_id')->toArray(),
        ];
    }

    /**
     * @param User $user
     * @return array
     */
    protected function getLikeData(User $user)
    {
        $records = $this->getTopLikedUserPerDayForStats($user)->get();

        $data = [];
        $ids = [];
        $date = null;
        $enoughData = false;
        foreach ($records as $record) {
            $record = (array) $record;
            $userId = (int) Arr::get($record, 'user_id');

            $date = Arr::get($record, 'react_date');
            $data[$date][] = $userId;

            if (! isset($ids[$userId])) {
                $ids[$userId] = 1;
            }

            if (count($ids) >= 15) {
                $enoughData = true;
                break;
            }
        }

        if ($enoughData && $date) {
            Cache::forever('limit_liked_date_filter_for_user_' . $user->getKey(), $date);
        }

        return [
            'data' => $data,
            'ids' => array_keys($ids),
            'posts' => $this->getUserAnswerLikedPostsForTest($user, -1)->pluck('post_id')->toArray(),
        ];
    }
}
