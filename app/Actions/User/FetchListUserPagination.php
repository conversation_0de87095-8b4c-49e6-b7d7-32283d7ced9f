<?php

namespace App\Actions\User;

use App\Repositories\UserRepository;
use Illuminate\Http\Request;

class FetchListUserPagination
{
    /**
     * @param UserRepository $repository
     */
    public function __construct(protected UserRepository $repository)
    {

    }

    /**
     * @param Request $request
     * @param int $perPage
     * @param int $currentPage
     * @param int $offset
     * @return \Illuminate\Pagination\LengthAwarePaginator
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function execute(Request $request, int $perPage, int $currentPage, int $offset = 0)
    {
        $payload = array_merge($request->only(['search', 'id']), [
            'count' => 'answers',
            'orderBy' => 'user_id',
            'orderDirection' => 'desc',
        ]);

        return $this->repository->pagination($payload, $perPage, $currentPage, $offset);
    }
}
