<?php

namespace App\Actions\User;

use App\Enums\ProfileType;
use App\Enums\QuestType;
use App\Events\CareerUpdated;
use App\Events\UserChangedSchool;
use App\Http\Requests\Api\User\UpdateCareerRequest;
use App\Models\User;
use App\Models\UserProfile;
use Illuminate\Support\Arr;

class UpdateCareer
{
    /**
     * @param UpdateCareerRequest $request
     * @return array
     */
    public function execute(UpdateCareerRequest $request)
    {
        /** @var User $user */
        $user = $request->user();
        $userId =(int) $user->getKey();

        /**
         * chỉ có giải thưởng khi trước đó chưa cập nhật thông tin profile (type)
         */
        $reward = empty($user->profile_type);
        $hasPrevSchool = $user->profile_type === ProfileType::STUDENT->value;

        $profile = $user->profile;
        $oldSchoolId = $hasPrevSchool ? (int) $profile?->school_id : 0;

        /**
         * update user profile type
         */
        $profileType = $request->post('type');
        $user->updateQuietly([
            'profile_type' => $profileType,
        ]);

        /**
         * update career
         */
        $values = match($user->profile_type) {
            ProfileType::STUDENT->value => ['school_id'],
            ProfileType::EMPLOYEE->value => ['work', 'expert', 'service_in_charge'],
            default => [],
        };

        $values = [
            ...[
                'school_id' => null,
                'work' => null,
                'expert' => null,
                'service_in_charge' => null,
            ],
            ...$request->only($values),
        ];

        $profile ??= UserProfile::query()->firstOrCreate(['user_id' => $userId], $values);
        $newSchoolId = $profileType === ProfileType::STUDENT->value ? (int) $request->post('school_id') : 0;

        /**
         * cập nhật thông tin nghề nghiệp
         */
        if (! $profile->wasRecentlyCreated) {
            $profile->updateQuietly($values);
        }

        $amount = 0;
        $unit = 'coin';

        $type = QuestType::CAREER->value;
        $questHelper = questHelper();

        $quest = $questHelper->getQuestByType($type);
        if ($quest) {
            $amount = (int) Arr::get($quest, 'amount', 0);
        }

        if ($reward && $amount) {
            CareerUpdated::dispatch($user);
        }

        /**
         * kiểm tra ghi đè (thoát)
         * thông tin trường học (cộng đồng) cũ
         */
        if ($oldSchoolId !== $newSchoolId) {
            UserChangedSchool::dispatch($user, (bool) $request->post('override_school'), $oldSchoolId, $request->getNewSchool());
        }

        return [$user, $amount, $unit];
    }
}
