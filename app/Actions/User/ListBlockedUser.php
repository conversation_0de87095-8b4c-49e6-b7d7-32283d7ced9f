<?php

namespace App\Actions\User;

use App\Models\User;
use Illuminate\Database\Query\Builder;

class ListBlockedUser
{
    /**
     * @param User $user
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function execute(User $user)
    {
        return User::query()
            ->whereExists(function (Builder $query) use ($user) {
                $query->from('user_relations')
                    ->where('user_id', $user->getKey())
                    ->where('is_blocked', 1)
                    ->whereColumn('users.user_id', 'user_relations.target_id');
            })
            ->where('status', 1)
            ->get();
    }
}
