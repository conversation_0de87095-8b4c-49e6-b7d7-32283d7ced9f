<?php

namespace App\Actions\User;

use App\Models\Notification;

class MarkNotificationWasRead
{
    /**
     * @param Notification|null $notification
     * @return void
     */
    public function execute(?Notification $notification = null)
    {
        if (! $notification) {
            return;
        }

        $notification->timestamps = false;
        $notification->fill([
            'status' => 1,
        ]);

        $notification->save();
    }
}
