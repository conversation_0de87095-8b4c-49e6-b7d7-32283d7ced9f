<?php

namespace App\Actions\User;

use App\Models\User;
use Illuminate\Database\Query\Builder;

class ListPendingFriend
{
    /**
     * @param User $user
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function execute(User $user)
    {
        return User::query()
            ->whereExists(function (Builder $query) use ($user) {
                $query->from('user_relations')
                    ->where('target_id', $user->getKey())
                    ->where('is_friend', 2) // 2 = pending
                    ->whereColumn('users.user_id', 'user_relations.user_id');
            })
            ->where('status', 1)
            ->get();
    }
}
