<?php

namespace App\Actions\User;

use App\Events\FriendInvitationCanceled;
use App\Models\User;

class CancelFriendInvitation
{
    /**
     * @param User $user
     * @param User $friend
     * @return void
     */
    public function execute(User $user, User $friend)
    {
        $invitation = $user->relationship()
            ->where([
                'target_id' => $friend->getKey(),
                'is_friend' => 2,
            ])
            ->first();

        $invitation?->update([
            'is_friend' => 0,
        ]);

        FriendInvitationCanceled::dispatch($user, $friend);
    }
}
