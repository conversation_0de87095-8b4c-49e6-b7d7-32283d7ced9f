<?php

namespace App\Actions\User;

use App\Models\User;

class UnblockUser
{
    /**
     * @param User $user
     * @param int $blockedUserId
     * @return void
     */
    public function execute(User $user, int $blockedUserId)
    {
        $user->relationship()
            ->where('target_id', $blockedUserId)
            ->update([
                'is_blocked' => 0,
                'blocked_at' => null,
            ]);

        blockHelper()->clearCache($user->getKey(), $blockedUserId);
    }
}
