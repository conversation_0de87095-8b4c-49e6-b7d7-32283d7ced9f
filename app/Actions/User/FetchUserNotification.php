<?php

namespace App\Actions\User;

use App\Models\Community;
use App\Models\Notification;
use App\Models\Post;
use App\Models\PostAnswer;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;

class FetchUserNotification
{
    /**
     * @param User $user
     * @param int $perPage
     * @param int $offset
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function execute(User $user, int $perPage = 10, int $offset = 0)
    {
        return Notification::query()
            ->with([
                'object',
                'reaction',
                'reactionUser',
                'users' => fn ($q) => $q->where('status', 1)->limit(3)->orderByDesc('updated_at'),
                'users.user',
            ])
            ->withCount([
                'users as user_count' => fn ($q) => $q->where('status', 1),
            ])
            ->joinSub(
                Notification::query()
                    ->from('notifications')
                    ->select('id')
                    ->where([
                        'user_id' => $user->getKey(),
                        'active' => 1,
                    ])
                    ->where(function (Builder $query) {
                        $query->whereNull('object_type')
                            ->orWhereHasMorph('object', [Post::class, PostAnswer::class], fn ($q) => $q->where('status', 1))
                            ->orWhereHasMorph('object', [Community::class], fn ($q) => $q->where('status', '!=', 0));
                    })
                    ->where(function (Builder $query) {
                        $query->whereNull('reaction_user_id')
                            ->orWhereHas('reactionUser', fn ($q) => $q->where('status', 1));
                    })
                    ->limit($perPage + 1)
                    ->offset($offset)
                    ->orderByDesc('updated_at')
                    ->orderByDesc('id'),
                'tmp',
                function (JoinClause $join) {
                    $join->on("tmp.id", '=', "notifications.id");
                }
            )
            ->orderByDesc('notifications.updated_at')
            ->orderByDesc('notifications.id')
            ->get();
    }

    /**
     * @param User $user
     * @param int $perPage
     * @param int $offset
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPinNotifications(User $user, int $perPage = 10, int $offset = 0)
    {
        return Notification::query()
            ->with([
                'object',
            ])
            ->joinSub(
                Notification::query()
                    ->from('notifications')
                    ->select('id')
                    ->where([
                        'user_id' => $user->getKey(),
                        'type' => 10,
                        'active' => 1,
                    ])
                    ->whereHasMorph('object', [Post::class], function (Builder $query) {
                        $query->where('status', 1);
                    })
                    ->where(function (Builder $query) {
                        $query->whereNull('reaction_user_id')
                            ->orWhereHas('reactionUser', fn ($q) => $q->where('status', 1));
                    })
                    ->limit($perPage + 1)
                    ->offset($offset)
                    ->orderByDesc('updated_at')
                    ->orderByDesc('id'),
                'tmp',
                function (JoinClause $join) {
                    $join->on("tmp.id", '=', "notifications.id");
                }
            )
            ->orderByDesc('notifications.updated_at')
            ->orderByDesc('notifications.id')
            ->get();
    }
}
