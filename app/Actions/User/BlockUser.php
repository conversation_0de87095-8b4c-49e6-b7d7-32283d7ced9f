<?php

namespace App\Actions\User;

use App\Events\UserBlocked;
use App\Models\User;
use App\Models\UserRelation;

class BlockUser
{
    /**
     * @param User $user
     * @param int $blockUserId
     * @return void
     */
    public function execute(User $user, int $blockUserId)
    {
        /** @var UserRelation $record */
        $record = $user->relationship()->firstOrNew([
            'user_id' => $user->getKey(),
            'target_id' => $blockUserId,
        ]);

        $record->fill([
            'is_blocked' => 1,
            'blocked_at' => now('UTC')->toDateTimeString(),
        ]);

        $record->save();

        UserBlocked::dispatch($user, $blockUserId);
    }
}
