<?php

namespace App\Actions\User;

use App\Models\User;
use App\Models\UserProfile;
use Illuminate\Support\Arr;
use <PERSON><PERSON>\Scout\EngineManager;
use <PERSON><PERSON>\Scout\Engines\TypesenseEngine;
use Typesense\MultiSearch;

class FetchRecommendation
{
    /**
     * @param string $question
     * @return string
     */
    protected function getProfileQuery(string $question)
    {
        return implode("\n", [
            __('Instruct: Given a web search query, retrieve relevant passages that can answer or are similar to it.'),
            'Query: ' . $question,
        ]);
    }

    /**
     * @param User $user
     * @param string $question
     * @param int $inCommunityId
     * @return array|\Illuminate\Database\Eloquent\Collection
     * @throws \Http\Client\Exception
     * @throws \Typesense\Exceptions\TypesenseClientError
     */
    public function execute(User $user, string $question, int $inCommunityId = 0)
    {
        if (! $question || ! ($userIds = $this->search($user, $question, $inCommunityId))) {
            return [];
        }

        $currentUserId = $user->getKey();
        $blockedIds = blockHelper()->getBlockedIds($currentUserId);
        $remainingIds = array_diff($userIds, $blockedIds);

        return User::query()
            ->where('user_id', '!=', $currentUserId)
            ->when($remainingIds, fn ($q, $ids) => $q->whereIn('user_id', $ids))
            ->where('status', 1)
            ->orderByRaw('FIELD(`user_id`, ' .implode(',', $remainingIds). ')')
            ->get();
    }

    /**
     * @param User $user
     * @param string $question
     * @param int $communityId
     * @return array
     * @throws \Http\Client\Exception
     * @throws \Typesense\Exceptions\TypesenseClientError
     * @noinspection PhpUndefinedMethodInspection
     */
    protected function search(User $user, string $question, int $communityId = 0)
    {
        /** @var TypesenseEngine $typesenseEngine */
        $typesenseEngine = app(EngineManager::class)->engine();

        /** @var MultiSearch $search */
        $search = $typesenseEngine->getMultiSearch();

        /** @var UserProfile $profileModel */
        $profileModel = app(UserProfile::class);

        $searchParams = [
            'prefix' => false,
            'collection' => $profileModel->searchableAs(),
            'query_by' => 'profile_embedding',
            'q' => $this->getProfileQuery($question),
            'sort_by' => '_vector_distance:asc',
            'exclude_fields' => 'profile_embedding',
            'vector_query' => 'profile_embedding:([], distance_threshold:0.25, k:10)',
        ];

        /**
         * filter theo users trong cùng cộng đồng
         */
        $filterBy = '';
        if ($communityId > 0) {
            $filterBy = "community_ids:=[$communityId]";
        }

        /**
         * filter users who have same gender as current user
         */
        if ($user->isMale() || $user->isFemale()) {
            if ($filterBy) {
                $filterBy .= ' && ';
            }

            $filterBy .= 'gender:=' . $user->gender;
        }

        if ($filterBy) {
            $searchParams['filter_by'] = $filterBy;
        }

        $results = $search->perform([
            'searches' => [
                $searchParams,
            ],
        ]);

        [$users] = Arr::get($results, 'results');

        $userIds = [];
        if ($users = Arr::get($users, 'hits')) {
            $userIds = Arr::pluck($users, 'document.id');
        }

        return $userIds;
    }
}
