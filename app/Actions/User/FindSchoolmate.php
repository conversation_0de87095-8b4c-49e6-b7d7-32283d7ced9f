<?php

namespace App\Actions\User;

use App\Enums\ProfileType;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

class FindSchoolmate
{
    /**
     * @param int $schoolId
     * @param int $currentUserId
     * @return \Illuminate\Support\Collection
     */
    public function execute(int $schoolId, int $currentUserId = 0)
    {
        $users = collect();

        $result = User::query()
            ->where([
                'status' => 1,
                'profile_type' => ProfileType::STUDENT->value,
            ])
            ->where('user_id', '!=', $currentUserId)
            ->when(blockHelper()->getBlockedIds($currentUserId), fn ($q, $blockedIds) => $q->whereNotIn('user_id', $blockedIds))
            ->whereHas('profile', fn (Builder $q) => $q->where('school_id', $schoolId))
            ->lazyById(200, 'user_id');

        foreach ($result as $user) {
            $users->push($user);
        }

        return $users->sortBy([
            ['name', 'asc'],
            ['user_id', 'asc'],
        ]);
    }
}
