<?php

namespace App\Actions\User;

use App\Models\User;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Http\Request;
use Laravel\Sanctum\PersonalAccessToken;

class LogoutUser
{
    /**
     * @param StatefulGuard $guard
     */
    public function __construct(protected StatefulGuard $guard)
    {

    }

    /**
     * @param Request $request
     * @return void
     */
    public function execute(Request $request)
    {
        /** @var User $user */
        $user = $request->user();

        /**
         * check api token
         * @var PersonalAccessToken $currentToken
         */
        if (($currentToken = $user->currentAccessToken()) && $currentToken instanceof PersonalAccessToken) {
            /**
             * disable token device
             */
            $currentTokenId = $currentToken->getKey();
            $user->devices()
                ->where('token_id', $currentTokenId)
                ->update([
                    'status' => 0,
                ]);

            if (config('app.debug')) {
                chatWork()->report([
                    'message' => 'Debug user logout',
                    'data' => [
                        'user_id' => $user->getKey(),
                        'current_token_id' => $currentTokenId,
                    ],
                ]);
            }

            /**
             * remove current token
             */
            $currentToken->delete();
        }

        $this->guard->logout();

        if ($request->hasSession()) {
            $request->session()->invalidate();
            $request->session()->regenerateToken();
        }
    }
}
