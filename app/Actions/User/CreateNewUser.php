<?php

namespace App\Actions\User;

use Illuminate\Support\Str;
use Lara<PERSON>\Fortify\Contracts\CreatesNewUsers;

class CreateNewUser implements CreatesNewUsers
{
    /**
     * @param array $input
     * @return \App\Models\User|\Illuminate\Foundation\Auth\User
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function create(array $input)
    {
        return userRepository()->create(array_merge([
            'uuid' => (string) Str::uuid7(),
            'name' => '',
            'email' => null,
            'password' => null,
            'total_coin' => 0,
            'total_point' => 0,
        ], $input));
    }
}
