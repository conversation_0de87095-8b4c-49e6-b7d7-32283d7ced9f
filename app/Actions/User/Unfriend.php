<?php

namespace App\Actions\User;

use App\Events\Unfriended;
use App\Models\User;
use App\Models\UserRelation;

class Unfriend
{
    /**
     * @param User $user
     * @param User $friend
     * @return void
     */
    public function execute(User $user, User $friend)
    {
        $this->_unfriend($user, $friend);
        $this->_unfriend($friend, $user);
    }

    /**
     * @param User $user
     * @param User $friend
     * @return void
     */
    protected function _unfriend(User $user, User $friend)
    {
        /** @var UserRelation $record */
        $record = $user->relationship()->firstOrNew([
            'user_id' => $user->getKey(),
            'target_id' => $friend->getKey(),
        ]);

        $record->fill([
            'is_friend' => 0,
            'is_follow' => 0,
        ]);

        $record->save();

        Unfriended::dispatch($user, $friend);
    }
}
