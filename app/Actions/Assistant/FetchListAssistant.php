<?php

namespace App\Actions\Assistant;

use App\Models\Assistant;
use Illuminate\Support\Arr;
use <PERSON><PERSON>\Scout\Engines\TypesenseEngine;
use Typesense\Documents;

class FetchListAssistant
{
    /**
     * @param array $payload
     * @param int $perPage
     * @param int $currentPage
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function execute(array $payload, int $perPage = 10, int $currentPage = 1)
    {
        $query = when(Arr::get($payload, 'search'), function ($search) {
            return implode("\n", [
                //'Instruct: Based on the user\'s work profile, recommend individuals who are qualified to answer the following question.',
                //'Query: ' . $search,
                'Instruct: Given a web search query, retrieve relevant passages that answer the query',
                'Query: ' . $search,
            ]);
        }, '*');

        $multiple = false;
        $language = Arr::get($payload, 'language', 'vi');

        return Assistant::search(
            $query,
            function (Documents $search, string $query, array $options) use ($multiple) {
                $extra = [
                    'prefix' => false,
                    'exclude_fields' => 'embedding',
                ];

                if ($query !== '*') {
                    $extra['sort_by'] = '_vector_distance:asc';

                    /*if ($multiple) {
                        return $this->multipleSearch($query, [
                            ...$options,
                            ...$extra,
                        ]);
                    }*/

                    $extra['vector_query'] = 'embedding:([], distance_threshold:0.2, k:5, ef:100)';
                }

                return $search->search([
                    ...$options,
                    ...$extra,
                ]);
            })
            ->where('language', $language)
            ->paginate($perPage, 'page', $currentPage);
    }

    /**
     * @param string $query
     * @param array $payload
     * @return array|\ArrayAccess|mixed
     * @noinspection PhpUndefinedMethodInspection
     */
    protected function multipleSearch(string $query, array $payload)
    {
        /** @var Assistant $model */
        $model = app(Assistant::class);

        /** @var TypesenseEngine $engine */
        $engine = $model->searchableUsing();
        $embeddingData = [];

        $payload = [
            'searches' => [
                [
                    ...$payload,
                    ...[
                        'q' => '*',
                        'query_by' => '',
                        'vector_query' => 'voyage_embedding:([' .implode(',', $embeddingData). '], distance_threshold:0.72, k:5, ef:100)',
                    ],
                ],
            ],
        ];

        $results = $engine->getMultiSearch()->perform($payload, [
            'collection' => $model->searchableAs(),
        ]);

        return Arr::get($results, 'results.0');
    }
}
