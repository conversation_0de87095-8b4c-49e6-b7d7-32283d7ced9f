<?php

namespace App\Actions\Survey;

use Illuminate\Support\Facades\DB;

class StoreSortedSurvey
{
    /**
     * @param array $ids
     * @return mixed
     */
    public function execute(array $ids)
    {
        return DB::transaction(function () use ($ids) {
            DB::table('published_surveys')->delete();

            $data = [];
            $index = 1;
            foreach ($ids as $id) {
                $data[] = [
                    'survey_id' => $id,
                    'sort' => $index,
                ];
                $index++;
            }

            DB::table('published_surveys')->insert($data);
        });
    }
}
