<?php

namespace App\Actions\Survey;

use App\Repositories\SurveyRepository;
use Illuminate\Http\Request;

class FetchListSurveyQuestion
{
    public function __construct(protected SurveyRepository $repository)
    {

    }

    /**
     * @param Request $request
     * @return array
     */
    public function execute(Request $request)
    {
        $surveyID = $request->post('survey_id');

        return $this->repository->listSurveyQuestions($surveyID);
    }
}
