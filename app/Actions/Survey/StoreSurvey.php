<?php

namespace App\Actions\Survey;

use App\Enums\QuestionType;
use App\Http\Requests\Web\StoreSurveyRequest;
use App\Models\Question;
use App\Models\QuestionChoice;
use App\Models\Survey;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class StoreSurvey
{
    /**
     * @var StoreSurveyRequest
     */
    protected $request;

    /**
     * @var bool
     */
    protected $isUpdate = false;

    /**
     * @param StoreSurveyRequest $request
     * @return void
     */
    protected function setRequest(StoreSurveyRequest $request)
    {
        $this->request = $request;
    }

    /**
     * @return StoreSurveyRequest
     */
    protected function getRequest()
    {
        return $this->request;
    }

    /**
     * @param bool $state
     * @return void
     */
    protected function setIsUpdateState(bool $state)
    {
        $this->isUpdate = $state;
    }

    /**
     * @param StoreSurveyRequest $request
     * @return Survey
     */
    public function execute(StoreSurveyRequest $request)
    {
        $this->setRequest($request);

        return DB::transaction(function () use ($request) {
            $survey = $request->getSurvey();

            $this->setIsUpdateState($survey->exists);

            $survey->fill([
                'title' => $request->post('title'),
            ]);

            $survey->save();

            /**
             * sync questions
             */
            $this->syncQuestions($survey);

            return $survey;
        });
    }

    /**
     * @param Survey $survey
     * @return void
     */
    protected function syncQuestions(Survey $survey)
    {
        $request = $this->getRequest();
        $surveyId = $survey->getKey();

        /**
         * remove old questions
         */
        $questionIds = $request->getQuestionIds();
        if ($this->isUpdate && ! empty($questionIds)) {
            DB::table('questions')
                ->where('survey_id', $surveyId)
                ->whereNotIn('question_id', $questionIds)
                ->delete();
        }

        $questions = $request->post('questions');
        if (! empty($questions)) {
            array_map(fn ($questionData) => $this->saveQuestion($questionData, $surveyId), $questions);
        }
    }

    /**
     * @param int $questionId
     * @return Question
     */
    protected function getQuestionInstance(int $questionId)
    {
        return $this->getRequest()->getQuestions()->firstWhere('question_id', $questionId) ?? app(Question::class);
    }

    /**
     * @param int $choiceId
     * @return QuestionChoice
     */
    protected function getChoiceInstance(int $choiceId)
    {
        return $this->getRequest()->getChoices()->firstWhere('choice_id', $choiceId) ?? app(QuestionChoice::class);
    }

    /**
     * @param array $questionData
     * @param int $surveyId
     * @return void
     */
    protected function saveQuestion(array $questionData, int $surveyId)
    {
        $questionId = Arr::get($questionData, 'question_id');
        $type = Arr::get($questionData, 'type');

        $question = $this->getQuestionInstance((int) $questionId);
        $question->fill([
            'survey_id' => $surveyId,
            ...Arr::only($questionData, [
                'content',
                'point',
                'type',
                'public',
            ]),
        ]);

        $question->save();

        /**
         * sync choices
         */
        if (in_array($type, [QuestionType::SELECT_BOX->value, QuestionType::CHECK_BOX->value])) {
            $choices = Arr::get($questionData, 'choices');
            $questionId = $question->getKey();

            /**
             * remove old choices
             */
            $choiceIds = Arr::pluck($choices, 'choice_id');
            if ($this->isUpdate && ! empty($choiceIds)) {
                DB::table('question_choices')
                    ->where('question_id', $questionId)
                    ->whereNotIn('choice_id', $choiceIds)
                    ->delete();
            }

            /**
             * save choices
             */
            if (! empty($choices)) {
                array_map(fn ($choiceData) => $this->saveChoice($choiceData, $questionId), $choices);
            }
        }
    }

    /**
     * @param array $choiceData
     * @param int $questionId
     * @return void
     */
    protected function saveChoice(array $choiceData, int $questionId)
    {
        $choiceId = Arr::get($choiceData, 'choice_id');
        $choice = $this->getChoiceInstance((int) $choiceId);
        $choice->fill([
            'question_id' => $questionId,
            'content' => Arr::get($choiceData, 'content'),
        ]);

        $choice->save();
    }
}
