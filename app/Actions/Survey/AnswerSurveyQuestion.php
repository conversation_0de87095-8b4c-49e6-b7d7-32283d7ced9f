<?php

namespace App\Actions\Survey;

use App\Enums\QuestionType;
use App\Http\Requests\Api\Survey\AnswerRequest;
use App\Models\Answer;
use App\Models\User;
use Illuminate\Contracts\Container\BindingResolutionException;

class AnswerSurveyQuestion
{
    /**
     * @param bool $isTextAnswer
     * @param string|null $value
     * @return int
     */
    protected function getPublicStatus(bool $isTextAnswer, ?string $value)
    {
        return $isTextAnswer && $value === 'yes' ? 1 : 0;
    }

    /**
     * @param AnswerRequest $request
     * @return Answer
     * @throws BindingResolutionException
     */
    public function create(AnswerRequest $request)
    {
        /** @var User $user */
        $user = $request->user();
        $question = $request->getQuestion();

        $isTextAnswer = in_array($question->type, [
            QuestionType::TEXT->value,
            QuestionType::TEXT_WITH_STAR->value,
        ]);

        $payload = [
            'user_id' => $user->getKey(),
            'survey_id' => $question->survey_id,
            'question_id' => $question->getKey(),
            'content' => $isTextAnswer ? $request->post('content') : null,
            'star' => $question->type === QuestionType::TEXT_WITH_STAR->value ? (int) $request->post('star') : 0,
            'point' => $question->point,
            'public' => $this->getPublicStatus($isTextAnswer, $request->post('public')),
        ];

        /**
         * create answer
         * @var Answer $answer
         */
        $answer = answerRepository()->create($payload);

        /**
         * create answer choices
         */
        if (! $isTextAnswer) {
            $answerIds = $request->getAnswerIds();
            $answerId = $answer->getKey();

            $answerChoiceData = [];
            foreach ($answerIds as $choiceId) {
                $answerChoiceData[] = [
                    'answer_id' => $answerId,
                    'choice_id' => $choiceId,
                ];
            }

            $answer->choices()->insert($answerChoiceData);
        }

        /**
         * update user point & answer time
         */
        $user->addPoint($question->point);

        return $answer;
    }
}
