<?php

namespace App\Actions\Survey;

use App\Http\Requests\Api\Survey\ListSurveyRequest;
use App\Http\Resources\Api\SurveyResource;
use App\Repositories\SurveyRepository;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class ApiFetchListSurvey
{
    /**
     * @param SurveyRepository $repository
     */
    public function __construct(protected SurveyRepository $repository)
    {

    }

    /**
     * @param ListSurveyRequest $request
     * @return AnonymousResourceCollection
     */
    public function execute(ListSurveyRequest $request)
    {
        $results = $this->repository->listPublishedSurveys($request->user());

        $collect = collect();
        foreach ($results as $result) {
            $collect->add($result->survey);
        }

        return SurveyResource::collection($collect);
    }
}
