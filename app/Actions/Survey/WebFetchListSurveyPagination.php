<?php

namespace App\Actions\Survey;

use App\Repositories\SurveyRepository;
use Illuminate\Http\Request;

class WebFetchListSurveyPagination
{
    /**
     * @param SurveyRepository $repository
     */
    public function __construct(protected SurveyRepository $repository)
    {

    }

    /**
     * @param Request $request
     * @param int $perPage
     * @param int $currentPage
     * @param int $offset
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function execute(Request $request, int $perPage, int $currentPage, int $offset = 0)
    {
        $payload = array_merge($request->only('search'), [
            'with' => [
                'questions',
                'questions.choices',
            ],
        ]);

        return $this->repository->pagination($payload, $perPage, $currentPage, $offset);
    }
}
