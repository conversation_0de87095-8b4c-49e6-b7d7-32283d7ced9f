<?php

namespace App\Contracts\Repositories;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * interface RepositoryInterface
 * @package App\Contracts\Repositories
 */
interface RepositoryInterface
{
    /**
     * @param mixed $id
     * @param array|string $columns
     * @return Model|null
     */
    public function find(mixed $id, array|string $columns = ['*']);

    /**
     * @param array $ids
     * @param array|string $columns
     * @return Collection|Model[]
     */
    public function findMany(array $ids, array|string $columns = ['*']);

    /**
     * @param mixed $id
     * @param array|string $columns
     * @return Model
     */
    public function findOrFail(mixed $id, array|string $columns = ['*']);

    /**
     * @param mixed $id
     * @param array|string $columns
     * @return Model
     */
    public function findOrNew(mixed $id, array|string $columns = ['*']);

    /**
     * @param array $attributes
     * @param array $values
     * @return Model
     */
    public function firstOrNew(array $attributes = [], array $values = []);

    /**
     * @param array $attributes
     * @param array $values
     * @return Model
     */
    public function firstOrCreate(array $attributes = [], array $values = []);

    /**
     * @param array $attributes
     * @param array $values
     * @return Model
     */
    public function updateOrCreate(array $attributes, array $values = []);

    /**
     * @param int $id
     * @param array $payload
     * @return Model
     */
    public function findOrCreate(int $id, array $payload);

    /**
     * @param Model $model
     * @return void
     */
    public function destroy(Model $model): void;

    /**
     * @param Model $model
     * @return void
     */
    public function forceDelete(Model $model): void;
}
