<?php

namespace App\Events;

use App\Models\School;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserChangedSchool
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        protected User $user,
        protected bool $override,
        protected int $oldSchoolId = 0,
        protected ?School $newSchool = null,
    )
    {

    }

    /**
     * @return User
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * @return bool
     */
    public function getOverride()
    {
        return $this->override;
    }

    /**
     * @return int
     */
    public function getOldSchoolId()
    {
        return $this->oldSchoolId;
    }

    /**
     * @return School|null
     */
    public function getNewSchool()
    {
        return $this->newSchool;
    }
}
