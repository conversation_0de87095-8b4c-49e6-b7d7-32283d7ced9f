<?php

namespace App\Events;

use App\Models\PostAnswer;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostAnswerLiked
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        protected User $user,
        protected PostAnswer $postAnswer,
        protected int $reactionId,
    )
    {

    }

    /**
     * @return User
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * @return PostAnswer
     */
    public function getPostAnswer()
    {
        return $this->postAnswer;
    }

    /**
     * @return int
     */
    public function getReactionId()
    {
        return $this->reactionId;
    }
}
