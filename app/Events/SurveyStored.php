<?php

namespace App\Events;

use App\Models\Survey;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SurveyStored
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(protected Survey $survey)
    {

    }

    /**
     * @return Survey
     */
    public function getSurvey()
    {
        return $this->survey;
    }
}
