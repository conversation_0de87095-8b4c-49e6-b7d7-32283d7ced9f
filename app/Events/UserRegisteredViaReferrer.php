<?php

namespace App\Events;

use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserRegisteredViaReferrer
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(protected User $user)
    {

    }

    /**
     * @return User
     */
    public function getUser()
    {
        return $this->user;
    }
}
