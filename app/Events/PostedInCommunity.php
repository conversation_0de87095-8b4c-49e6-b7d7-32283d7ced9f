<?php

namespace App\Events;

use App\Models\CommunityPost;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostedInCommunity
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(protected CommunityPost $communityPost)
    {

    }

    /**
     * @return CommunityPost
     */
    public function getCommunityPost()
    {
        return $this->communityPost;
    }
}
