<?php

namespace App\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ContentReported
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(protected $reportedObject)
    {

    }

    /**
     * @return mixed
     */
    public function getReportedObject()
    {
        return $this->reportedObject;
    }
}
