<?php

namespace App\Events;

use App\Models\PostAnswer;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostAnswerStoredInCommunity
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(protected User $user, protected PostAnswer $postAnswer)
    {
        //
    }

    /**
     * @return User
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * @return PostAnswer
     */
    public function getPostAnswer()
    {
        return $this->postAnswer;
    }
}
