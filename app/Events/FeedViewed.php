<?php

namespace App\Events;

use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class FeedViewed
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(protected User $user, protected string $feed)
    {

    }

    /**
     * @return User
     */
    public function getViewedUser()
    {
        return $this->user;
    }

    /**
     * @return string
     */
    public function getFeed()
    {
        return $this->feed;
    }
}
