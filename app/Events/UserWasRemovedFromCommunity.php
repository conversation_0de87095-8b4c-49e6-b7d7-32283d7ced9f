<?php

namespace App\Events;

use App\Models\Community;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserWasRemovedFromCommunity
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(protected Community $community, protected int $removedUserId)
    {

    }

    /**
     * @return Community
     */
    public function getCommunity()
    {
        return $this->community;
    }

    /**
     * @return int
     */
    public function getRemovedUserId()
    {
        return $this->removedUserId;
    }
}
