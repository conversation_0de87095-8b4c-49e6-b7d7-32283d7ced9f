<?php

namespace App\Events;

use App\Models\PostAnswer;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostAnswerDeleted
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(protected PostAnswer $answer)
    {

    }

    /**
     * @return PostAnswer
     */
    public function getPostAnswer()
    {
        return $this->answer;
    }
}
