<?php

namespace App\Events;

use App\Models\Community;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CommunityDeleted
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(protected Community $community)
    {

    }

    /**
     * @return Community
     */
    public function getCommunity()
    {
        return $this->community;
    }
}
