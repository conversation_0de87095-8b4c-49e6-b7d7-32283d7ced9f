<?php

namespace App\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UpdateSearchableProfile
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(protected int $userId)
    {

    }

    /**
     * @return int
     */
    public function getUserId()
    {
        return $this->userId;
    }
}
