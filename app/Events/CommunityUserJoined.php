<?php

namespace App\Events;

use App\Models\Community;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CommunityUserJoined
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(protected Community $community, protected User $communityUser, protected int $reactionUserId)
    {

    }

    /**
     * @return User
     */
    public function getCommunityUser()
    {
        return $this->communityUser;
    }

    /**
     * @return int
     */
    public function getReactionUserId()
    {
        return $this->reactionUserId;
    }

    /**
     * @return Community
     */
    public function getCommunity()
    {
        return $this->community;
    }
}
