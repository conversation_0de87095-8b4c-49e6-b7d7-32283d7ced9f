<?php

namespace App\Events;

use App\Models\PostAnswer;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostAnswerVoted
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(protected PostAnswer $postAnswer, protected int $rewardedCoin = 0)
    {

    }

    /**
     * @return PostAnswer
     */
    public function getPostAnswer()
    {
        return $this->postAnswer;
    }

    /**
     * @return int
     */
    public function getRewardedCoin()
    {
        return $this->rewardedCoin;
    }
}
