<?php

namespace App\Events;

use App\Models\AttachedSurvey;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AttachedSurveyStored
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(protected AttachedSurvey $attachedSurvey)
    {

    }

    /**
     * @return AttachedSurvey
     */
    public function getAttachedSurvey()
    {
        return $this->attachedSurvey;
    }
}
