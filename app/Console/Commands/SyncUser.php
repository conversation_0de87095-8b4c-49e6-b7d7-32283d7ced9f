<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Auto sync user profile.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        User::query()
            ->whereNotExists(DB::table('profiles')->whereColumn('users.user_id', 'profiles.user_id'))
            ->chunkById(100, function ($users) {
                foreach ($users as $user) {
                    $user->profile()->create();
                }
            });
    }
}
