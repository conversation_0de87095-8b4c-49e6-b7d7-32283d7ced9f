<?php

namespace App\Console\Commands;

use App\Exports\DataExport;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Excel;

class ExportData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:export-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export posts & answers data.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        return (new DataExport())->store('data.xlsx', 'local', Excel::XLSX);
    }
}
