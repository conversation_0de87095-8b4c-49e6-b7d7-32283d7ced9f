<?php

namespace App\Console\Commands;

use App\Models\QAViewedPost;
use Illuminate\Console\Command;

class OptimizeViewedPost extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:optimize-viewed-post';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize QA viewed posts.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        QAViewedPost::query()
            ->where('created_at', '<', now('UTC')->subDays(5))
            ->delete();
    }
}
