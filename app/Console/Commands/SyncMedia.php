<?php

namespace App\Console\Commands;

use App\Helpers\Storage;
use App\Models\Media;
use Illuminate\Console\Command;

class SyncMedia extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-media';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync media is_dark metadata.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $storage = storage();

        Media::query()
            ->where('is_dark', 0)
            ->chunkById(100, function ($items) use ($storage) {
                foreach ($items as $media) {
                    $media->is_dark = $this->determineIsDark($storage, $media);

                    if ($media->isDirty()) {
                        $media->saveQuietly();
                    }
                }
            });
    }

    /**
     * @param mixed $storage
     * @param Media $media
     * @return int 
     */
    public function determineIsDark(Storage $storage, Media $media): int
    {
        $imagePath = storage_path('app/public/' . ltrim($media->image, '/'));
        if (! file_exists($imagePath)) {
            return 0;
        }

        $avgLuminance = $storage->getAvgLuminance($imagePath);

        return (int) ($avgLuminance < 50);
    }
}
