<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncCommunity extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-community';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync student community.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $subQuery = DB::table('post_answers', 'pa')
            ->select('pa.post_id', 'pa.user_id')
            ->selectRaw('MIN(pa.created_at) AS first_answer_time')
            ->whereExists(
                DB::table('community_posts', 'cp')
                    ->whereColumn('cp.post_id', 'pa.post_id')
            )
            ->groupBy('pa.post_id', 'pa.user_id');

        $data = DB::table(DB::raw('(' .$subQuery->toRawSql(). ') as `tmp`'))
            ->select('tmp.post_id')
            ->selectRaw('MAX(tmp.first_answer_time) AS last_time_from_unique_users')
            ->groupBy('post_id')
            ->get();

        foreach ($data as $item) {
            DB::table('posts')
                ->where('post_id', $item->post_id)
                ->update(['sort_answered_at' => $item->last_time_from_unique_users]);
        }

        $ids = $data->pluck('post_id')->toArray();
        DB::table('posts', 'p')
            ->whereExists(
                DB::table('community_posts', 'cp')
                    ->whereColumn('cp.post_id', 'p.post_id')
            )
            ->whereNotIn('p.post_id', $ids)
            ->update([
                'sort_answered_at' => DB::raw('created_at'),
            ]);
    }
}
