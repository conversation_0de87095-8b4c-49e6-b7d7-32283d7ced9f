<?php

namespace App\Console\Commands;

use App\Actions\Reaction\CreateNewNotification;
use App\Models\Post;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class SendVoteBestAnswerNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:vote-answer-notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Push vote best answer notification.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        /** @var CreateNewNotification $createNewNotification */
        $createNewNotification = app(CreateNewNotification::class);
        $time = now('UTC')->subHours(168)->toDateTimeString();

        Post::query()
            ->with('createdBy')
            ->where('status', 1)

            /**
             * chưa vote
             */
            ->where('voted', 0)

            ->where('answer_count', '>', 0)

            /**
             * quá 24h kể từ câu trả lời đầu tiên
             */
            ->where('first_answered_at', '<', $time)

            /**
             * của người dùng đang kích hoạt
             */
            ->whereHas('createdBy', function (Builder $query) {
                $query->select('user_id')->where('status', 1);
            })

            /**
             * có câu trả lời của người khác, ngoài mình
             */
            ->whereHas('answers', function (Builder $query) {
                $query->select('answer_id')
                    ->where('status', 1)
                    ->where('post_answers.user_id', '!=', DB::raw('`posts`.`user_id`'))
                    ->whereHas('createdBy', function (Builder $query) {
                        $query->select('users.user_id')->where('users.status', 1);
                    });
            })
            ->chunkById(100, function ($records) use ($createNewNotification) {
                /** @var Collection|Post[] $records */
                if ($records->count() <= 0) {
                    return false;
                }

                foreach ($records as $record) {
                    $createNewNotification->execute($record->createdBy, [
                        'object_type' => 'post',
                        'object_id' => $record->getKey(),
                        'type' => 10,
                    ]);
                }
            });
    }
}
