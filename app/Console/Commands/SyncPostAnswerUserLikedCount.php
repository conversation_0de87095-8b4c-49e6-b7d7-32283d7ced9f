<?php

namespace App\Console\Commands;

use App\Models\PostAnswer;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncPostAnswerUserLikedCount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-post-answer-user-liked-count';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync post answer user liked count.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        PostAnswer::query()->where('status', 1)
            ->whereHas('createdBy', fn ($q) => $q->where('status', 1))
            ->whereHas('reactions', fn ($q) => $q->where('action', 'liked'))
            ->chunkById(100, function ($records) {
                $records->loadCount([
                    'likedReactions as user_liked_count' => function ($query) {
                        $query->select(DB::raw('COUNT(DISTINCT user_id)'));
                    },
                ]);

                /** @var PostAnswer $record */
                foreach ($records as $record) {
                    $record->updateQuietly([
                        'like_user_count' => $record->getAttribute('user_liked_count'),
                    ]);
                }
            });
    }
}
