<?php

namespace App\Console\Commands;

use App\Exports\LabelExport;
use App\Imports\AnswerDataImport;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Maatwebsite\Excel\Excel;
use Throwable;

class AutomaticTextLabeling extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:text-labeling';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatic text labeling.';

    /**
     * @var Client
     */
    protected $client;

    /**
     * @var array
     */
    protected $categories = [
        'アルバイト経験',
        'ストレス解消法',
        '休日の過ごし方',
        '健康のために気をつけていること',
        '記念日や誕生日は大切にする？',
        '平均的な睡眠時間',
        '朝起きて最初にすること',
        '夜寝る前のルーティン',
        '得意料理',
        '住んでいる街、住んでた街について',
        '中学・高校時代の部活動',
        'お気に入りのコスメやスキンケア',
        'こだわりのファッションアイテム',
        '好きなファッションブランド',
        '好きなヘアスタイルや髪色、お気に入りのヘアケア用品',
        '身だしなみで気を使っていること（スキンケア、ヘアセットなど）',
        'サプリメントについて',
        '好きなアニメ・漫画',
        '好きなゲーム',
        '好きなゲームのジャンル',
        '好きなキャラクター',
        '好きな映画(ジャンル)',
        '好きなテレビ番組',
        '好きなスポーツ（観る・する）',
        '好きな音楽のジャンル',
        '好きなミュージシャン、曲',
        '好きなガジェットやこだわりのPC周辺機器',
        '部屋のお気に入りアイテム',
        'ペットを飼った経験',
        '好きな食べ物',
        '好きなラーメンの種類',
        '好きなカレーの種類',
        '好きなお菓子・デザート',
        '好きなアイスのフレーバー',
        '好きな飲み物',
        '好きなお酒',
        '好きなカフェ',
        '行きつけのお店',
        '海外旅行の経験',
        '行ってみたい国や場所',
        '国内旅行で良かった場所',
        'もらって一番嬉しかったプレゼント',
        '欲しいもの',
        '理想のプロポーズ',
    ];

    /**
     * @var string
     */
    protected $promptText = <<<PROMPT_TEXT
以下の質問と回答のペアごとに、次の手順に従って分類してください:
1.質問と回答を確認し、それらが意味のある会話になっているかどうかを判断してください。
2.意味のある会話である場合、回答が上記の「項目」リストのいずれかに該当するかどうかを注意深く確認してください。
3.該当する場合は、最も適切な項目を "category" フィールドに出力してください。該当しない場合は "category" と "extracted_text" フィールドを空欄にしてください。
4.適切な項目が適用された場合は、そのカテゴリに基づいて分類可能な情報を回答から抽出し、 "extracted_text" フィールドに出力してください。
PROMPT_TEXT;

    /**
     * @var bool
     */
    protected $openAI = false;

    /**
     * @var string
     */
    protected $model = 'google/gemini-2.5-pro';

    /**
     * @return Client
     */
    protected function getClient()
    {
        if (! $this->client) {
            $apiKey = $this->openAI ? config('services.openai.api_key') : config('services.openrouter.api_key');
            $this->client = new Client([
                'base_uri' => $this->openAI ? 'https://api.openai.com/v1/' : 'https://openrouter.ai/api/v1/',
                'headers' => [
                    'Authorization' => 'Bearer ' . $apiKey,
                ],
            ]);
        }

        return $this->client;
    }

    /**
     * @param array $data
     * @return string
     */
    protected function getPrompt(array $data)
    {
        $categories = '- ' . implode("\n- ", $this->categories);
        $data = '- ' . implode("\n- ", $data);

        return <<<PROMPT
あなたはテキスト分類器です。
以下に、分類可能な項目のリストを示します：
$categories

{$this->promptText}
結果は必ずJSON配列で返し、各要素は以下を含みます：
- "id": データペアの番号またはID
- "category": 正確なカテゴリ名（または空欄）
- "extracted_text": 正確なカテゴリ名（または空欄）

データ：
$data

JSON 出力形式の提案：
[
  { "id": 1, "category": "感情", "extracted_text": "嬉しい" },
  { "id": 2, "category": "", "extracted_text": "" },
  { "id": 3, "category": "健康", "extracted_text": "毎日散歩する" }
]

説明:
ここで
- `id`: 質問と回答ペアの ID または番号
- `category`: 分類された項目（該当しない場合は空文字）
- `extracted_text`: 回答から抽出されたデータ（`category` が空の場合は空文字）
PROMPT;
    }

    /**
     * @param array $data
     * @return array
     */
    protected function parseData(array $data)
    {
        $result = [];
        foreach ($data as $item) {
            [$id, $question, $answer] = $item;
            $result[] = sprintf('id:%d, 質問:%s、 回答:%s', $id, $question, $answer);
        }

        return $result;
    }

    /**
     * @throws GuzzleException
     */
    protected function aiRequest(array $data)
    {
        $data = $this->parseData($data);
        $prompt = $this->getPrompt($data);

        $client = $this->getClient();
        $response = $client->post('chat/completions', [
            'json' => [
                'model' => $this->model,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $prompt,
                    ]
                ],
                'temperature' => 0,
                'top_p' => 1,
            ],
        ]);

        $body = json_decode($response->getBody(), true);

        return Arr::get($body, 'choices.0.message.content');
    }

    /**
     * @param array $data
     * @return mixed
     * @throws GuzzleException
     */
    protected function getResponse(array $data)
    {
        while (true) {
            $body = $this->aiRequest($data);
            $response = str_replace(['```json', '```'], '', $body);
            $response = json_decode($response, true);

            if ($response) {
                break;
            }

            dump($body);
        }

        return $response;
    }

    /**
     * @return void
     */
    public function handle()
    {
        $answerDataImport = app(AnswerDataImport::class);
        $dataFile = storage_path('tmp/Answerr_Data.xlsx');
        $items = $answerDataImport->toArray($dataFile, null, Excel::XLSX);
        $items = $items[0];

        $count = 0;
        $chunkedItems = array_chunk($items, 100);

        foreach ($chunkedItems as $items) {
            $data = [];
            $i = 1;
            foreach ($items as $item) {
                $question = $item[0] ?? '';
                $answer = $item[1] ?? '';
                if ($question && $answer) {
                    $item = Arr::prepend($item, $i);
                    $data[] = $item;
                    $i++;
                }
            }

            if ($data) {
                $collection = collect();

                try {
                    $response = $this->getResponse($data);

                    if ($response) {
                        foreach ($data as $item) {
                            $i = array_shift($item);

                            $key = array_search($i, array_column($response, 'id'));
                            $result = $key !== false ? $response[$key] : [];

                            $category = $result['category'] ?? '';
                            if ($category && ! in_array($category, $this->categories)) {
                                $category = '';
                            }

                            $item[] = $category;
                            $item[] = $category ? ($result['extracted_text'] ?? '') : '';
                            $collection->push($item);
                        }
                    }
                } catch (Throwable $e) {
                    dump($e->getMessage());
                }

                $count++;

                if ($collection->isNotEmpty()) {
                    $collection->prepend([
                        'Question',
                        'Answer',
                        '種別',
                        '抽出',
                    ]);

                    $dataFile = 'Answerr_Data_' . $count . '.xlsx';
                    (new LabelExport($collection))->store($dataFile, 'local', Excel::XLSX);
                }

                $this->info('Processed: ' . $count);
            }
        }

        $this->info('Done!');
    }
}
