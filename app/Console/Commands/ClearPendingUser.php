<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ClearPendingUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:clear-pending-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear pending users.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Clear pending users...');

        DB::table('users')
            ->where('status', 2)
            ->where('updated_at', '<=', now()->subHours(24))
            ->delete();

        $this->info('Pending users cleared successfully.');
    }
}
