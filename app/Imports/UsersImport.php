<?php

namespace App\Imports;

use App\Enums\ProfileType;
use App\Models\User;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class UsersImport implements ToModel, WithStartRow, WithValidation, SkipsOnFailure
{
    use Importable, SkipsFailures;

    /**
     * @var \Faker\Generator
     */
    protected $faker;

    /**
     * @return \Faker\Generator
     */
    public function getFaker()
    {
        if (! $this->faker) {
            $this->faker = fake();
        }

        return $this->faker;
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function customValidationAttributes()
    {
        return [
            '0' => 'name',
        ];
    }

    /**
     * @return string
     */
    protected function getPhoneNumber()
    {
        $faker = $this->getFaker();

        $phone = $faker->unique()->phoneNumber();
        return substr(8 . str_replace('-', '', $phone), 0, 11);
    }

    /**
     * @param array $row
     * @return User|null
     */
    public function model(array $row)
    {
        $user = User::query()->create([
            'name' => $row[0],
            'phone' => $this->getPhoneNumber(),
            'birthday' => Carbon::createFromFormat('Y.m.d', $row[1])->toDateString(),
            'total_point' => 0,
            'total_coin' => 0,
            'profile_type' => ProfileType::EMPLOYEE->value,
        ]);

        $user->profile()->create([
            'work' => $row[2],
            'expert' => $row[3],
            'service_in_charge' => $row[4],
            'brief' => $row[5],
        ]);

        return $user;
    }

    /**
     * @return int
     */
    public function startRow(): int
    {
        return 2;
    }

    /**
     * @return int
     */
    public function chunkSize(): int
    {
        return 500;
    }
}
