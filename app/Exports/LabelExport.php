<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\Exportable;

class LabelExport implements FromCollection
{
    use Exportable;

    protected $collection;

    /**
     * @param $collection
     */
    public function __construct($collection)
    {
        $this->collection = $collection;
    }

    /**
     * @return void
     */
    public function collection()
    {
        return $this->collection;
    }
}
