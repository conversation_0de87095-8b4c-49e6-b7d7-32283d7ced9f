<?php

namespace App\Exports;

use App\Models\Post;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;

class DataExport implements FromQuery
{
    use Exportable;

    /**
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Relations\Relation|\Illuminate\Database\Query\Builder|\Laravel\Scout\Builder
     */
    public function query()
    {
        return Post::query()
            ->selectRaw('`posts`.`content` as `post_content`, `post_answers`.`content` AS `answer_content`')
            ->leftJoin('post_answers', 'posts.post_id', '=', 'post_answers.post_id')
            ->where('posts.status', 1)
            ->where('post_answers.status', 1)
            ->orderByDesc('posts.post_id')
            ->orderByDesc('post_answers.answer_id');
    }
}
