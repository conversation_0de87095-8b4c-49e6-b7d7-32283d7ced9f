<?php

use App\Console\Commands\OptimizeViewedPost;
use App\Console\Commands\SendVoteBestAnswerNotification;
use App\Http\Middleware\AddRequestId;
use App\Http\Middleware\AuthenticatedAdmin;
use App\Http\Middleware\HandleInertiaRequests;
use App\Http\Middleware\AppendLazyResponseDataIfNeeded;
use App\Http\Middleware\VerifyAuthenticatedUser;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Symfony\Component\HttpFoundation\Response;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->append([
            AddRequestId::class,
        ]);

        $middleware->web(append: [
            HandleInertiaRequests::class,
            AddLinkHeadersForPreloadedAssets::class,
        ]);

        $middleware->api(append: [
            VerifyAuthenticatedUser::class,
        ]);

        $middleware->alias([
            'admin' => AuthenticatedAdmin::class,
            'prepare.data' => AppendLazyResponseDataIfNeeded::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        /**
         * report callback
         */
        $exceptions->report(function (Throwable $exception) {
            $errorMessage = $exception->getMessage();
            $request = request();

            $errors = [];
            $trace = true;
            if ($exception instanceof ValidationException) {
                $errorMessage = __('Validation Exception');
                $trace = false;

                foreach ($exception->errors() as $key => $error) {
                    $errors[$key] = $error[0];
                }
            }

            chatWork()->report([
                'url' => $request->fullUrl(),
                'data' => [
                    'get' => $request->query(),
                    'post' => $request->post(),
                    'files' => $request->allFiles(),
                ],
                'message' => $errorMessage ?: 'API Error',
                'trace' => $trace ? $exception->getTraceAsString() : null,
                'errors' => $errors,
            ]);
        });

        /**
         * respond callback
         */
        $exceptions->respond(function (Response $response, Throwable $exception, Request $request) {
            $isApiRequest = ($request->isJson() || str_starts_with($request->path(), 'api/')) && ! $request->hasHeader('x-inertia');

            /**
             * ThrottleRequestsException
             */
            if ($exception instanceof ThrottleRequestsException) {
                $headers = $exception->getHeaders();

                if ($isApiRequest) {
                    return response()->json(apiResponse()->error(
                        400,
                        __('throttle', [
                            'total' => Arr::get($headers, 'X-RateLimit-Limit', 5),
                            'seconds' => Arr::get($headers, 'Retry-After', 0),
                        ]),
                    ));
                }

                return back()->with('flash', [
                    'throttle' => __('throttle', [
                        'total' => Arr::get($headers, 'X-RateLimit-Limit', 5),
                        'seconds' => Arr::get($headers, 'Retry-After', 0),
                    ]),
                ]);
            }

            /**
             * api requests
             */
            if ($isApiRequest) {
                $errorCode = (int) (method_exists($exception, 'getStatusCode') ? $exception->getStatusCode() : $exception->getCode());
                $errorMessage = $exception->getMessage();

                $errors = [];
                if ($exception instanceof ValidationException) {
                    $routeName = (string) $request->route()->getName();

                    $errorCode = 2;
                    $errorMessage = __('validationException.' . $routeName);

                    foreach ($exception->errors() as $key => $error) {
                        $errors[$key] = $error[0];
                    }

                    if (config('app.debug')) {
                        chatWork()->report([
                            'url' => $request->fullUrl(),
                            'data' => [
                                'post' => $request->post(),
                                'files' => $request->allFiles(),
                            ],
                            'errors' => $errors,
                        ]);
                    }
                }

                if ($errorMessage === 'Unauthenticated.') {
                    $errorCode = 1;
                }

                /**
                 * blocked content
                 */
                if ($errorCode === 109) {
                    $errorMessage = __('blockedMessage');
                }

                if ($errorCode === 503) {
                    $errorMessage = __('Service Unavailable');
                }

                return response()->json(apiResponse()->error(
                    $errorCode,
                    $errorMessage,
                    null,
                    $errors,
                ));
            }

            /**
             * other errors
             */
            if (in_array($response->getStatusCode(), [500, 503, 404, 403])) {
                $statusCode = $response->getStatusCode();

                return Inertia::render('Error', [
                    'status' => $statusCode,
                    'message' => $exception->getMessage(),
                ])
                    ->toResponse($request)
                    ->setStatusCode($statusCode);
            }

            elseif ($response->getStatusCode() === 419) {
                return back()->with([
                    'message' => __('The page expired, please try again.'),
                ]);
            }

            return $response;
        });
    })

    /**
     * task schedule
     */
    ->withSchedule(function (Schedule $schedule) {
        $schedule->command(SendVoteBestAnswerNotification::class)
            ->everyThirtyMinutes()
            ->onOneServer();

        $schedule->command(OptimizeViewedPost::class)
            ->dailyAt('01:00')
            ->onOneServer();

        $schedule->call(function () {
            DB::statement('OPTIMIZE TABLE `qa_viewed_posts`');
        })
            ->name('Optimize viewed posts')
            ->twiceMonthly(1, 16, '01:10')
            ->onOneServer();
    })
    ->create();
