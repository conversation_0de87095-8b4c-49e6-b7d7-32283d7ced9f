<?php

use App\Helpers\ApiResponse;
use App\Helpers\ChatWork;
use App\Helpers\DateTimeHelper;
use App\Helpers\Encryptor;
use App\Helpers\LazyResponseData;
use App\Helpers\FirebaseCloudMessage;
use App\Helpers\OneTimePassword;
use App\Helpers\QueryLog;
use App\Helpers\QuestHelper;
use App\Helpers\Storage;
use App\Helpers\UserBlockHelper;

if (! function_exists('apiResponse')) {
    /**
     * @return ApiResponse
     */
    function apiResponse(): ApiResponse
    {
        return app(ApiResponse::class);
    }
}

if (! function_exists('chatWork')) {
    /**
     * @return ChatWork
     */
    function chatWork(): ChatWork
    {
        return app(ChatWork::class);
    }
}

if (! function_exists('cloudMessage')) {
    /**
     * @return FirebaseCloudMessage
     */
    function cloudMessage(): FirebaseCloudMessage
    {
        return app(FirebaseCloudMessage::class);
    }
}

if (! function_exists('datetime')) {
    /**
     * @return DateTimeHelper
     */
    function datetime(): DateTimeHelper
    {
        return app(DateTimeHelper::class);
    }
}

if (! function_exists('encryptor')) {
    /**
     * @return Encryptor
     */
    function encryptor(): Encryptor
    {
        return app(Encryptor::class);
    }
}

if (! function_exists('lazyData')) {
    /**
     * @return LazyResponseData
     */
    function lazyData(): LazyResponseData
    {
        return app(LazyResponseData::class);
    }
}

if (! function_exists('queryLog')) {
    /**
     * @return QueryLog
     */
    function queryLog(): QueryLog
    {
        return app(QueryLog::class);
    }
}

if (! function_exists('questHelper')) {
    /**
     * @return QuestHelper
     */
    function questHelper(): QuestHelper
    {
        return app(QuestHelper::class);
    }
}

if (! function_exists('otp')) {
    /**
     * @return OneTimePassword
     */
    function otp()
    {
        return app(OneTimePassword::class);
    }
}

if (! function_exists('storage')) {
    /**
     * @return Storage
     */
    function storage()
    {
        return app(Storage::class);
    }
}

if (! function_exists('blockHelper')) {
    /**
     * @return UserBlockHelper
     */
    function blockHelper()
    {
        return app(UserBlockHelper::class);
    }
}
