<?php

use App\Repositories\AnswerRepository;
use App\Repositories\AttachedSurveyRepository;
use App\Repositories\CommentRepository;
use App\Repositories\CommunityRepository;
use App\Repositories\FeedViewedStatRepository;
use App\Repositories\NewsRepository;
use App\Repositories\PostRepository;
use App\Repositories\PostAnswerRepository;
use App\Repositories\QuestionRepository;
use App\Repositories\QuestRepository;
use App\Repositories\SettingRepository;
use App\Repositories\SurveyRepository;
use App\Repositories\UserRepository;
use App\Repositories\PostViewHistoryRepository;
use App\Repositories\ViewedPostRepository;

if (! function_exists('answerRepository')) {
    /**
     * @return AnswerRepository
     */
    function answerRepository(): AnswerRepository
    {
        return app(AnswerRepository::class);
    }
}

if (! function_exists('attachedSurveyRepository')) {
    /**
     * @return AttachedSurveyRepository
     */
    function attachedSurveyRepository(): AttachedSurveyRepository
    {
        return app(AttachedSurveyRepository::class);
    }
}

if (! function_exists('commentRepository')) {
    /**
     * @return CommentRepository
     */
    function commentRepository(): CommentRepository
    {
        return app(CommentRepository::class);
    }
}

if (! function_exists('communityRepository')) {
    /**
     * @return CommunityRepository
     */
    function communityRepository(): CommunityRepository
    {
        return app(CommunityRepository::class);
    }
}

if (! function_exists('feedViewedRepository')) {
    /**
     * @return FeedViewedStatRepository
     */
    function feedViewedRepository(): FeedViewedStatRepository
    {
        return app(FeedViewedStatRepository::class);
    }
}

if (! function_exists('newsRepository')) {
    /**
     * @return NewsRepository
     */
    function newsRepository(): NewsRepository
    {
        return app(NewsRepository::class);
    }
}

if (! function_exists('postRepository')) {
    /**
     * @return PostRepository
     */
    function postRepository(): PostRepository
    {
        return app(PostRepository::class);
    }
}

if (! function_exists('postAnswerRepository')) {
    /**
     * @return PostAnswerRepository
     */
    function postAnswerRepository(): PostAnswerRepository
    {
        return app(PostAnswerRepository::class);
    }
}

if (! function_exists('questionRepository')) {
    /**
     * @return QuestionRepository
     */
    function questionRepository(): QuestionRepository
    {
        return app(QuestionRepository::class);
    }
}

if (! function_exists('questRepository')) {
    /**
     * @return QuestRepository
     */
    function questRepository(): QuestRepository
    {
        return app(QuestRepository::class);
    }
}

if (! function_exists('settingRepository')) {
    /**
     * @return SettingRepository
     */
    function settingRepository(): SettingRepository
    {
        return app(SettingRepository::class);
    }
}

if (! function_exists('surveyRepository')) {
    /**
     * @return SurveyRepository
     */
    function surveyRepository(): SurveyRepository
    {
        return app(SurveyRepository::class);
    }
}

if (! function_exists('userRepository')) {
    /**
     * @return UserRepository
     */
    function userRepository(): UserRepository
    {
        return app(UserRepository::class);
    }
}

if (! function_exists('postViewHistoryRepos')) {
    /**
     * @return PostViewHistoryRepository
     */
    function postViewHistoryRepos(): PostViewHistoryRepository
    {
        return app(PostViewHistoryRepository::class);
    }
}

if (! function_exists('viewedPostRepos')) {
    /**
     * @return ViewedPostRepository
     */
    function viewedPostRepos(): ViewedPostRepository
    {
        return app(ViewedPostRepository::class);
    }
}
