{"private": true, "type": "module", "scripts": {"dev": "vite --host=localhost", "build": "vite build"}, "devDependencies": {"@headlessui/vue": "^1.7.22", "@heroicons/vue": "v1", "@inertiajs/vue3": "^1.0.14", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.16", "axios": "^1.6.4", "chokidar": "^3.6.0", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.32", "postcss-import": "^16.1.0", "pug": "^3.0.3", "sass-embedded": "^1.77.8", "tailwindcss": "^3.4.10", "tailwindcss-primeui": "^0.3.4", "vite": "^5.4.11", "vue": "^3.3.13"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@primevue/themes": "^4.2.1", "@types/lodash": "^4.17.13", "@vueup/vue-quill": "^1.2.0", "izitoast": "^1.4.0", "lodash": "^4.17.21", "moment": "^2.30.1", "pinia": "^2.2.2", "primeicons": "^7.0.0", "primevue": "^4.2.1", "vue-i18n": "^9.14.0", "ziggy-js": "^2.3.0"}, "overrides": {"quill": "^2.0.2"}}