{"editor.fontFamily": "Monaco", "editor.fontSize": 14, "editor.cursorBlinking": "expand", "editor.mouseWheelZoom": true, "editor.minimap.autohide": "mouseover", "editor.minimap.enabled": false, "terminal.integrated.fontFamily": "Monaco", "terminal.integrated.fontSize": 14, "vscode_custom_css.imports": ["https://ductri.dev/assets/vscode-custom/tweak.css?v=2025-03-20.4", "https://ductri.dev/assets/vscode-custom/tweak.js"], "css.customData": [".vscode/tailwind.json"], "workbench.tree.indent": 20, "workbench.tree.renderIndentGuides": "none", "workbench.layoutControl.enabled": false, "workbench.tips.enabled": false, "editor.stickyScroll.enabled": false, "window.title": " ", "files.autoSave": "after<PERSON>elay", "files.exclude": {"**/node_modules": true, "**/vendor": false}, "circleci.filters.branchFilter": "allBranches", "intelephense.diagnostics.undefinedTypes": false, "intelephense.diagnostics.undefinedFunctions": false, "intelephense.diagnostics.undefinedConstants": false, "intelephense.diagnostics.undefinedClassConstants": false, "intelephense.diagnostics.undefinedMethods": false, "intelephense.diagnostics.undefinedProperties": false, "intelephense.diagnostics.undefinedVariables": true}