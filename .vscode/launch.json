{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Laravel Octane",
            "type": "php",
            "request": "launch",
            "program": "",
            "cwd": "${workspaceFolder}",
            "runtimeArgs": [
                "artisan",
                "octane:start",
                "--watch",
                "--port=8000",
                "--workers=1",
                "--task-workers=1"
            ],
            "port": 0,
        },
        {
            "name": "Redis Queue",
            "type": "php",
            "request": "launch",
            "program": "",
            "cwd": "${workspaceFolder}",
            "port": 0,
            "runtimeArgs": [
                "artisan",
                "queue:listen",
                "redis"
            ],
        },
    ],
}
